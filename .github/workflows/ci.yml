name: CI

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main, develop]

env:
  MAVEN_OPTS: '-Djava.security.egd=file:/dev/./urandom'
  MAVEN_CLI_OPTS: '--batch-mode --errors --fail-at-end --show-version -DinstallAtEnd=true -DdeployAtEnd=true'
  SPRING_PROFILES_ACTIVE: 'dev,swagger,no-liquibase'
  SPRING_DATASOURCE_URL: '*************************************************************************************************************************************************************************************'
  SPRING_DATASOURCE_USERNAME: 'root'
  SPRING_DATASOURCE_PASSWORD: 'root'
  SPRING_REDIS_HOST: 'localhost'
  SPRING_REDIS_PORT: '6379'
  SPRING_CONSUL_HOST: 'localhost'
  SPRING_CONSUL_PORT: '8500'

jobs:
  build:
    runs-on: ubuntu-latest
    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: root
          MYSQL_DATABASE: whiskerguard_org_service
        ports:
          - 3306:3306
        options: >-
          --health-cmd="mysqladmin ping -h localhost -u root -proot"
          --health-interval=10s
          --health-timeout=5s
          --health-retries=3
      redis:
        image: redis:6.2
        ports:
          - 6379:6379
        options: >-
          --health-cmd="redis-cli ping"
          --health-interval=10s
          --health-timeout=5s
          --health-retries=3
      consul:
        image: consul:1.15
        ports:
          - 8500:8500
        options: >-
          --health-cmd="wget --no-verbose --tries=1 --spider http://localhost:8500/v1/status/leader"
          --health-interval=10s
          --health-timeout=5s
          --health-retries=3

    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Set up JDK 17
        uses: actions/setup-java@v4
        with:
          java-version: '17'
          distribution: 'temurin'
          cache: 'maven'

      - name: Cache Maven packages
        uses: actions/cache@v3
        with:
          path: ~/.m2
          key: ${{ runner.os }}-m2-${{ hashFiles('**/pom.xml') }}
          restore-keys: ${{ runner.os }}-m2

      - name: Checkstyle
        run: ./mvnw checkstyle:check

      - name: Build and Test
        run: |
          ./mvnw clean verify -Pprod,webapp
          mkdir -p target/test-results
          touch target/test-results/TEST-dummy.xml
        env:
          SPRING_PROFILES_ACTIVE: ${{ env.SPRING_PROFILES_ACTIVE }}
          SPRING_DATASOURCE_URL: ${{ env.SPRING_DATASOURCE_URL }}
          SPRING_DATASOURCE_USERNAME: ${{ env.SPRING_DATASOURCE_USERNAME }}
          SPRING_DATASOURCE_PASSWORD: ${{ env.SPRING_DATASOURCE_PASSWORD }}
          SPRING_REDIS_HOST: ${{ env.SPRING_REDIS_HOST }}
          SPRING_REDIS_PORT: ${{ env.SPRING_REDIS_PORT }}
          SPRING_CONSUL_HOST: ${{ env.SPRING_CONSUL_HOST }}
          SPRING_CONSUL_PORT: ${{ env.SPRING_CONSUL_PORT }}

      - name: List Test Results Directory
        run: |
          ls -la target/test-results || true
          ls -la target/surefire-reports || true

      - name: SonarQube Scan
        if: github.ref == 'refs/heads/main'
        uses: sonarsource/sonarqube-scan-action@master
        env:
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
          SONAR_HOST_URL: ${{ secrets.SONAR_HOST_URL }}

      - name: Build Docker Image
        if: github.ref == 'refs/heads/main'
        run: ./mvnw package -Pprod jib:build
        env:
          DOCKER_USERNAME: ${{ secrets.DOCKER_USERNAME }}
          DOCKER_PASSWORD: ${{ secrets.DOCKER_PASSWORD }}

      - name: Upload Test Results
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: test-results
          path: target/surefire-reports/
          if-no-files-found: warn
          retention-days: 30

      - name: Upload Coverage Report
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: coverage-report
          path: target/site/jacoco/
          if-no-files-found: warn
          retention-days: 30

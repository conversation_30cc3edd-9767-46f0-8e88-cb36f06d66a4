---

# .cursor/bug-helper.md

## 🐛 问题定位模板（适用于 auth → org → 权限相关故障分析）

### 【微服务名称】whiskerguard-organization-service

### 【问题描述】

如：auth-service 调用 /api/employees/{id}/permissions 返回 403，或返回空数据。

### 【调用来源】

* 来源服务：auth-service / gateway-service
* 是否通过网关：是 / 否

### 【请求路径】

`GET /api/employees/{id}/permissions`

### 【请求 Header】

* Authorization: Bearer xxx
* X-Tenant-Id: xxx

### 【期望行为】

应返回员工的全部角色与权限信息，用于 JWT claims 生成。

### 【实际返回】

空列表 / 无权限 / 报错日志（贴出）

### 【涉及代码】

可粘贴：EmployeePermissionResource.java、PermissionServiceImpl.java、FeignClient 调用定义

### 【目标】

* [ ] 分析调用链路与安全配置
* [ ] 检查租户过滤或权限缺失问题
* [ ] 提供修复方案 / SQL 检查建议

---

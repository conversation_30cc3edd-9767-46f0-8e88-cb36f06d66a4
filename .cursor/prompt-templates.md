## 🎯 常用 AI 协作指令模板集

---

### ✅ 场景 1：生成查询接口（角色权限）

```text
【模块】EmployeePermissionResource
【目标】实现接口：根据员工ID查询角色与权限列表
【要求】
- 方法路径：GET /api/employees/{id}/permissions
- 返回结构为统一格式 {code, message, data}
- Controller + Service + DTO 三层完整
- 基于租户ID进行数据隔离
```

---

### ✅ 场景 2：生成租户隔离拦截器

```text
【模块】全局租户数据隔离
【目标】生成一个 HandlerInterceptor 拦截所有请求，提取 Token 中的 tenantId 注入上下文
【要求】
- 支持从 JWT 中提取 tenant_id
- 使用 ThreadLocal 或 TenantContextHolder 存储
- 所有 Repository 查询需添加租户过滤条件
```

---

### ✅ 场景 3：权限同步机制设计

```text
【目标】设计一个权限刷新机制，当角色权限被修改后，通知 auth-service 刷新权限缓存
【要求】
- 使用 FeignClient 回调或 Kafka 消息通知
- 记录权限变更日志
- 提供 Admin 手动触发刷新权限的 API
```

---

### ✅ 场景 4：数据结构生成

```text
【目标】生成组织结构树接口的数据模型
【要求】
- Entity: OrgUnit
- 字段包含 id, name, parentId, children
- 支持递归嵌套 JSON 结构
- 返回所有启用状态的组织单元，按照层级组织
```

---

### ✅ 场景 5：SQL 问题调试

```text
【问题】某个租户下调用 /api/roles 返回空列表，但数据库中存在数据
【目标】请检查是否租户过滤器失效或 repository 查询条件写法错误，并提供修复建议
```

---
description:
globs:
alwaysApply: false
---
# Protected Files Rules

## Read-only Files
The following files are protected and should not be modified:


## Code Style Rules
1. All new code must follow the existing code style
2. Use meaningful variable and method names
3. Add proper JavaDoc comments for public methods
4. Keep methods focused and single-purpose

## Security Rules
1. Never expose sensitive information in logs
2. Always validate user input
3. Use proper authentication and authorization checks
4. Follow the principle of least privilege

## Database Rules
1. Use JPA annotations for entity mapping
2. Include proper indexes for frequently queried fields
3. Use appropriate cascade types for relationships
4. Implement soft delete where appropriate

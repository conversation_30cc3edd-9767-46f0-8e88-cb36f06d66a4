## 🏷 项目：whiskerguard-organization-service

### 📌 服务职责

Whiskerguard Organization Service 是猫伯伯合规平台的“企业组织架构微服务”，负责企业多租户下的：

- 组织单元管理（OrgUnit）
- 员工管理（Employee）
- 职位管理（Position）
- 角色权限分配（Role、Permission）
- 多租户信息与资源隔离（Tenant）
- 安全审计与合规日志记录（AuditLog）

该服务是权限中心的关键支撑，被多个微服务依赖查询用户、角色与权限。

### 🧠 AI 使用说明

你是我的 AI 编程助手。我是该项目的高级系统架构师，目前正在使用 Spring Cloud + JHipster + Docker 开发一个分布式合规管理 SaaS 系统。请你不要重复解释基础语法，而是聚焦于架构建议、模块协同、性能优化和符合企业规范的代码实现。你的回答要精准、专业、简洁。

---

## 🔧 技术栈与架构说明

- Spring Boot 3.4.4 + Spring Cloud
- JHipster 8.10.0 生成，符合微服务分层架构规范
- 数据库：MySQL + Redis
- 服务注册：Consul
- 安全机制：JWT + RBAC（基于角色权限控制）
- 服务通信：OpenFeign
- 接口文档：SpringDoc / Swagger
- 审计合规：审计日志 + 数据变更跟踪

---

## ✅ 已实现主要模块

| 模块              | 描述                                             |
| ----------------- | ------------------------------------------------ |
| OrgUnit           | 树状结构的组织单元管理，支持上下级、启用禁用等   |
| Employee          | 支持员工信息、状态、组织关系管理                 |
| Position          | 职位定义与组织挂钩，支持层级、状态变更、历史记录 |
| Role / Permission | 角色定义、权限定义、角色-权限映射、员工-角色分配 |
| Tenant            | 多租户信息与配置、附件管理、租户隔离支持         |
| AuditLog          | 用户操作审计、安全日志追踪、合规检查             |

---

## 🔗 微服务集成依赖

- 被 auth-service 调用：获取用户信息（UserFullInfoDTO）、角色权限（权限嵌入 JWT Claims）
- 被 gateway-service 使用：进行租户/用户权限注入
- 内部调用：用于权限刷新、租户配置变更通知

---

## 🔐 安全与多租户支持

- 所有实体都带有 tenantId 字段（软隔离）
- 认证依赖 JWT，由网关或 auth-service 提供
- 支持基于租户的权限继承与控制

---

## 🎯 AI 编码要求

- 所有返回需为统一结构 `{code, message, data}`
- 代码需符合 Spring + JHipster 结构（DTO、Service、Mapper、Controller 分层）
- 命名需企业规范化：`employeeId`，`orgUnitCode`，`roleName` 等
- 所有接口需具备注释、鉴权注解、接口分组（Swagger）
- Token 中必须带有 tenant_id / user_id

---

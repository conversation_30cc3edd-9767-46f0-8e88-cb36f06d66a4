/**
 * 租户基础信息初始化表
 */
entity TenantInitialize {
  /** 主键 ID */
  id Long,
  /** 租户 ID */
  tenantId Long required,
  /** 类别：1、部门 2、岗位 3、角色 */
  type Integer required,
  /** 名称 */
  name String required maxlength(32),
  /** 编码 */
  code String required maxlength(32),
  /** 部门编码 */
  departmentCode String required maxlength(32),
  /** 描述 */
  description String maxlength(128),
  /** 扩展元数据 */
  metadata String maxlength(255),
  /** 乐观锁版本 */
  version Integer required,
  /** 创建者 */
  createdBy String maxlength(255),
  /** 创建时间 */
  createdAt Instant required,
  /** 更新者 */
  updatedBy String maxlength(255),
  /** 更新时间 */
  updatedAt Instant required,
  /** 软删除标志 */
  isDeleted Boolean required
}

dto TenantInitialize with mapstruct
service TenantInitialize with serviceImpl
paginate TenantInitialize with pagination

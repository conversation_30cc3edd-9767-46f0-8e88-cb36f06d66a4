/**
 * ============================================
 * 猫伯伯 · 风险模型多租户配置
 * ============================================
 */

/***************************************
 * 1. 枚举类型
 ***************************************/
enum RiskLevel {
  HIGH,
  MEDIUM,
  LOW
} // /** 风险等级 */

enum RiskRuleType {
  THRESHOLD, // /** 阈值判断（数值区间） */
  PATTERN, // /** 关键词 / 正则匹配 */
  SCRIPT     // /** 自定义脚本（SpEL / Groovy） */
}

/***************************************
 * 2. 实体定义
 ***************************************/

/** 风险模型主表 */
entity RiskModel {
  /** 租户 ID */
  tenantId Long      required,
  /** 模型名称 */
  name String    required maxlength(128),
  /** 模型描述 */
  description String    maxlength(512),
  /** 生效开始时间 */
  effectiveFrom Instant   required,
  /** 生效结束时间 */
  effectiveTo Instant,
  /** 是否默认模型 */
  isDefault Boolean   required,
  /** 乐观锁版本 */
  version Integer   required,
  /** 扩展元数据 */
  metadata String,
  /** 创建者 */
  createdBy String,
  /** 创建时间 */
  createdAt Instant   required,
  /** 更新者 */
  updatedBy String,
  /** 更新时间 */
  updatedAt Instant   required,
  /** 软删除标志 */
  isDeleted Boolean   required
}

/** 风险类别（高/中/低等，可自定义） */
entity RiskCategory {
  /** 租户 ID */
  tenantId Long      required,
  /** 类别名称 */
  name String    required maxlength(128),
  /** 风险等级 */
  level RiskLevel required,
  /** 入组表达式 */
  expression String    required maxlength(1024),
  /** 描述信息 */
  description String    maxlength(512),
  /** 乐观锁版本 */
  version Integer   required,
  /** 扩展元数据 */
  metadata String,
  /** 创建者 */
  createdBy String,
  /** 创建时间 */
  createdAt Instant   required,
  /** 更新者 */
  updatedBy String,
  /** 更新时间 */
  updatedAt Instant   required,
  /** 软删除标志 */
  isDeleted Boolean   required
}

/** 具体规则（关键词、阈值、脚本等） */
entity RiskRule {
  /** 租户 ID */
  tenantId Long         required,
  /** 规则编码 */
  code String       required minlength(1) maxlength(64),
  /** 规则名称 */
  name String       required maxlength(128),
  /** 规则类型 */
  ruleType RiskRuleType required,
  /** 规则参数 */
  params String,
  /** 描述信息 */
  description String       maxlength(512),
  /** 评分/权重 */
  score Integer,
  /** 乐观锁版本 */
  version Integer      required,
  /** 扩展元数据 */
  metadata String,
  /** 创建者 */
  createdBy String,
  /** 创建时间 */
  createdAt Instant      required,
  /** 更新者 */
  updatedBy String,
  /** 更新时间 */
  updatedAt Instant      required,
  /** 软删除标志 */
  isDeleted Boolean      required
}

/***************************************
 * 3. 实体关系
 ***************************************/
relationship ManyToOne {
  /** RiskCategory ➜ RiskModel */
  RiskCategory{riskModel(name)} to RiskModel,
  /** RiskRule ➜ RiskCategory */
  RiskRule{riskCategory(name)}  to RiskCategory
}

/***************************************
 * 4. 其他生成器设置（可按需开启）
 ***************************************/
service all with serviceImpl
dto all with mapstruct
paginate all with pagination

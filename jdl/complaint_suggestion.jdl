/**
 * 投诉与建议表
 */
entity ComplaintSuggestion {
  /** 租户ID */
  tenantId Long
  /** 员工ID */
  employeeId Long
  /** 详情 */
  detail String maxlength(500)
  /** 是否匿名：0、否 1、是 */
  isAnonymous Integer
  /** 联系方式 */
  contactWay String maxlength(32)
  /** 扩展元数据（JSONB） */
  metadata TextBlob
  /** 乐观锁版本 */
  version Integer required
  /** 创建者 */
  createdBy String maxlength(64)
  /** 创建时间 */
  createdAt Instant required
  /** 更新者 */
  updatedBy String maxlength(64)
  /** 更新时间 */
  updatedAt Instant required
  /** 软删除标志 */
  isDeleted Boolean required
}

/**
 * 投诉与建议附件表，用于存储合规案例相关的附件信息
 */
entity ComplaintSuggestionAttachment {
  /** 租户ID */
  tenantId Long
  /** 关联投诉与建议ID */
  suggestionId Long required
  /** 附件名称 */
  fileName String maxlength(256) required
  /** 附件存储路径或URL */
  filePath String maxlength(512) required
  /** 附件类型 */
  fileType String maxlength(32) required
  /** 附件大小 */
  fileSize String maxlength(32)
  /** 附件描述 */
  fileDesc String maxlength(512)
  /** 补充字段 */
  metadata TextBlob
  /** 当前版本号 */
  version Integer
  /** 上传者 */
  uploadedBy String maxlength(64) required
  /** 上传时间 */
  uploadedAt Instant
  /** 是否删除：0 表示正常 1 表示已删除 */
  isDeleted Boolean
}


/**
 * 分页配置
 */
paginate ComplaintSuggestion, ComplaintSuggestionAttachment with pagination

/**
 * 服务配置
 */
service ComplaintSuggestion, ComplaintSuggestionAttachment with serviceImpl

/**
 * DTO配置
 */
dto ComplaintSuggestion, ComplaintSuggestionAttachment with mapstruct

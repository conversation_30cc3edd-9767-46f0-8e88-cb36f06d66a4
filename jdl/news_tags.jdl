/**
 * 新闻标签关联实体
 *
 * 用于管理新闻和标签之间的多对多关联关系
 * 包含租户隔离、版本控制和审计字段
 */
entity NewsTags {
  /**
   * 主键 ID
   */
  id Long required

  /**
   * 租户 ID
   */
  tenantId Long required

  /**
   * 扩展元数据
   */
  metadata TextBlob

  /**
   * 乐观锁版本
   */
  version Integer required

  /**
   * 创建者
   */
  createdBy String

  /**
   * 创建时间
   */
  createdAt Instant required

  /**
   * 更新者
   */
  updatedBy String

  /**
   * 更新时间
   */
  updatedAt Instant required

  /**
   * 软删除标志
   */
  isDeleted Boolean required
}

/**
 * 关系定义
 * Relationship Definitions
 */
relationship ManyToOne {

  NewsTags{tags required} to Tag with builtInEntity,

  NewsTags{news required} to News with builtInEntity,
}

/**
 * 分页配置
 */
paginate NewsTags with pagination

/**
 * 服务配置
 */
service NewsTags with serviceImpl

/**
 * DTO 配置
 */
dto NewsTags with mapstruct

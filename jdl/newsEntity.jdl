/**
 * 完善后的新闻管理实体 — 添加到 whiskerguard-org-service 的 JDL
 * 包含状态枚举、Tag 实体、多对多标签、JSONB 元数据及审计字段
 * 增加了附件管理、评论状态、阅读记录、举报功能、标签分类等功能
 */

enum NewsStatus {
  DRAFT,
  PENDING_REVIEW,
  PUBLISHED,
  ARCHIVED
}

enum CommentStatus {
  PENDING_REVIEW,
  APPROVED,
  REJECTED,
  HIDDEN
}

enum ReportStatus {
  PENDING,
  PROCESSING,
  RESOLVED,
  REJECTED,
  IGNORED
}

entity NewsCategory {
  id Long,
  /** 状态 */
  status NewsStatus required,
  /** 排序序号 */
  sortOrder Integer,
  /** 分类名称 */
  name String required,
  /** 描述信息 */
  description String,
  /** 封面图 URL */
  coverImageUrl String,
  /** 扩展元数据（JSONB） */
  metadata String,
  /** 乐观锁版本 */
  version Integer required,
  /** 创建者 */
  createdBy String,
  /** 创建时间 */
  createdAt Instant required,
  /** 更新者 */
  updatedBy String,
  /** 更新时间 */
  updatedAt Instant required,
  /** 软删除标志 */
  isDeleted Boolean required
}

entity TagCategory {
  id Long,
  /** 分类名称 */
  name String required,
  /** 描述 */
  description String,
  /** 乐观锁版本 */
  version Integer required,
  /** 创建者 */
  createdBy String,
  /** 创建时间 */
  createdAt Instant required,
  /** 更新者 */
  updatedBy String,
  /** 更新时间 */
  updatedAt Instant required,
  /** 软删除标志 */
  isDeleted Boolean required
}

entity Tag {
  id Long,
  /** 标签名称 */
  name String required,
  /** 描述 */
  description String,
  /** 乐观锁版本 */
  version Integer required,
  /** 创建者 */
  createdBy String,
  /** 创建时间 */
  createdAt Instant required,
  /** 更新者 */
  updatedBy String,
  /** 更新时间 */
  updatedAt Instant required,
  /** 软删除标志 */
  isDeleted Boolean required
}

entity News {
  id Long,
  /** 状态 */
  status NewsStatus required,
  /** 排序序号 */
  sortOrder Integer,
  /** 副标题 */
  subtitle String,
  /** 标题 */
  title String required,
  /** 摘要 */
  summary String,
  /** 关键词（用于SEO，全局搜索） */
  keywords String,
  /** 正文内容 */
  content String,
  /** 发布时间 */
  publishDate Instant,
  /** 正式发布时戳 */
  publishedAt Instant,
  /** 浏览量 */
  viewCount Integer,
  /** 点赞数 */
  likeCount Integer,
  /** 评论数 */
  commentCount Integer,
  /** 分享数 */
  shareCount Integer,
  /** 封面图 URL */
  coverImageUrl String,
  /** 是否置顶 */
  isSticky Boolean,
  /** 置顶开始时间 */
  stickyStartTime Instant,
  /** 置顶结束时间 */
  stickyEndTime Instant,
  /** 扩展元数据（JSONB） */
  metadata String ,
  /** 乐观锁版本 */
  version Integer required,
  /** 创建者 */
  createdBy String,
  /** 创建时间 */
  createdAt Instant required,
  /** 更新者 */
  updatedBy String,
  /** 更新时间 */
  updatedAt Instant required,
  /** 软删除标志 */
  isDeleted Boolean required
}

entity NewsComment {
  id Long,
  /** 状态 */
  status CommentStatus required,
  /** 排序序号 */
  sortOrder Integer,
  /** 评论内容 */
  content String required,
  /** 点赞数 */
  likeCount Integer,
  /** 扩展元数据（JSONB） */
  metadata String,
  /** 乐观锁版本 */
  version Integer required,
  /** 创建者 */
  createdBy String,
  /** 创建时间 */
  createdAt Instant required,
  /** 更新者 */
  updatedBy String,
  /** 更新时间 */
  updatedAt Instant required,
  /** 软删除标志 */
  isDeleted Boolean required
}

entity NewsLike {
  id Long,
  /** 扩展元数据（JSONB） */
  metadata String ,
  /** 乐观锁版本 */
  version Integer required,
  /** 创建者 */
  createdBy String,
  /** 创建时间 */
  createdAt Instant required,
  /** 更新者 */
  updatedBy String,
  /** 更新时间 */
  updatedAt Instant required,
  /** 软删除标志 */
  isDeleted Boolean required
}

entity NewsAttachment {
  id Long,
  /** 附件名称 */
  name String required,
  /** 附件类型 */
  type String required,
  /** 附件URL */
  url String required,
  /** 附件大小(KB) */
  fileSize Long,
  /** 排序序号 */
  sortOrder Integer,
  /** 扩展元数据（JSONB） */
  metadata String ,
  /** 乐观锁版本 */
  version Integer required,
  /** 创建者 */
  createdBy String,
  /** 创建时间 */
  createdAt Instant required,
  /** 更新者 */
  updatedBy String,
  /** 更新时间 */
  updatedAt Instant required,
  /** 软删除标志 */
  isDeleted Boolean required
}

entity NewsReport {
  id Long,
  /** 举报原因 */
  reason String required,
  /** 举报详情 */
  details String ,
  /** 处理状态 */
  status ReportStatus required,
  /** 处理结果 */
  result String,
  /** 扩展元数据（JSONB） */
  metadata String ,
  /** 乐观锁版本 */
  version Integer required,
  /** 创建者 */
  createdBy String,
  /** 创建时间 */
  createdAt Instant required,
  /** 更新者 */
  updatedBy String,
  /** 更新时间 */
  updatedAt Instant required,
  /** 软删除标志 */
  isDeleted Boolean required
}

entity NewsReadRecord {
  id Long,
  /** 阅读时间 */
  readAt Instant required,
  /** 阅读来源 */
  source String,
  /** 阅读设备 */
  device String,
  /** 阅读时长(秒) */
  duration Integer,
  /** 扩展元数据（JSONB） */
  metadata String ,
  /** 乐观锁版本 */
  version Integer required,
  /** 创建者 */
  createdBy String,
  /** 创建时间 */
  createdAt Instant required,
  /** 更新者 */
  updatedBy String,
  /** 更新时间 */
  updatedAt Instant required,
  /** 软删除标志 */
  isDeleted Boolean required
}

relationship ManyToOne {
  // 分类与组织单元、多级分类
  NewsCategory{orgUnit(name)}    to OrgUnit with builtInEntity,
  NewsCategory{parent(name)}     to NewsCategory,

  // 新闻与分类、组织、作者
  News{category(name)}           to NewsCategory,
  News{orgUnit(name)}            to OrgUnit with builtInEntity,
  News{author(username)}         to Employee with builtInEntity,

  // 评论与新闻、回复、评论者
  NewsComment{news(title)}       to News,
  NewsComment{parent(id)}        to NewsComment,
  NewsComment{commenter(username)} to Employee with builtInEntity,

  // 点赞与新闻、用户
  NewsLike{news(title)}          to News,
  NewsLike{user(username)}       to Employee with builtInEntity,

  // 标签与分类
  Tag{category(name)}            to TagCategory,

  // 附件与新闻
  NewsAttachment{news(title)}    to News,

  // 举报与新闻、评论、举报者
  NewsReport{news(title)}        to News,
  NewsReport{comment(id)}        to NewsComment,
  NewsReport{reporter(username)} to Employee with builtInEntity,

  // 阅读记录与新闻、读者
  NewsReadRecord{news(title)}    to News,
  NewsReadRecord{reader(username)} to Employee with builtInEntity
}

relationship ManyToMany {
  // 新闻与标签多对多
  News{tags(name)} to Tag
}
dto * with mapstruct
service all with serviceImpl
paginate * with pagination

/**
 * 员工状态枚举
 */
enum EmployeeStatus {
  ACTIVE,     // 在职
  INACTIVE,   // 离职
  FROZEN      // 冻结
}

/**
 * 员工性别枚举
 */
enum EmployeeGender {
  UNKNOWN,    // 未知
  MALE,       // 男
  FEMALE      // 女
}

/**
 * 员工（Employee）实体
 */
entity Employee {
  /** 主键 ID */
  id Long,
  /** 租户 ID */
  tenantId Long required,
  /** 登录用户名 */
  username String required maxlength(64),
  /** 登录密码（加密存储） */
  password String required maxlength(128),
  /** 密码盐值 */
  salt String maxlength(32),
  /** 真实姓名 */
  realName String required maxlength(128),
  /** 头像 */
  avatar String required maxlength(255),
  /** 邮箱地址 */
  email String required maxlength(128),
  /** 手机号 */
  phone String maxlength(32),
  /** 性别 */
  gender EmployeeGender,
  /** 生日 */
  birthDate LocalDate,
  /** 身份证号 */
  idCard String maxlength(18),
  /** 员工编号（工号） */
  employeeNo String maxlength(64),
  /** 员工状态 */
  status EmployeeStatus required,
  /** 入职日期 */
  hireDate LocalDate,
  /** 离职日期 */
  leaveDate LocalDate,
  /** 扩展元数据 */
  metadata String,
  /** 乐观锁版本 */
  version Integer required,
  /** 创建者 */
  createdBy String,
  /** 创建时间 */
  createdAt Instant required,
  /** 更新者 */
  updatedBy String,
  /** 更新时间 */
  updatedAt Instant required,
  /** 软删除标志 */
  isDeleted Boolean required,
  /** 最后登录时间 */
  lastLoginTime Instant,
  /** 最后登录IP */
  lastLoginIp String maxlength(64),
  /** 登录失败次数 */
  loginFailureCount Integer,
  /** 账号锁定时间 */
  accountLockedTime Instant,
  /** 密码修改时间 */
  passwordChangedTime Instant,
  /** 密码过期时间 */
  passwordExpiredTime Instant,
  /** 是否首次登录 */
  isFirstLogin Boolean required,
  /** 是否强制修改密码 */
  forceChangePassword Boolean required
  /** 微信OpenID */
  wechatOpenId String maxlength(64),
  /** 微信UnionID */
  wechatUnionId String maxlength(64),
}

dto * with mapstruct

service * with serviceImpl

paginate * with pagination

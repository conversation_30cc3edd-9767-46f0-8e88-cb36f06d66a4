/**
 * 预约体验表
 * 用于存储用户预约体验产品的相关信息
 */

/**
 * 预约体验（Reservation）实体
 */
entity Reservation {
  /** 主键ID */
  id Long,
  /** 您的姓名 */
  name String required maxlength(64),
  /** 职位 */
  position String required maxlength(64),
  /** 手机号码 */
  mobile String required maxlength(32),
  /** 电子邮箱 */
  email String required maxlength(128),
  /** 公司名称 */
  company String required maxlength(128),
  /** 所属行业 */
  industry String required maxlength(64),
  /** 企业规模 */
  companySize String required,
  /** 最关注合规管理需求 */
  focusNeed String required,
  /** 其他需求说明 */
  otherDesc String,
  /** 补充字段 */
  metadata String,
  /** 当前版本号 */
  version Integer required,
  /** 创建者账号或姓名 */
  createdBy String maxlength(64),
  /** 创建时间 */
  createdAt Instant required,
  /** 最后修改者 */
  updatedBy String maxlength(64),
  /** 最后更新时间 */
  updatedAt Instant required,
  /** 是否删除：0 表示正常 1 表示已删除 */
  isDeleted Boolean required
}

/**
 * 分页配置
 */
paginate Reservation with pagination

/**
 * 服务配置
 */
service Reservation with serviceImpl

/**
 * DTO配置
 */
dto Reservation with mapstruct

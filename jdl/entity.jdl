/**
 * WhiskerGuard Organization Service JDL 定义（12 个实体）
 * 包含组织单元、员工、岗位、关联、角色、权限、租户等实体及枚举与关系
 */

/**
 * 组织单元类型枚举
 * 定义组织单元的分类：公司、部门、事业群、团队
 */
enum OrgUnitType {
  COMPANY,
  DEPARTMENT,
  BUSINESS_GROUP,
  TEAM
}

/**
 * 资源类型枚举
 * 区分后端接口权限与前端菜单/按钮/字段权限
 */
enum ResourceType {
  API,
  MENU,
  BUTTON,
  FIELD
}

/**
 * 岗位分类枚举
 */
enum PositionCategory {
  MANAGEMENT("管理类"),
  TECHNICAL("技术类"),
  BUSINESS("业务类"),
  SUPPORT("支持类")
}

/**
 * 员工状态枚举
 */
enum EmployeeStatus {
  ACTIVE, // 在职
  INACTIVE, // 离职
  FROZEN      // 冻结
}

/**
 * 员工性别枚举
 */
enum EmployeeGender {
  UNKNOWN, // 未知
  MALE, // 男
  FEMALE      // 女
}

/**
 * 组织单元（OrgUnit）实体
 * 管理公司、部门、事业群、团队等组织层级，并维护树形结构
 */
entity OrgUnit {
  /** 主键 ID */
  id Long,
  /** 租户 ID，标识不同租户的数据隔离 */
  tenantId Long required,
  /** 组织单位名称 */
  name String required maxlength(128),
  /** 唯一编码，用于外部系统对接或导入映射 */
  code String required maxlength(64),
  /** 组织单元类型 */
  type OrgUnitType required,
  /** 层级深度，根节点为 1 */
  level Integer required,
  /** 状态：1=启用，0=禁用 */
  status Integer required,
  /** 排序序号 */
  sortOrder Integer,
  /** 描述信息 */
  description String,
  /** 扩展元数据（JSON） */
  metadata String,
  /** 乐观锁版本 */
  version Integer required,
  /** 创建者 */
  createdBy String,
  /** 创建时间 */
  createdAt Instant required,
  /** 更新者 */
  updatedBy String,
  /** 更新时间 */
  updatedAt Instant required,
  /** 软删除标志 */
  isDeleted Boolean required
}

/**
 * 员工（Employee）实体
 */
entity Employee {
  /** 主键 ID */
  id Long,
  /** 租户 ID */
  tenantId Long required,
  /** 登录用户名 */
  username String required maxlength(64),
  /** 登录密码（加密存储） */
  password String required maxlength(128),
  /** 密码盐值 */
  salt String maxlength(32),
  /** 真实姓名 */
  realName String required maxlength(128),
  /** 邮箱地址 */
  email String required maxlength(128),
  /** 手机号 */
  phone String maxlength(32),
  /** 性别 */
  gender EmployeeGender,
  /** 生日 */
  birthDate LocalDate,
  /** 身份证号 */
  idCard String maxlength(18),
  /** 员工编号（工号） */
  employeeNo String maxlength(64),
  /** 员工状态 */
  status EmployeeStatus required,
  /** 入职日期 */
  hireDate LocalDate,
  /** 离职日期 */
  leaveDate LocalDate,
  /** 扩展元数据 */
  metadata String,
  /** 乐观锁版本 */
  version Integer required,
  /** 创建者 */
  createdBy String,
  /** 创建时间 */
  createdAt Instant required,
  /** 更新者 */
  updatedBy String,
  /** 更新时间 */
  updatedAt Instant required,
  /** 软删除标志 */
  isDeleted Boolean required,
  /** 最后登录时间 */
  lastLoginTime Instant,
  /** 最后登录IP */
  lastLoginIp String maxlength(64),
  /** 登录失败次数 */
  loginFailureCount Integer,
  /** 账号锁定时间 */
  accountLockedTime Instant,
  /** 密码修改时间 */
  passwordChangedTime Instant,
  /** 密码过期时间 */
  passwordExpiredTime Instant,
  /** 是否首次登录 */
  isFirstLogin Boolean required,
  /** 是否强制修改密码 */
  forceChangePassword Boolean required
}

/**
 * 岗位/职位（Position）实体
 */
entity Position {
  /** 主键 ID */
  id Long,
  /** 租户 ID */
  tenantId Long required,
  /** 岗位编码 */
  code String required maxlength(64) unique,
  /** 岗位名称 */
  name String required maxlength(128),
  /** 岗位级别 */
  level Integer min(1) max(10),
  /** 岗位分类 */
  category PositionCategory,
  /** 描述信息 */
  description String,
  /** 扩展元数据 */
  metadata String,
  /** 乐观锁版本 */
  version Integer required,
  /** 创建者 */
  createdBy String,
  /** 创建时间 */
  createdAt Instant required,
  /** 更新者 */
  updatedBy String,
  /** 更新时间 */
  updatedAt Instant required,
  /** 软删除标志 */
  isDeleted Boolean required
}

/**
 * 员工—组织关联（EmployeeOrg）实体
 */
entity EmployeeOrg {
  /** 主键 ID */
  id Long,
  /** 租户 ID */
  tenantId Long required,
  /** 任职开始日期 */
  startDate LocalDate,
  /** 任职结束日期 */
  endDate LocalDate,
  /** 是否主部门 */
  isPrimary Boolean required,
  /** 乐观锁版本 */
  version Integer required,
  /** 创建者 */
  createdBy String,
  /** 创建时间 */
  createdAt Instant required,
  /** 更新者 */
  updatedBy String,
  /** 更新时间 */
  updatedAt Instant required,
  /** 软删除标志 */
  isDeleted Boolean required
}

/**
 * 角色（Role）实体
 */
entity Role {
  /** 主键 ID */
  id Long,
  /** 租户 ID */
  tenantId Long required,
  /** 角色名称 */
  name String required maxlength(64),
  /** 角色编码 */
  code String required maxlength(64),
  /** 描述 */
  description String,
  /** 状态 */
  status Integer required,
  /** 扩展元数据 */
  metadata String,
  /** 乐观锁版本 */
  version Integer required,
  /** 创建者 */
  createdBy String,
  /** 创建时间 */
  createdAt Instant required,
  /** 更新者 */
  updatedBy String,
  /** 更新时间 */
  updatedAt Instant required,
  /** 软删除标志 */
  isDeleted Boolean required
}

/**
 * 权限（Permission）实体
 */
entity Permission {
  /** 主键 ID */
  id Long,
  /** 租户 ID */
  tenantId Long required,
  /** 服务名称 */
  serviceName String required maxlength(64),
  /** 权限编码 */
  code String required maxlength(64),
  /** 权限名称 */
  name String required maxlength(128),
  /** 资源类型 */
  resourceType ResourceType required,
  /** URL 模式 */
  urlPattern String maxlength(256),
  /** HTTP 方法 */
  method String maxlength(16),
  /** 前端路由 */
  frontendRoute String maxlength(128),
  /** 后端接口 */
  backendUrl String maxlength(128),
  /** 图标 */
  icon String maxlength(64),
  /** 排序 */
  sortOrder Integer,
  /** 组件路径 */
  component String,
  /** 重定向 */
  redirect String,
  /** 描述 */
  description String,
  /** 扩展元数据 */
  metadata String,
  /** 乐观锁版本 */
  version Integer required,
  /** 创建者 */
  createdBy String,
  /** 创建时间 */
  createdAt Instant required,
  /** 更新者 */
  updatedBy String,
  /** 更新时间 */
  updatedAt Instant required,
  /** 软删除标志 */
  isDeleted Boolean required
}

/**
 * 角色—权限（RolePermission）实体
 */
entity RolePermission {
  /** 主键 ID */
  id Long,
  /** 租户 ID */
  tenantId Long required,
  /** 乐观锁版本 */
  version Integer required,
  /** 创建者 */
  createdBy String,
  /** 创建时间 */
  createdAt Instant required,
  /** 更新者 */
  updatedBy String,
  /** 更新时间 */
  updatedAt Instant required,
  /** 软删除标志 */
  isDeleted Boolean required
}

/**
 * 员工—角色（EmployeeRole）实体
 */
entity EmployeeRole {
  /** 主键 ID */
  id Long,
  /** 租户 ID */
  tenantId Long required,
  /** 分配者 */
  assignedBy String,
  /** 分配时间 */
  assignedAt Instant,
  /** 乐观锁版本 */
  version Integer required,
  /** 创建者 */
  createdBy String,
  /** 创建时间 */
  createdAt Instant required,
  /** 更新者 */
  updatedBy String,
  /** 更新时间 */
  updatedAt Instant required,
  /** 软删除标志 */
  isDeleted Boolean required
}

/**
 * 审计日志（AuditLog）实体
 */
entity AuditLog {
  /** 主键 ID */
  id Long,
  /** 租户 ID */
  tenantId Long required,
  /** 实体名称 */
  entityName String required maxlength(64),
  /** 实体 ID */
  entityId Long required,
  /** 操作类型 */
  operation String required maxlength(16),
  /** 操作者 */
  operator String maxlength(64),
  /** 操作时间 */
  timestamp Instant required,
  /** 差异 (JSON) */
  diff String,
  /** 乐观锁版本 */
  version Integer required,
  /** 创建者 */
  createdBy String,
  /** 创建时间 */
  createdAt Instant required,
  /** 更新者 */
  updatedBy String,
  /** 更新时间 */
  updatedAt Instant required,
  /** 软删除标志 */
  isDeleted Boolean required
}

/**
 * 租户（Tenant）实体
 */
entity Tenant {
  /** 主键 ID */
  id Long,
  /** 租户编码 */
  tenantCode String required maxlength(64),
  /** 租户名称 */
  name String required maxlength(128),
  /** 租户状态 */
  status Integer required,
  /** 套餐类型 */
  subscriptionPlan String maxlength(64),
  /** 套餐开始日期 */
  subscriptionStart LocalDate,
  /** 套餐结束日期 */
  subscriptionEnd LocalDate,
  /** 联系人邮箱 */
  contactEmail String maxlength(128),
  /** 联系人电话 */
  contactPhone String maxlength(32),
  /** 扩展元数据 */
  metadata String,
  /** 乐观锁版本 */
  version Integer required,
  /** 创建者 */
  createdBy String,
  /** 创建时间 */
  createdAt Instant required,
  /** 更新者 */
  updatedBy String,
  /** 更新时间 */
  updatedAt Instant required,
  /** 软删除标志 */
  isDeleted Boolean required,
  /** 父级租户 ID */
  parentId Long
}

/**
 * 租户详情（TenantProfile）实体
 */
entity TenantProfile {
  /** 主键 ID */
  id Long,
  /** 工商注册号 */
  registrationNumber String,
  /** 注册日期 */
  registrationDate LocalDate,
  /** 注册资本 */
  registeredCapital BigDecimal,
  /** 公司类型 */
  companyType String,
  /** 经营范围 */
  businessScope String,
  /** 所属行业 */
  industry String,
  /** 税务登记号 */
  taxRegistrationNumber String,
  /** 组织机构代码 */
  organizationCode String,
  /** 注册地址 */
  registeredAddress String,
  /** 邮政编码 */
  postalCode String,
  /** 官网 */
  website String,
  /** 传真 */
  fax String,
  /** 联系人 */
  contactPerson String,
  /** 联系人手机 */
  contactMobile String,
  /** 联系人邮箱 */
  contactEmail String,
  /** 开户行 */
  bankName String,
  /** 银行账号 */
  bankAccount String,
  /** 营业执照路径 */
  businessLicensePath String,
  /** 法人代表 */
  legalPerson String,
  /** 法人证件号 */
  legalPersonId String,
  /** 扩展元数据 */
  metadata String,
  /** 乐观锁版本 */
  version Integer required,
  /** 创建者 */
  createdBy String,
  /** 创建时间 */
  createdAt Instant required,
  /** 更新者 */
  updatedBy String,
  /** 更新时间 */
  updatedAt Instant required,
  /** 软删除标志 */
  isDeleted Boolean required
}

/**
 * 租户附件（TenantAttachment）实体
 */
entity TenantAttachment {
  /** 主键 ID */
  id Long,
  /** 附件类型 */
  type String,
  /** 文件 URL */
  fileUrl String,
  /** 文件名 */
  fileName String,
  /** 文件大小 */
  fileSize Long,
  /** 描述 */
  description String,
  /** 扩展元数据 */
  metadata String,
  /** 乐观锁版本 */
  version Integer required,
  /** 上传者 */
  uploadedBy String,
  /** 上传时间 */
  uploadedAt Instant required,
  /** 更新者 */
  updatedBy String,
  /** 更新时间 */
  updatedAt Instant required,
  /** 软删除标志 */
  isDeleted Boolean required
}

/**
 * 实体关系定义
 */
relationship ManyToOne {
  OrgUnit{parent} to OrgUnit,
  EmployeeOrg{employee} to Employee,
  EmployeeOrg{orgUnit} to OrgUnit,
  EmployeeOrg{position} to Position,
  Role{parent} to Role,
  Permission{parent} to Permission,
  EmployeeRole{employee} to Employee,
  EmployeeRole{role} to Role,
  EmployeeRole{orgUnit} to OrgUnit,
  RolePermission{role} to Role,
  RolePermission{permission} to Permission,
  TenantProfile{tenant} to Tenant,
  TenantAttachment{tenant} to Tenant
}

dto * with mapstruct
service all with serviceImpl
paginate * with pagination

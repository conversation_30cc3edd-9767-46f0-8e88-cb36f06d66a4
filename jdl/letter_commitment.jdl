/**
 * 承诺书（LetterCommitment）实体
 * 管理员工的各类承诺书，包括合规承诺等
 */
entity LetterCommitment {
  /**
   * 主键 ID
   */
  id Long,

  /**
   * 租户 ID，标识不同租户的数据隔离
   */
  tenantId Long required,

  /**
   * 员工 ID
   */
  employeeId Long required,

  /**
   * 类别：1、合规承诺
   */
  type Integer required,

  /**
   * 文件地址
   */
  filePath String required maxlength(64),

  /**
   * 是否已签名：0、否 1、是
   */
  isSigned Boolean required,

  /**
   * 扩展元数据（JSON）
   */
  metadata String maxlength(255),

  /**
   * 乐观锁版本
   */
  version Integer required,

  /**
   * 创建者
   */
  createdBy String maxlength(255),

  /**
   * 创建时间
   */
  createdAt Instant required,

  /**
   * 更新者
   */
  updatedBy String maxlength(255),

  /**
   * 更新时间
   */
  updatedAt Instant required,

  /**
   * 软删除标志
   */
  isDeleted Boolean required
}

/**
 * 分页配置
 */
paginate LetterCommitment with pagination

/**
 * DTO 配置
 */
dto LetterCommitment with mapstruct

/**
 * 服务配置
 */
service LetterCommitment with serviceImpl

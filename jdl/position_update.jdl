
/**
 * 岗位/职位（Position）实体
 */
entity Position {
  /** 主键 ID */
  id Long,
  /** 租户 ID */
  tenantId Long required,
  /** 岗位编码 */
  code String required maxlength(64) unique,
  /** 岗位名称 */
  name String required maxlength(128),
  /** 岗位级别 */
  level Integer min(1) max(10),
  /** 岗位分类 */
  category PositionCategory,
  /** 描述信息 */
  description String,
  /** 扩展元数据 */
  metadata String,
  /** 乐观锁版本 */
  version Integer required,
  /** 创建者 */
  createdBy String,
  /** 创建时间 */
  createdAt Instant required,
  /** 更新者 */
  updatedBy String,
  /** 更新时间 */
  updatedAt Instant required,
  /** 软删除标志 */
  isDeleted Boolean required
}

/**
 * 岗位分类枚举
 */
enum PositionCategory {
  MANAGEMENT("管理类"),
  TECHNICAL("技术类"),
  BUSINESS("业务类"),
  SUPPORT("支持类")
}


/**
 * 实体关系定义
 */
relationship ManyToOne {
  Position{orgUnit} to OrgUnit with builtInEntity,
}


dto Position with mapstruct
service Position with serviceImpl
paginate Position with pagination

package com.whiskerguard.organization.service.mapper;

import static com.whiskerguard.organization.domain.RolePermissionAsserts.*;
import static com.whiskerguard.organization.domain.RolePermissionTestSamples.*;

import com.whiskerguard.organization.IntegrationTest;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

@IntegrationTest
class RolePermissionMapperTest {

    @Autowired
    private RolePermissionMapper rolePermissionMapper;

    @Test
    void shouldConvertToDtoAndBack() {
        var expected = getRolePermissionSample1();
        var actual = rolePermissionMapper.toEntity(rolePermissionMapper.toDto(expected));
        assertRolePermissionAllPropertiesEquals(expected, actual);
    }
}

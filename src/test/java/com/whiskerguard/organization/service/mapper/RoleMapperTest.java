package com.whiskerguard.organization.service.mapper;

import static com.whiskerguard.organization.domain.RoleAsserts.*;
import static com.whiskerguard.organization.domain.RoleTestSamples.*;

import com.whiskerguard.organization.IntegrationTest;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

@IntegrationTest
class RoleMapperTest {

    @Autowired
    private RoleMapper roleMapper;

    @Test
    void shouldConvertToDtoAndBack() {
        var expected = getRoleSample1();
        var actual = roleMapper.toEntity(roleMapper.toDto(expected));
        assertRoleAllPropertiesEquals(expected, actual);
    }
}

package com.whiskerguard.organization.service.mapper;

import static com.whiskerguard.organization.domain.AuditLogAsserts.*;
import static com.whiskerguard.organization.domain.AuditLogTestSamples.*;

import com.whiskerguard.organization.IntegrationTest;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

@IntegrationTest
class AuditLogMapperTest {

    @Autowired
    private AuditLogMapper auditLogMapper;

    @Test
    void shouldConvertToDtoAndBack() {
        var expected = getAuditLogSample1();
        var actual = auditLogMapper.toEntity(auditLogMapper.toDto(expected));
        assertAuditLogAllPropertiesEquals(expected, actual);
    }
}

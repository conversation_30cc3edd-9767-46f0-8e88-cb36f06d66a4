package com.whiskerguard.organization.service.mapper;

import static com.whiskerguard.organization.domain.NewsCommentAsserts.*;
import static com.whiskerguard.organization.domain.NewsCommentTestSamples.*;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class NewsCommentMapperTest {

    private NewsCommentMapper newsCommentMapper;

    @BeforeEach
    void setUp() {
        newsCommentMapper = new NewsCommentMapperImpl();
    }

    @Test
    void shouldConvertToDtoAndBack() {
        var expected = getNewsCommentSample1();
        var actual = newsCommentMapper.toEntity(newsCommentMapper.toDto(expected));
        assertNewsCommentAllPropertiesEquals(expected, actual);
    }
}

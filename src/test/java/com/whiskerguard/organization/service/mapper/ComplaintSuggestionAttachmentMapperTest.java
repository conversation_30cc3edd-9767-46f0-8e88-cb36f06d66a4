package com.whiskerguard.organization.service.mapper;

import static com.whiskerguard.organization.domain.ComplaintSuggestionAttachmentAsserts.*;
import static com.whiskerguard.organization.domain.ComplaintSuggestionAttachmentTestSamples.*;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class ComplaintSuggestionAttachmentMapperTest {

    private ComplaintSuggestionAttachmentMapper complaintSuggestionAttachmentMapper;

    @BeforeEach
    void setUp() {
        complaintSuggestionAttachmentMapper = new ComplaintSuggestionAttachmentMapperImpl();
    }

    @Test
    void shouldConvertToDtoAndBack() {
        var expected = getComplaintSuggestionAttachmentSample1();
        var actual = complaintSuggestionAttachmentMapper.toEntity(complaintSuggestionAttachmentMapper.toDto(expected));
        assertComplaintSuggestionAttachmentAllPropertiesEquals(expected, actual);
    }
}

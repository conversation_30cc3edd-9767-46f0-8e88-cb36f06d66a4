package com.whiskerguard.organization.service.mapper;

import static com.whiskerguard.organization.domain.EmployeeRoleAsserts.*;
import static com.whiskerguard.organization.domain.EmployeeRoleTestSamples.*;

import com.whiskerguard.organization.IntegrationTest;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

@IntegrationTest
class EmployeeRoleMapperTest {

    @Autowired
    private EmployeeRoleMapper employeeRoleMapper;

    @Test
    void shouldConvertToDtoAndBack() {
        var expected = getEmployeeRoleSample1();
        var actual = employeeRoleMapper.toEntity(employeeRoleMapper.toDto(expected));
        assertEmployeeRoleAllPropertiesEquals(expected, actual);
    }
}

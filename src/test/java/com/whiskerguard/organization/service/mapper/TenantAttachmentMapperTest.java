package com.whiskerguard.organization.service.mapper;

import static com.whiskerguard.organization.domain.TenantAttachmentAsserts.*;
import static com.whiskerguard.organization.domain.TenantAttachmentTestSamples.*;

import com.whiskerguard.organization.IntegrationTest;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

@IntegrationTest
class TenantAttachmentMapperTest {

    @Autowired
    private TenantAttachmentMapper tenantAttachmentMapper;

    @Test
    void shouldConvertToDtoAndBack() {
        var expected = getTenantAttachmentSample1();
        var actual = tenantAttachmentMapper.toEntity(tenantAttachmentMapper.toDto(expected));
        assertTenantAttachmentAllPropertiesEquals(expected, actual);
    }
}

package com.whiskerguard.organization.service.mapper;

import static com.whiskerguard.organization.domain.RiskModelAsserts.*;
import static com.whiskerguard.organization.domain.RiskModelTestSamples.*;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class RiskModelMapperTest {

    private RiskModelMapper riskModelMapper;

    @BeforeEach
    void setUp() {
        riskModelMapper = new RiskModelMapperImpl();
    }

    @Test
    void shouldConvertToDtoAndBack() {
        var expected = getRiskModelSample1();
        var actual = riskModelMapper.toEntity(riskModelMapper.toDto(expected));
        assertRiskModelAllPropertiesEquals(expected, actual);
    }
}

package com.whiskerguard.organization.service.mapper;

import static com.whiskerguard.organization.domain.NewsTagsAsserts.*;
import static com.whiskerguard.organization.domain.NewsTagsTestSamples.*;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class NewsTagsMapperTest {

    private NewsTagsMapper newsTagsMapper;

    @BeforeEach
    void setUp() {
        newsTagsMapper = new NewsTagsMapperImpl();
    }

    @Test
    void shouldConvertToDtoAndBack() {
        var expected = getNewsTagsSample1();
        var actual = newsTagsMapper.toEntity(newsTagsMapper.toDto(expected));
        assertNewsTagsAllPropertiesEquals(expected, actual);
    }
}

package com.whiskerguard.organization.service.mapper;

import static com.whiskerguard.organization.domain.RiskRuleAsserts.*;
import static com.whiskerguard.organization.domain.RiskRuleTestSamples.*;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class RiskRuleMapperTest {

    private RiskRuleMapper riskRuleMapper;

    @BeforeEach
    void setUp() {
        riskRuleMapper = new RiskRuleMapperImpl();
    }

    @Test
    void shouldConvertToDtoAndBack() {
        var expected = getRiskRuleSample1();
        var actual = riskRuleMapper.toEntity(riskRuleMapper.toDto(expected));
        assertRiskRuleAllPropertiesEquals(expected, actual);
    }
}

package com.whiskerguard.organization.service.mapper;

import static com.whiskerguard.organization.domain.ComplaintSuggestionAsserts.*;
import static com.whiskerguard.organization.domain.ComplaintSuggestionTestSamples.*;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class ComplaintSuggestionMapperTest {

    private ComplaintSuggestionMapper complaintSuggestionMapper;

    @BeforeEach
    void setUp() {
        complaintSuggestionMapper = new ComplaintSuggestionMapperImpl();
    }

    @Test
    void shouldConvertToDtoAndBack() {
        var expected = getComplaintSuggestionSample1();
        var actual = complaintSuggestionMapper.toEntity(complaintSuggestionMapper.toDto(expected));
        assertComplaintSuggestionAllPropertiesEquals(expected, actual);
    }
}

package com.whiskerguard.organization.service.mapper;

import static com.whiskerguard.organization.domain.TagCategoryAsserts.*;
import static com.whiskerguard.organization.domain.TagCategoryTestSamples.*;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class TagCategoryMapperTest {

    private TagCategoryMapper tagCategoryMapper;

    @BeforeEach
    void setUp() {
        tagCategoryMapper = new TagCategoryMapperImpl();
    }

    @Test
    void shouldConvertToDtoAndBack() {
        var expected = getTagCategorySample1();
        var actual = tagCategoryMapper.toEntity(tagCategoryMapper.toDto(expected));
        assertTagCategoryAllPropertiesEquals(expected, actual);
    }
}

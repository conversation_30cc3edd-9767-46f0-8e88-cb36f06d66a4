package com.whiskerguard.organization.service.mapper;

import static com.whiskerguard.organization.domain.LetterCommitmentAsserts.*;
import static com.whiskerguard.organization.domain.LetterCommitmentTestSamples.*;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class LetterCommitmentMapperTest {

    private LetterCommitmentMapper letterCommitmentMapper;

    @BeforeEach
    void setUp() {
        letterCommitmentMapper = new LetterCommitmentMapperImpl();
    }

    @Test
    void shouldConvertToDtoAndBack() {
        var expected = getLetterCommitmentSample1();
        var actual = letterCommitmentMapper.toEntity(letterCommitmentMapper.toDto(expected));
        assertLetterCommitmentAllPropertiesEquals(expected, actual);
    }
}

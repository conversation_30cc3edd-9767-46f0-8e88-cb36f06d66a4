package com.whiskerguard.organization.service.mapper;

import static com.whiskerguard.organization.domain.NewsAttachmentAsserts.*;
import static com.whiskerguard.organization.domain.NewsAttachmentTestSamples.*;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class NewsAttachmentMapperTest {

    private NewsAttachmentMapper newsAttachmentMapper;

    @BeforeEach
    void setUp() {
        newsAttachmentMapper = new NewsAttachmentMapperImpl();
    }

    @Test
    void shouldConvertToDtoAndBack() {
        var expected = getNewsAttachmentSample1();
        var actual = newsAttachmentMapper.toEntity(newsAttachmentMapper.toDto(expected));
        assertNewsAttachmentAllPropertiesEquals(expected, actual);
    }
}

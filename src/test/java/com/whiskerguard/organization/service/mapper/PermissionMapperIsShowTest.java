package com.whiskerguard.organization.service.mapper;

import com.whiskerguard.organization.domain.Permission;
import com.whiskerguard.organization.domain.enumeration.ResourceType;
import com.whiskerguard.organization.service.dto.PermissionDTO;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import static org.assertj.core.api.Assertions.assertThat;

import java.time.Instant;

/**
 * 测试PermissionMapper中isShow字段的映射功能
 */
class PermissionMapperIsShowTest {

    private PermissionMapper permissionMapper;

    @BeforeEach
    void setUp() {
        // 注意：这里我们需要使用生成的实现类
        // 在实际环境中，这会通过Spring注入
        permissionMapper = new PermissionMapperImpl();
    }

    @Test
    void shouldMapIsShowFieldFromEntityToDto() {
        // 创建一个Permission实体，设置isShow为true
        Permission permission = createTestPermission();
        permission.setIsShow(true);

        // 转换为DTO
        PermissionDTO dto = permissionMapper.toDto(permission);

        // 验证isShow字段被正确映射
        assertThat(dto).isNotNull();
        assertThat(dto.getIsShow()).isEqualTo(true);
    }

    @Test
    void shouldMapIsShowFieldFromEntityToDtoWhenFalse() {
        // 创建一个Permission实体，设置isShow为false
        Permission permission = createTestPermission();
        permission.setIsShow(false);

        // 转换为DTO
        PermissionDTO dto = permissionMapper.toDto(permission);

        // 验证isShow字段被正确映射
        assertThat(dto).isNotNull();
        assertThat(dto.getIsShow()).isEqualTo(false);
    }

    @Test
    void shouldMapIsShowFieldFromEntityToDtoWhenNull() {
        // 创建一个Permission实体，isShow为null
        Permission permission = createTestPermission();
        permission.setIsShow(null);

        // 转换为DTO
        PermissionDTO dto = permissionMapper.toDto(permission);

        // 验证isShow字段被正确映射
        assertThat(dto).isNotNull();
        assertThat(dto.getIsShow()).isNull();
    }

    @Test
    void shouldMapIsShowFieldFromDtoToEntity() {
        // 创建一个PermissionDTO，设置isShow为true
        PermissionDTO dto = createTestPermissionDTO();
        dto.setIsShow(true);

        // 转换为实体
        Permission entity = permissionMapper.toEntity(dto);

        // 验证isShow字段被正确映射
        assertThat(entity).isNotNull();
        assertThat(entity.getIsShow()).isEqualTo(true);
    }

    private Permission createTestPermission() {
        Permission permission = new Permission();
        permission.setId(1L);
        permission.setTenantId(1L);
        permission.setServiceName("test-service");
        permission.setCode("TEST_CODE");
        permission.setName("Test Permission");
        permission.setResourceType(ResourceType.MENU);
        permission.setVersion(1);
        permission.setCreatedAt(Instant.now());
        permission.setUpdatedAt(Instant.now());
        permission.setIsDeleted(false);
        permission.setIsAvailable(true);
        return permission;
    }

    private PermissionDTO createTestPermissionDTO() {
        PermissionDTO dto = new PermissionDTO();
        dto.setId(1L);
        dto.setServiceName("test-service");
        dto.setCode("TEST_CODE");
        dto.setName("Test Permission");
        dto.setResourceType(ResourceType.MENU);
        dto.setVersion(1);
        dto.setCreatedAt(Instant.now());
        dto.setUpdatedAt(Instant.now());
        dto.setIsDeleted(false);
        dto.setIsAvailable(true);
        return dto;
    }
}

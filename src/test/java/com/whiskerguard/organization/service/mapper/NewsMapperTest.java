package com.whiskerguard.organization.service.mapper;

import static com.whiskerguard.organization.domain.NewsAsserts.*;
import static com.whiskerguard.organization.domain.NewsTestSamples.*;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class NewsMapperTest {

    private NewsMapper newsMapper;

    @BeforeEach
    void setUp() {
        newsMapper = new NewsMapperImpl();
    }

    @Test
    void shouldConvertToDtoAndBack() {
        var expected = getNewsSample1();
        var actual = newsMapper.toEntity(newsMapper.toDto(expected));
        assertNewsAllPropertiesEquals(expected, actual);
    }
}

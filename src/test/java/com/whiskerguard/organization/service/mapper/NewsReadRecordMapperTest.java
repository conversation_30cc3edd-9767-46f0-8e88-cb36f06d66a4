package com.whiskerguard.organization.service.mapper;

import static com.whiskerguard.organization.domain.NewsReadRecordAsserts.*;
import static com.whiskerguard.organization.domain.NewsReadRecordTestSamples.*;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class NewsReadRecordMapperTest {

    private NewsReadRecordMapper newsReadRecordMapper;

    @BeforeEach
    void setUp() {
        newsReadRecordMapper = new NewsReadRecordMapperImpl();
    }

    @Test
    void shouldConvertToDtoAndBack() {
        var expected = getNewsReadRecordSample1();
        var actual = newsReadRecordMapper.toEntity(newsReadRecordMapper.toDto(expected));
        assertNewsReadRecordAllPropertiesEquals(expected, actual);
    }
}

package com.whiskerguard.organization.service.mapper;

import static com.whiskerguard.organization.domain.NewsReportAsserts.*;
import static com.whiskerguard.organization.domain.NewsReportTestSamples.*;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class NewsReportMapperTest {

    private NewsReportMapper newsReportMapper;

    @BeforeEach
    void setUp() {
        newsReportMapper = new NewsReportMapperImpl();
    }

    @Test
    void shouldConvertToDtoAndBack() {
        var expected = getNewsReportSample1();
        var actual = newsReportMapper.toEntity(newsReportMapper.toDto(expected));
        assertNewsReportAllPropertiesEquals(expected, actual);
    }
}

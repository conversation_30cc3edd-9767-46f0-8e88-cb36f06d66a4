package com.whiskerguard.organization.service.mapper;

import static com.whiskerguard.organization.domain.NewsCategoryAsserts.*;
import static com.whiskerguard.organization.domain.NewsCategoryTestSamples.*;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class NewsCategoryMapperTest {

    private NewsCategoryMapper newsCategoryMapper;

    @BeforeEach
    void setUp() {
        newsCategoryMapper = new NewsCategoryMapperImpl();
    }

    @Test
    void shouldConvertToDtoAndBack() {
        var expected = getNewsCategorySample1();
        var actual = newsCategoryMapper.toEntity(newsCategoryMapper.toDto(expected));
        assertNewsCategoryAllPropertiesEquals(expected, actual);
    }
}

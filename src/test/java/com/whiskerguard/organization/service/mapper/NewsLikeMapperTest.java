package com.whiskerguard.organization.service.mapper;

import static com.whiskerguard.organization.domain.NewsLikeAsserts.*;
import static com.whiskerguard.organization.domain.NewsLikeTestSamples.*;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class NewsLikeMapperTest {

    private NewsLikeMapper newsLikeMapper;

    @BeforeEach
    void setUp() {
        newsLikeMapper = new NewsLikeMapperImpl();
    }

    @Test
    void shouldConvertToDtoAndBack() {
        var expected = getNewsLikeSample1();
        var actual = newsLikeMapper.toEntity(newsLikeMapper.toDto(expected));
        assertNewsLikeAllPropertiesEquals(expected, actual);
    }
}

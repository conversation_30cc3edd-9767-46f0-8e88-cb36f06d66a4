package com.whiskerguard.organization.service.mapper;

import static com.whiskerguard.organization.domain.RiskCategoryAsserts.*;
import static com.whiskerguard.organization.domain.RiskCategoryTestSamples.*;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class RiskCategoryMapperTest {

    private RiskCategoryMapper riskCategoryMapper;

    @BeforeEach
    void setUp() {
        riskCategoryMapper = new RiskCategoryMapperImpl();
    }

    @Test
    void shouldConvertToDtoAndBack() {
        var expected = getRiskCategorySample1();
        var actual = riskCategoryMapper.toEntity(riskCategoryMapper.toDto(expected));
        assertRiskCategoryAllPropertiesEquals(expected, actual);
    }
}

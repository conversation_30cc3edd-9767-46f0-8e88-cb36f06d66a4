package com.whiskerguard.organization.service.mapper;

import static com.whiskerguard.organization.domain.TenantInitializeAsserts.*;
import static com.whiskerguard.organization.domain.TenantInitializeTestSamples.*;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class TenantInitializeMapperTest {

    private TenantInitializeMapper tenantInitializeMapper;

    @BeforeEach
    void setUp() {
        tenantInitializeMapper = new TenantInitializeMapperImpl();
    }

    @Test
    void shouldConvertToDtoAndBack() {
        var expected = getTenantInitializeSample1();
        var actual = tenantInitializeMapper.toEntity(tenantInitializeMapper.toDto(expected));
        assertTenantInitializeAllPropertiesEquals(expected, actual);
    }
}

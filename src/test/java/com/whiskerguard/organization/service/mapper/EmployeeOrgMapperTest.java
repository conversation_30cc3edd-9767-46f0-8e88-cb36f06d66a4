package com.whiskerguard.organization.service.mapper;

import static com.whiskerguard.organization.domain.EmployeeOrgAsserts.*;
import static com.whiskerguard.organization.domain.EmployeeOrgTestSamples.*;

import com.whiskerguard.organization.IntegrationTest;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

@IntegrationTest
class EmployeeOrgMapperTest {

    @Autowired
    private EmployeeOrgMapper employeeOrgMapper;

    @Test
    void shouldConvertToDtoAndBack() {
        var expected = getEmployeeOrgSample1();
        var actual = employeeOrgMapper.toEntity(employeeOrgMapper.toDto(expected));
        assertEmployeeOrgAllPropertiesEquals(expected, actual);
    }
}

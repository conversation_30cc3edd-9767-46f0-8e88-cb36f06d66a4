package com.whiskerguard.organization.service.mapper;

import static com.whiskerguard.organization.domain.OrgUnitAsserts.*;
import static com.whiskerguard.organization.domain.OrgUnitTestSamples.*;

import com.whiskerguard.organization.IntegrationTest;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

@IntegrationTest
class OrgUnitMapperTest {

    @Autowired
    private OrgUnitMapper orgUnitMapper;

    @Test
    void shouldConvertToDtoAndBack() {
        var expected = getOrgUnitSample1();
        var actual = orgUnitMapper.toEntity(orgUnitMapper.toDto(expected));
        assertOrgUnitAllPropertiesEquals(expected, actual);
    }
}

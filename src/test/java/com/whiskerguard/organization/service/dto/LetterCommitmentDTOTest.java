package com.whiskerguard.organization.service.dto;

import static org.assertj.core.api.Assertions.assertThat;

import com.whiskerguard.organization.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

class LetterCommitmentDTOTest {

    @Test
    void dtoEqualsVerifier() throws Exception {
        TestUtil.equalsVerifier(LetterCommitmentDTO.class);
        LetterCommitmentDTO letterCommitmentDTO1 = new LetterCommitmentDTO();
        letterCommitmentDTO1.setId(1L);
        LetterCommitmentDTO letterCommitmentDTO2 = new LetterCommitmentDTO();
        assertThat(letterCommitmentDTO1).isNotEqualTo(letterCommitmentDTO2);
        letterCommitmentDTO2.setId(letterCommitmentDTO1.getId());
        assertThat(letterCommitmentDTO1).isEqualTo(letterCommitmentDTO2);
        letterCommitmentDTO2.setId(2L);
        assertThat(letterCommitmentDTO1).isNotEqualTo(letterCommitmentDTO2);
        letterCommitmentDTO1.setId(null);
        assertThat(letterCommitmentDTO1).isNotEqualTo(letterCommitmentDTO2);
    }
}

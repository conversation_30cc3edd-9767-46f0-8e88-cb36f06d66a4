package com.whiskerguard.organization.service.dto;

import static org.assertj.core.api.Assertions.assertThat;

import com.whiskerguard.organization.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

class ComplaintSuggestionAttachmentDTOTest {

    @Test
    void dtoEqualsVerifier() throws Exception {
        TestUtil.equalsVerifier(ComplaintSuggestionAttachmentDTO.class);
        ComplaintSuggestionAttachmentDTO complaintSuggestionAttachmentDTO1 = new ComplaintSuggestionAttachmentDTO();
        complaintSuggestionAttachmentDTO1.setId(1L);
        ComplaintSuggestionAttachmentDTO complaintSuggestionAttachmentDTO2 = new ComplaintSuggestionAttachmentDTO();
        assertThat(complaintSuggestionAttachmentDTO1).isNotEqualTo(complaintSuggestionAttachmentDTO2);
        complaintSuggestionAttachmentDTO2.setId(complaintSuggestionAttachmentDTO1.getId());
        assertThat(complaintSuggestionAttachmentDTO1).isEqualTo(complaintSuggestionAttachmentDTO2);
        complaintSuggestionAttachmentDTO2.setId(2L);
        assertThat(complaintSuggestionAttachmentDTO1).isNotEqualTo(complaintSuggestionAttachmentDTO2);
        complaintSuggestionAttachmentDTO1.setId(null);
        assertThat(complaintSuggestionAttachmentDTO1).isNotEqualTo(complaintSuggestionAttachmentDTO2);
    }
}

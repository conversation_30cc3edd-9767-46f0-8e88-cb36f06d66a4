package com.whiskerguard.organization.service.dto;

import static org.assertj.core.api.Assertions.assertThat;

import com.whiskerguard.organization.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

class NewsCommentDTOTest {

    @Test
    void dtoEqualsVerifier() throws Exception {
        TestUtil.equalsVerifier(NewsCommentDTO.class);
        NewsCommentDTO newsCommentDTO1 = new NewsCommentDTO();
        newsCommentDTO1.setId(1L);
        NewsCommentDTO newsCommentDTO2 = new NewsCommentDTO();
        assertThat(newsCommentDTO1).isNotEqualTo(newsCommentDTO2);
        newsCommentDTO2.setId(newsCommentDTO1.getId());
        assertThat(newsCommentDTO1).isEqualTo(newsCommentDTO2);
        newsCommentDTO2.setId(2L);
        assertThat(newsCommentDTO1).isNotEqualTo(newsCommentDTO2);
        newsCommentDTO1.setId(null);
        assertThat(newsCommentDTO1).isNotEqualTo(newsCommentDTO2);
    }
}

package com.whiskerguard.organization.service.dto;

import static org.assertj.core.api.Assertions.assertThat;

import com.whiskerguard.organization.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

class EmployeeOrgDTOTest {

    @Test
    void dtoEqualsVerifier() throws Exception {
        TestUtil.equalsVerifier(EmployeeOrgDTO.class);
        EmployeeOrgDTO employeeOrgDTO1 = new EmployeeOrgDTO();
        employeeOrgDTO1.setId(1L);
        EmployeeOrgDTO employeeOrgDTO2 = new EmployeeOrgDTO();
        assertThat(employeeOrgDTO1).isNotEqualTo(employeeOrgDTO2);
        employeeOrgDTO2.setId(employeeOrgDTO1.getId());
        assertThat(employeeOrgDTO1).isEqualTo(employeeOrgDTO2);
        employeeOrgDTO2.setId(2L);
        assertThat(employeeOrgDTO1).isNotEqualTo(employeeOrgDTO2);
        employeeOrgDTO1.setId(null);
        assertThat(employeeOrgDTO1).isNotEqualTo(employeeOrgDTO2);
    }
}

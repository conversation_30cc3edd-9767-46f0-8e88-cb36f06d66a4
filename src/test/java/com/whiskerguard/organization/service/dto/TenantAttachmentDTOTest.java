package com.whiskerguard.organization.service.dto;

import static org.assertj.core.api.Assertions.assertThat;

import com.whiskerguard.organization.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

class TenantAttachmentDTOTest {

    @Test
    void dtoEqualsVerifier() throws Exception {
        TestUtil.equalsVerifier(TenantAttachmentDTO.class);
        TenantAttachmentDTO tenantAttachmentDTO1 = new TenantAttachmentDTO();
        tenantAttachmentDTO1.setId(1L);
        TenantAttachmentDTO tenantAttachmentDTO2 = new TenantAttachmentDTO();
        assertThat(tenantAttachmentDTO1).isNotEqualTo(tenantAttachmentDTO2);
        tenantAttachmentDTO2.setId(tenantAttachmentDTO1.getId());
        assertThat(tenantAttachmentDTO1).isEqualTo(tenantAttachmentDTO2);
        tenantAttachmentDTO2.setId(2L);
        assertThat(tenantAttachmentDTO1).isNotEqualTo(tenantAttachmentDTO2);
        tenantAttachmentDTO1.setId(null);
        assertThat(tenantAttachmentDTO1).isNotEqualTo(tenantAttachmentDTO2);
    }
}

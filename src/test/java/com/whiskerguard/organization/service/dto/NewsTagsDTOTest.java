package com.whiskerguard.organization.service.dto;

import static org.assertj.core.api.Assertions.assertThat;

import com.whiskerguard.organization.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

class NewsTagsDTOTest {

    @Test
    void dtoEqualsVerifier() throws Exception {
        TestUtil.equalsVerifier(NewsTagsDTO.class);
        NewsTagsDTO newsTagsDTO1 = new NewsTagsDTO();
        newsTagsDTO1.setId(1L);
        NewsTagsDTO newsTagsDTO2 = new NewsTagsDTO();
        assertThat(newsTagsDTO1).isNotEqualTo(newsTagsDTO2);
        newsTagsDTO2.setId(newsTagsDTO1.getId());
        assertThat(newsTagsDTO1).isEqualTo(newsTagsDTO2);
        newsTagsDTO2.setId(2L);
        assertThat(newsTagsDTO1).isNotEqualTo(newsTagsDTO2);
        newsTagsDTO1.setId(null);
        assertThat(newsTagsDTO1).isNotEqualTo(newsTagsDTO2);
    }
}

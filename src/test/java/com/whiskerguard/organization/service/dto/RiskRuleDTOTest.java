package com.whiskerguard.organization.service.dto;

import static org.assertj.core.api.Assertions.assertThat;

import com.whiskerguard.organization.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

class RiskRuleDTOTest {

    @Test
    void dtoEqualsVerifier() throws Exception {
        TestUtil.equalsVerifier(RiskRuleDTO.class);
        RiskRuleDTO riskRuleDTO1 = new RiskRuleDTO();
        riskRuleDTO1.setId(1L);
        RiskRuleDTO riskRuleDTO2 = new RiskRuleDTO();
        assertThat(riskRuleDTO1).isNotEqualTo(riskRuleDTO2);
        riskRuleDTO2.setId(riskRuleDTO1.getId());
        assertThat(riskRuleDTO1).isEqualTo(riskRuleDTO2);
        riskRuleDTO2.setId(2L);
        assertThat(riskRuleDTO1).isNotEqualTo(riskRuleDTO2);
        riskRuleDTO1.setId(null);
        assertThat(riskRuleDTO1).isNotEqualTo(riskRuleDTO2);
    }
}

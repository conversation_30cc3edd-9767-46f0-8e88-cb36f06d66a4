package com.whiskerguard.organization.service.dto;

import static org.assertj.core.api.Assertions.assertThat;

import com.whiskerguard.organization.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

class NewsLikeDTOTest {

    @Test
    void dtoEqualsVerifier() throws Exception {
        TestUtil.equalsVerifier(NewsLikeDTO.class);
        NewsLikeDTO newsLikeDTO1 = new NewsLikeDTO();
        newsLikeDTO1.setId(1L);
        NewsLikeDTO newsLikeDTO2 = new NewsLikeDTO();
        assertThat(newsLikeDTO1).isNotEqualTo(newsLikeDTO2);
        newsLikeDTO2.setId(newsLikeDTO1.getId());
        assertThat(newsLikeDTO1).isEqualTo(newsLikeDTO2);
        newsLikeDTO2.setId(2L);
        assertThat(newsLikeDTO1).isNotEqualTo(newsLikeDTO2);
        newsLikeDTO1.setId(null);
        assertThat(newsLikeDTO1).isNotEqualTo(newsLikeDTO2);
    }
}

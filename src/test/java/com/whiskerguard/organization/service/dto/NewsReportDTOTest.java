package com.whiskerguard.organization.service.dto;

import static org.assertj.core.api.Assertions.assertThat;

import com.whiskerguard.organization.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

class NewsReportDTOTest {

    @Test
    void dtoEqualsVerifier() throws Exception {
        TestUtil.equalsVerifier(NewsReportDTO.class);
        NewsReportDTO newsReportDTO1 = new NewsReportDTO();
        newsReportDTO1.setId(1L);
        NewsReportDTO newsReportDTO2 = new NewsReportDTO();
        assertThat(newsReportDTO1).isNotEqualTo(newsReportDTO2);
        newsReportDTO2.setId(newsReportDTO1.getId());
        assertThat(newsReportDTO1).isEqualTo(newsReportDTO2);
        newsReportDTO2.setId(2L);
        assertThat(newsReportDTO1).isNotEqualTo(newsReportDTO2);
        newsReportDTO1.setId(null);
        assertThat(newsReportDTO1).isNotEqualTo(newsReportDTO2);
    }
}

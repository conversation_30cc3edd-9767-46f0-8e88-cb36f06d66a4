package com.whiskerguard.organization.service.dto;

import static org.assertj.core.api.Assertions.assertThat;

import com.whiskerguard.organization.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

class EmployeeRoleDTOTest {

    @Test
    void dtoEqualsVerifier() throws Exception {
        TestUtil.equalsVerifier(EmployeeRoleDTO.class);
        EmployeeRoleDTO employeeRoleDTO1 = new EmployeeRoleDTO();
        employeeRoleDTO1.setId(1L);
        EmployeeRoleDTO employeeRoleDTO2 = new EmployeeRoleDTO();
        assertThat(employeeRoleDTO1).isNotEqualTo(employeeRoleDTO2);
        employeeRoleDTO2.setId(employeeRoleDTO1.getId());
        assertThat(employeeRoleDTO1).isEqualTo(employeeRoleDTO2);
        employeeRoleDTO2.setId(2L);
        assertThat(employeeRoleDTO1).isNotEqualTo(employeeRoleDTO2);
        employeeRoleDTO1.setId(null);
        assertThat(employeeRoleDTO1).isNotEqualTo(employeeRoleDTO2);
    }
}

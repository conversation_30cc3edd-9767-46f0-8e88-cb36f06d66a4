package com.whiskerguard.organization.service.dto;

import static org.assertj.core.api.Assertions.assertThat;

import com.whiskerguard.organization.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

class RiskModelDTOTest {

    @Test
    void dtoEqualsVerifier() throws Exception {
        TestUtil.equalsVerifier(RiskModelDTO.class);
        RiskModelDTO riskModelDTO1 = new RiskModelDTO();
        riskModelDTO1.setId(1L);
        RiskModelDTO riskModelDTO2 = new RiskModelDTO();
        assertThat(riskModelDTO1).isNotEqualTo(riskModelDTO2);
        riskModelDTO2.setId(riskModelDTO1.getId());
        assertThat(riskModelDTO1).isEqualTo(riskModelDTO2);
        riskModelDTO2.setId(2L);
        assertThat(riskModelDTO1).isNotEqualTo(riskModelDTO2);
        riskModelDTO1.setId(null);
        assertThat(riskModelDTO1).isNotEqualTo(riskModelDTO2);
    }
}

package com.whiskerguard.organization.service.dto;

import static org.assertj.core.api.Assertions.assertThat;

import com.whiskerguard.organization.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

class NewsAttachmentDTOTest {

    @Test
    void dtoEqualsVerifier() throws Exception {
        TestUtil.equalsVerifier(NewsAttachmentDTO.class);
        NewsAttachmentDTO newsAttachmentDTO1 = new NewsAttachmentDTO();
        newsAttachmentDTO1.setId(1L);
        NewsAttachmentDTO newsAttachmentDTO2 = new NewsAttachmentDTO();
        assertThat(newsAttachmentDTO1).isNotEqualTo(newsAttachmentDTO2);
        newsAttachmentDTO2.setId(newsAttachmentDTO1.getId());
        assertThat(newsAttachmentDTO1).isEqualTo(newsAttachmentDTO2);
        newsAttachmentDTO2.setId(2L);
        assertThat(newsAttachmentDTO1).isNotEqualTo(newsAttachmentDTO2);
        newsAttachmentDTO1.setId(null);
        assertThat(newsAttachmentDTO1).isNotEqualTo(newsAttachmentDTO2);
    }
}

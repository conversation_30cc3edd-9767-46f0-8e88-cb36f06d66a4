package com.whiskerguard.organization.service.dto;

import static org.assertj.core.api.Assertions.assertThat;

import com.whiskerguard.organization.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

class NewsReadRecordDTOTest {

    @Test
    void dtoEqualsVerifier() throws Exception {
        TestUtil.equalsVerifier(NewsReadRecordDTO.class);
        NewsReadRecordDTO newsReadRecordDTO1 = new NewsReadRecordDTO();
        newsReadRecordDTO1.setId(1L);
        NewsReadRecordDTO newsReadRecordDTO2 = new NewsReadRecordDTO();
        assertThat(newsReadRecordDTO1).isNotEqualTo(newsReadRecordDTO2);
        newsReadRecordDTO2.setId(newsReadRecordDTO1.getId());
        assertThat(newsReadRecordDTO1).isEqualTo(newsReadRecordDTO2);
        newsReadRecordDTO2.setId(2L);
        assertThat(newsReadRecordDTO1).isNotEqualTo(newsReadRecordDTO2);
        newsReadRecordDTO1.setId(null);
        assertThat(newsReadRecordDTO1).isNotEqualTo(newsReadRecordDTO2);
    }
}

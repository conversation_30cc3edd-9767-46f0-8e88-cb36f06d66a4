package com.whiskerguard.organization.service.dto;

import static org.assertj.core.api.Assertions.assertThat;

import com.whiskerguard.organization.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

class TenantInitializeDTOTest {

    @Test
    void dtoEqualsVerifier() throws Exception {
        TestUtil.equalsVerifier(TenantInitializeDTO.class);
        TenantInitializeDTO tenantInitializeDTO1 = new TenantInitializeDTO();
        tenantInitializeDTO1.setId(1L);
        TenantInitializeDTO tenantInitializeDTO2 = new TenantInitializeDTO();
        assertThat(tenantInitializeDTO1).isNotEqualTo(tenantInitializeDTO2);
        tenantInitializeDTO2.setId(tenantInitializeDTO1.getId());
        assertThat(tenantInitializeDTO1).isEqualTo(tenantInitializeDTO2);
        tenantInitializeDTO2.setId(2L);
        assertThat(tenantInitializeDTO1).isNotEqualTo(tenantInitializeDTO2);
        tenantInitializeDTO1.setId(null);
        assertThat(tenantInitializeDTO1).isNotEqualTo(tenantInitializeDTO2);
    }
}

package com.whiskerguard.organization.service.dto;

import static org.assertj.core.api.Assertions.assertThat;

import com.whiskerguard.organization.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

class ComplaintSuggestionDTOTest {

    @Test
    void dtoEqualsVerifier() throws Exception {
        TestUtil.equalsVerifier(ComplaintSuggestionDTO.class);
        ComplaintSuggestionDTO complaintSuggestionDTO1 = new ComplaintSuggestionDTO();
        complaintSuggestionDTO1.setId(1L);
        ComplaintSuggestionDTO complaintSuggestionDTO2 = new ComplaintSuggestionDTO();
        assertThat(complaintSuggestionDTO1).isNotEqualTo(complaintSuggestionDTO2);
        complaintSuggestionDTO2.setId(complaintSuggestionDTO1.getId());
        assertThat(complaintSuggestionDTO1).isEqualTo(complaintSuggestionDTO2);
        complaintSuggestionDTO2.setId(2L);
        assertThat(complaintSuggestionDTO1).isNotEqualTo(complaintSuggestionDTO2);
        complaintSuggestionDTO1.setId(null);
        assertThat(complaintSuggestionDTO1).isNotEqualTo(complaintSuggestionDTO2);
    }
}

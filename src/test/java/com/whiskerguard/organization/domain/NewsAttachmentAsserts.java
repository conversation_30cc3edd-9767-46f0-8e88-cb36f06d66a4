package com.whiskerguard.organization.domain;

import static org.assertj.core.api.Assertions.assertThat;

public class NewsAttachmentAsserts {

    /**
     * Asserts that the entity has all properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertNewsAttachmentAllPropertiesEquals(NewsAttachment expected, NewsAttachment actual) {
        assertNewsAttachmentAutoGeneratedPropertiesEquals(expected, actual);
        assertNewsAttachmentAllUpdatablePropertiesEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all updatable properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertNewsAttachmentAllUpdatablePropertiesEquals(NewsAttachment expected, NewsAttachment actual) {
        assertNewsAttachmentUpdatableFieldsEquals(expected, actual);
        assertNewsAttachmentUpdatableRelationshipsEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all the auto generated properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertNewsAttachmentAutoGeneratedPropertiesEquals(NewsAttachment expected, NewsAttachment actual) {
        assertThat(actual)
            .as("Verify NewsAttachment auto generated properties")
            .satisfies(a -> assertThat(a.getId()).as("check id").isEqualTo(expected.getId()));
    }

    /**
     * Asserts that the entity has all the updatable fields set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertNewsAttachmentUpdatableFieldsEquals(NewsAttachment expected, NewsAttachment actual) {
        assertThat(actual)
            .as("Verify NewsAttachment relevant properties")
            .satisfies(a -> assertThat(a.getName()).as("check name").isEqualTo(expected.getName()))
            .satisfies(a -> assertThat(a.getType()).as("check type").isEqualTo(expected.getType()))
            .satisfies(a -> assertThat(a.getUrl()).as("check url").isEqualTo(expected.getUrl()))
            .satisfies(a -> assertThat(a.getFileSize()).as("check fileSize").isEqualTo(expected.getFileSize()))
            .satisfies(a -> assertThat(a.getSortOrder()).as("check sortOrder").isEqualTo(expected.getSortOrder()))
            .satisfies(a -> assertThat(a.getMetadata()).as("check metadata").isEqualTo(expected.getMetadata()))
            .satisfies(a -> assertThat(a.getVersion()).as("check version").isEqualTo(expected.getVersion()))
            .satisfies(a -> assertThat(a.getCreatedBy()).as("check createdBy").isEqualTo(expected.getCreatedBy()))
            .satisfies(a -> assertThat(a.getCreatedAt()).as("check createdAt").isEqualTo(expected.getCreatedAt()))
            .satisfies(a -> assertThat(a.getUpdatedBy()).as("check updatedBy").isEqualTo(expected.getUpdatedBy()))
            .satisfies(a -> assertThat(a.getUpdatedAt()).as("check updatedAt").isEqualTo(expected.getUpdatedAt()))
            .satisfies(a -> assertThat(a.getIsDeleted()).as("check isDeleted").isEqualTo(expected.getIsDeleted()));
    }

    /**
     * Asserts that the entity has all the updatable relationships set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertNewsAttachmentUpdatableRelationshipsEquals(NewsAttachment expected, NewsAttachment actual) {
        assertThat(actual)
            .as("Verify NewsAttachment relationships")
            .satisfies(a -> assertThat(a.getNews()).as("check news").isEqualTo(expected.getNews()));
    }
}

package com.whiskerguard.organization.domain;

import static com.whiskerguard.organization.domain.EmployeeTestSamples.*;
import static com.whiskerguard.organization.domain.NewsCategoryTestSamples.*;
import static com.whiskerguard.organization.domain.NewsTestSamples.*;
import static com.whiskerguard.organization.domain.OrgUnitTestSamples.*;
import static com.whiskerguard.organization.domain.TagTestSamples.*;
import static org.assertj.core.api.Assertions.assertThat;

import com.whiskerguard.organization.web.rest.TestUtil;
import java.util.HashSet;
import java.util.Set;
import org.junit.jupiter.api.Test;

class NewsTest {

    @Test
    void equalsVerifier() throws Exception {
        TestUtil.equalsVerifier(News.class);
        News news1 = getNewsSample1();
        News news2 = new News();
        assertThat(news1).isNotEqualTo(news2);

        news2.setId(news1.getId());
        assertThat(news1).isEqualTo(news2);

        news2 = getNewsSample2();
        assertThat(news1).isNotEqualTo(news2);
    }

    @Test
    void categoryTest() {
        News news = getNewsRandomSampleGenerator();
        NewsCategory newsCategoryBack = getNewsCategoryRandomSampleGenerator();

        news.setCategory(newsCategoryBack);
        assertThat(news.getCategory()).isEqualTo(newsCategoryBack);

        news.category(null);
        assertThat(news.getCategory()).isNull();
    }

    @Test
    void orgUnitTest() {
        News news = getNewsRandomSampleGenerator();
        OrgUnit orgUnitBack = getOrgUnitRandomSampleGenerator();

        news.setOrgUnit(orgUnitBack);
        assertThat(news.getOrgUnit()).isEqualTo(orgUnitBack);

        news.orgUnit(null);
        assertThat(news.getOrgUnit()).isNull();
    }

    @Test
    void authorTest() {
        News news = getNewsRandomSampleGenerator();
        Employee employeeBack = getEmployeeRandomSampleGenerator();

        news.setAuthor(employeeBack);
        assertThat(news.getAuthor()).isEqualTo(employeeBack);

        news.author(null);
        assertThat(news.getAuthor()).isNull();
    }

    @Test
    void tagsTest() {
        News news = getNewsRandomSampleGenerator();
        Tag tagBack = getTagRandomSampleGenerator();

        news.addTags(tagBack);
        assertThat(news.getTags()).containsOnly(tagBack);

        news.removeTags(tagBack);
        assertThat(news.getTags()).doesNotContain(tagBack);

        news.tags(new HashSet<>(Set.of(tagBack)));
        assertThat(news.getTags()).containsOnly(tagBack);

        news.setTags(new HashSet<>());
        assertThat(news.getTags()).doesNotContain(tagBack);
    }
}

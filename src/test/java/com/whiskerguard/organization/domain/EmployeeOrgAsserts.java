package com.whiskerguard.organization.domain;

import static org.assertj.core.api.Assertions.assertThat;

public class EmployeeOrgAsserts {

    /**
     * Asserts that the entity has all properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertEmployeeOrgAllPropertiesEquals(EmployeeOrg expected, EmployeeOrg actual) {
        assertEmployeeOrgAutoGeneratedPropertiesEquals(expected, actual);
        assertEmployeeOrgAllUpdatablePropertiesEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all updatable properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertEmployeeOrgAllUpdatablePropertiesEquals(EmployeeOrg expected, EmployeeOrg actual) {
        assertEmployeeOrgUpdatableFieldsEquals(expected, actual);
        assertEmployeeOrgUpdatableRelationshipsEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all the auto generated properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertEmployeeOrgAutoGeneratedPropertiesEquals(EmployeeOrg expected, EmployeeOrg actual) {
        assertThat(actual)
            .as("Verify EmployeeOrg auto generated properties")
            .satisfies(a -> assertThat(a.getId()).as("check id").isEqualTo(expected.getId()));
    }

    /**
     * Asserts that the entity has all the updatable fields set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertEmployeeOrgUpdatableFieldsEquals(EmployeeOrg expected, EmployeeOrg actual) {
        assertThat(actual)
            .as("Verify EmployeeOrg relevant properties")
            .satisfies(a -> assertThat(a.getTenantId()).as("check tenantId").isEqualTo(expected.getTenantId()))
            .satisfies(a -> assertThat(a.getStartDate()).as("check startDate").isEqualTo(expected.getStartDate()))
            .satisfies(a -> assertThat(a.getEndDate()).as("check endDate").isEqualTo(expected.getEndDate()))
            .satisfies(a -> assertThat(a.getIsPrimary()).as("check isPrimary").isEqualTo(expected.getIsPrimary()))
            .satisfies(a -> assertThat(a.getVersion()).as("check version").isEqualTo(expected.getVersion()))
            .satisfies(a -> assertThat(a.getCreatedBy()).as("check createdBy").isEqualTo(expected.getCreatedBy()))
            .satisfies(a -> assertThat(a.getCreatedAt()).as("check createdAt").isEqualTo(expected.getCreatedAt()))
            .satisfies(a -> assertThat(a.getUpdatedBy()).as("check updatedBy").isEqualTo(expected.getUpdatedBy()))
            .satisfies(a -> assertThat(a.getUpdatedAt()).as("check updatedAt").isEqualTo(expected.getUpdatedAt()))
            .satisfies(a -> assertThat(a.getIsDeleted()).as("check isDeleted").isEqualTo(expected.getIsDeleted()));
    }

    /**
     * Asserts that the entity has all the updatable relationships set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertEmployeeOrgUpdatableRelationshipsEquals(EmployeeOrg expected, EmployeeOrg actual) {
        assertThat(actual)
            .as("Verify EmployeeOrg relationships")
            .satisfies(a -> assertThat(a.getEmployee()).as("check employee").isEqualTo(expected.getEmployee()))
            .satisfies(a -> assertThat(a.getOrgUnit()).as("check orgUnit").isEqualTo(expected.getOrgUnit()))
            .satisfies(a -> assertThat(a.getPosition()).as("check position").isEqualTo(expected.getPosition()));
    }
}

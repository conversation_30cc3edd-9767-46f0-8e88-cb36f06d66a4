package com.whiskerguard.organization.domain;

import static org.assertj.core.api.Assertions.assertThat;

public class NewsCategoryAsserts {

    /**
     * Asserts that the entity has all properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertNewsCategoryAllPropertiesEquals(NewsCategory expected, NewsCategory actual) {
        assertNewsCategoryAutoGeneratedPropertiesEquals(expected, actual);
        assertNewsCategoryAllUpdatablePropertiesEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all updatable properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertNewsCategoryAllUpdatablePropertiesEquals(NewsCategory expected, NewsCategory actual) {
        assertNewsCategoryUpdatableFieldsEquals(expected, actual);
        assertNewsCategoryUpdatableRelationshipsEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all the auto generated properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertNewsCategoryAutoGeneratedPropertiesEquals(NewsCategory expected, NewsCategory actual) {
        assertThat(actual)
            .as("Verify NewsCategory auto generated properties")
            .satisfies(a -> assertThat(a.getId()).as("check id").isEqualTo(expected.getId()));
    }

    /**
     * Asserts that the entity has all the updatable fields set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertNewsCategoryUpdatableFieldsEquals(NewsCategory expected, NewsCategory actual) {
        assertThat(actual)
            .as("Verify NewsCategory relevant properties")
            .satisfies(a -> assertThat(a.getStatus()).as("check status").isEqualTo(expected.getStatus()))
            .satisfies(a -> assertThat(a.getSortOrder()).as("check sortOrder").isEqualTo(expected.getSortOrder()))
            .satisfies(a -> assertThat(a.getName()).as("check name").isEqualTo(expected.getName()))
            .satisfies(a -> assertThat(a.getDescription()).as("check description").isEqualTo(expected.getDescription()))
            .satisfies(a -> assertThat(a.getCoverImageUrl()).as("check coverImageUrl").isEqualTo(expected.getCoverImageUrl()))
            .satisfies(a -> assertThat(a.getMetadata()).as("check metadata").isEqualTo(expected.getMetadata()))
            .satisfies(a -> assertThat(a.getVersion()).as("check version").isEqualTo(expected.getVersion()))
            .satisfies(a -> assertThat(a.getCreatedBy()).as("check createdBy").isEqualTo(expected.getCreatedBy()))
            .satisfies(a -> assertThat(a.getCreatedAt()).as("check createdAt").isEqualTo(expected.getCreatedAt()))
            .satisfies(a -> assertThat(a.getUpdatedBy()).as("check updatedBy").isEqualTo(expected.getUpdatedBy()))
            .satisfies(a -> assertThat(a.getUpdatedAt()).as("check updatedAt").isEqualTo(expected.getUpdatedAt()))
            .satisfies(a -> assertThat(a.getIsDeleted()).as("check isDeleted").isEqualTo(expected.getIsDeleted()));
    }

    /**
     * Asserts that the entity has all the updatable relationships set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertNewsCategoryUpdatableRelationshipsEquals(NewsCategory expected, NewsCategory actual) {
        assertThat(actual)
            .as("Verify NewsCategory relationships")
            .satisfies(a -> assertThat(a.getOrgUnit()).as("check orgUnit").isEqualTo(expected.getOrgUnit()))
            .satisfies(a -> assertThat(a.getParent()).as("check parent").isEqualTo(expected.getParent()));
    }
}

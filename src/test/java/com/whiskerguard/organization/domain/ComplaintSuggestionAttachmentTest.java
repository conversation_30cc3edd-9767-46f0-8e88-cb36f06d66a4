package com.whiskerguard.organization.domain;

import static com.whiskerguard.organization.domain.ComplaintSuggestionAttachmentTestSamples.*;
import static org.assertj.core.api.Assertions.assertThat;

import com.whiskerguard.organization.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

class ComplaintSuggestionAttachmentTest {

    @Test
    void equalsVerifier() throws Exception {
        TestUtil.equalsVerifier(ComplaintSuggestionAttachment.class);
        ComplaintSuggestionAttachment complaintSuggestionAttachment1 = getComplaintSuggestionAttachmentSample1();
        ComplaintSuggestionAttachment complaintSuggestionAttachment2 = new ComplaintSuggestionAttachment();
        assertThat(complaintSuggestionAttachment1).isNotEqualTo(complaintSuggestionAttachment2);

        complaintSuggestionAttachment2.setId(complaintSuggestionAttachment1.getId());
        assertThat(complaintSuggestionAttachment1).isEqualTo(complaintSuggestionAttachment2);

        complaintSuggestionAttachment2 = getComplaintSuggestionAttachmentSample2();
        assertThat(complaintSuggestionAttachment1).isNotEqualTo(complaintSuggestionAttachment2);
    }
}

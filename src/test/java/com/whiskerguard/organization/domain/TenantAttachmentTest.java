package com.whiskerguard.organization.domain;

import static com.whiskerguard.organization.domain.TenantAttachmentTestSamples.*;
import static com.whiskerguard.organization.domain.TenantTestSamples.*;
import static org.assertj.core.api.Assertions.assertThat;

import com.whiskerguard.organization.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

class TenantAttachmentTest {

    @Test
    void equalsVerifier() throws Exception {
        TestUtil.equalsVerifier(TenantAttachment.class);
        TenantAttachment tenantAttachment1 = getTenantAttachmentSample1();
        TenantAttachment tenantAttachment2 = new TenantAttachment();
        assertThat(tenantAttachment1).isNotEqualTo(tenantAttachment2);

        tenantAttachment2.setId(tenantAttachment1.getId());
        assertThat(tenantAttachment1).isEqualTo(tenantAttachment2);

        tenantAttachment2 = getTenantAttachmentSample2();
        assertThat(tenantAttachment1).isNotEqualTo(tenantAttachment2);
    }

    @Test
    void tenantTest() {
        TenantAttachment tenantAttachment = getTenantAttachmentRandomSampleGenerator();
        Tenant tenantBack = getTenantRandomSampleGenerator();

        tenantAttachment.setTenant(tenantBack);
        assertThat(tenantAttachment.getTenant()).isEqualTo(tenantBack);

        tenantAttachment.tenant(null);
        assertThat(tenantAttachment.getTenant()).isNull();
    }
}

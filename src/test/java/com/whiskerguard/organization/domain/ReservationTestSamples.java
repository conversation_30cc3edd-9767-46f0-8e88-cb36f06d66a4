package com.whiskerguard.organization.domain;

import java.util.Random;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

public class ReservationTestSamples {

    private static final Random random = new Random();
    private static final AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));
    private static final AtomicInteger intCount = new AtomicInteger(random.nextInt() + (2 * Short.MAX_VALUE));

    public static Reservation getReservationSample1() {
        return new Reservation()
            .id(1L)
            .name("name1")
            .position("position1")
            .mobile("mobile1")
            .email("email1")
            .company("company1")
            .industry("industry1")
            .companySize("companySize1")
            .focusNeed("focusNeed1")
            .otherDesc("otherDesc1")
            .metadata("metadata1")
            .version(1)
            .createdBy("createdBy1")
            .updatedBy("updatedBy1");
    }

    public static Reservation getReservationSample2() {
        return new Reservation()
            .id(2L)
            .name("name2")
            .position("position2")
            .mobile("mobile2")
            .email("email2")
            .company("company2")
            .industry("industry2")
            .companySize("companySize2")
            .focusNeed("focusNeed2")
            .otherDesc("otherDesc2")
            .metadata("metadata2")
            .version(2)
            .createdBy("createdBy2")
            .updatedBy("updatedBy2");
    }

    public static Reservation getReservationRandomSampleGenerator() {
        return new Reservation()
            .id(longCount.incrementAndGet())
            .name(UUID.randomUUID().toString())
            .position(UUID.randomUUID().toString())
            .mobile(UUID.randomUUID().toString())
            .email(UUID.randomUUID().toString())
            .company(UUID.randomUUID().toString())
            .industry(UUID.randomUUID().toString())
            .companySize(UUID.randomUUID().toString())
            .focusNeed(UUID.randomUUID().toString())
            .otherDesc(UUID.randomUUID().toString())
            .metadata(UUID.randomUUID().toString())
            .version(intCount.incrementAndGet())
            .createdBy(UUID.randomUUID().toString())
            .updatedBy(UUID.randomUUID().toString());
    }
}

package com.whiskerguard.organization.domain;

import static org.assertj.core.api.Assertions.assertThat;

public class RiskModelAsserts {

    /**
     * Asserts that the entity has all properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertRiskModelAllPropertiesEquals(RiskModel expected, RiskModel actual) {
        assertRiskModelAutoGeneratedPropertiesEquals(expected, actual);
        assertRiskModelAllUpdatablePropertiesEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all updatable properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertRiskModelAllUpdatablePropertiesEquals(RiskModel expected, RiskModel actual) {
        assertRiskModelUpdatableFieldsEquals(expected, actual);
        assertRiskModelUpdatableRelationshipsEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all the auto generated properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertRiskModelAutoGeneratedPropertiesEquals(RiskModel expected, RiskModel actual) {
        assertThat(actual)
            .as("Verify RiskModel auto generated properties")
            .satisfies(a -> assertThat(a.getId()).as("check id").isEqualTo(expected.getId()));
    }

    /**
     * Asserts that the entity has all the updatable fields set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertRiskModelUpdatableFieldsEquals(RiskModel expected, RiskModel actual) {
        assertThat(actual)
            .as("Verify RiskModel relevant properties")
            .satisfies(a -> assertThat(a.getTenantId()).as("check tenantId").isEqualTo(expected.getTenantId()))
            .satisfies(a -> assertThat(a.getName()).as("check name").isEqualTo(expected.getName()))
            .satisfies(a -> assertThat(a.getDescription()).as("check description").isEqualTo(expected.getDescription()))
            .satisfies(a -> assertThat(a.getEffectiveFrom()).as("check effectiveFrom").isEqualTo(expected.getEffectiveFrom()))
            .satisfies(a -> assertThat(a.getEffectiveTo()).as("check effectiveTo").isEqualTo(expected.getEffectiveTo()))
            .satisfies(a -> assertThat(a.getIsDefault()).as("check isDefault").isEqualTo(expected.getIsDefault()))
            .satisfies(a -> assertThat(a.getVersion()).as("check version").isEqualTo(expected.getVersion()))
            .satisfies(a -> assertThat(a.getMetadata()).as("check metadata").isEqualTo(expected.getMetadata()))
            .satisfies(a -> assertThat(a.getCreatedBy()).as("check createdBy").isEqualTo(expected.getCreatedBy()))
            .satisfies(a -> assertThat(a.getCreatedAt()).as("check createdAt").isEqualTo(expected.getCreatedAt()))
            .satisfies(a -> assertThat(a.getUpdatedBy()).as("check updatedBy").isEqualTo(expected.getUpdatedBy()))
            .satisfies(a -> assertThat(a.getUpdatedAt()).as("check updatedAt").isEqualTo(expected.getUpdatedAt()))
            .satisfies(a -> assertThat(a.getIsDeleted()).as("check isDeleted").isEqualTo(expected.getIsDeleted()));
    }

    /**
     * Asserts that the entity has all the updatable relationships set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertRiskModelUpdatableRelationshipsEquals(RiskModel expected, RiskModel actual) {
        // empty method
    }
}

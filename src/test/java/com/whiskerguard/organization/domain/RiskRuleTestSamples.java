package com.whiskerguard.organization.domain;

import java.util.Random;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

public class RiskRuleTestSamples {

    private static final Random random = new Random();
    private static final AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));
    private static final AtomicInteger intCount = new AtomicInteger(random.nextInt() + (2 * Short.MAX_VALUE));

    public static RiskRule getRiskRuleSample1() {
        return new RiskRule()
            .id(1L)
            .tenantId(1L)
            .code("code1")
            .name("name1")
            .params("params1")
            .description("description1")
            .score(1)
            .version(1)
            .metadata("metadata1")
            .createdBy("createdBy1")
            .updatedBy("updatedBy1");
    }

    public static RiskRule getRiskRuleSample2() {
        return new RiskRule()
            .id(2L)
            .tenantId(2L)
            .code("code2")
            .name("name2")
            .params("params2")
            .description("description2")
            .score(2)
            .version(2)
            .metadata("metadata2")
            .createdBy("createdBy2")
            .updatedBy("updatedBy2");
    }

    public static RiskRule getRiskRuleRandomSampleGenerator() {
        return new RiskRule()
            .id(longCount.incrementAndGet())
            .tenantId(longCount.incrementAndGet())
            .code(UUID.randomUUID().toString())
            .name(UUID.randomUUID().toString())
            .params(UUID.randomUUID().toString())
            .description(UUID.randomUUID().toString())
            .score(intCount.incrementAndGet())
            .version(intCount.incrementAndGet())
            .metadata(UUID.randomUUID().toString())
            .createdBy(UUID.randomUUID().toString())
            .updatedBy(UUID.randomUUID().toString());
    }
}

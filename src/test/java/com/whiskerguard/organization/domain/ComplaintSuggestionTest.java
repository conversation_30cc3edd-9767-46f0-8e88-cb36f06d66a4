package com.whiskerguard.organization.domain;

import static com.whiskerguard.organization.domain.ComplaintSuggestionTestSamples.*;
import static org.assertj.core.api.Assertions.assertThat;

import com.whiskerguard.organization.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

class ComplaintSuggestionTest {

    @Test
    void equalsVerifier() throws Exception {
        TestUtil.equalsVerifier(ComplaintSuggestion.class);
        ComplaintSuggestion complaintSuggestion1 = getComplaintSuggestionSample1();
        ComplaintSuggestion complaintSuggestion2 = new ComplaintSuggestion();
        assertThat(complaintSuggestion1).isNotEqualTo(complaintSuggestion2);

        complaintSuggestion2.setId(complaintSuggestion1.getId());
        assertThat(complaintSuggestion1).isEqualTo(complaintSuggestion2);

        complaintSuggestion2 = getComplaintSuggestionSample2();
        assertThat(complaintSuggestion1).isNotEqualTo(complaintSuggestion2);
    }
}

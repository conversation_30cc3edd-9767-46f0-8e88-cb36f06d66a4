package com.whiskerguard.organization.domain;

import static com.whiskerguard.organization.domain.EmployeeTestSamples.*;
import static com.whiskerguard.organization.domain.NewsReadRecordTestSamples.*;
import static com.whiskerguard.organization.domain.NewsTestSamples.*;
import static org.assertj.core.api.Assertions.assertThat;

import com.whiskerguard.organization.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

class NewsReadRecordTest {

    @Test
    void equalsVerifier() throws Exception {
        TestUtil.equalsVerifier(NewsReadRecord.class);
        NewsReadRecord newsReadRecord1 = getNewsReadRecordSample1();
        NewsReadRecord newsReadRecord2 = new NewsReadRecord();
        assertThat(newsReadRecord1).isNotEqualTo(newsReadRecord2);

        newsReadRecord2.setId(newsReadRecord1.getId());
        assertThat(newsReadRecord1).isEqualTo(newsReadRecord2);

        newsReadRecord2 = getNewsReadRecordSample2();
        assertThat(newsReadRecord1).isNotEqualTo(newsReadRecord2);
    }

    @Test
    void newsTest() {
        NewsReadRecord newsReadRecord = getNewsReadRecordRandomSampleGenerator();
        News newsBack = getNewsRandomSampleGenerator();

        newsReadRecord.setNews(newsBack);
        assertThat(newsReadRecord.getNews()).isEqualTo(newsBack);

        newsReadRecord.news(null);
        assertThat(newsReadRecord.getNews()).isNull();
    }

    @Test
    void readerTest() {
        NewsReadRecord newsReadRecord = getNewsReadRecordRandomSampleGenerator();
        Employee employeeBack = getEmployeeRandomSampleGenerator();

        newsReadRecord.setReader(employeeBack);
        assertThat(newsReadRecord.getReader()).isEqualTo(employeeBack);

        newsReadRecord.reader(null);
        assertThat(newsReadRecord.getReader()).isNull();
    }
}

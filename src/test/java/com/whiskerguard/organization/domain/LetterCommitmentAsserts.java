package com.whiskerguard.organization.domain;

import static org.assertj.core.api.Assertions.assertThat;

public class LetterCommitmentAsserts {

    /**
     * Asserts that the entity has all properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertLetterCommitmentAllPropertiesEquals(LetterCommitment expected, LetterCommitment actual) {
        assertLetterCommitmentAutoGeneratedPropertiesEquals(expected, actual);
        assertLetterCommitmentAllUpdatablePropertiesEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all updatable properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertLetterCommitmentAllUpdatablePropertiesEquals(LetterCommitment expected, LetterCommitment actual) {
        assertLetterCommitmentUpdatableFieldsEquals(expected, actual);
        assertLetterCommitmentUpdatableRelationshipsEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all the auto generated properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertLetterCommitmentAutoGeneratedPropertiesEquals(LetterCommitment expected, LetterCommitment actual) {
        assertThat(actual)
            .as("Verify LetterCommitment auto generated properties")
            .satisfies(a -> assertThat(a.getId()).as("check id").isEqualTo(expected.getId()));
    }

    /**
     * Asserts that the entity has all the updatable fields set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertLetterCommitmentUpdatableFieldsEquals(LetterCommitment expected, LetterCommitment actual) {
        assertThat(actual)
            .as("Verify LetterCommitment relevant properties")
            .satisfies(a -> assertThat(a.getTenantId()).as("check tenantId").isEqualTo(expected.getTenantId()))
            .satisfies(a -> assertThat(a.getEmployeeId()).as("check employeeId").isEqualTo(expected.getEmployeeId()))
            .satisfies(a -> assertThat(a.getType()).as("check type").isEqualTo(expected.getType()))
            .satisfies(a -> assertThat(a.getFilePath()).as("check filePath").isEqualTo(expected.getFilePath()))
            .satisfies(a -> assertThat(a.getIsSigned()).as("check isSigned").isEqualTo(expected.getIsSigned()))
            .satisfies(a -> assertThat(a.getMetadata()).as("check metadata").isEqualTo(expected.getMetadata()))
            .satisfies(a -> assertThat(a.getVersion()).as("check version").isEqualTo(expected.getVersion()))
            .satisfies(a -> assertThat(a.getCreatedBy()).as("check createdBy").isEqualTo(expected.getCreatedBy()))
            .satisfies(a -> assertThat(a.getCreatedAt()).as("check createdAt").isEqualTo(expected.getCreatedAt()))
            .satisfies(a -> assertThat(a.getUpdatedBy()).as("check updatedBy").isEqualTo(expected.getUpdatedBy()))
            .satisfies(a -> assertThat(a.getUpdatedAt()).as("check updatedAt").isEqualTo(expected.getUpdatedAt()))
            .satisfies(a -> assertThat(a.getIsDeleted()).as("check isDeleted").isEqualTo(expected.getIsDeleted()));
    }

    /**
     * Asserts that the entity has all the updatable relationships set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertLetterCommitmentUpdatableRelationshipsEquals(LetterCommitment expected, LetterCommitment actual) {
        // empty method
    }
}

package com.whiskerguard.organization.domain;

import static org.assertj.core.api.Assertions.assertThat;

public class NewsTagsAsserts {

    /**
     * Asserts that the entity has all properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertNewsTagsAllPropertiesEquals(NewsTags expected, NewsTags actual) {
        assertNewsTagsAutoGeneratedPropertiesEquals(expected, actual);
        assertNewsTagsAllUpdatablePropertiesEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all updatable properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertNewsTagsAllUpdatablePropertiesEquals(NewsTags expected, NewsTags actual) {
        assertNewsTagsUpdatableFieldsEquals(expected, actual);
        assertNewsTagsUpdatableRelationshipsEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all the auto generated properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertNewsTagsAutoGeneratedPropertiesEquals(NewsTags expected, NewsTags actual) {
        assertThat(actual)
            .as("Verify NewsTags auto generated properties")
            .satisfies(a -> assertThat(a.getId()).as("check id").isEqualTo(expected.getId()));
    }

    /**
     * Asserts that the entity has all the updatable fields set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertNewsTagsUpdatableFieldsEquals(NewsTags expected, NewsTags actual) {
        assertThat(actual)
            .as("Verify NewsTags relevant properties")
            .satisfies(a -> assertThat(a.getTenantId()).as("check tenantId").isEqualTo(expected.getTenantId()))
            .satisfies(a -> assertThat(a.getMetadata()).as("check metadata").isEqualTo(expected.getMetadata()))
            .satisfies(a -> assertThat(a.getVersion()).as("check version").isEqualTo(expected.getVersion()))
            .satisfies(a -> assertThat(a.getCreatedBy()).as("check createdBy").isEqualTo(expected.getCreatedBy()))
            .satisfies(a -> assertThat(a.getCreatedAt()).as("check createdAt").isEqualTo(expected.getCreatedAt()))
            .satisfies(a -> assertThat(a.getUpdatedBy()).as("check updatedBy").isEqualTo(expected.getUpdatedBy()))
            .satisfies(a -> assertThat(a.getUpdatedAt()).as("check updatedAt").isEqualTo(expected.getUpdatedAt()))
            .satisfies(a -> assertThat(a.getIsDeleted()).as("check isDeleted").isEqualTo(expected.getIsDeleted()));
    }

    /**
     * Asserts that the entity has all the updatable relationships set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertNewsTagsUpdatableRelationshipsEquals(NewsTags expected, NewsTags actual) {
        assertThat(actual)
            .as("Verify NewsTags relationships")
            .satisfies(a -> assertThat(a.getTags()).as("check tags").isEqualTo(expected.getTags()))
            .satisfies(a -> assertThat(a.getNews()).as("check news").isEqualTo(expected.getNews()));
    }
}

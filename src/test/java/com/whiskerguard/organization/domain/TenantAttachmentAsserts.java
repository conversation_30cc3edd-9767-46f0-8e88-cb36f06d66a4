package com.whiskerguard.organization.domain;

import static org.assertj.core.api.Assertions.assertThat;

public class TenantAttachmentAsserts {

    /**
     * Asserts that the entity has all properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertTenantAttachmentAllPropertiesEquals(TenantAttachment expected, TenantAttachment actual) {
        assertTenantAttachmentAutoGeneratedPropertiesEquals(expected, actual);
        assertTenantAttachmentAllUpdatablePropertiesEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all updatable properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertTenantAttachmentAllUpdatablePropertiesEquals(TenantAttachment expected, TenantAttachment actual) {
        assertTenantAttachmentUpdatableFieldsEquals(expected, actual);
        assertTenantAttachmentUpdatableRelationshipsEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all the auto generated properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertTenantAttachmentAutoGeneratedPropertiesEquals(TenantAttachment expected, TenantAttachment actual) {
        assertThat(actual)
            .as("Verify TenantAttachment auto generated properties")
            .satisfies(a -> assertThat(a.getId()).as("check id").isEqualTo(expected.getId()));
    }

    /**
     * Asserts that the entity has all the updatable fields set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertTenantAttachmentUpdatableFieldsEquals(TenantAttachment expected, TenantAttachment actual) {
        assertThat(actual)
            .as("Verify TenantAttachment relevant properties")
            .satisfies(a -> assertThat(a.getType()).as("check type").isEqualTo(expected.getType()))
            .satisfies(a -> assertThat(a.getFileUrl()).as("check fileUrl").isEqualTo(expected.getFileUrl()))
            .satisfies(a -> assertThat(a.getFileName()).as("check fileName").isEqualTo(expected.getFileName()))
            .satisfies(a -> assertThat(a.getFileSize()).as("check fileSize").isEqualTo(expected.getFileSize()))
            .satisfies(a -> assertThat(a.getDescription()).as("check description").isEqualTo(expected.getDescription()))
            .satisfies(a -> assertThat(a.getMetadata()).as("check metadata").isEqualTo(expected.getMetadata()))
            .satisfies(a -> assertThat(a.getVersion()).as("check version").isEqualTo(expected.getVersion()))
            .satisfies(a -> assertThat(a.getUploadedBy()).as("check uploadedBy").isEqualTo(expected.getUploadedBy()))
            .satisfies(a -> assertThat(a.getUploadedAt()).as("check uploadedAt").isEqualTo(expected.getUploadedAt()))
            .satisfies(a -> assertThat(a.getUpdatedBy()).as("check updatedBy").isEqualTo(expected.getUpdatedBy()))
            .satisfies(a -> assertThat(a.getUpdatedAt()).as("check updatedAt").isEqualTo(expected.getUpdatedAt()))
            .satisfies(a -> assertThat(a.getIsDeleted()).as("check isDeleted").isEqualTo(expected.getIsDeleted()));
    }

    /**
     * Asserts that the entity has all the updatable relationships set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertTenantAttachmentUpdatableRelationshipsEquals(TenantAttachment expected, TenantAttachment actual) {
        assertThat(actual)
            .as("Verify TenantAttachment relationships")
            .satisfies(a -> assertThat(a.getTenant()).as("check tenant").isEqualTo(expected.getTenant()));
    }
}

package com.whiskerguard.organization.domain;

import static org.assertj.core.api.Assertions.assertThat;

public class RiskCategoryAsserts {

    /**
     * Asserts that the entity has all properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertRiskCategoryAllPropertiesEquals(RiskCategory expected, RiskCategory actual) {
        assertRiskCategoryAutoGeneratedPropertiesEquals(expected, actual);
        assertRiskCategoryAllUpdatablePropertiesEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all updatable properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertRiskCategoryAllUpdatablePropertiesEquals(RiskCategory expected, RiskCategory actual) {
        assertRiskCategoryUpdatableFieldsEquals(expected, actual);
        assertRiskCategoryUpdatableRelationshipsEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all the auto generated properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertRiskCategoryAutoGeneratedPropertiesEquals(RiskCategory expected, RiskCategory actual) {
        assertThat(actual)
            .as("Verify RiskCategory auto generated properties")
            .satisfies(a -> assertThat(a.getId()).as("check id").isEqualTo(expected.getId()));
    }

    /**
     * Asserts that the entity has all the updatable fields set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertRiskCategoryUpdatableFieldsEquals(RiskCategory expected, RiskCategory actual) {
        assertThat(actual)
            .as("Verify RiskCategory relevant properties")
            .satisfies(a -> assertThat(a.getTenantId()).as("check tenantId").isEqualTo(expected.getTenantId()))
            .satisfies(a -> assertThat(a.getName()).as("check name").isEqualTo(expected.getName()))
            .satisfies(a -> assertThat(a.getLevel()).as("check level").isEqualTo(expected.getLevel()))
            .satisfies(a -> assertThat(a.getExpression()).as("check expression").isEqualTo(expected.getExpression()))
            .satisfies(a -> assertThat(a.getDescription()).as("check description").isEqualTo(expected.getDescription()))
            .satisfies(a -> assertThat(a.getVersion()).as("check version").isEqualTo(expected.getVersion()))
            .satisfies(a -> assertThat(a.getMetadata()).as("check metadata").isEqualTo(expected.getMetadata()))
            .satisfies(a -> assertThat(a.getCreatedBy()).as("check createdBy").isEqualTo(expected.getCreatedBy()))
            .satisfies(a -> assertThat(a.getCreatedAt()).as("check createdAt").isEqualTo(expected.getCreatedAt()))
            .satisfies(a -> assertThat(a.getUpdatedBy()).as("check updatedBy").isEqualTo(expected.getUpdatedBy()))
            .satisfies(a -> assertThat(a.getUpdatedAt()).as("check updatedAt").isEqualTo(expected.getUpdatedAt()))
            .satisfies(a -> assertThat(a.getIsDeleted()).as("check isDeleted").isEqualTo(expected.getIsDeleted()));
    }

    /**
     * Asserts that the entity has all the updatable relationships set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertRiskCategoryUpdatableRelationshipsEquals(RiskCategory expected, RiskCategory actual) {
        assertThat(actual)
            .as("Verify RiskCategory relationships")
            .satisfies(a -> assertThat(a.getRiskModel()).as("check riskModel").isEqualTo(expected.getRiskModel()));
    }
}

package com.whiskerguard.organization.domain;

import java.util.Random;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

public class NewsCategoryTestSamples {

    private static final Random random = new Random();
    private static final AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));
    private static final AtomicInteger intCount = new AtomicInteger(random.nextInt() + (2 * Short.MAX_VALUE));

    public static NewsCategory getNewsCategorySample1() {
        return new NewsCategory()
            .id(1L)
            .sortOrder(1)
            .name("name1")
            .description("description1")
            .coverImageUrl("coverImageUrl1")
            .metadata("metadata1")
            .version(1)
            .createdBy("createdBy1")
            .updatedBy("updatedBy1");
    }

    public static NewsCategory getNewsCategorySample2() {
        return new NewsCategory()
            .id(2L)
            .sortOrder(2)
            .name("name2")
            .description("description2")
            .coverImageUrl("coverImageUrl2")
            .metadata("metadata2")
            .version(2)
            .createdBy("createdBy2")
            .updatedBy("updatedBy2");
    }

    public static NewsCategory getNewsCategoryRandomSampleGenerator() {
        return new NewsCategory()
            .id(longCount.incrementAndGet())
            .sortOrder(intCount.incrementAndGet())
            .name(UUID.randomUUID().toString())
            .description(UUID.randomUUID().toString())
            .coverImageUrl(UUID.randomUUID().toString())
            .metadata(UUID.randomUUID().toString())
            .version(intCount.incrementAndGet())
            .createdBy(UUID.randomUUID().toString())
            .updatedBy(UUID.randomUUID().toString());
    }
}

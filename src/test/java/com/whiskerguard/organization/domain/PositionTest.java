package com.whiskerguard.organization.domain;

import static com.whiskerguard.organization.domain.OrgUnitTestSamples.*;
import static com.whiskerguard.organization.domain.PositionTestSamples.*;
import static org.assertj.core.api.Assertions.assertThat;

import com.whiskerguard.organization.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

class PositionTest {

    @Test
    void equalsVerifier() throws Exception {
        TestUtil.equalsVerifier(Position.class);
        Position position1 = getPositionSample1();
        Position position2 = new Position();
        assertThat(position1).isNotEqualTo(position2);

        position2.setId(position1.getId());
        assertThat(position1).isEqualTo(position2);

        position2 = getPositionSample2();
        assertThat(position1).isNotEqualTo(position2);
    }

    @Test
    void orgUnitTest() {
        Position position = getPositionRandomSampleGenerator();
        OrgUnit orgUnitBack = getOrgUnitRandomSampleGenerator();

        position.setOrgUnit(orgUnitBack);
        assertThat(position.getOrgUnit()).isEqualTo(orgUnitBack);

        position.orgUnit(null);
        assertThat(position.getOrgUnit()).isNull();
    }
}

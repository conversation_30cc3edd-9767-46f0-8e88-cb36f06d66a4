package com.whiskerguard.organization.domain;

import static org.assertj.core.api.Assertions.assertThat;

public class ReservationAsserts {

    /**
     * Asserts that the entity has all properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertReservationAllPropertiesEquals(Reservation expected, Reservation actual) {
        assertReservationAutoGeneratedPropertiesEquals(expected, actual);
        assertReservationAllUpdatablePropertiesEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all updatable properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertReservationAllUpdatablePropertiesEquals(Reservation expected, Reservation actual) {
        assertReservationUpdatableFieldsEquals(expected, actual);
        assertReservationUpdatableRelationshipsEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all the auto generated properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertReservationAutoGeneratedPropertiesEquals(Reservation expected, Reservation actual) {
        assertThat(actual)
            .as("Verify Reservation auto generated properties")
            .satisfies(a -> assertThat(a.getId()).as("check id").isEqualTo(expected.getId()));
    }

    /**
     * Asserts that the entity has all the updatable fields set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertReservationUpdatableFieldsEquals(Reservation expected, Reservation actual) {
        assertThat(actual)
            .as("Verify Reservation relevant properties")
            .satisfies(a -> assertThat(a.getName()).as("check name").isEqualTo(expected.getName()))
            .satisfies(a -> assertThat(a.getPosition()).as("check position").isEqualTo(expected.getPosition()))
            .satisfies(a -> assertThat(a.getMobile()).as("check mobile").isEqualTo(expected.getMobile()))
            .satisfies(a -> assertThat(a.getEmail()).as("check email").isEqualTo(expected.getEmail()))
            .satisfies(a -> assertThat(a.getCompany()).as("check company").isEqualTo(expected.getCompany()))
            .satisfies(a -> assertThat(a.getIndustry()).as("check industry").isEqualTo(expected.getIndustry()))
            .satisfies(a -> assertThat(a.getCompanySize()).as("check companySize").isEqualTo(expected.getCompanySize()))
            .satisfies(a -> assertThat(a.getFocusNeed()).as("check focusNeed").isEqualTo(expected.getFocusNeed()))
            .satisfies(a -> assertThat(a.getOtherDesc()).as("check otherDesc").isEqualTo(expected.getOtherDesc()))
            .satisfies(a -> assertThat(a.getMetadata()).as("check metadata").isEqualTo(expected.getMetadata()))
            .satisfies(a -> assertThat(a.getVersion()).as("check version").isEqualTo(expected.getVersion()))
            .satisfies(a -> assertThat(a.getCreatedBy()).as("check createdBy").isEqualTo(expected.getCreatedBy()))
            .satisfies(a -> assertThat(a.getCreatedAt()).as("check createdAt").isEqualTo(expected.getCreatedAt()))
            .satisfies(a -> assertThat(a.getUpdatedBy()).as("check updatedBy").isEqualTo(expected.getUpdatedBy()))
            .satisfies(a -> assertThat(a.getUpdatedAt()).as("check updatedAt").isEqualTo(expected.getUpdatedAt()))
            .satisfies(a -> assertThat(a.getIsDeleted()).as("check isDeleted").isEqualTo(expected.getIsDeleted()));
    }

    /**
     * Asserts that the entity has all the updatable relationships set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertReservationUpdatableRelationshipsEquals(Reservation expected, Reservation actual) {
        // empty method
    }
}

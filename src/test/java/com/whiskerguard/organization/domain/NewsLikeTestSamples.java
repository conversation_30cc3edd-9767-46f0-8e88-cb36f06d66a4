package com.whiskerguard.organization.domain;

import java.util.Random;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

public class NewsLikeTestSamples {

    private static final Random random = new Random();
    private static final AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));
    private static final AtomicInteger intCount = new AtomicInteger(random.nextInt() + (2 * Short.MAX_VALUE));

    public static NewsLike getNewsLikeSample1() {
        return new NewsLike().id(1L).metadata("metadata1").version(1).createdBy("createdBy1").updatedBy("updatedBy1");
    }

    public static NewsLike getNewsLikeSample2() {
        return new NewsLike().id(2L).metadata("metadata2").version(2).createdBy("createdBy2").updatedBy("updatedBy2");
    }

    public static NewsLike getNewsLikeRandomSampleGenerator() {
        return new NewsLike()
            .id(longCount.incrementAndGet())
            .metadata(UUID.randomUUID().toString())
            .version(intCount.incrementAndGet())
            .createdBy(UUID.randomUUID().toString())
            .updatedBy(UUID.randomUUID().toString());
    }
}

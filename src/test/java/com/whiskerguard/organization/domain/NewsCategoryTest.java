package com.whiskerguard.organization.domain;

import static com.whiskerguard.organization.domain.NewsCategoryTestSamples.*;
import static com.whiskerguard.organization.domain.NewsCategoryTestSamples.*;
import static com.whiskerguard.organization.domain.OrgUnitTestSamples.*;
import static org.assertj.core.api.Assertions.assertThat;

import com.whiskerguard.organization.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

class NewsCategoryTest {

    @Test
    void equalsVerifier() throws Exception {
        TestUtil.equalsVerifier(NewsCategory.class);
        NewsCategory newsCategory1 = getNewsCategorySample1();
        NewsCategory newsCategory2 = new NewsCategory();
        assertThat(newsCategory1).isNotEqualTo(newsCategory2);

        newsCategory2.setId(newsCategory1.getId());
        assertThat(newsCategory1).isEqualTo(newsCategory2);

        newsCategory2 = getNewsCategorySample2();
        assertThat(newsCategory1).isNotEqualTo(newsCategory2);
    }

    @Test
    void orgUnitTest() {
        NewsCategory newsCategory = getNewsCategoryRandomSampleGenerator();
        OrgUnit orgUnitBack = getOrgUnitRandomSampleGenerator();

        newsCategory.setOrgUnit(orgUnitBack);
        assertThat(newsCategory.getOrgUnit()).isEqualTo(orgUnitBack);

        newsCategory.orgUnit(null);
        assertThat(newsCategory.getOrgUnit()).isNull();
    }

    @Test
    void parentTest() {
        NewsCategory newsCategory = getNewsCategoryRandomSampleGenerator();
        NewsCategory newsCategoryBack = getNewsCategoryRandomSampleGenerator();

        newsCategory.setParent(newsCategoryBack);
        assertThat(newsCategory.getParent()).isEqualTo(newsCategoryBack);

        newsCategory.parent(null);
        assertThat(newsCategory.getParent()).isNull();
    }
}

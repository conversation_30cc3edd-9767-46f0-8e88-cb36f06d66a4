package com.whiskerguard.organization.domain;

import java.util.Random;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

public class TenantProfileTestSamples {

    private static final Random random = new Random();
    private static final AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));
    private static final AtomicInteger intCount = new AtomicInteger(random.nextInt() + (2 * Short.MAX_VALUE));

    public static TenantProfile getTenantProfileSample1() {
        return new TenantProfile()
            .id(1L)
            .registrationNumber("registrationNumber1")
            .companyType("companyType1")
            .businessScope("businessScope1")
            .industry("industry1")
            .taxRegistrationNumber("taxRegistrationNumber1")
            .organizationCode("organizationCode1")
            .registeredAddress("registeredAddress1")
            .postalCode("postalCode1")
            .website("website1")
            .fax("fax1")
            .contactPerson("contactPerson1")
            .contactMobile("contactMobile1")
            .contactEmail("contactEmail1")
            .bankName("bankName1")
            .bankAccount("bankAccount1")
            .businessLicensePath("businessLicensePath1")
            .legalPerson("legalPerson1")
            .legalPersonId("legalPersonId1")
            .metadata("metadata1")
            .version(1)
            .createdBy("createdBy1")
            .updatedBy("updatedBy1");
    }

    public static TenantProfile getTenantProfileSample2() {
        return new TenantProfile()
            .id(2L)
            .registrationNumber("registrationNumber2")
            .companyType("companyType2")
            .businessScope("businessScope2")
            .industry("industry2")
            .taxRegistrationNumber("taxRegistrationNumber2")
            .organizationCode("organizationCode2")
            .registeredAddress("registeredAddress2")
            .postalCode("postalCode2")
            .website("website2")
            .fax("fax2")
            .contactPerson("contactPerson2")
            .contactMobile("contactMobile2")
            .contactEmail("contactEmail2")
            .bankName("bankName2")
            .bankAccount("bankAccount2")
            .businessLicensePath("businessLicensePath2")
            .legalPerson("legalPerson2")
            .legalPersonId("legalPersonId2")
            .metadata("metadata2")
            .version(2)
            .createdBy("createdBy2")
            .updatedBy("updatedBy2");
    }

    public static TenantProfile getTenantProfileRandomSampleGenerator() {
        return new TenantProfile()
            .id(longCount.incrementAndGet())
            .registrationNumber(UUID.randomUUID().toString())
            .companyType(UUID.randomUUID().toString())
            .businessScope(UUID.randomUUID().toString())
            .industry(UUID.randomUUID().toString())
            .taxRegistrationNumber(UUID.randomUUID().toString())
            .organizationCode(UUID.randomUUID().toString())
            .registeredAddress(UUID.randomUUID().toString())
            .postalCode(UUID.randomUUID().toString())
            .website(UUID.randomUUID().toString())
            .fax(UUID.randomUUID().toString())
            .contactPerson(UUID.randomUUID().toString())
            .contactMobile(UUID.randomUUID().toString())
            .contactEmail(UUID.randomUUID().toString())
            .bankName(UUID.randomUUID().toString())
            .bankAccount(UUID.randomUUID().toString())
            .businessLicensePath(UUID.randomUUID().toString())
            .legalPerson(UUID.randomUUID().toString())
            .legalPersonId(UUID.randomUUID().toString())
            .metadata(UUID.randomUUID().toString())
            .version(intCount.incrementAndGet())
            .createdBy(UUID.randomUUID().toString())
            .updatedBy(UUID.randomUUID().toString());
    }
}

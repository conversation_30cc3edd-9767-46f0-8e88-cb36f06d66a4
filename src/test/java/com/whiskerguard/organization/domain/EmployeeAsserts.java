package com.whiskerguard.organization.domain;

import static org.assertj.core.api.Assertions.assertThat;

public class EmployeeAsserts {

    /**
     * Asserts that the entity has all properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertEmployeeAllPropertiesEquals(Employee expected, Employee actual) {
        assertEmployeeAutoGeneratedPropertiesEquals(expected, actual);
        assertEmployeeAllUpdatablePropertiesEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all updatable properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertEmployeeAllUpdatablePropertiesEquals(Employee expected, Employee actual) {
        assertEmployeeUpdatableFieldsEquals(expected, actual);
        assertEmployeeUpdatableRelationshipsEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all the auto generated properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertEmployeeAutoGeneratedPropertiesEquals(Employee expected, Employee actual) {
        assertThat(actual)
            .as("Verify Employee auto generated properties")
            .satisfies(a -> assertThat(a.getId()).as("check id").isEqualTo(expected.getId()));
    }

    /**
     * Asserts that the entity has all the updatable fields set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertEmployeeUpdatableFieldsEquals(Employee expected, Employee actual) {
        assertThat(actual)
            .as("Verify Employee relevant properties")
            .satisfies(a -> assertThat(a.getTenantId()).as("check tenantId").isEqualTo(expected.getTenantId()))
            .satisfies(a -> assertThat(a.getUsername()).as("check username").isEqualTo(expected.getUsername()))
            .satisfies(a -> assertThat(a.getPassword()).as("check password").isEqualTo(expected.getPassword()))
            .satisfies(a -> assertThat(a.getSalt()).as("check salt").isEqualTo(expected.getSalt()))
            .satisfies(a -> assertThat(a.getRealName()).as("check realName").isEqualTo(expected.getRealName()))
            .satisfies(a -> assertThat(a.getAvatar()).as("check avatar").isEqualTo(expected.getAvatar()))
            .satisfies(a -> assertThat(a.getEmail()).as("check email").isEqualTo(expected.getEmail()))
            .satisfies(a -> assertThat(a.getPhone()).as("check phone").isEqualTo(expected.getPhone()))
            .satisfies(a -> assertThat(a.getGender()).as("check gender").isEqualTo(expected.getGender()))
            .satisfies(a -> assertThat(a.getBirthDate()).as("check birthDate").isEqualTo(expected.getBirthDate()))
            .satisfies(a -> assertThat(a.getIdCard()).as("check idCard").isEqualTo(expected.getIdCard()))
            .satisfies(a -> assertThat(a.getEmployeeNo()).as("check employeeNo").isEqualTo(expected.getEmployeeNo()))
            .satisfies(a -> assertThat(a.getStatus()).as("check status").isEqualTo(expected.getStatus()))
            .satisfies(a -> assertThat(a.getHireDate()).as("check hireDate").isEqualTo(expected.getHireDate()))
            .satisfies(a -> assertThat(a.getLeaveDate()).as("check leaveDate").isEqualTo(expected.getLeaveDate()))
            .satisfies(a -> assertThat(a.getMetadata()).as("check metadata").isEqualTo(expected.getMetadata()))
            .satisfies(a -> assertThat(a.getVersion()).as("check version").isEqualTo(expected.getVersion()))
            .satisfies(a -> assertThat(a.getCreatedBy()).as("check createdBy").isEqualTo(expected.getCreatedBy()))
            .satisfies(a -> assertThat(a.getCreatedAt()).as("check createdAt").isEqualTo(expected.getCreatedAt()))
            .satisfies(a -> assertThat(a.getUpdatedBy()).as("check updatedBy").isEqualTo(expected.getUpdatedBy()))
            .satisfies(a -> assertThat(a.getUpdatedAt()).as("check updatedAt").isEqualTo(expected.getUpdatedAt()))
            .satisfies(a -> assertThat(a.getIsDeleted()).as("check isDeleted").isEqualTo(expected.getIsDeleted()))
            .satisfies(a -> assertThat(a.getLastLoginTime()).as("check lastLoginTime").isEqualTo(expected.getLastLoginTime()))
            .satisfies(a -> assertThat(a.getLastLoginIp()).as("check lastLoginIp").isEqualTo(expected.getLastLoginIp()))
            .satisfies(a -> assertThat(a.getLoginFailureCount()).as("check loginFailureCount").isEqualTo(expected.getLoginFailureCount()))
            .satisfies(a -> assertThat(a.getAccountLockedTime()).as("check accountLockedTime").isEqualTo(expected.getAccountLockedTime()))
            .satisfies(a ->
                assertThat(a.getPasswordChangedTime()).as("check passwordChangedTime").isEqualTo(expected.getPasswordChangedTime())
            )
            .satisfies(a ->
                assertThat(a.getPasswordExpiredTime()).as("check passwordExpiredTime").isEqualTo(expected.getPasswordExpiredTime())
            )
            .satisfies(a -> assertThat(a.getIsFirstLogin()).as("check isFirstLogin").isEqualTo(expected.getIsFirstLogin()))
            .satisfies(a ->
                assertThat(a.getForceChangePassword()).as("check forceChangePassword").isEqualTo(expected.getForceChangePassword())
            )
            .satisfies(a -> assertThat(a.getWechatOpenId()).as("check wechatOpenId").isEqualTo(expected.getWechatOpenId()))
            .satisfies(a -> assertThat(a.getWechatUnionId()).as("check wechatUnionId").isEqualTo(expected.getWechatUnionId()));
    }

    /**
     * Asserts that the entity has all the updatable relationships set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertEmployeeUpdatableRelationshipsEquals(Employee expected, Employee actual) {
        // empty method
    }
}

package com.whiskerguard.organization.domain;

import static com.whiskerguard.organization.domain.TenantInitializeTestSamples.*;
import static org.assertj.core.api.Assertions.assertThat;

import com.whiskerguard.organization.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

class TenantInitializeTest {

    @Test
    void equalsVerifier() throws Exception {
        TestUtil.equalsVerifier(TenantInitialize.class);
        TenantInitialize tenantInitialize1 = getTenantInitializeSample1();
        TenantInitialize tenantInitialize2 = new TenantInitialize();
        assertThat(tenantInitialize1).isNotEqualTo(tenantInitialize2);

        tenantInitialize2.setId(tenantInitialize1.getId());
        assertThat(tenantInitialize1).isEqualTo(tenantInitialize2);

        tenantInitialize2 = getTenantInitializeSample2();
        assertThat(tenantInitialize1).isNotEqualTo(tenantInitialize2);
    }
}

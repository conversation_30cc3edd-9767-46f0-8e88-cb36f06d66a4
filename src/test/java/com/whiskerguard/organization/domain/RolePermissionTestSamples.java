package com.whiskerguard.organization.domain;

import java.util.Random;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

public class RolePermissionTestSamples {

    private static final Random random = new Random();
    private static final AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));
    private static final AtomicInteger intCount = new AtomicInteger(random.nextInt() + (2 * Short.MAX_VALUE));

    public static RolePermission getRolePermissionSample1() {
        return new RolePermission().id(1L).tenantId(1L).version(1).createdBy("createdBy1").updatedBy("updatedBy1");
    }

    public static RolePermission getRolePermissionSample2() {
        return new RolePermission().id(2L).tenantId(2L).version(2).createdBy("createdBy2").updatedBy("updatedBy2");
    }

    public static RolePermission getRolePermissionRandomSampleGenerator() {
        return new RolePermission()
            .id(longCount.incrementAndGet())
            .tenantId(longCount.incrementAndGet())
            .version(intCount.incrementAndGet())
            .createdBy(UUID.randomUUID().toString())
            .updatedBy(UUID.randomUUID().toString());
    }
}

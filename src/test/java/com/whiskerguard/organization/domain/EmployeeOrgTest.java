package com.whiskerguard.organization.domain;

import static com.whiskerguard.organization.domain.EmployeeOrgTestSamples.*;
import static com.whiskerguard.organization.domain.EmployeeTestSamples.*;
import static com.whiskerguard.organization.domain.OrgUnitTestSamples.*;
import static com.whiskerguard.organization.domain.PositionTestSamples.*;
import static org.assertj.core.api.Assertions.assertThat;

import com.whiskerguard.organization.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

class EmployeeOrgTest {

    @Test
    void equalsVerifier() throws Exception {
        TestUtil.equalsVerifier(EmployeeOrg.class);
        EmployeeOrg employeeOrg1 = getEmployeeOrgSample1();
        EmployeeOrg employeeOrg2 = new EmployeeOrg();
        assertThat(employeeOrg1).isNotEqualTo(employeeOrg2);

        employeeOrg2.setId(employeeOrg1.getId());
        assertThat(employeeOrg1).isEqualTo(employeeOrg2);

        employeeOrg2 = getEmployeeOrgSample2();
        assertThat(employeeOrg1).isNotEqualTo(employeeOrg2);
    }

    @Test
    void employeeTest() {
        EmployeeOrg employeeOrg = getEmployeeOrgRandomSampleGenerator();
        Employee employeeBack = getEmployeeRandomSampleGenerator();

        employeeOrg.setEmployee(employeeBack);
        assertThat(employeeOrg.getEmployee()).isEqualTo(employeeBack);

        employeeOrg.employee(null);
        assertThat(employeeOrg.getEmployee()).isNull();
    }

    @Test
    void orgUnitTest() {
        EmployeeOrg employeeOrg = getEmployeeOrgRandomSampleGenerator();
        OrgUnit orgUnitBack = getOrgUnitRandomSampleGenerator();

        employeeOrg.setOrgUnit(orgUnitBack);
        assertThat(employeeOrg.getOrgUnit()).isEqualTo(orgUnitBack);

        employeeOrg.orgUnit(null);
        assertThat(employeeOrg.getOrgUnit()).isNull();
    }

    @Test
    void positionTest() {
        EmployeeOrg employeeOrg = getEmployeeOrgRandomSampleGenerator();
        Position positionBack = getPositionRandomSampleGenerator();

        employeeOrg.setPosition(positionBack);
        assertThat(employeeOrg.getPosition()).isEqualTo(positionBack);

        employeeOrg.position(null);
        assertThat(employeeOrg.getPosition()).isNull();
    }
}

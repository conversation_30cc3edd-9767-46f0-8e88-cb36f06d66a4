package com.whiskerguard.organization.domain;

import static com.whiskerguard.organization.domain.RoleTestSamples.*;
import static com.whiskerguard.organization.domain.RoleTestSamples.*;
import static org.assertj.core.api.Assertions.assertThat;

import com.whiskerguard.organization.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

class RoleTest {

    @Test
    void equalsVerifier() throws Exception {
        TestUtil.equalsVerifier(Role.class);
        Role role1 = getRoleSample1();
        Role role2 = new Role();
        assertThat(role1).isNotEqualTo(role2);

        role2.setId(role1.getId());
        assertThat(role1).isEqualTo(role2);

        role2 = getRoleSample2();
        assertThat(role1).isNotEqualTo(role2);
    }

    @Test
    void parentTest() {
        Role role = getRoleRandomSampleGenerator();
        Role roleBack = getRoleRandomSampleGenerator();

        role.setParent(roleBack);
        assertThat(role.getParent()).isEqualTo(roleBack);

        role.parent(null);
        assertThat(role.getParent()).isNull();
    }
}

package com.whiskerguard.organization.domain;

import static com.whiskerguard.organization.domain.NewsTestSamples.*;
import static com.whiskerguard.organization.domain.TagCategoryTestSamples.*;
import static com.whiskerguard.organization.domain.TagTestSamples.*;
import static org.assertj.core.api.Assertions.assertThat;

import com.whiskerguard.organization.web.rest.TestUtil;
import java.util.HashSet;
import java.util.Set;
import org.junit.jupiter.api.Test;

class TagTest {

    @Test
    void equalsVerifier() throws Exception {
        TestUtil.equalsVerifier(Tag.class);
        Tag tag1 = getTagSample1();
        Tag tag2 = new Tag();
        assertThat(tag1).isNotEqualTo(tag2);

        tag2.setId(tag1.getId());
        assertThat(tag1).isEqualTo(tag2);

        tag2 = getTagSample2();
        assertThat(tag1).isNotEqualTo(tag2);
    }

    @Test
    void categoryTest() {
        Tag tag = getTagRandomSampleGenerator();
        TagCategory tagCategoryBack = getTagCategoryRandomSampleGenerator();

        tag.setCategory(tagCategoryBack);
        assertThat(tag.getCategory()).isEqualTo(tagCategoryBack);

        tag.category(null);
        assertThat(tag.getCategory()).isNull();
    }

    @Test
    void newsTest() {
        Tag tag = getTagRandomSampleGenerator();
        News newsBack = getNewsRandomSampleGenerator();

        tag.addNews(newsBack);
        assertThat(tag.getNews()).containsOnly(newsBack);
        assertThat(newsBack.getTags()).containsOnly(tag);

        tag.removeNews(newsBack);
        assertThat(tag.getNews()).doesNotContain(newsBack);
        assertThat(newsBack.getTags()).doesNotContain(tag);

        tag.news(new HashSet<>(Set.of(newsBack)));
        assertThat(tag.getNews()).containsOnly(newsBack);
        assertThat(newsBack.getTags()).containsOnly(tag);

        tag.setNews(new HashSet<>());
        assertThat(tag.getNews()).doesNotContain(newsBack);
        assertThat(newsBack.getTags()).doesNotContain(tag);
    }
}

package com.whiskerguard.organization.domain;

import static com.whiskerguard.organization.domain.NewsTagsTestSamples.*;
import static com.whiskerguard.organization.domain.NewsTestSamples.*;
import static com.whiskerguard.organization.domain.TagTestSamples.*;
import static org.assertj.core.api.Assertions.assertThat;

import com.whiskerguard.organization.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

class NewsTagsTest {

    @Test
    void equalsVerifier() throws Exception {
        TestUtil.equalsVerifier(NewsTags.class);
        NewsTags newsTags1 = getNewsTagsSample1();
        NewsTags newsTags2 = new NewsTags();
        assertThat(newsTags1).isNotEqualTo(newsTags2);

        newsTags2.setId(newsTags1.getId());
        assertThat(newsTags1).isEqualTo(newsTags2);

        newsTags2 = getNewsTagsSample2();
        assertThat(newsTags1).isNotEqualTo(newsTags2);
    }

    @Test
    void tagsTest() {
        NewsTags newsTags = getNewsTagsRandomSampleGenerator();
        Tag tagBack = getTagRandomSampleGenerator();

        newsTags.setTags(tagBack);
        assertThat(newsTags.getTags()).isEqualTo(tagBack);

        newsTags.tags(null);
        assertThat(newsTags.getTags()).isNull();
    }

    @Test
    void newsTest() {
        NewsTags newsTags = getNewsTagsRandomSampleGenerator();
        News newsBack = getNewsRandomSampleGenerator();

        newsTags.setNews(newsBack);
        assertThat(newsTags.getNews()).isEqualTo(newsBack);

        newsTags.news(null);
        assertThat(newsTags.getNews()).isNull();
    }
}

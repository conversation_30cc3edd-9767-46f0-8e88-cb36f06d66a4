package com.whiskerguard.organization.domain;

import static org.assertj.core.api.Assertions.assertThat;

public class TenantAsserts {

    /**
     * Asserts that the entity has all properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertTenantAllPropertiesEquals(Tenant expected, Tenant actual) {
        assertTenantAutoGeneratedPropertiesEquals(expected, actual);
        assertTenantAllUpdatablePropertiesEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all updatable properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertTenantAllUpdatablePropertiesEquals(Tenant expected, Tenant actual) {
        assertTenantUpdatableFieldsEquals(expected, actual);
        assertTenantUpdatableRelationshipsEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all the auto generated properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertTenantAutoGeneratedPropertiesEquals(Tenant expected, Tenant actual) {
        assertThat(actual)
            .as("Verify Tenant auto generated properties")
            .satisfies(a -> assertThat(a.getId()).as("check id").isEqualTo(expected.getId()));
    }

    /**
     * Asserts that the entity has all the updatable fields set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertTenantUpdatableFieldsEquals(Tenant expected, Tenant actual) {
        assertThat(actual)
            .as("Verify Tenant relevant properties")
            .satisfies(a -> assertThat(a.getTenantCode()).as("check tenantCode").isEqualTo(expected.getTenantCode()))
            .satisfies(a -> assertThat(a.getName()).as("check name").isEqualTo(expected.getName()))
            .satisfies(a -> assertThat(a.getStatus()).as("check status").isEqualTo(expected.getStatus()))
            .satisfies(a -> assertThat(a.getSubscriptionPlan()).as("check subscriptionPlan").isEqualTo(expected.getSubscriptionPlan()))
            .satisfies(a -> assertThat(a.getSubscriptionStart()).as("check subscriptionStart").isEqualTo(expected.getSubscriptionStart()))
            .satisfies(a -> assertThat(a.getSubscriptionEnd()).as("check subscriptionEnd").isEqualTo(expected.getSubscriptionEnd()))
            .satisfies(a -> assertThat(a.getContactEmail()).as("check contactEmail").isEqualTo(expected.getContactEmail()))
            .satisfies(a -> assertThat(a.getContactPhone()).as("check contactPhone").isEqualTo(expected.getContactPhone()))
            .satisfies(a -> assertThat(a.getIsSystem()).as("check isSystem").isEqualTo(expected.getIsSystem()))
            .satisfies(a -> assertThat(a.getMetadata()).as("check metadata").isEqualTo(expected.getMetadata()))
            .satisfies(a -> assertThat(a.getVersion()).as("check version").isEqualTo(expected.getVersion()))
            .satisfies(a -> assertThat(a.getCreatedBy()).as("check createdBy").isEqualTo(expected.getCreatedBy()))
            .satisfies(a -> assertThat(a.getCreatedAt()).as("check createdAt").isEqualTo(expected.getCreatedAt()))
            .satisfies(a -> assertThat(a.getUpdatedBy()).as("check updatedBy").isEqualTo(expected.getUpdatedBy()))
            .satisfies(a -> assertThat(a.getUpdatedAt()).as("check updatedAt").isEqualTo(expected.getUpdatedAt()))
            .satisfies(a -> assertThat(a.getIsDeleted()).as("check isDeleted").isEqualTo(expected.getIsDeleted()));
    }

    /**
     * Asserts that the entity has all the updatable relationships set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertTenantUpdatableRelationshipsEquals(Tenant expected, Tenant actual) {
        // empty method
    }
}

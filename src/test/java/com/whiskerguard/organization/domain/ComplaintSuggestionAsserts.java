package com.whiskerguard.organization.domain;

import static org.assertj.core.api.Assertions.assertThat;

public class ComplaintSuggestionAsserts {

    /**
     * Asserts that the entity has all properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertComplaintSuggestionAllPropertiesEquals(ComplaintSuggestion expected, ComplaintSuggestion actual) {
        assertComplaintSuggestionAutoGeneratedPropertiesEquals(expected, actual);
        assertComplaintSuggestionAllUpdatablePropertiesEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all updatable properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertComplaintSuggestionAllUpdatablePropertiesEquals(ComplaintSuggestion expected, ComplaintSuggestion actual) {
        assertComplaintSuggestionUpdatableFieldsEquals(expected, actual);
        assertComplaintSuggestionUpdatableRelationshipsEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all the auto generated properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertComplaintSuggestionAutoGeneratedPropertiesEquals(ComplaintSuggestion expected, ComplaintSuggestion actual) {
        assertThat(actual)
            .as("Verify ComplaintSuggestion auto generated properties")
            .satisfies(a -> assertThat(a.getId()).as("check id").isEqualTo(expected.getId()));
    }

    /**
     * Asserts that the entity has all the updatable fields set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertComplaintSuggestionUpdatableFieldsEquals(ComplaintSuggestion expected, ComplaintSuggestion actual) {
        assertThat(actual)
            .as("Verify ComplaintSuggestion relevant properties")
            .satisfies(a -> assertThat(a.getTenantId()).as("check tenantId").isEqualTo(expected.getTenantId()))
            .satisfies(a -> assertThat(a.getEmployeeId()).as("check employeeId").isEqualTo(expected.getEmployeeId()))
            .satisfies(a -> assertThat(a.getDetail()).as("check detail").isEqualTo(expected.getDetail()))
            .satisfies(a -> assertThat(a.getIsAnonymous()).as("check isAnonymous").isEqualTo(expected.getIsAnonymous()))
            .satisfies(a -> assertThat(a.getContactWay()).as("check contactWay").isEqualTo(expected.getContactWay()))
            .satisfies(a -> assertThat(a.getMetadata()).as("check metadata").isEqualTo(expected.getMetadata()))
            .satisfies(a -> assertThat(a.getVersion()).as("check version").isEqualTo(expected.getVersion()))
            .satisfies(a -> assertThat(a.getCreatedBy()).as("check createdBy").isEqualTo(expected.getCreatedBy()))
            .satisfies(a -> assertThat(a.getCreatedAt()).as("check createdAt").isEqualTo(expected.getCreatedAt()))
            .satisfies(a -> assertThat(a.getUpdatedBy()).as("check updatedBy").isEqualTo(expected.getUpdatedBy()))
            .satisfies(a -> assertThat(a.getUpdatedAt()).as("check updatedAt").isEqualTo(expected.getUpdatedAt()))
            .satisfies(a -> assertThat(a.getIsDeleted()).as("check isDeleted").isEqualTo(expected.getIsDeleted()));
    }

    /**
     * Asserts that the entity has all the updatable relationships set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertComplaintSuggestionUpdatableRelationshipsEquals(ComplaintSuggestion expected, ComplaintSuggestion actual) {
        // empty method
    }
}

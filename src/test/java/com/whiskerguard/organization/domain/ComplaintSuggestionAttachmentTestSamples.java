package com.whiskerguard.organization.domain;

import java.util.Random;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

public class ComplaintSuggestionAttachmentTestSamples {

    private static final Random random = new Random();
    private static final AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));
    private static final AtomicInteger intCount = new AtomicInteger(random.nextInt() + (2 * Short.MAX_VALUE));

    public static ComplaintSuggestionAttachment getComplaintSuggestionAttachmentSample1() {
        return new ComplaintSuggestionAttachment()
            .id(1L)
            .tenantId(1L)
            .suggestionId(1L)
            .fileName("fileName1")
            .filePath("filePath1")
            .fileType("fileType1")
            .fileSize("fileSize1")
            .fileDesc("fileDesc1")
            .version(1)
            .uploadedBy("uploadedBy1");
    }

    public static ComplaintSuggestionAttachment getComplaintSuggestionAttachmentSample2() {
        return new ComplaintSuggestionAttachment()
            .id(2L)
            .tenantId(2L)
            .suggestionId(2L)
            .fileName("fileName2")
            .filePath("filePath2")
            .fileType("fileType2")
            .fileSize("fileSize2")
            .fileDesc("fileDesc2")
            .version(2)
            .uploadedBy("uploadedBy2");
    }

    public static ComplaintSuggestionAttachment getComplaintSuggestionAttachmentRandomSampleGenerator() {
        return new ComplaintSuggestionAttachment()
            .id(longCount.incrementAndGet())
            .tenantId(longCount.incrementAndGet())
            .suggestionId(longCount.incrementAndGet())
            .fileName(UUID.randomUUID().toString())
            .filePath(UUID.randomUUID().toString())
            .fileType(UUID.randomUUID().toString())
            .fileSize(UUID.randomUUID().toString())
            .fileDesc(UUID.randomUUID().toString())
            .version(intCount.incrementAndGet())
            .uploadedBy(UUID.randomUUID().toString());
    }
}

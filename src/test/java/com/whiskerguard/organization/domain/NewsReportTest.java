package com.whiskerguard.organization.domain;

import static com.whiskerguard.organization.domain.EmployeeTestSamples.*;
import static com.whiskerguard.organization.domain.NewsCommentTestSamples.*;
import static com.whiskerguard.organization.domain.NewsReportTestSamples.*;
import static com.whiskerguard.organization.domain.NewsTestSamples.*;
import static org.assertj.core.api.Assertions.assertThat;

import com.whiskerguard.organization.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

class NewsReportTest {

    @Test
    void equalsVerifier() throws Exception {
        TestUtil.equalsVerifier(NewsReport.class);
        NewsReport newsReport1 = getNewsReportSample1();
        NewsReport newsReport2 = new NewsReport();
        assertThat(newsReport1).isNotEqualTo(newsReport2);

        newsReport2.setId(newsReport1.getId());
        assertThat(newsReport1).isEqualTo(newsReport2);

        newsReport2 = getNewsReportSample2();
        assertThat(newsReport1).isNotEqualTo(newsReport2);
    }

    @Test
    void newsTest() {
        NewsReport newsReport = getNewsReportRandomSampleGenerator();
        News newsBack = getNewsRandomSampleGenerator();

        newsReport.setNews(newsBack);
        assertThat(newsReport.getNews()).isEqualTo(newsBack);

        newsReport.news(null);
        assertThat(newsReport.getNews()).isNull();
    }

    @Test
    void commentTest() {
        NewsReport newsReport = getNewsReportRandomSampleGenerator();
        NewsComment newsCommentBack = getNewsCommentRandomSampleGenerator();

        newsReport.setComment(newsCommentBack);
        assertThat(newsReport.getComment()).isEqualTo(newsCommentBack);

        newsReport.comment(null);
        assertThat(newsReport.getComment()).isNull();
    }

    @Test
    void reporterTest() {
        NewsReport newsReport = getNewsReportRandomSampleGenerator();
        Employee employeeBack = getEmployeeRandomSampleGenerator();

        newsReport.setReporter(employeeBack);
        assertThat(newsReport.getReporter()).isEqualTo(employeeBack);

        newsReport.reporter(null);
        assertThat(newsReport.getReporter()).isNull();
    }
}

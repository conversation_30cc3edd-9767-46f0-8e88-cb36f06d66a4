package com.whiskerguard.organization.domain;

import static com.whiskerguard.organization.domain.EmployeeTestSamples.*;
import static com.whiskerguard.organization.domain.NewsLikeTestSamples.*;
import static com.whiskerguard.organization.domain.NewsTestSamples.*;
import static org.assertj.core.api.Assertions.assertThat;

import com.whiskerguard.organization.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

class NewsLikeTest {

    @Test
    void equalsVerifier() throws Exception {
        TestUtil.equalsVerifier(NewsLike.class);
        NewsLike newsLike1 = getNewsLikeSample1();
        NewsLike newsLike2 = new NewsLike();
        assertThat(newsLike1).isNotEqualTo(newsLike2);

        newsLike2.setId(newsLike1.getId());
        assertThat(newsLike1).isEqualTo(newsLike2);

        newsLike2 = getNewsLikeSample2();
        assertThat(newsLike1).isNotEqualTo(newsLike2);
    }

    @Test
    void newsTest() {
        NewsLike newsLike = getNewsLikeRandomSampleGenerator();
        News newsBack = getNewsRandomSampleGenerator();

        newsLike.setNews(newsBack);
        assertThat(newsLike.getNews()).isEqualTo(newsBack);

        newsLike.news(null);
        assertThat(newsLike.getNews()).isNull();
    }

    @Test
    void userTest() {
        NewsLike newsLike = getNewsLikeRandomSampleGenerator();
        Employee employeeBack = getEmployeeRandomSampleGenerator();

        newsLike.setUser(employeeBack);
        assertThat(newsLike.getUser()).isEqualTo(employeeBack);

        newsLike.user(null);
        assertThat(newsLike.getUser()).isNull();
    }
}

package com.whiskerguard.organization.domain;

import static com.whiskerguard.organization.domain.AssertUtils.bigDecimalCompareTo;
import static org.assertj.core.api.Assertions.assertThat;

public class TenantProfileAsserts {

    /**
     * Asserts that the entity has all properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertTenantProfileAllPropertiesEquals(TenantProfile expected, TenantProfile actual) {
        assertTenantProfileAutoGeneratedPropertiesEquals(expected, actual);
        assertTenantProfileAllUpdatablePropertiesEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all updatable properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertTenantProfileAllUpdatablePropertiesEquals(TenantProfile expected, TenantProfile actual) {
        assertTenantProfileUpdatableFieldsEquals(expected, actual);
        assertTenantProfileUpdatableRelationshipsEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all the auto generated properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertTenantProfileAutoGeneratedPropertiesEquals(TenantProfile expected, TenantProfile actual) {
        assertThat(actual)
            .as("Verify TenantProfile auto generated properties")
            .satisfies(a -> assertThat(a.getId()).as("check id").isEqualTo(expected.getId()));
    }

    /**
     * Asserts that the entity has all the updatable fields set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertTenantProfileUpdatableFieldsEquals(TenantProfile expected, TenantProfile actual) {
        assertThat(actual)
            .as("Verify TenantProfile relevant properties")
            .satisfies(a -> assertThat(a.getRegistrationNumber()).as("check registrationNumber").isEqualTo(expected.getRegistrationNumber())
            )
            .satisfies(a -> assertThat(a.getRegistrationDate()).as("check registrationDate").isEqualTo(expected.getRegistrationDate()))
            .satisfies(a ->
                assertThat(a.getRegisteredCapital())
                    .as("check registeredCapital")
                    .usingComparator(bigDecimalCompareTo)
                    .isEqualTo(expected.getRegisteredCapital())
            )
            .satisfies(a -> assertThat(a.getCompanyType()).as("check companyType").isEqualTo(expected.getCompanyType()))
            .satisfies(a -> assertThat(a.getBusinessScope()).as("check businessScope").isEqualTo(expected.getBusinessScope()))
            .satisfies(a -> assertThat(a.getIndustry()).as("check industry").isEqualTo(expected.getIndustry()))
            .satisfies(a ->
                assertThat(a.getTaxRegistrationNumber()).as("check taxRegistrationNumber").isEqualTo(expected.getTaxRegistrationNumber())
            )
            .satisfies(a -> assertThat(a.getOrganizationCode()).as("check organizationCode").isEqualTo(expected.getOrganizationCode()))
            .satisfies(a -> assertThat(a.getRegisteredAddress()).as("check registeredAddress").isEqualTo(expected.getRegisteredAddress()))
            .satisfies(a -> assertThat(a.getPostalCode()).as("check postalCode").isEqualTo(expected.getPostalCode()))
            .satisfies(a -> assertThat(a.getWebsite()).as("check website").isEqualTo(expected.getWebsite()))
            .satisfies(a -> assertThat(a.getFax()).as("check fax").isEqualTo(expected.getFax()))
            .satisfies(a -> assertThat(a.getContactPerson()).as("check contactPerson").isEqualTo(expected.getContactPerson()))
            .satisfies(a -> assertThat(a.getContactMobile()).as("check contactMobile").isEqualTo(expected.getContactMobile()))
            .satisfies(a -> assertThat(a.getContactEmail()).as("check contactEmail").isEqualTo(expected.getContactEmail()))
            .satisfies(a -> assertThat(a.getBankName()).as("check bankName").isEqualTo(expected.getBankName()))
            .satisfies(a -> assertThat(a.getBankAccount()).as("check bankAccount").isEqualTo(expected.getBankAccount()))
            .satisfies(a ->
                assertThat(a.getBusinessLicensePath()).as("check businessLicensePath").isEqualTo(expected.getBusinessLicensePath())
            )
            .satisfies(a -> assertThat(a.getLegalPerson()).as("check legalPerson").isEqualTo(expected.getLegalPerson()))
            .satisfies(a -> assertThat(a.getLegalPersonId()).as("check legalPersonId").isEqualTo(expected.getLegalPersonId()))
            .satisfies(a -> assertThat(a.getMetadata()).as("check metadata").isEqualTo(expected.getMetadata()))
            .satisfies(a -> assertThat(a.getVersion()).as("check version").isEqualTo(expected.getVersion()))
            .satisfies(a -> assertThat(a.getCreatedBy()).as("check createdBy").isEqualTo(expected.getCreatedBy()))
            .satisfies(a -> assertThat(a.getCreatedAt()).as("check createdAt").isEqualTo(expected.getCreatedAt()))
            .satisfies(a -> assertThat(a.getUpdatedBy()).as("check updatedBy").isEqualTo(expected.getUpdatedBy()))
            .satisfies(a -> assertThat(a.getUpdatedAt()).as("check updatedAt").isEqualTo(expected.getUpdatedAt()))
            .satisfies(a -> assertThat(a.getIsDeleted()).as("check isDeleted").isEqualTo(expected.getIsDeleted()));
    }

    /**
     * Asserts that the entity has all the updatable relationships set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertTenantProfileUpdatableRelationshipsEquals(TenantProfile expected, TenantProfile actual) {
        assertThat(actual)
            .as("Verify TenantProfile relationships")
            .satisfies(a -> assertThat(a.getTenant()).as("check tenant").isEqualTo(expected.getTenant()));
    }
}

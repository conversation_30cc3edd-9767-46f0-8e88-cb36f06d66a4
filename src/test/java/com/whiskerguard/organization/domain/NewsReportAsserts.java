package com.whiskerguard.organization.domain;

import static org.assertj.core.api.Assertions.assertThat;

public class NewsReportAsserts {

    /**
     * Asserts that the entity has all properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertNewsReportAllPropertiesEquals(NewsReport expected, NewsReport actual) {
        assertNewsReportAutoGeneratedPropertiesEquals(expected, actual);
        assertNewsReportAllUpdatablePropertiesEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all updatable properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertNewsReportAllUpdatablePropertiesEquals(NewsReport expected, NewsReport actual) {
        assertNewsReportUpdatableFieldsEquals(expected, actual);
        assertNewsReportUpdatableRelationshipsEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all the auto generated properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertNewsReportAutoGeneratedPropertiesEquals(NewsReport expected, NewsReport actual) {
        assertThat(actual)
            .as("Verify NewsReport auto generated properties")
            .satisfies(a -> assertThat(a.getId()).as("check id").isEqualTo(expected.getId()));
    }

    /**
     * Asserts that the entity has all the updatable fields set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertNewsReportUpdatableFieldsEquals(NewsReport expected, NewsReport actual) {
        assertThat(actual)
            .as("Verify NewsReport relevant properties")
            .satisfies(a -> assertThat(a.getReason()).as("check reason").isEqualTo(expected.getReason()))
            .satisfies(a -> assertThat(a.getDetails()).as("check details").isEqualTo(expected.getDetails()))
            .satisfies(a -> assertThat(a.getStatus()).as("check status").isEqualTo(expected.getStatus()))
            .satisfies(a -> assertThat(a.getResult()).as("check result").isEqualTo(expected.getResult()))
            .satisfies(a -> assertThat(a.getMetadata()).as("check metadata").isEqualTo(expected.getMetadata()))
            .satisfies(a -> assertThat(a.getVersion()).as("check version").isEqualTo(expected.getVersion()))
            .satisfies(a -> assertThat(a.getCreatedBy()).as("check createdBy").isEqualTo(expected.getCreatedBy()))
            .satisfies(a -> assertThat(a.getCreatedAt()).as("check createdAt").isEqualTo(expected.getCreatedAt()))
            .satisfies(a -> assertThat(a.getUpdatedBy()).as("check updatedBy").isEqualTo(expected.getUpdatedBy()))
            .satisfies(a -> assertThat(a.getUpdatedAt()).as("check updatedAt").isEqualTo(expected.getUpdatedAt()))
            .satisfies(a -> assertThat(a.getIsDeleted()).as("check isDeleted").isEqualTo(expected.getIsDeleted()));
    }

    /**
     * Asserts that the entity has all the updatable relationships set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertNewsReportUpdatableRelationshipsEquals(NewsReport expected, NewsReport actual) {
        assertThat(actual)
            .as("Verify NewsReport relationships")
            .satisfies(a -> assertThat(a.getNews()).as("check news").isEqualTo(expected.getNews()))
            .satisfies(a -> assertThat(a.getComment()).as("check comment").isEqualTo(expected.getComment()))
            .satisfies(a -> assertThat(a.getReporter()).as("check reporter").isEqualTo(expected.getReporter()));
    }
}

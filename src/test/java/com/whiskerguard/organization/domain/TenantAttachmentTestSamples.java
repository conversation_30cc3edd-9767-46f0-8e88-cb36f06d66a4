package com.whiskerguard.organization.domain;

import java.util.Random;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

public class TenantAttachmentTestSamples {

    private static final Random random = new Random();
    private static final AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));
    private static final AtomicInteger intCount = new AtomicInteger(random.nextInt() + (2 * Short.MAX_VALUE));

    public static TenantAttachment getTenantAttachmentSample1() {
        return new TenantAttachment()
            .id(1L)
            .type("type1")
            .fileUrl("fileUrl1")
            .fileName("fileName1")
            .fileSize(1L)
            .description("description1")
            .metadata("metadata1")
            .version(1)
            .uploadedBy("uploadedBy1")
            .updatedBy("updatedBy1");
    }

    public static TenantAttachment getTenantAttachmentSample2() {
        return new TenantAttachment()
            .id(2L)
            .type("type2")
            .fileUrl("fileUrl2")
            .fileName("fileName2")
            .fileSize(2L)
            .description("description2")
            .metadata("metadata2")
            .version(2)
            .uploadedBy("uploadedBy2")
            .updatedBy("updatedBy2");
    }

    public static TenantAttachment getTenantAttachmentRandomSampleGenerator() {
        return new TenantAttachment()
            .id(longCount.incrementAndGet())
            .type(UUID.randomUUID().toString())
            .fileUrl(UUID.randomUUID().toString())
            .fileName(UUID.randomUUID().toString())
            .fileSize(longCount.incrementAndGet())
            .description(UUID.randomUUID().toString())
            .metadata(UUID.randomUUID().toString())
            .version(intCount.incrementAndGet())
            .uploadedBy(UUID.randomUUID().toString())
            .updatedBy(UUID.randomUUID().toString());
    }
}

package com.whiskerguard.organization.domain;

import static org.assertj.core.api.Assertions.assertThat;

public class RolePermissionAsserts {

    /**
     * Asserts that the entity has all properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertRolePermissionAllPropertiesEquals(RolePermission expected, RolePermission actual) {
        assertRolePermissionAutoGeneratedPropertiesEquals(expected, actual);
        assertRolePermissionAllUpdatablePropertiesEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all updatable properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertRolePermissionAllUpdatablePropertiesEquals(RolePermission expected, RolePermission actual) {
        assertRolePermissionUpdatableFieldsEquals(expected, actual);
        assertRolePermissionUpdatableRelationshipsEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all the auto generated properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertRolePermissionAutoGeneratedPropertiesEquals(RolePermission expected, RolePermission actual) {
        assertThat(actual)
            .as("Verify RolePermission auto generated properties")
            .satisfies(a -> assertThat(a.getId()).as("check id").isEqualTo(expected.getId()));
    }

    /**
     * Asserts that the entity has all the updatable fields set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertRolePermissionUpdatableFieldsEquals(RolePermission expected, RolePermission actual) {
        assertThat(actual)
            .as("Verify RolePermission relevant properties")
            .satisfies(a -> assertThat(a.getTenantId()).as("check tenantId").isEqualTo(expected.getTenantId()))
            .satisfies(a -> assertThat(a.getVersion()).as("check version").isEqualTo(expected.getVersion()))
            .satisfies(a -> assertThat(a.getCreatedBy()).as("check createdBy").isEqualTo(expected.getCreatedBy()))
            .satisfies(a -> assertThat(a.getCreatedAt()).as("check createdAt").isEqualTo(expected.getCreatedAt()))
            .satisfies(a -> assertThat(a.getUpdatedBy()).as("check updatedBy").isEqualTo(expected.getUpdatedBy()))
            .satisfies(a -> assertThat(a.getUpdatedAt()).as("check updatedAt").isEqualTo(expected.getUpdatedAt()))
            .satisfies(a -> assertThat(a.getIsDeleted()).as("check isDeleted").isEqualTo(expected.getIsDeleted()));
    }

    /**
     * Asserts that the entity has all the updatable relationships set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertRolePermissionUpdatableRelationshipsEquals(RolePermission expected, RolePermission actual) {
        assertThat(actual)
            .as("Verify RolePermission relationships")
            .satisfies(a -> assertThat(a.getRole()).as("check role").isEqualTo(expected.getRole()))
            .satisfies(a -> assertThat(a.getPermission()).as("check permission").isEqualTo(expected.getPermission()));
    }
}

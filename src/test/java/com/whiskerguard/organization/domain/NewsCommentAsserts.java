package com.whiskerguard.organization.domain;

import static org.assertj.core.api.Assertions.assertThat;

public class NewsCommentAsserts {

    /**
     * Asserts that the entity has all properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertNewsCommentAllPropertiesEquals(NewsComment expected, NewsComment actual) {
        assertNewsCommentAutoGeneratedPropertiesEquals(expected, actual);
        assertNewsCommentAllUpdatablePropertiesEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all updatable properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertNewsCommentAllUpdatablePropertiesEquals(NewsComment expected, NewsComment actual) {
        assertNewsCommentUpdatableFieldsEquals(expected, actual);
        assertNewsCommentUpdatableRelationshipsEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all the auto generated properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertNewsCommentAutoGeneratedPropertiesEquals(NewsComment expected, NewsComment actual) {
        assertThat(actual)
            .as("Verify NewsComment auto generated properties")
            .satisfies(a -> assertThat(a.getId()).as("check id").isEqualTo(expected.getId()));
    }

    /**
     * Asserts that the entity has all the updatable fields set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertNewsCommentUpdatableFieldsEquals(NewsComment expected, NewsComment actual) {
        assertThat(actual)
            .as("Verify NewsComment relevant properties")
            .satisfies(a -> assertThat(a.getStatus()).as("check status").isEqualTo(expected.getStatus()))
            .satisfies(a -> assertThat(a.getSortOrder()).as("check sortOrder").isEqualTo(expected.getSortOrder()))
            .satisfies(a -> assertThat(a.getContent()).as("check content").isEqualTo(expected.getContent()))
            .satisfies(a -> assertThat(a.getLikeCount()).as("check likeCount").isEqualTo(expected.getLikeCount()))
            .satisfies(a -> assertThat(a.getMetadata()).as("check metadata").isEqualTo(expected.getMetadata()))
            .satisfies(a -> assertThat(a.getVersion()).as("check version").isEqualTo(expected.getVersion()))
            .satisfies(a -> assertThat(a.getCreatedBy()).as("check createdBy").isEqualTo(expected.getCreatedBy()))
            .satisfies(a -> assertThat(a.getCreatedAt()).as("check createdAt").isEqualTo(expected.getCreatedAt()))
            .satisfies(a -> assertThat(a.getUpdatedBy()).as("check updatedBy").isEqualTo(expected.getUpdatedBy()))
            .satisfies(a -> assertThat(a.getUpdatedAt()).as("check updatedAt").isEqualTo(expected.getUpdatedAt()))
            .satisfies(a -> assertThat(a.getIsDeleted()).as("check isDeleted").isEqualTo(expected.getIsDeleted()));
    }

    /**
     * Asserts that the entity has all the updatable relationships set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertNewsCommentUpdatableRelationshipsEquals(NewsComment expected, NewsComment actual) {
        assertThat(actual)
            .as("Verify NewsComment relationships")
            .satisfies(a -> assertThat(a.getNews()).as("check news").isEqualTo(expected.getNews()))
            .satisfies(a -> assertThat(a.getParent()).as("check parent").isEqualTo(expected.getParent()))
            .satisfies(a -> assertThat(a.getCommenter()).as("check commenter").isEqualTo(expected.getCommenter()));
    }
}

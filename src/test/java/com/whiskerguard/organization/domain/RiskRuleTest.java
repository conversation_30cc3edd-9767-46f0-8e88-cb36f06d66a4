package com.whiskerguard.organization.domain;

import static com.whiskerguard.organization.domain.RiskCategoryTestSamples.*;
import static com.whiskerguard.organization.domain.RiskRuleTestSamples.*;
import static org.assertj.core.api.Assertions.assertThat;

import com.whiskerguard.organization.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

class RiskRuleTest {

    @Test
    void equalsVerifier() throws Exception {
        TestUtil.equalsVerifier(RiskRule.class);
        RiskRule riskRule1 = getRiskRuleSample1();
        RiskRule riskRule2 = new RiskRule();
        assertThat(riskRule1).isNotEqualTo(riskRule2);

        riskRule2.setId(riskRule1.getId());
        assertThat(riskRule1).isEqualTo(riskRule2);

        riskRule2 = getRiskRuleSample2();
        assertThat(riskRule1).isNotEqualTo(riskRule2);
    }

    @Test
    void riskCategoryTest() {
        RiskRule riskRule = getRiskRuleRandomSampleGenerator();
        RiskCategory riskCategoryBack = getRiskCategoryRandomSampleGenerator();

        riskRule.setRiskCategory(riskCategoryBack);
        assertThat(riskRule.getRiskCategory()).isEqualTo(riskCategoryBack);

        riskRule.riskCategory(null);
        assertThat(riskRule.getRiskCategory()).isNull();
    }
}

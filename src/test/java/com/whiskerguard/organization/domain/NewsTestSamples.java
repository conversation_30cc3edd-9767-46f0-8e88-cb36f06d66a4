package com.whiskerguard.organization.domain;

import java.util.Random;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

public class NewsTestSamples {

    private static final Random random = new Random();
    private static final AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));
    private static final AtomicInteger intCount = new AtomicInteger(random.nextInt() + (2 * Short.MAX_VALUE));

    public static News getNewsSample1() {
        return new News()
            .id(1L)
            .sortOrder(1)
            .subtitle("subtitle1")
            .title("title1")
            .summary("summary1")
            .keywords("keywords1")
            .content("content1")
            .viewCount(1)
            .likeCount(1)
            .commentCount(1)
            .shareCount(1)
            .coverImageUrl("coverImageUrl1")
            .metadata("metadata1")
            .version(1)
            .createdBy("createdBy1")
            .updatedBy("updatedBy1");
    }

    public static News getNewsSample2() {
        return new News()
            .id(2L)
            .sortOrder(2)
            .subtitle("subtitle2")
            .title("title2")
            .summary("summary2")
            .keywords("keywords2")
            .content("content2")
            .viewCount(2)
            .likeCount(2)
            .commentCount(2)
            .shareCount(2)
            .coverImageUrl("coverImageUrl2")
            .metadata("metadata2")
            .version(2)
            .createdBy("createdBy2")
            .updatedBy("updatedBy2");
    }

    public static News getNewsRandomSampleGenerator() {
        return new News()
            .id(longCount.incrementAndGet())
            .sortOrder(intCount.incrementAndGet())
            .subtitle(UUID.randomUUID().toString())
            .title(UUID.randomUUID().toString())
            .summary(UUID.randomUUID().toString())
            .keywords(UUID.randomUUID().toString())
            .content(UUID.randomUUID().toString())
            .viewCount(intCount.incrementAndGet())
            .likeCount(intCount.incrementAndGet())
            .commentCount(intCount.incrementAndGet())
            .shareCount(intCount.incrementAndGet())
            .coverImageUrl(UUID.randomUUID().toString())
            .metadata(UUID.randomUUID().toString())
            .version(intCount.incrementAndGet())
            .createdBy(UUID.randomUUID().toString())
            .updatedBy(UUID.randomUUID().toString());
    }
}

package com.whiskerguard.organization.domain;

import java.util.Random;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

public class OrgUnitTestSamples {

    private static final Random random = new Random();
    private static final AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));
    private static final AtomicInteger intCount = new AtomicInteger(random.nextInt() + (2 * Short.MAX_VALUE));

    public static OrgUnit getOrgUnitSample1() {
        return new OrgUnit()
            .id(1L)
            .tenantId(1L)
            .name("name1")
            .code("code1")
            .level(1)
            .status(1)
            .sortOrder(1)
            .description("description1")
            .metadata("metadata1")
            .version(1)
            .createdBy("createdBy1")
            .updatedBy("updatedBy1");
    }

    public static OrgUnit getOrgUnitSample2() {
        return new OrgUnit()
            .id(2L)
            .tenantId(2L)
            .name("name2")
            .code("code2")
            .level(2)
            .status(2)
            .sortOrder(2)
            .description("description2")
            .metadata("metadata2")
            .version(2)
            .createdBy("createdBy2")
            .updatedBy("updatedBy2");
    }

    public static OrgUnit getOrgUnitRandomSampleGenerator() {
        return new OrgUnit()
            .id(longCount.incrementAndGet())
            .tenantId(longCount.incrementAndGet())
            .name(UUID.randomUUID().toString())
            .code(UUID.randomUUID().toString())
            .level(intCount.incrementAndGet())
            .status(intCount.incrementAndGet())
            .sortOrder(intCount.incrementAndGet())
            .description(UUID.randomUUID().toString())
            .metadata(UUID.randomUUID().toString())
            .version(intCount.incrementAndGet())
            .createdBy(UUID.randomUUID().toString())
            .updatedBy(UUID.randomUUID().toString());
    }
}

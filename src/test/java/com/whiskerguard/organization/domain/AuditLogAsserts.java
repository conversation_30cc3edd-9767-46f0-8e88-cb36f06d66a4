package com.whiskerguard.organization.domain;

import static org.assertj.core.api.Assertions.assertThat;

public class AuditLogAsserts {

    /**
     * Asserts that the entity has all properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertAuditLogAllPropertiesEquals(AuditLog expected, AuditLog actual) {
        assertAuditLogAutoGeneratedPropertiesEquals(expected, actual);
        assertAuditLogAllUpdatablePropertiesEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all updatable properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertAuditLogAllUpdatablePropertiesEquals(AuditLog expected, AuditLog actual) {
        assertAuditLogUpdatableFieldsEquals(expected, actual);
        assertAuditLogUpdatableRelationshipsEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all the auto generated properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertAuditLogAutoGeneratedPropertiesEquals(AuditLog expected, AuditLog actual) {
        assertThat(actual)
            .as("Verify AuditLog auto generated properties")
            .satisfies(a -> assertThat(a.getId()).as("check id").isEqualTo(expected.getId()));
    }

    /**
     * Asserts that the entity has all the updatable fields set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertAuditLogUpdatableFieldsEquals(AuditLog expected, AuditLog actual) {
        assertThat(actual)
            .as("Verify AuditLog relevant properties")
            .satisfies(a -> assertThat(a.getTenantId()).as("check tenantId").isEqualTo(expected.getTenantId()))
            .satisfies(a -> assertThat(a.getEntityName()).as("check entityName").isEqualTo(expected.getEntityName()))
            .satisfies(a -> assertThat(a.getEntityId()).as("check entityId").isEqualTo(expected.getEntityId()))
            .satisfies(a -> assertThat(a.getOperation()).as("check operation").isEqualTo(expected.getOperation()))
            .satisfies(a -> assertThat(a.getOperator()).as("check operator").isEqualTo(expected.getOperator()))
            .satisfies(a -> assertThat(a.getTimestamp()).as("check timestamp").isEqualTo(expected.getTimestamp()))
            .satisfies(a -> assertThat(a.getDiff()).as("check diff").isEqualTo(expected.getDiff()))
            .satisfies(a -> assertThat(a.getVersion()).as("check version").isEqualTo(expected.getVersion()))
            .satisfies(a -> assertThat(a.getCreatedBy()).as("check createdBy").isEqualTo(expected.getCreatedBy()))
            .satisfies(a -> assertThat(a.getCreatedAt()).as("check createdAt").isEqualTo(expected.getCreatedAt()))
            .satisfies(a -> assertThat(a.getUpdatedBy()).as("check updatedBy").isEqualTo(expected.getUpdatedBy()))
            .satisfies(a -> assertThat(a.getUpdatedAt()).as("check updatedAt").isEqualTo(expected.getUpdatedAt()))
            .satisfies(a -> assertThat(a.getIsDeleted()).as("check isDeleted").isEqualTo(expected.getIsDeleted()));
    }

    /**
     * Asserts that the entity has all the updatable relationships set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertAuditLogUpdatableRelationshipsEquals(AuditLog expected, AuditLog actual) {
        // empty method
    }
}

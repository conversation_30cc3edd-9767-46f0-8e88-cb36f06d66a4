package com.whiskerguard.organization.domain;

import java.util.Random;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

public class TenantInitializeTestSamples {

    private static final Random random = new Random();
    private static final AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));
    private static final AtomicInteger intCount = new AtomicInteger(random.nextInt() + (2 * Short.MAX_VALUE));

    public static TenantInitialize getTenantInitializeSample1() {
        return new TenantInitialize()
            .id(1L)
            .tenantId(1L)
            .type(1)
            .name("name1")
            .code("code1")
            .departmentCode("departmentCode1")
            .description("description1")
            .metadata("metadata1")
            .version(1)
            .createdBy("createdBy1")
            .updatedBy("updatedBy1");
    }

    public static TenantInitialize getTenantInitializeSample2() {
        return new TenantInitialize()
            .id(2L)
            .tenantId(2L)
            .type(2)
            .name("name2")
            .code("code2")
            .departmentCode("departmentCode2")
            .description("description2")
            .metadata("metadata2")
            .version(2)
            .createdBy("createdBy2")
            .updatedBy("updatedBy2");
    }

    public static TenantInitialize getTenantInitializeRandomSampleGenerator() {
        return new TenantInitialize()
            .id(longCount.incrementAndGet())
            .tenantId(longCount.incrementAndGet())
            .type(intCount.incrementAndGet())
            .name(UUID.randomUUID().toString())
            .code(UUID.randomUUID().toString())
            .departmentCode(UUID.randomUUID().toString())
            .description(UUID.randomUUID().toString())
            .metadata(UUID.randomUUID().toString())
            .version(intCount.incrementAndGet())
            .createdBy(UUID.randomUUID().toString())
            .updatedBy(UUID.randomUUID().toString());
    }
}

package com.whiskerguard.organization.domain;

import java.util.Random;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

public class EmployeeTestSamples {

    private static final Random random = new Random();
    private static final AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));
    private static final AtomicInteger intCount = new AtomicInteger(random.nextInt() + (2 * Short.MAX_VALUE));

    public static Employee getEmployeeSample1() {
        return new Employee()
            .id(1L)
            .tenantId(1L)
            .username("username1")
            .password("password1")
            .salt("salt1")
            .realName("realName1")
            .avatar("avatar1")
            .email("email1")
            .phone("phone1")
            .idCard("idCard1")
            .employeeNo("employeeNo1")
            .metadata("metadata1")
            .version(1)
            .createdBy("createdBy1")
            .updatedBy("updatedBy1")
            .lastLoginIp("lastLoginIp1")
            .loginFailureCount(1)
            .wechatOpenId("wechatOpenId1")
            .wechatUnionId("wechatUnionId1");
    }

    public static Employee getEmployeeSample2() {
        return new Employee()
            .id(2L)
            .tenantId(2L)
            .username("username2")
            .password("password2")
            .salt("salt2")
            .realName("realName2")
            .avatar("avatar2")
            .email("email2")
            .phone("phone2")
            .idCard("idCard2")
            .employeeNo("employeeNo2")
            .metadata("metadata2")
            .version(2)
            .createdBy("createdBy2")
            .updatedBy("updatedBy2")
            .lastLoginIp("lastLoginIp2")
            .loginFailureCount(2)
            .wechatOpenId("wechatOpenId2")
            .wechatUnionId("wechatUnionId2");
    }

    public static Employee getEmployeeRandomSampleGenerator() {
        return new Employee()
            .id(longCount.incrementAndGet())
            .tenantId(longCount.incrementAndGet())
            .username(UUID.randomUUID().toString())
            .password(UUID.randomUUID().toString())
            .salt(UUID.randomUUID().toString())
            .realName(UUID.randomUUID().toString())
            .avatar(UUID.randomUUID().toString())
            .email(UUID.randomUUID().toString())
            .phone(UUID.randomUUID().toString())
            .idCard(UUID.randomUUID().toString())
            .employeeNo(UUID.randomUUID().toString())
            .metadata(UUID.randomUUID().toString())
            .version(intCount.incrementAndGet())
            .createdBy(UUID.randomUUID().toString())
            .updatedBy(UUID.randomUUID().toString())
            .lastLoginIp(UUID.randomUUID().toString())
            .loginFailureCount(intCount.incrementAndGet())
            .wechatOpenId(UUID.randomUUID().toString())
            .wechatUnionId(UUID.randomUUID().toString());
    }
}

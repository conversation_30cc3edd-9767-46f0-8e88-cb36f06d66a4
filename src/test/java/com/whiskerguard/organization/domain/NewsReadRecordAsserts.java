package com.whiskerguard.organization.domain;

import static org.assertj.core.api.Assertions.assertThat;

public class NewsReadRecordAsserts {

    /**
     * Asserts that the entity has all properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertNewsReadRecordAllPropertiesEquals(NewsReadRecord expected, NewsReadRecord actual) {
        assertNewsReadRecordAutoGeneratedPropertiesEquals(expected, actual);
        assertNewsReadRecordAllUpdatablePropertiesEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all updatable properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertNewsReadRecordAllUpdatablePropertiesEquals(NewsReadRecord expected, NewsReadRecord actual) {
        assertNewsReadRecordUpdatableFieldsEquals(expected, actual);
        assertNewsReadRecordUpdatableRelationshipsEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all the auto generated properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertNewsReadRecordAutoGeneratedPropertiesEquals(NewsReadRecord expected, NewsReadRecord actual) {
        assertThat(actual)
            .as("Verify NewsReadRecord auto generated properties")
            .satisfies(a -> assertThat(a.getId()).as("check id").isEqualTo(expected.getId()));
    }

    /**
     * Asserts that the entity has all the updatable fields set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertNewsReadRecordUpdatableFieldsEquals(NewsReadRecord expected, NewsReadRecord actual) {
        assertThat(actual)
            .as("Verify NewsReadRecord relevant properties")
            .satisfies(a -> assertThat(a.getReadAt()).as("check readAt").isEqualTo(expected.getReadAt()))
            .satisfies(a -> assertThat(a.getSource()).as("check source").isEqualTo(expected.getSource()))
            .satisfies(a -> assertThat(a.getDevice()).as("check device").isEqualTo(expected.getDevice()))
            .satisfies(a -> assertThat(a.getDuration()).as("check duration").isEqualTo(expected.getDuration()))
            .satisfies(a -> assertThat(a.getMetadata()).as("check metadata").isEqualTo(expected.getMetadata()))
            .satisfies(a -> assertThat(a.getVersion()).as("check version").isEqualTo(expected.getVersion()))
            .satisfies(a -> assertThat(a.getCreatedBy()).as("check createdBy").isEqualTo(expected.getCreatedBy()))
            .satisfies(a -> assertThat(a.getCreatedAt()).as("check createdAt").isEqualTo(expected.getCreatedAt()))
            .satisfies(a -> assertThat(a.getUpdatedBy()).as("check updatedBy").isEqualTo(expected.getUpdatedBy()))
            .satisfies(a -> assertThat(a.getUpdatedAt()).as("check updatedAt").isEqualTo(expected.getUpdatedAt()))
            .satisfies(a -> assertThat(a.getIsDeleted()).as("check isDeleted").isEqualTo(expected.getIsDeleted()));
    }

    /**
     * Asserts that the entity has all the updatable relationships set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertNewsReadRecordUpdatableRelationshipsEquals(NewsReadRecord expected, NewsReadRecord actual) {
        assertThat(actual)
            .as("Verify NewsReadRecord relationships")
            .satisfies(a -> assertThat(a.getNews()).as("check news").isEqualTo(expected.getNews()))
            .satisfies(a -> assertThat(a.getReader()).as("check reader").isEqualTo(expected.getReader()));
    }
}

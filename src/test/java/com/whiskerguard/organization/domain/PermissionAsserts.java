package com.whiskerguard.organization.domain;

import static org.assertj.core.api.Assertions.assertThat;

public class PermissionAsserts {

    /**
     * Asserts that the entity has all properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertPermissionAllPropertiesEquals(Permission expected, Permission actual) {
        assertPermissionAutoGeneratedPropertiesEquals(expected, actual);
        assertPermissionAllUpdatablePropertiesEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all updatable properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertPermissionAllUpdatablePropertiesEquals(Permission expected, Permission actual) {
        assertPermissionUpdatableFieldsEquals(expected, actual);
        assertPermissionUpdatableRelationshipsEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all the auto generated properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertPermissionAutoGeneratedPropertiesEquals(Permission expected, Permission actual) {
        assertThat(actual)
            .as("Verify Permission auto generated properties")
            .satisfies(a -> assertThat(a.getId()).as("check id").isEqualTo(expected.getId()));
    }

    /**
     * Asserts that the entity has all the updatable fields set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertPermissionUpdatableFieldsEquals(Permission expected, Permission actual) {
        assertThat(actual)
            .as("Verify Permission relevant properties")
            .satisfies(a -> assertThat(a.getTenantId()).as("check tenantId").isEqualTo(expected.getTenantId()))
            .satisfies(a -> assertThat(a.getServiceName()).as("check serviceName").isEqualTo(expected.getServiceName()))
            .satisfies(a -> assertThat(a.getCode()).as("check code").isEqualTo(expected.getCode()))
            .satisfies(a -> assertThat(a.getName()).as("check name").isEqualTo(expected.getName()))
            .satisfies(a -> assertThat(a.getResourceType()).as("check resourceType").isEqualTo(expected.getResourceType()))
            .satisfies(a -> assertThat(a.getUrlPattern()).as("check urlPattern").isEqualTo(expected.getUrlPattern()))
            .satisfies(a -> assertThat(a.getMethod()).as("check method").isEqualTo(expected.getMethod()))
            .satisfies(a -> assertThat(a.getFrontendRoute()).as("check frontendRoute").isEqualTo(expected.getFrontendRoute()))
            .satisfies(a -> assertThat(a.getBackendUrl()).as("check backendUrl").isEqualTo(expected.getBackendUrl()))
            .satisfies(a -> assertThat(a.getIcon()).as("check icon").isEqualTo(expected.getIcon()))
            .satisfies(a -> assertThat(a.getSortOrder()).as("check sortOrder").isEqualTo(expected.getSortOrder()))
            .satisfies(a -> assertThat(a.getComponent()).as("check component").isEqualTo(expected.getComponent()))
            .satisfies(a -> assertThat(a.getRedirect()).as("check redirect").isEqualTo(expected.getRedirect()))
            .satisfies(a -> assertThat(a.getDescription()).as("check description").isEqualTo(expected.getDescription()))
            .satisfies(a -> assertThat(a.getMetadata()).as("check metadata").isEqualTo(expected.getMetadata()))
            .satisfies(a -> assertThat(a.getVersion()).as("check version").isEqualTo(expected.getVersion()))
            .satisfies(a -> assertThat(a.getCreatedBy()).as("check createdBy").isEqualTo(expected.getCreatedBy()))
            .satisfies(a -> assertThat(a.getCreatedAt()).as("check createdAt").isEqualTo(expected.getCreatedAt()))
            .satisfies(a -> assertThat(a.getUpdatedBy()).as("check updatedBy").isEqualTo(expected.getUpdatedBy()))
            .satisfies(a -> assertThat(a.getUpdatedAt()).as("check updatedAt").isEqualTo(expected.getUpdatedAt()))
            .satisfies(a -> assertThat(a.getIsDeleted()).as("check isDeleted").isEqualTo(expected.getIsDeleted()))
            .satisfies(a -> assertThat(a.getIsShow()).as("check isShow").isEqualTo(expected.getIsShow()));
    }

    /**
     * Asserts that the entity has all the updatable relationships set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertPermissionUpdatableRelationshipsEquals(Permission expected, Permission actual) {
        // empty method
    }
}

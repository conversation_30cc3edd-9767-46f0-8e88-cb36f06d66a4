package com.whiskerguard.organization.domain;

import static com.whiskerguard.organization.domain.RiskModelTestSamples.*;
import static org.assertj.core.api.Assertions.assertThat;

import com.whiskerguard.organization.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

class RiskModelTest {

    @Test
    void equalsVerifier() throws Exception {
        TestUtil.equalsVerifier(RiskModel.class);
        RiskModel riskModel1 = getRiskModelSample1();
        RiskModel riskModel2 = new RiskModel();
        assertThat(riskModel1).isNotEqualTo(riskModel2);

        riskModel2.setId(riskModel1.getId());
        assertThat(riskModel1).isEqualTo(riskModel2);

        riskModel2 = getRiskModelSample2();
        assertThat(riskModel1).isNotEqualTo(riskModel2);
    }
}

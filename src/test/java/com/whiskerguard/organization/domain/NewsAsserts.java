package com.whiskerguard.organization.domain;

import static org.assertj.core.api.Assertions.assertThat;

public class NewsAsserts {

    /**
     * Asserts that the entity has all properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertNewsAllPropertiesEquals(News expected, News actual) {
        assertNewsAutoGeneratedPropertiesEquals(expected, actual);
        assertNewsAllUpdatablePropertiesEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all updatable properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertNewsAllUpdatablePropertiesEquals(News expected, News actual) {
        assertNewsUpdatableFieldsEquals(expected, actual);
        assertNewsUpdatableRelationshipsEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all the auto generated properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertNewsAutoGeneratedPropertiesEquals(News expected, News actual) {
        assertThat(actual)
            .as("Verify News auto generated properties")
            .satisfies(a -> assertThat(a.getId()).as("check id").isEqualTo(expected.getId()));
    }

    /**
     * Asserts that the entity has all the updatable fields set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertNewsUpdatableFieldsEquals(News expected, News actual) {
        assertThat(actual)
            .as("Verify News relevant properties")
            .satisfies(a -> assertThat(a.getStatus()).as("check status").isEqualTo(expected.getStatus()))
            .satisfies(a -> assertThat(a.getSortOrder()).as("check sortOrder").isEqualTo(expected.getSortOrder()))
            .satisfies(a -> assertThat(a.getSubtitle()).as("check subtitle").isEqualTo(expected.getSubtitle()))
            .satisfies(a -> assertThat(a.getTitle()).as("check title").isEqualTo(expected.getTitle()))
            .satisfies(a -> assertThat(a.getSummary()).as("check summary").isEqualTo(expected.getSummary()))
            .satisfies(a -> assertThat(a.getKeywords()).as("check keywords").isEqualTo(expected.getKeywords()))
            .satisfies(a -> assertThat(a.getContent()).as("check content").isEqualTo(expected.getContent()))
            .satisfies(a -> assertThat(a.getPublishDate()).as("check publishDate").isEqualTo(expected.getPublishDate()))
            .satisfies(a -> assertThat(a.getPublishedAt()).as("check publishedAt").isEqualTo(expected.getPublishedAt()))
            .satisfies(a -> assertThat(a.getViewCount()).as("check viewCount").isEqualTo(expected.getViewCount()))
            .satisfies(a -> assertThat(a.getLikeCount()).as("check likeCount").isEqualTo(expected.getLikeCount()))
            .satisfies(a -> assertThat(a.getCommentCount()).as("check commentCount").isEqualTo(expected.getCommentCount()))
            .satisfies(a -> assertThat(a.getShareCount()).as("check shareCount").isEqualTo(expected.getShareCount()))
            .satisfies(a -> assertThat(a.getCoverImageUrl()).as("check coverImageUrl").isEqualTo(expected.getCoverImageUrl()))
            .satisfies(a -> assertThat(a.getIsSticky()).as("check isSticky").isEqualTo(expected.getIsSticky()))
            .satisfies(a -> assertThat(a.getStickyStartTime()).as("check stickyStartTime").isEqualTo(expected.getStickyStartTime()))
            .satisfies(a -> assertThat(a.getStickyEndTime()).as("check stickyEndTime").isEqualTo(expected.getStickyEndTime()))
            .satisfies(a -> assertThat(a.getMetadata()).as("check metadata").isEqualTo(expected.getMetadata()))
            .satisfies(a -> assertThat(a.getVersion()).as("check version").isEqualTo(expected.getVersion()))
            .satisfies(a -> assertThat(a.getCreatedBy()).as("check createdBy").isEqualTo(expected.getCreatedBy()))
            .satisfies(a -> assertThat(a.getCreatedAt()).as("check createdAt").isEqualTo(expected.getCreatedAt()))
            .satisfies(a -> assertThat(a.getUpdatedBy()).as("check updatedBy").isEqualTo(expected.getUpdatedBy()))
            .satisfies(a -> assertThat(a.getUpdatedAt()).as("check updatedAt").isEqualTo(expected.getUpdatedAt()))
            .satisfies(a -> assertThat(a.getIsDeleted()).as("check isDeleted").isEqualTo(expected.getIsDeleted()));
    }

    /**
     * Asserts that the entity has all the updatable relationships set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertNewsUpdatableRelationshipsEquals(News expected, News actual) {
        assertThat(actual)
            .as("Verify News relationships")
            .satisfies(a -> assertThat(a.getCategory()).as("check category").isEqualTo(expected.getCategory()))
            .satisfies(a -> assertThat(a.getOrgUnit()).as("check orgUnit").isEqualTo(expected.getOrgUnit()))
            .satisfies(a -> assertThat(a.getAuthor()).as("check author").isEqualTo(expected.getAuthor()))
            .satisfies(a -> assertThat(a.getTags()).as("check tags").isEqualTo(expected.getTags()));
    }
}

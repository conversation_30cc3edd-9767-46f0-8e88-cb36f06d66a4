package com.whiskerguard.organization.domain;

import static com.whiskerguard.organization.domain.EmployeeRoleTestSamples.*;
import static com.whiskerguard.organization.domain.EmployeeTestSamples.*;
import static com.whiskerguard.organization.domain.OrgUnitTestSamples.*;
import static com.whiskerguard.organization.domain.RoleTestSamples.*;
import static org.assertj.core.api.Assertions.assertThat;

import com.whiskerguard.organization.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

class EmployeeRoleTest {

    @Test
    void equalsVerifier() throws Exception {
        TestUtil.equalsVerifier(EmployeeRole.class);
        EmployeeRole employeeRole1 = getEmployeeRoleSample1();
        EmployeeRole employeeRole2 = new EmployeeRole();
        assertThat(employeeRole1).isNotEqualTo(employeeRole2);

        employeeRole2.setId(employeeRole1.getId());
        assertThat(employeeRole1).isEqualTo(employeeRole2);

        employeeRole2 = getEmployeeRoleSample2();
        assertThat(employeeRole1).isNotEqualTo(employeeRole2);
    }

    @Test
    void employeeTest() {
        EmployeeRole employeeRole = getEmployeeRoleRandomSampleGenerator();
        Employee employeeBack = getEmployeeRandomSampleGenerator();

        employeeRole.setEmployee(employeeBack);
        assertThat(employeeRole.getEmployee()).isEqualTo(employeeBack);

        employeeRole.employee(null);
        assertThat(employeeRole.getEmployee()).isNull();
    }

    @Test
    void roleTest() {
        EmployeeRole employeeRole = getEmployeeRoleRandomSampleGenerator();
        Role roleBack = getRoleRandomSampleGenerator();

        employeeRole.setRole(roleBack);
        assertThat(employeeRole.getRole()).isEqualTo(roleBack);

        employeeRole.role(null);
        assertThat(employeeRole.getRole()).isNull();
    }

    @Test
    void orgUnitTest() {
        EmployeeRole employeeRole = getEmployeeRoleRandomSampleGenerator();
        OrgUnit orgUnitBack = getOrgUnitRandomSampleGenerator();

        employeeRole.setOrgUnit(orgUnitBack);
        assertThat(employeeRole.getOrgUnit()).isEqualTo(orgUnitBack);

        employeeRole.orgUnit(null);
        assertThat(employeeRole.getOrgUnit()).isNull();
    }
}

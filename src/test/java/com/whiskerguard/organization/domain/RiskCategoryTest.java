package com.whiskerguard.organization.domain;

import static com.whiskerguard.organization.domain.RiskCategoryTestSamples.*;
import static com.whiskerguard.organization.domain.RiskModelTestSamples.*;
import static org.assertj.core.api.Assertions.assertThat;

import com.whiskerguard.organization.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

class RiskCategoryTest {

    @Test
    void equalsVerifier() throws Exception {
        TestUtil.equalsVerifier(RiskCategory.class);
        RiskCategory riskCategory1 = getRiskCategorySample1();
        RiskCategory riskCategory2 = new RiskCategory();
        assertThat(riskCategory1).isNotEqualTo(riskCategory2);

        riskCategory2.setId(riskCategory1.getId());
        assertThat(riskCategory1).isEqualTo(riskCategory2);

        riskCategory2 = getRiskCategorySample2();
        assertThat(riskCategory1).isNotEqualTo(riskCategory2);
    }

    @Test
    void riskModelTest() {
        RiskCategory riskCategory = getRiskCategoryRandomSampleGenerator();
        RiskModel riskModelBack = getRiskModelRandomSampleGenerator();

        riskCategory.setRiskModel(riskModelBack);
        assertThat(riskCategory.getRiskModel()).isEqualTo(riskModelBack);

        riskCategory.riskModel(null);
        assertThat(riskCategory.getRiskModel()).isNull();
    }
}

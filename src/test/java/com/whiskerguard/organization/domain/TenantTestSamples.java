package com.whiskerguard.organization.domain;

import java.util.Random;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

public class TenantTestSamples {

    private static final Random random = new Random();
    private static final AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));
    private static final AtomicInteger intCount = new AtomicInteger(random.nextInt() + (2 * Short.MAX_VALUE));

    public static Tenant getTenantSample1() {
        return new Tenant()
            .id(1L)
            .tenantCode("tenantCode1")
            .name("name1")
            .status(1)
            .subscriptionPlan("subscriptionPlan1")
            .contactEmail("contactEmail1")
            .contactPhone("contactPhone1")
            .metadata("metadata1")
            .version(1)
            .createdBy("createdBy1")
            .updatedBy("updatedBy1");
    }

    public static Tenant getTenantSample2() {
        return new Tenant()
            .id(2L)
            .tenantCode("tenantCode2")
            .name("name2")
            .status(2)
            .subscriptionPlan("subscriptionPlan2")
            .contactEmail("contactEmail2")
            .contactPhone("contactPhone2")
            .metadata("metadata2")
            .version(2)
            .createdBy("createdBy2")
            .updatedBy("updatedBy2");
    }

    public static Tenant getTenantRandomSampleGenerator() {
        return new Tenant()
            .id(longCount.incrementAndGet())
            .tenantCode(UUID.randomUUID().toString())
            .name(UUID.randomUUID().toString())
            .status(intCount.incrementAndGet())
            .subscriptionPlan(UUID.randomUUID().toString())
            .contactEmail(UUID.randomUUID().toString())
            .contactPhone(UUID.randomUUID().toString())
            .metadata(UUID.randomUUID().toString())
            .version(intCount.incrementAndGet())
            .createdBy(UUID.randomUUID().toString())
            .updatedBy(UUID.randomUUID().toString());
    }
}

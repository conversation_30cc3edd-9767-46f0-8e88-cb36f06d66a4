package com.whiskerguard.organization.domain;

import java.util.Random;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

public class LetterCommitmentTestSamples {

    private static final Random random = new Random();
    private static final AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));
    private static final AtomicInteger intCount = new AtomicInteger(random.nextInt() + (2 * Short.MAX_VALUE));

    public static LetterCommitment getLetterCommitmentSample1() {
        return new LetterCommitment()
            .id(1L)
            .tenantId(1L)
            .employeeId(1L)
            .type(1)
            .filePath("filePath1")
            .metadata("metadata1")
            .version(1)
            .createdBy("createdBy1")
            .updatedBy("updatedBy1");
    }

    public static LetterCommitment getLetterCommitmentSample2() {
        return new LetterCommitment()
            .id(2L)
            .tenantId(2L)
            .employeeId(2L)
            .type(2)
            .filePath("filePath2")
            .metadata("metadata2")
            .version(2)
            .createdBy("createdBy2")
            .updatedBy("updatedBy2");
    }

    public static LetterCommitment getLetterCommitmentRandomSampleGenerator() {
        return new LetterCommitment()
            .id(longCount.incrementAndGet())
            .tenantId(longCount.incrementAndGet())
            .employeeId(longCount.incrementAndGet())
            .type(intCount.incrementAndGet())
            .filePath(UUID.randomUUID().toString())
            .metadata(UUID.randomUUID().toString())
            .version(intCount.incrementAndGet())
            .createdBy(UUID.randomUUID().toString())
            .updatedBy(UUID.randomUUID().toString());
    }
}

package com.whiskerguard.organization.domain;

import static org.assertj.core.api.Assertions.assertThat;

public class ComplaintSuggestionAttachmentAsserts {

    /**
     * Asserts that the entity has all properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertComplaintSuggestionAttachmentAllPropertiesEquals(
        ComplaintSuggestionAttachment expected,
        ComplaintSuggestionAttachment actual
    ) {
        assertComplaintSuggestionAttachmentAutoGeneratedPropertiesEquals(expected, actual);
        assertComplaintSuggestionAttachmentAllUpdatablePropertiesEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all updatable properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertComplaintSuggestionAttachmentAllUpdatablePropertiesEquals(
        ComplaintSuggestionAttachment expected,
        ComplaintSuggestionAttachment actual
    ) {
        assertComplaintSuggestionAttachmentUpdatableFieldsEquals(expected, actual);
        assertComplaintSuggestionAttachmentUpdatableRelationshipsEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all the auto generated properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertComplaintSuggestionAttachmentAutoGeneratedPropertiesEquals(
        ComplaintSuggestionAttachment expected,
        ComplaintSuggestionAttachment actual
    ) {
        assertThat(actual)
            .as("Verify ComplaintSuggestionAttachment auto generated properties")
            .satisfies(a -> assertThat(a.getId()).as("check id").isEqualTo(expected.getId()));
    }

    /**
     * Asserts that the entity has all the updatable fields set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertComplaintSuggestionAttachmentUpdatableFieldsEquals(
        ComplaintSuggestionAttachment expected,
        ComplaintSuggestionAttachment actual
    ) {
        assertThat(actual)
            .as("Verify ComplaintSuggestionAttachment relevant properties")
            .satisfies(a -> assertThat(a.getTenantId()).as("check tenantId").isEqualTo(expected.getTenantId()))
            .satisfies(a -> assertThat(a.getSuggestionId()).as("check suggestionId").isEqualTo(expected.getSuggestionId()))
            .satisfies(a -> assertThat(a.getFileName()).as("check fileName").isEqualTo(expected.getFileName()))
            .satisfies(a -> assertThat(a.getFilePath()).as("check filePath").isEqualTo(expected.getFilePath()))
            .satisfies(a -> assertThat(a.getFileType()).as("check fileType").isEqualTo(expected.getFileType()))
            .satisfies(a -> assertThat(a.getFileSize()).as("check fileSize").isEqualTo(expected.getFileSize()))
            .satisfies(a -> assertThat(a.getFileDesc()).as("check fileDesc").isEqualTo(expected.getFileDesc()))
            .satisfies(a -> assertThat(a.getMetadata()).as("check metadata").isEqualTo(expected.getMetadata()))
            .satisfies(a -> assertThat(a.getVersion()).as("check version").isEqualTo(expected.getVersion()))
            .satisfies(a -> assertThat(a.getUploadedBy()).as("check uploadedBy").isEqualTo(expected.getUploadedBy()))
            .satisfies(a -> assertThat(a.getUploadedAt()).as("check uploadedAt").isEqualTo(expected.getUploadedAt()))
            .satisfies(a -> assertThat(a.getIsDeleted()).as("check isDeleted").isEqualTo(expected.getIsDeleted()));
    }

    /**
     * Asserts that the entity has all the updatable relationships set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertComplaintSuggestionAttachmentUpdatableRelationshipsEquals(
        ComplaintSuggestionAttachment expected,
        ComplaintSuggestionAttachment actual
    ) {
        // empty method
    }
}

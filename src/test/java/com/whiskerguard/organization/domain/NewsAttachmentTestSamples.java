package com.whiskerguard.organization.domain;

import java.util.Random;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

public class NewsAttachmentTestSamples {

    private static final Random random = new Random();
    private static final AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));
    private static final AtomicInteger intCount = new AtomicInteger(random.nextInt() + (2 * Short.MAX_VALUE));

    public static NewsAttachment getNewsAttachmentSample1() {
        return new NewsAttachment()
            .id(1L)
            .name("name1")
            .type("type1")
            .url("url1")
            .fileSize(1L)
            .sortOrder(1)
            .metadata("metadata1")
            .version(1)
            .createdBy("createdBy1")
            .updatedBy("updatedBy1");
    }

    public static NewsAttachment getNewsAttachmentSample2() {
        return new NewsAttachment()
            .id(2L)
            .name("name2")
            .type("type2")
            .url("url2")
            .fileSize(2L)
            .sortOrder(2)
            .metadata("metadata2")
            .version(2)
            .createdBy("createdBy2")
            .updatedBy("updatedBy2");
    }

    public static NewsAttachment getNewsAttachmentRandomSampleGenerator() {
        return new NewsAttachment()
            .id(longCount.incrementAndGet())
            .name(UUID.randomUUID().toString())
            .type(UUID.randomUUID().toString())
            .url(UUID.randomUUID().toString())
            .fileSize(longCount.incrementAndGet())
            .sortOrder(intCount.incrementAndGet())
            .metadata(UUID.randomUUID().toString())
            .version(intCount.incrementAndGet())
            .createdBy(UUID.randomUUID().toString())
            .updatedBy(UUID.randomUUID().toString());
    }
}

package com.whiskerguard.organization.domain;

import java.util.Random;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

public class PermissionTestSamples {

    private static final Random random = new Random();
    private static final AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));
    private static final AtomicInteger intCount = new AtomicInteger(random.nextInt() + (2 * Short.MAX_VALUE));

    public static Permission getPermissionSample1() {
        return new Permission()
            .id(1L)
            .tenantId(1L)
            .serviceName("serviceName1")
            .code("code1")
            .name("name1")
            .urlPattern("urlPattern1")
            .method("method1")
            .frontendRoute("frontendRoute1")
            .backendUrl("backendUrl1")
            .icon("icon1")
            .sortOrder(1)
            .component("component1")
            .redirect("redirect1")
            .description("description1")
            .metadata("metadata1")
            .version(1)
            .createdBy("createdBy1")
            .updatedBy("updatedBy1")
            .isAvailable(true)
            .isShow(true);
    }

    public static Permission getPermissionSample2() {
        return new Permission()
            .id(2L)
            .tenantId(2L)
            .serviceName("serviceName2")
            .code("code2")
            .name("name2")
            .urlPattern("urlPattern2")
            .method("method2")
            .frontendRoute("frontendRoute2")
            .backendUrl("backendUrl2")
            .icon("icon2")
            .sortOrder(2)
            .component("component2")
            .redirect("redirect2")
            .description("description2")
            .metadata("metadata2")
            .version(2)
            .createdBy("createdBy2")
            .updatedBy("updatedBy2")
            .isAvailable(false)
            .isShow(false);
    }

    public static Permission getPermissionRandomSampleGenerator() {
        return new Permission()
            .id(longCount.incrementAndGet())
            .tenantId(longCount.incrementAndGet())
            .serviceName(UUID.randomUUID().toString())
            .code(UUID.randomUUID().toString())
            .name(UUID.randomUUID().toString())
            .urlPattern(UUID.randomUUID().toString())
            .method(UUID.randomUUID().toString())
            .frontendRoute(UUID.randomUUID().toString())
            .backendUrl(UUID.randomUUID().toString())
            .icon(UUID.randomUUID().toString())
            .sortOrder(intCount.incrementAndGet())
            .component(UUID.randomUUID().toString())
            .redirect(UUID.randomUUID().toString())
            .description(UUID.randomUUID().toString())
            .metadata(UUID.randomUUID().toString())
            .version(intCount.incrementAndGet())
            .createdBy(UUID.randomUUID().toString())
            .updatedBy(UUID.randomUUID().toString())
            .isAvailable(random.nextBoolean())
            .isShow(random.nextBoolean());
    }
}

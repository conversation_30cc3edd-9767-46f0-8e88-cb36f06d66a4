package com.whiskerguard.organization.domain;

import static com.whiskerguard.organization.domain.LetterCommitmentTestSamples.*;
import static org.assertj.core.api.Assertions.assertThat;

import com.whiskerguard.organization.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

class LetterCommitmentTest {

    @Test
    void equalsVerifier() throws Exception {
        TestUtil.equalsVerifier(LetterCommitment.class);
        LetterCommitment letterCommitment1 = getLetterCommitmentSample1();
        LetterCommitment letterCommitment2 = new LetterCommitment();
        assertThat(letterCommitment1).isNotEqualTo(letterCommitment2);

        letterCommitment2.setId(letterCommitment1.getId());
        assertThat(letterCommitment1).isEqualTo(letterCommitment2);

        letterCommitment2 = getLetterCommitmentSample2();
        assertThat(letterCommitment1).isNotEqualTo(letterCommitment2);
    }
}

package com.whiskerguard.organization.domain;

import static org.assertj.core.api.Assertions.assertThat;

public class RiskRuleAsserts {

    /**
     * Asserts that the entity has all properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertRiskRuleAllPropertiesEquals(RiskRule expected, RiskRule actual) {
        assertRiskRuleAutoGeneratedPropertiesEquals(expected, actual);
        assertRiskRuleAllUpdatablePropertiesEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all updatable properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertRiskRuleAllUpdatablePropertiesEquals(RiskRule expected, RiskRule actual) {
        assertRiskRuleUpdatableFieldsEquals(expected, actual);
        assertRiskRuleUpdatableRelationshipsEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all the auto generated properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertRiskRuleAutoGeneratedPropertiesEquals(RiskRule expected, RiskRule actual) {
        assertThat(actual)
            .as("Verify RiskRule auto generated properties")
            .satisfies(a -> assertThat(a.getId()).as("check id").isEqualTo(expected.getId()));
    }

    /**
     * Asserts that the entity has all the updatable fields set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertRiskRuleUpdatableFieldsEquals(RiskRule expected, RiskRule actual) {
        assertThat(actual)
            .as("Verify RiskRule relevant properties")
            .satisfies(a -> assertThat(a.getTenantId()).as("check tenantId").isEqualTo(expected.getTenantId()))
            .satisfies(a -> assertThat(a.getCode()).as("check code").isEqualTo(expected.getCode()))
            .satisfies(a -> assertThat(a.getName()).as("check name").isEqualTo(expected.getName()))
            .satisfies(a -> assertThat(a.getRuleType()).as("check ruleType").isEqualTo(expected.getRuleType()))
            .satisfies(a -> assertThat(a.getParams()).as("check params").isEqualTo(expected.getParams()))
            .satisfies(a -> assertThat(a.getDescription()).as("check description").isEqualTo(expected.getDescription()))
            .satisfies(a -> assertThat(a.getScore()).as("check score").isEqualTo(expected.getScore()))
            .satisfies(a -> assertThat(a.getVersion()).as("check version").isEqualTo(expected.getVersion()))
            .satisfies(a -> assertThat(a.getMetadata()).as("check metadata").isEqualTo(expected.getMetadata()))
            .satisfies(a -> assertThat(a.getCreatedBy()).as("check createdBy").isEqualTo(expected.getCreatedBy()))
            .satisfies(a -> assertThat(a.getCreatedAt()).as("check createdAt").isEqualTo(expected.getCreatedAt()))
            .satisfies(a -> assertThat(a.getUpdatedBy()).as("check updatedBy").isEqualTo(expected.getUpdatedBy()))
            .satisfies(a -> assertThat(a.getUpdatedAt()).as("check updatedAt").isEqualTo(expected.getUpdatedAt()))
            .satisfies(a -> assertThat(a.getIsDeleted()).as("check isDeleted").isEqualTo(expected.getIsDeleted()));
    }

    /**
     * Asserts that the entity has all the updatable relationships set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertRiskRuleUpdatableRelationshipsEquals(RiskRule expected, RiskRule actual) {
        assertThat(actual)
            .as("Verify RiskRule relationships")
            .satisfies(a -> assertThat(a.getRiskCategory()).as("check riskCategory").isEqualTo(expected.getRiskCategory()));
    }
}

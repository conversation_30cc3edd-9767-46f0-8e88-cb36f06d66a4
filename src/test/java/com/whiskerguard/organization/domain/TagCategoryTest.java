package com.whiskerguard.organization.domain;

import static com.whiskerguard.organization.domain.TagCategoryTestSamples.*;
import static org.assertj.core.api.Assertions.assertThat;

import com.whiskerguard.organization.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

class TagCategoryTest {

    @Test
    void equalsVerifier() throws Exception {
        TestUtil.equalsVerifier(TagCategory.class);
        TagCategory tagCategory1 = getTagCategorySample1();
        TagCategory tagCategory2 = new TagCategory();
        assertThat(tagCategory1).isNotEqualTo(tagCategory2);

        tagCategory2.setId(tagCategory1.getId());
        assertThat(tagCategory1).isEqualTo(tagCategory2);

        tagCategory2 = getTagCategorySample2();
        assertThat(tagCategory1).isNotEqualTo(tagCategory2);
    }
}

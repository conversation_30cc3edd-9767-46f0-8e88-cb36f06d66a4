package com.whiskerguard.organization.domain;

import java.util.Random;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

public class NewsReportTestSamples {

    private static final Random random = new Random();
    private static final AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));
    private static final AtomicInteger intCount = new AtomicInteger(random.nextInt() + (2 * Short.MAX_VALUE));

    public static NewsReport getNewsReportSample1() {
        return new NewsReport()
            .id(1L)
            .reason("reason1")
            .details("details1")
            .result("result1")
            .metadata("metadata1")
            .version(1)
            .createdBy("createdBy1")
            .updatedBy("updatedBy1");
    }

    public static NewsReport getNewsReportSample2() {
        return new NewsReport()
            .id(2L)
            .reason("reason2")
            .details("details2")
            .result("result2")
            .metadata("metadata2")
            .version(2)
            .createdBy("createdBy2")
            .updatedBy("updatedBy2");
    }

    public static NewsReport getNewsReportRandomSampleGenerator() {
        return new NewsReport()
            .id(longCount.incrementAndGet())
            .reason(UUID.randomUUID().toString())
            .details(UUID.randomUUID().toString())
            .result(UUID.randomUUID().toString())
            .metadata(UUID.randomUUID().toString())
            .version(intCount.incrementAndGet())
            .createdBy(UUID.randomUUID().toString())
            .updatedBy(UUID.randomUUID().toString());
    }
}

package com.whiskerguard.organization.domain;

import static com.whiskerguard.organization.domain.OrgUnitTestSamples.*;
import static com.whiskerguard.organization.domain.OrgUnitTestSamples.*;
import static org.assertj.core.api.Assertions.assertThat;

import com.whiskerguard.organization.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

class OrgUnitTest {

    @Test
    void equalsVerifier() throws Exception {
        TestUtil.equalsVerifier(OrgUnit.class);
        OrgUnit orgUnit1 = getOrgUnitSample1();
        OrgUnit orgUnit2 = new OrgUnit();
        assertThat(orgUnit1).isNotEqualTo(orgUnit2);

        orgUnit2.setId(orgUnit1.getId());
        assertThat(orgUnit1).isEqualTo(orgUnit2);

        orgUnit2 = getOrgUnitSample2();
        assertThat(orgUnit1).isNotEqualTo(orgUnit2);
    }

    @Test
    void parentTest() {
        OrgUnit orgUnit = getOrgUnitRandomSampleGenerator();
        OrgUnit orgUnitBack = getOrgUnitRandomSampleGenerator();

        orgUnit.setParent(orgUnitBack);
        assertThat(orgUnit.getParent()).isEqualTo(orgUnitBack);

        orgUnit.parent(null);
        assertThat(orgUnit.getParent()).isNull();
    }
}

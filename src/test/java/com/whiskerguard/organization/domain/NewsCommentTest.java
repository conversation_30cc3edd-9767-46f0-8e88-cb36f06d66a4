package com.whiskerguard.organization.domain;

import static com.whiskerguard.organization.domain.EmployeeTestSamples.*;
import static com.whiskerguard.organization.domain.NewsCommentTestSamples.*;
import static com.whiskerguard.organization.domain.NewsCommentTestSamples.*;
import static com.whiskerguard.organization.domain.NewsTestSamples.*;
import static org.assertj.core.api.Assertions.assertThat;

import com.whiskerguard.organization.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

class NewsCommentTest {

    @Test
    void equalsVerifier() throws Exception {
        TestUtil.equalsVerifier(NewsComment.class);
        NewsComment newsComment1 = getNewsCommentSample1();
        NewsComment newsComment2 = new NewsComment();
        assertThat(newsComment1).isNotEqualTo(newsComment2);

        newsComment2.setId(newsComment1.getId());
        assertThat(newsComment1).isEqualTo(newsComment2);

        newsComment2 = getNewsCommentSample2();
        assertThat(newsComment1).isNotEqualTo(newsComment2);
    }

    @Test
    void newsTest() {
        NewsComment newsComment = getNewsCommentRandomSampleGenerator();
        News newsBack = getNewsRandomSampleGenerator();

        newsComment.setNews(newsBack);
        assertThat(newsComment.getNews()).isEqualTo(newsBack);

        newsComment.news(null);
        assertThat(newsComment.getNews()).isNull();
    }

    @Test
    void parentTest() {
        NewsComment newsComment = getNewsCommentRandomSampleGenerator();
        NewsComment newsCommentBack = getNewsCommentRandomSampleGenerator();

        newsComment.setParent(newsCommentBack);
        assertThat(newsComment.getParent()).isEqualTo(newsCommentBack);

        newsComment.parent(null);
        assertThat(newsComment.getParent()).isNull();
    }

    @Test
    void commenterTest() {
        NewsComment newsComment = getNewsCommentRandomSampleGenerator();
        Employee employeeBack = getEmployeeRandomSampleGenerator();

        newsComment.setCommenter(employeeBack);
        assertThat(newsComment.getCommenter()).isEqualTo(employeeBack);

        newsComment.commenter(null);
        assertThat(newsComment.getCommenter()).isNull();
    }
}

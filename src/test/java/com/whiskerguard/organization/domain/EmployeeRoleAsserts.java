package com.whiskerguard.organization.domain;

import static org.assertj.core.api.Assertions.assertThat;

public class EmployeeRoleAsserts {

    /**
     * Asserts that the entity has all properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertEmployeeRoleAllPropertiesEquals(EmployeeRole expected, EmployeeRole actual) {
        assertEmployeeRoleAutoGeneratedPropertiesEquals(expected, actual);
        assertEmployeeRoleAllUpdatablePropertiesEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all updatable properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertEmployeeRoleAllUpdatablePropertiesEquals(EmployeeRole expected, EmployeeRole actual) {
        assertEmployeeRoleUpdatableFieldsEquals(expected, actual);
        assertEmployeeRoleUpdatableRelationshipsEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all the auto generated properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertEmployeeRoleAutoGeneratedPropertiesEquals(EmployeeRole expected, EmployeeRole actual) {
        assertThat(actual)
            .as("Verify EmployeeRole auto generated properties")
            .satisfies(a -> assertThat(a.getId()).as("check id").isEqualTo(expected.getId()));
    }

    /**
     * Asserts that the entity has all the updatable fields set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertEmployeeRoleUpdatableFieldsEquals(EmployeeRole expected, EmployeeRole actual) {
        assertThat(actual)
            .as("Verify EmployeeRole relevant properties")
            .satisfies(a -> assertThat(a.getTenantId()).as("check tenantId").isEqualTo(expected.getTenantId()))
            .satisfies(a -> assertThat(a.getAssignedBy()).as("check assignedBy").isEqualTo(expected.getAssignedBy()))
            .satisfies(a -> assertThat(a.getAssignedAt()).as("check assignedAt").isEqualTo(expected.getAssignedAt()))
            .satisfies(a -> assertThat(a.getVersion()).as("check version").isEqualTo(expected.getVersion()))
            .satisfies(a -> assertThat(a.getCreatedBy()).as("check createdBy").isEqualTo(expected.getCreatedBy()))
            .satisfies(a -> assertThat(a.getCreatedAt()).as("check createdAt").isEqualTo(expected.getCreatedAt()))
            .satisfies(a -> assertThat(a.getUpdatedBy()).as("check updatedBy").isEqualTo(expected.getUpdatedBy()))
            .satisfies(a -> assertThat(a.getUpdatedAt()).as("check updatedAt").isEqualTo(expected.getUpdatedAt()))
            .satisfies(a -> assertThat(a.getIsDeleted()).as("check isDeleted").isEqualTo(expected.getIsDeleted()));
    }

    /**
     * Asserts that the entity has all the updatable relationships set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertEmployeeRoleUpdatableRelationshipsEquals(EmployeeRole expected, EmployeeRole actual) {
        assertThat(actual)
            .as("Verify EmployeeRole relationships")
            .satisfies(a -> assertThat(a.getEmployee()).as("check employee").isEqualTo(expected.getEmployee()))
            .satisfies(a -> assertThat(a.getRole()).as("check role").isEqualTo(expected.getRole()))
            .satisfies(a -> assertThat(a.getOrgUnit()).as("check orgUnit").isEqualTo(expected.getOrgUnit()));
    }
}

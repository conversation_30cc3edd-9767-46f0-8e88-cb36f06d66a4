package com.whiskerguard.organization.domain;

import static org.assertj.core.api.Assertions.assertThat;

public class NewsLikeAsserts {

    /**
     * Asserts that the entity has all properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertNewsLikeAllPropertiesEquals(NewsLike expected, NewsLike actual) {
        assertNewsLikeAutoGeneratedPropertiesEquals(expected, actual);
        assertNewsLikeAllUpdatablePropertiesEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all updatable properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertNewsLikeAllUpdatablePropertiesEquals(NewsLike expected, NewsLike actual) {
        assertNewsLikeUpdatableFieldsEquals(expected, actual);
        assertNewsLikeUpdatableRelationshipsEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all the auto generated properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertNewsLikeAutoGeneratedPropertiesEquals(NewsLike expected, NewsLike actual) {
        assertThat(actual)
            .as("Verify NewsLike auto generated properties")
            .satisfies(a -> assertThat(a.getId()).as("check id").isEqualTo(expected.getId()));
    }

    /**
     * Asserts that the entity has all the updatable fields set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertNewsLikeUpdatableFieldsEquals(NewsLike expected, NewsLike actual) {
        assertThat(actual)
            .as("Verify NewsLike relevant properties")
            .satisfies(a -> assertThat(a.getMetadata()).as("check metadata").isEqualTo(expected.getMetadata()))
            .satisfies(a -> assertThat(a.getVersion()).as("check version").isEqualTo(expected.getVersion()))
            .satisfies(a -> assertThat(a.getCreatedBy()).as("check createdBy").isEqualTo(expected.getCreatedBy()))
            .satisfies(a -> assertThat(a.getCreatedAt()).as("check createdAt").isEqualTo(expected.getCreatedAt()))
            .satisfies(a -> assertThat(a.getUpdatedBy()).as("check updatedBy").isEqualTo(expected.getUpdatedBy()))
            .satisfies(a -> assertThat(a.getUpdatedAt()).as("check updatedAt").isEqualTo(expected.getUpdatedAt()))
            .satisfies(a -> assertThat(a.getIsDeleted()).as("check isDeleted").isEqualTo(expected.getIsDeleted()));
    }

    /**
     * Asserts that the entity has all the updatable relationships set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertNewsLikeUpdatableRelationshipsEquals(NewsLike expected, NewsLike actual) {
        assertThat(actual)
            .as("Verify NewsLike relationships")
            .satisfies(a -> assertThat(a.getNews()).as("check news").isEqualTo(expected.getNews()))
            .satisfies(a -> assertThat(a.getUser()).as("check user").isEqualTo(expected.getUser()));
    }
}

package com.whiskerguard.organization.domain;

import java.util.Random;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

public class ComplaintSuggestionTestSamples {

    private static final Random random = new Random();
    private static final AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));
    private static final AtomicInteger intCount = new AtomicInteger(random.nextInt() + (2 * Short.MAX_VALUE));

    public static ComplaintSuggestion getComplaintSuggestionSample1() {
        return new ComplaintSuggestion()
            .id(1L)
            .tenantId(1L)
            .employeeId(1L)
            .detail("detail1")
            .isAnonymous(1)
            .contactWay("contactWay1")
            .version(1)
            .createdBy("createdBy1")
            .updatedBy("updatedBy1");
    }

    public static ComplaintSuggestion getComplaintSuggestionSample2() {
        return new ComplaintSuggestion()
            .id(2L)
            .tenantId(2L)
            .employeeId(2L)
            .detail("detail2")
            .isAnonymous(2)
            .contactWay("contactWay2")
            .version(2)
            .createdBy("createdBy2")
            .updatedBy("updatedBy2");
    }

    public static ComplaintSuggestion getComplaintSuggestionRandomSampleGenerator() {
        return new ComplaintSuggestion()
            .id(longCount.incrementAndGet())
            .tenantId(longCount.incrementAndGet())
            .employeeId(longCount.incrementAndGet())
            .detail(UUID.randomUUID().toString())
            .isAnonymous(intCount.incrementAndGet())
            .contactWay(UUID.randomUUID().toString())
            .version(intCount.incrementAndGet())
            .createdBy(UUID.randomUUID().toString())
            .updatedBy(UUID.randomUUID().toString());
    }
}

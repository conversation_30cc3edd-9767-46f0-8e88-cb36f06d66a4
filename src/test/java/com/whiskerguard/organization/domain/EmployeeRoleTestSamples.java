package com.whiskerguard.organization.domain;

import java.util.Random;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

public class EmployeeRoleTestSamples {

    private static final Random random = new Random();
    private static final AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));
    private static final AtomicInteger intCount = new AtomicInteger(random.nextInt() + (2 * Short.MAX_VALUE));

    public static EmployeeRole getEmployeeRoleSample1() {
        return new EmployeeRole().id(1L).tenantId(1L).assignedBy("assignedBy1").version(1).createdBy("createdBy1").updatedBy("updatedBy1");
    }

    public static EmployeeRole getEmployeeRoleSample2() {
        return new EmployeeRole().id(2L).tenantId(2L).assignedBy("assignedBy2").version(2).createdBy("createdBy2").updatedBy("updatedBy2");
    }

    public static EmployeeRole getEmployeeRoleRandomSampleGenerator() {
        return new EmployeeRole()
            .id(longCount.incrementAndGet())
            .tenantId(longCount.incrementAndGet())
            .assignedBy(UUID.randomUUID().toString())
            .version(intCount.incrementAndGet())
            .createdBy(UUID.randomUUID().toString())
            .updatedBy(UUID.randomUUID().toString());
    }
}

package com.whiskerguard.organization.domain;

import static com.whiskerguard.organization.domain.NewsAttachmentTestSamples.*;
import static com.whiskerguard.organization.domain.NewsTestSamples.*;
import static org.assertj.core.api.Assertions.assertThat;

import com.whiskerguard.organization.web.rest.TestUtil;
import org.junit.jupiter.api.Test;

class NewsAttachmentTest {

    @Test
    void equalsVerifier() throws Exception {
        TestUtil.equalsVerifier(NewsAttachment.class);
        NewsAttachment newsAttachment1 = getNewsAttachmentSample1();
        NewsAttachment newsAttachment2 = new NewsAttachment();
        assertThat(newsAttachment1).isNotEqualTo(newsAttachment2);

        newsAttachment2.setId(newsAttachment1.getId());
        assertThat(newsAttachment1).isEqualTo(newsAttachment2);

        newsAttachment2 = getNewsAttachmentSample2();
        assertThat(newsAttachment1).isNotEqualTo(newsAttachment2);
    }

    @Test
    void newsTest() {
        NewsAttachment newsAttachment = getNewsAttachmentRandomSampleGenerator();
        News newsBack = getNewsRandomSampleGenerator();

        newsAttachment.setNews(newsBack);
        assertThat(newsAttachment.getNews()).isEqualTo(newsBack);

        newsAttachment.news(null);
        assertThat(newsAttachment.getNews()).isNull();
    }
}

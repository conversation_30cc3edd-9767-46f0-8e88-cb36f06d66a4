package com.whiskerguard.organization.domain;

import java.util.Random;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

public class NewsReadRecordTestSamples {

    private static final Random random = new Random();
    private static final AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));
    private static final AtomicInteger intCount = new AtomicInteger(random.nextInt() + (2 * Short.MAX_VALUE));

    public static NewsReadRecord getNewsReadRecordSample1() {
        return new NewsReadRecord()
            .id(1L)
            .source("source1")
            .device("device1")
            .duration(1)
            .metadata("metadata1")
            .version(1)
            .createdBy("createdBy1")
            .updatedBy("updatedBy1");
    }

    public static NewsReadRecord getNewsReadRecordSample2() {
        return new NewsReadRecord()
            .id(2L)
            .source("source2")
            .device("device2")
            .duration(2)
            .metadata("metadata2")
            .version(2)
            .createdBy("createdBy2")
            .updatedBy("updatedBy2");
    }

    public static NewsReadRecord getNewsReadRecordRandomSampleGenerator() {
        return new NewsReadRecord()
            .id(longCount.incrementAndGet())
            .source(UUID.randomUUID().toString())
            .device(UUID.randomUUID().toString())
            .duration(intCount.incrementAndGet())
            .metadata(UUID.randomUUID().toString())
            .version(intCount.incrementAndGet())
            .createdBy(UUID.randomUUID().toString())
            .updatedBy(UUID.randomUUID().toString());
    }
}

package com.whiskerguard.organization.domain;

import java.util.Random;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

public class NewsCommentTestSamples {

    private static final Random random = new Random();
    private static final AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));
    private static final AtomicInteger intCount = new AtomicInteger(random.nextInt() + (2 * Short.MAX_VALUE));

    public static NewsComment getNewsCommentSample1() {
        return new NewsComment()
            .id(1L)
            .sortOrder(1)
            .content("content1")
            .likeCount(1)
            .metadata("metadata1")
            .version(1)
            .createdBy("createdBy1")
            .updatedBy("updatedBy1");
    }

    public static NewsComment getNewsCommentSample2() {
        return new NewsComment()
            .id(2L)
            .sortOrder(2)
            .content("content2")
            .likeCount(2)
            .metadata("metadata2")
            .version(2)
            .createdBy("createdBy2")
            .updatedBy("updatedBy2");
    }

    public static NewsComment getNewsCommentRandomSampleGenerator() {
        return new NewsComment()
            .id(longCount.incrementAndGet())
            .sortOrder(intCount.incrementAndGet())
            .content(UUID.randomUUID().toString())
            .likeCount(intCount.incrementAndGet())
            .metadata(UUID.randomUUID().toString())
            .version(intCount.incrementAndGet())
            .createdBy(UUID.randomUUID().toString())
            .updatedBy(UUID.randomUUID().toString());
    }
}

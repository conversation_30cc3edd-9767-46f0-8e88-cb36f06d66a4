package com.whiskerguard.organization.domain;

import java.util.Random;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

public class RiskCategoryTestSamples {

    private static final Random random = new Random();
    private static final AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));
    private static final AtomicInteger intCount = new AtomicInteger(random.nextInt() + (2 * Short.MAX_VALUE));

    public static RiskCategory getRiskCategorySample1() {
        return new RiskCategory()
            .id(1L)
            .tenantId(1L)
            .name("name1")
            .expression("expression1")
            .description("description1")
            .version(1)
            .metadata("metadata1")
            .createdBy("createdBy1")
            .updatedBy("updatedBy1");
    }

    public static RiskCategory getRiskCategorySample2() {
        return new RiskCategory()
            .id(2L)
            .tenantId(2L)
            .name("name2")
            .expression("expression2")
            .description("description2")
            .version(2)
            .metadata("metadata2")
            .createdBy("createdBy2")
            .updatedBy("updatedBy2");
    }

    public static RiskCategory getRiskCategoryRandomSampleGenerator() {
        return new RiskCategory()
            .id(longCount.incrementAndGet())
            .tenantId(longCount.incrementAndGet())
            .name(UUID.randomUUID().toString())
            .expression(UUID.randomUUID().toString())
            .description(UUID.randomUUID().toString())
            .version(intCount.incrementAndGet())
            .metadata(UUID.randomUUID().toString())
            .createdBy(UUID.randomUUID().toString())
            .updatedBy(UUID.randomUUID().toString());
    }
}

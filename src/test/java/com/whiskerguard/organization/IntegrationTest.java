package com.whiskerguard.organization;

import com.whiskerguard.organization.config.AsyncSyncConfiguration;
import com.whiskerguard.organization.config.EmbeddedRedis;
import com.whiskerguard.organization.config.EmbeddedSQL;
import com.whiskerguard.organization.config.JacksonConfiguration;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * Base composite annotation for integration tests.
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@SpringBootTest(classes = { WhiskerguardOrgServiceApp.class, JacksonConfiguration.class, AsyncSyncConfiguration.class })
@EmbeddedRedis
@EmbeddedSQL
public @interface IntegrationTest {
}

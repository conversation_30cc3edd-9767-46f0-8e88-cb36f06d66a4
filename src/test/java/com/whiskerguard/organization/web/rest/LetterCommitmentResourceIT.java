package com.whiskerguard.organization.web.rest;

import static com.whiskerguard.organization.domain.LetterCommitmentAsserts.*;
import static com.whiskerguard.organization.web.rest.TestUtil.createUpdateProxyForBean;
import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.hasItem;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.whiskerguard.organization.IntegrationTest;
import com.whiskerguard.organization.domain.LetterCommitment;
import com.whiskerguard.organization.repository.LetterCommitmentRepository;
import com.whiskerguard.organization.service.dto.LetterCommitmentDTO;
import com.whiskerguard.organization.service.mapper.LetterCommitmentMapper;
import jakarta.persistence.EntityManager;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Random;
import java.util.concurrent.atomic.AtomicLong;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

/**
 * Integration tests for the {@link LetterCommitmentResource} REST controller.
 */
@IntegrationTest
@AutoConfigureMockMvc
@WithMockUser
class LetterCommitmentResourceIT {

    private static final Long DEFAULT_TENANT_ID = 1L;
    private static final Long UPDATED_TENANT_ID = 2L;

    private static final Long DEFAULT_EMPLOYEE_ID = 1L;
    private static final Long UPDATED_EMPLOYEE_ID = 2L;

    private static final Integer DEFAULT_TYPE = 1;
    private static final Integer UPDATED_TYPE = 2;

    private static final String DEFAULT_FILE_PATH = "AAAAAAAAAA";
    private static final String UPDATED_FILE_PATH = "BBBBBBBBBB";

    private static final Boolean DEFAULT_IS_SIGNED = false;
    private static final Boolean UPDATED_IS_SIGNED = true;

    private static final String DEFAULT_METADATA = "AAAAAAAAAA";
    private static final String UPDATED_METADATA = "BBBBBBBBBB";

    private static final Integer DEFAULT_VERSION = 1;
    private static final Integer UPDATED_VERSION = 2;

    private static final String DEFAULT_CREATED_BY = "AAAAAAAAAA";
    private static final String UPDATED_CREATED_BY = "BBBBBBBBBB";

    private static final Instant DEFAULT_CREATED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_CREATED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final String DEFAULT_UPDATED_BY = "AAAAAAAAAA";
    private static final String UPDATED_UPDATED_BY = "BBBBBBBBBB";

    private static final Instant DEFAULT_UPDATED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_UPDATED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final Boolean DEFAULT_IS_DELETED = false;
    private static final Boolean UPDATED_IS_DELETED = true;

    private static final String ENTITY_API_URL = "/api/letter-commitments";
    private static final String ENTITY_API_URL_ID = ENTITY_API_URL + "/{id}";

    private static Random random = new Random();
    private static AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));

    @Autowired
    private ObjectMapper om;

    @Autowired
    private LetterCommitmentRepository letterCommitmentRepository;

    @Autowired
    private LetterCommitmentMapper letterCommitmentMapper;

    @Autowired
    private EntityManager em;

    @Autowired
    private MockMvc restLetterCommitmentMockMvc;

    private LetterCommitment letterCommitment;

    private LetterCommitment insertedLetterCommitment;

    /**
     * Create an entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static LetterCommitment createEntity() {
        return new LetterCommitment()
            .tenantId(DEFAULT_TENANT_ID)
            .employeeId(DEFAULT_EMPLOYEE_ID)
            .type(DEFAULT_TYPE)
            .filePath(DEFAULT_FILE_PATH)
            .isSigned(DEFAULT_IS_SIGNED)
            .metadata(DEFAULT_METADATA)
            .version(DEFAULT_VERSION)
            .createdBy(DEFAULT_CREATED_BY)
            .createdAt(DEFAULT_CREATED_AT)
            .updatedBy(DEFAULT_UPDATED_BY)
            .updatedAt(DEFAULT_UPDATED_AT)
            .isDeleted(DEFAULT_IS_DELETED);
    }

    /**
     * Create an updated entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static LetterCommitment createUpdatedEntity() {
        return new LetterCommitment()
            .tenantId(UPDATED_TENANT_ID)
            .employeeId(UPDATED_EMPLOYEE_ID)
            .type(UPDATED_TYPE)
            .filePath(UPDATED_FILE_PATH)
            .isSigned(UPDATED_IS_SIGNED)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);
    }

    @BeforeEach
    void initTest() {
        letterCommitment = createEntity();
    }

    @AfterEach
    void cleanup() {
        if (insertedLetterCommitment != null) {
            letterCommitmentRepository.delete(insertedLetterCommitment);
            insertedLetterCommitment = null;
        }
    }

    @Test
    @Transactional
    void createLetterCommitment() throws Exception {
        long databaseSizeBeforeCreate = getRepositoryCount();
        // Create the LetterCommitment
        LetterCommitmentDTO letterCommitmentDTO = letterCommitmentMapper.toDto(letterCommitment);
        var returnedLetterCommitmentDTO = om.readValue(
            restLetterCommitmentMockMvc
                .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(letterCommitmentDTO)))
                .andExpect(status().isCreated())
                .andReturn()
                .getResponse()
                .getContentAsString(),
            LetterCommitmentDTO.class
        );

        // Validate the LetterCommitment in the database
        assertIncrementedRepositoryCount(databaseSizeBeforeCreate);
        var returnedLetterCommitment = letterCommitmentMapper.toEntity(returnedLetterCommitmentDTO);
        assertLetterCommitmentUpdatableFieldsEquals(returnedLetterCommitment, getPersistedLetterCommitment(returnedLetterCommitment));

        insertedLetterCommitment = returnedLetterCommitment;
    }

    @Test
    @Transactional
    void createLetterCommitmentWithExistingId() throws Exception {
        // Create the LetterCommitment with an existing ID
        letterCommitment.setId(1L);
        LetterCommitmentDTO letterCommitmentDTO = letterCommitmentMapper.toDto(letterCommitment);

        long databaseSizeBeforeCreate = getRepositoryCount();

        // An entity with an existing ID cannot be created, so this API call must fail
        restLetterCommitmentMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(letterCommitmentDTO)))
            .andExpect(status().isBadRequest());

        // Validate the LetterCommitment in the database
        assertSameRepositoryCount(databaseSizeBeforeCreate);
    }

    @Test
    @Transactional
    void checkTenantIdIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        letterCommitment.setTenantId(null);

        // Create the LetterCommitment, which fails.
        LetterCommitmentDTO letterCommitmentDTO = letterCommitmentMapper.toDto(letterCommitment);

        restLetterCommitmentMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(letterCommitmentDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkEmployeeIdIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        letterCommitment.setEmployeeId(null);

        // Create the LetterCommitment, which fails.
        LetterCommitmentDTO letterCommitmentDTO = letterCommitmentMapper.toDto(letterCommitment);

        restLetterCommitmentMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(letterCommitmentDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkTypeIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        letterCommitment.setType(null);

        // Create the LetterCommitment, which fails.
        LetterCommitmentDTO letterCommitmentDTO = letterCommitmentMapper.toDto(letterCommitment);

        restLetterCommitmentMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(letterCommitmentDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkFilePathIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        letterCommitment.setFilePath(null);

        // Create the LetterCommitment, which fails.
        LetterCommitmentDTO letterCommitmentDTO = letterCommitmentMapper.toDto(letterCommitment);

        restLetterCommitmentMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(letterCommitmentDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkIsSignedIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        letterCommitment.setIsSigned(null);

        // Create the LetterCommitment, which fails.
        LetterCommitmentDTO letterCommitmentDTO = letterCommitmentMapper.toDto(letterCommitment);

        restLetterCommitmentMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(letterCommitmentDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkVersionIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        letterCommitment.setVersion(null);

        // Create the LetterCommitment, which fails.
        LetterCommitmentDTO letterCommitmentDTO = letterCommitmentMapper.toDto(letterCommitment);

        restLetterCommitmentMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(letterCommitmentDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkCreatedAtIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        letterCommitment.setCreatedAt(null);

        // Create the LetterCommitment, which fails.
        LetterCommitmentDTO letterCommitmentDTO = letterCommitmentMapper.toDto(letterCommitment);

        restLetterCommitmentMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(letterCommitmentDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkUpdatedAtIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        letterCommitment.setUpdatedAt(null);

        // Create the LetterCommitment, which fails.
        LetterCommitmentDTO letterCommitmentDTO = letterCommitmentMapper.toDto(letterCommitment);

        restLetterCommitmentMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(letterCommitmentDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkIsDeletedIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        letterCommitment.setIsDeleted(null);

        // Create the LetterCommitment, which fails.
        LetterCommitmentDTO letterCommitmentDTO = letterCommitmentMapper.toDto(letterCommitment);

        restLetterCommitmentMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(letterCommitmentDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void getAllLetterCommitments() throws Exception {
        // Initialize the database
        insertedLetterCommitment = letterCommitmentRepository.saveAndFlush(letterCommitment);

        // Get all the letterCommitmentList
        restLetterCommitmentMockMvc
            .perform(get(ENTITY_API_URL + "?sort=id,desc"))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.[*].id").value(hasItem(letterCommitment.getId().intValue())))
            .andExpect(jsonPath("$.[*].tenantId").value(hasItem(DEFAULT_TENANT_ID.intValue())))
            .andExpect(jsonPath("$.[*].employeeId").value(hasItem(DEFAULT_EMPLOYEE_ID.intValue())))
            .andExpect(jsonPath("$.[*].type").value(hasItem(DEFAULT_TYPE)))
            .andExpect(jsonPath("$.[*].filePath").value(hasItem(DEFAULT_FILE_PATH)))
            .andExpect(jsonPath("$.[*].isSigned").value(hasItem(DEFAULT_IS_SIGNED)))
            .andExpect(jsonPath("$.[*].metadata").value(hasItem(DEFAULT_METADATA)))
            .andExpect(jsonPath("$.[*].version").value(hasItem(DEFAULT_VERSION)))
            .andExpect(jsonPath("$.[*].createdBy").value(hasItem(DEFAULT_CREATED_BY)))
            .andExpect(jsonPath("$.[*].createdAt").value(hasItem(DEFAULT_CREATED_AT.toString())))
            .andExpect(jsonPath("$.[*].updatedBy").value(hasItem(DEFAULT_UPDATED_BY)))
            .andExpect(jsonPath("$.[*].updatedAt").value(hasItem(DEFAULT_UPDATED_AT.toString())))
            .andExpect(jsonPath("$.[*].isDeleted").value(hasItem(DEFAULT_IS_DELETED)));
    }

    @Test
    @Transactional
    void getLetterCommitment() throws Exception {
        // Initialize the database
        insertedLetterCommitment = letterCommitmentRepository.saveAndFlush(letterCommitment);

        // Get the letterCommitment
        restLetterCommitmentMockMvc
            .perform(get(ENTITY_API_URL_ID, letterCommitment.getId()))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.id").value(letterCommitment.getId().intValue()))
            .andExpect(jsonPath("$.tenantId").value(DEFAULT_TENANT_ID.intValue()))
            .andExpect(jsonPath("$.employeeId").value(DEFAULT_EMPLOYEE_ID.intValue()))
            .andExpect(jsonPath("$.type").value(DEFAULT_TYPE))
            .andExpect(jsonPath("$.filePath").value(DEFAULT_FILE_PATH))
            .andExpect(jsonPath("$.isSigned").value(DEFAULT_IS_SIGNED))
            .andExpect(jsonPath("$.metadata").value(DEFAULT_METADATA))
            .andExpect(jsonPath("$.version").value(DEFAULT_VERSION))
            .andExpect(jsonPath("$.createdBy").value(DEFAULT_CREATED_BY))
            .andExpect(jsonPath("$.createdAt").value(DEFAULT_CREATED_AT.toString()))
            .andExpect(jsonPath("$.updatedBy").value(DEFAULT_UPDATED_BY))
            .andExpect(jsonPath("$.updatedAt").value(DEFAULT_UPDATED_AT.toString()))
            .andExpect(jsonPath("$.isDeleted").value(DEFAULT_IS_DELETED));
    }

    @Test
    @Transactional
    void getNonExistingLetterCommitment() throws Exception {
        // Get the letterCommitment
        restLetterCommitmentMockMvc.perform(get(ENTITY_API_URL_ID, Long.MAX_VALUE)).andExpect(status().isNotFound());
    }

    @Test
    @Transactional
    void putExistingLetterCommitment() throws Exception {
        // Initialize the database
        insertedLetterCommitment = letterCommitmentRepository.saveAndFlush(letterCommitment);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the letterCommitment
        LetterCommitment updatedLetterCommitment = letterCommitmentRepository.findById(letterCommitment.getId()).orElseThrow();
        // Disconnect from session so that the updates on updatedLetterCommitment are not directly saved in db
        em.detach(updatedLetterCommitment);
        updatedLetterCommitment
            .tenantId(UPDATED_TENANT_ID)
            .employeeId(UPDATED_EMPLOYEE_ID)
            .type(UPDATED_TYPE)
            .filePath(UPDATED_FILE_PATH)
            .isSigned(UPDATED_IS_SIGNED)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);
        LetterCommitmentDTO letterCommitmentDTO = letterCommitmentMapper.toDto(updatedLetterCommitment);

        restLetterCommitmentMockMvc
            .perform(
                put(ENTITY_API_URL_ID, letterCommitmentDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(letterCommitmentDTO))
            )
            .andExpect(status().isOk());

        // Validate the LetterCommitment in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertPersistedLetterCommitmentToMatchAllProperties(updatedLetterCommitment);
    }

    @Test
    @Transactional
    void putNonExistingLetterCommitment() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        letterCommitment.setId(longCount.incrementAndGet());

        // Create the LetterCommitment
        LetterCommitmentDTO letterCommitmentDTO = letterCommitmentMapper.toDto(letterCommitment);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restLetterCommitmentMockMvc
            .perform(
                put(ENTITY_API_URL_ID, letterCommitmentDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(letterCommitmentDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the LetterCommitment in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithIdMismatchLetterCommitment() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        letterCommitment.setId(longCount.incrementAndGet());

        // Create the LetterCommitment
        LetterCommitmentDTO letterCommitmentDTO = letterCommitmentMapper.toDto(letterCommitment);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restLetterCommitmentMockMvc
            .perform(
                put(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(letterCommitmentDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the LetterCommitment in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithMissingIdPathParamLetterCommitment() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        letterCommitment.setId(longCount.incrementAndGet());

        // Create the LetterCommitment
        LetterCommitmentDTO letterCommitmentDTO = letterCommitmentMapper.toDto(letterCommitment);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restLetterCommitmentMockMvc
            .perform(put(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(letterCommitmentDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the LetterCommitment in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void partialUpdateLetterCommitmentWithPatch() throws Exception {
        // Initialize the database
        insertedLetterCommitment = letterCommitmentRepository.saveAndFlush(letterCommitment);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the letterCommitment using partial update
        LetterCommitment partialUpdatedLetterCommitment = new LetterCommitment();
        partialUpdatedLetterCommitment.setId(letterCommitment.getId());

        partialUpdatedLetterCommitment
            .tenantId(UPDATED_TENANT_ID)
            .employeeId(UPDATED_EMPLOYEE_ID)
            .type(UPDATED_TYPE)
            .filePath(UPDATED_FILE_PATH)
            .isDeleted(UPDATED_IS_DELETED);

        restLetterCommitmentMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedLetterCommitment.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedLetterCommitment))
            )
            .andExpect(status().isOk());

        // Validate the LetterCommitment in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertLetterCommitmentUpdatableFieldsEquals(
            createUpdateProxyForBean(partialUpdatedLetterCommitment, letterCommitment),
            getPersistedLetterCommitment(letterCommitment)
        );
    }

    @Test
    @Transactional
    void fullUpdateLetterCommitmentWithPatch() throws Exception {
        // Initialize the database
        insertedLetterCommitment = letterCommitmentRepository.saveAndFlush(letterCommitment);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the letterCommitment using partial update
        LetterCommitment partialUpdatedLetterCommitment = new LetterCommitment();
        partialUpdatedLetterCommitment.setId(letterCommitment.getId());

        partialUpdatedLetterCommitment
            .tenantId(UPDATED_TENANT_ID)
            .employeeId(UPDATED_EMPLOYEE_ID)
            .type(UPDATED_TYPE)
            .filePath(UPDATED_FILE_PATH)
            .isSigned(UPDATED_IS_SIGNED)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);

        restLetterCommitmentMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedLetterCommitment.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedLetterCommitment))
            )
            .andExpect(status().isOk());

        // Validate the LetterCommitment in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertLetterCommitmentUpdatableFieldsEquals(
            partialUpdatedLetterCommitment,
            getPersistedLetterCommitment(partialUpdatedLetterCommitment)
        );
    }

    @Test
    @Transactional
    void patchNonExistingLetterCommitment() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        letterCommitment.setId(longCount.incrementAndGet());

        // Create the LetterCommitment
        LetterCommitmentDTO letterCommitmentDTO = letterCommitmentMapper.toDto(letterCommitment);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restLetterCommitmentMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, letterCommitmentDTO.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(letterCommitmentDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the LetterCommitment in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithIdMismatchLetterCommitment() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        letterCommitment.setId(longCount.incrementAndGet());

        // Create the LetterCommitment
        LetterCommitmentDTO letterCommitmentDTO = letterCommitmentMapper.toDto(letterCommitment);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restLetterCommitmentMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(letterCommitmentDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the LetterCommitment in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithMissingIdPathParamLetterCommitment() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        letterCommitment.setId(longCount.incrementAndGet());

        // Create the LetterCommitment
        LetterCommitmentDTO letterCommitmentDTO = letterCommitmentMapper.toDto(letterCommitment);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restLetterCommitmentMockMvc
            .perform(patch(ENTITY_API_URL).contentType("application/merge-patch+json").content(om.writeValueAsBytes(letterCommitmentDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the LetterCommitment in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void deleteLetterCommitment() throws Exception {
        // Initialize the database
        insertedLetterCommitment = letterCommitmentRepository.saveAndFlush(letterCommitment);

        long databaseSizeBeforeDelete = getRepositoryCount();

        // Delete the letterCommitment
        restLetterCommitmentMockMvc
            .perform(delete(ENTITY_API_URL_ID, letterCommitment.getId()).accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isNoContent());

        // Validate the database contains one less item
        assertDecrementedRepositoryCount(databaseSizeBeforeDelete);
    }

    protected long getRepositoryCount() {
        return letterCommitmentRepository.count();
    }

    protected void assertIncrementedRepositoryCount(long countBefore) {
        assertThat(countBefore + 1).isEqualTo(getRepositoryCount());
    }

    protected void assertDecrementedRepositoryCount(long countBefore) {
        assertThat(countBefore - 1).isEqualTo(getRepositoryCount());
    }

    protected void assertSameRepositoryCount(long countBefore) {
        assertThat(countBefore).isEqualTo(getRepositoryCount());
    }

    protected LetterCommitment getPersistedLetterCommitment(LetterCommitment letterCommitment) {
        return letterCommitmentRepository.findById(letterCommitment.getId()).orElseThrow();
    }

    protected void assertPersistedLetterCommitmentToMatchAllProperties(LetterCommitment expectedLetterCommitment) {
        assertLetterCommitmentAllPropertiesEquals(expectedLetterCommitment, getPersistedLetterCommitment(expectedLetterCommitment));
    }

    protected void assertPersistedLetterCommitmentToMatchUpdatableProperties(LetterCommitment expectedLetterCommitment) {
        assertLetterCommitmentAllUpdatablePropertiesEquals(
            expectedLetterCommitment,
            getPersistedLetterCommitment(expectedLetterCommitment)
        );
    }
}

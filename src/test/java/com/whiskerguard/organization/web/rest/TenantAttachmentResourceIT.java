package com.whiskerguard.organization.web.rest;

import static com.whiskerguard.organization.domain.TenantAttachmentAsserts.*;
import static com.whiskerguard.organization.web.rest.TestUtil.createUpdateProxyForBean;
import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.hasItem;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.whiskerguard.organization.IntegrationTest;
import com.whiskerguard.organization.domain.TenantAttachment;
import com.whiskerguard.organization.repository.TenantAttachmentRepository;
import com.whiskerguard.organization.service.dto.TenantAttachmentDTO;
import com.whiskerguard.organization.service.mapper.TenantAttachmentMapper;
import jakarta.persistence.EntityManager;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Random;
import java.util.concurrent.atomic.AtomicLong;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

/**
 * Integration tests for the {@link TenantAttachmentResource} REST controller.
 */
@IntegrationTest
@AutoConfigureMockMvc
@WithMockUser
class TenantAttachmentResourceIT {

    private static final String DEFAULT_TYPE = "AAAAAAAAAA";
    private static final String UPDATED_TYPE = "BBBBBBBBBB";

    private static final String DEFAULT_FILE_URL = "AAAAAAAAAA";
    private static final String UPDATED_FILE_URL = "BBBBBBBBBB";

    private static final String DEFAULT_FILE_NAME = "AAAAAAAAAA";
    private static final String UPDATED_FILE_NAME = "BBBBBBBBBB";

    private static final Long DEFAULT_FILE_SIZE = 1L;
    private static final Long UPDATED_FILE_SIZE = 2L;

    private static final String DEFAULT_DESCRIPTION = "AAAAAAAAAA";
    private static final String UPDATED_DESCRIPTION = "BBBBBBBBBB";

    private static final String DEFAULT_METADATA = "AAAAAAAAAA";
    private static final String UPDATED_METADATA = "BBBBBBBBBB";

    private static final Integer DEFAULT_VERSION = 1;
    private static final Integer UPDATED_VERSION = 2;

    private static final String DEFAULT_UPLOADED_BY = "AAAAAAAAAA";
    private static final String UPDATED_UPLOADED_BY = "BBBBBBBBBB";

    private static final Instant DEFAULT_UPLOADED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_UPLOADED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final String DEFAULT_UPDATED_BY = "AAAAAAAAAA";
    private static final String UPDATED_UPDATED_BY = "BBBBBBBBBB";

    private static final Instant DEFAULT_UPDATED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_UPDATED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final Boolean DEFAULT_IS_DELETED = false;
    private static final Boolean UPDATED_IS_DELETED = true;

    private static final String ENTITY_API_URL = "/api/tenant-attachments";
    private static final String ENTITY_API_URL_ID = ENTITY_API_URL + "/{id}";

    private static Random random = new Random();
    private static AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));

    @Autowired
    private ObjectMapper om;

    @Autowired
    private TenantAttachmentRepository tenantAttachmentRepository;

    @Autowired
    private TenantAttachmentMapper tenantAttachmentMapper;

    @Autowired
    private EntityManager em;

    @Autowired
    private MockMvc restTenantAttachmentMockMvc;

    private TenantAttachment tenantAttachment;

    private TenantAttachment insertedTenantAttachment;

    /**
     * Create an entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static TenantAttachment createEntity() {
        return new TenantAttachment()
            .type(DEFAULT_TYPE)
            .fileUrl(DEFAULT_FILE_URL)
            .fileName(DEFAULT_FILE_NAME)
            .fileSize(DEFAULT_FILE_SIZE)
            .description(DEFAULT_DESCRIPTION)
            .metadata(DEFAULT_METADATA)
            .version(DEFAULT_VERSION)
            .uploadedBy(DEFAULT_UPLOADED_BY)
            .uploadedAt(DEFAULT_UPLOADED_AT)
            .updatedBy(DEFAULT_UPDATED_BY)
            .updatedAt(DEFAULT_UPDATED_AT)
            .isDeleted(DEFAULT_IS_DELETED);
    }

    /**
     * Create an updated entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static TenantAttachment createUpdatedEntity() {
        return new TenantAttachment()
            .type(UPDATED_TYPE)
            .fileUrl(UPDATED_FILE_URL)
            .fileName(UPDATED_FILE_NAME)
            .fileSize(UPDATED_FILE_SIZE)
            .description(UPDATED_DESCRIPTION)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .uploadedBy(UPDATED_UPLOADED_BY)
            .uploadedAt(UPDATED_UPLOADED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);
    }

    @BeforeEach
    void initTest() {
        tenantAttachment = createEntity();
    }

    @AfterEach
    void cleanup() {
        if (insertedTenantAttachment != null) {
            tenantAttachmentRepository.delete(insertedTenantAttachment);
            insertedTenantAttachment = null;
        }
    }

    @Test
    @Transactional
    void createTenantAttachment() throws Exception {
        long databaseSizeBeforeCreate = getRepositoryCount();
        // Create the TenantAttachment
        TenantAttachmentDTO tenantAttachmentDTO = tenantAttachmentMapper.toDto(tenantAttachment);
        var returnedTenantAttachmentDTO = om.readValue(
            restTenantAttachmentMockMvc
                .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(tenantAttachmentDTO)))
                .andExpect(status().isCreated())
                .andReturn()
                .getResponse()
                .getContentAsString(),
            TenantAttachmentDTO.class
        );

        // Validate the TenantAttachment in the database
        assertIncrementedRepositoryCount(databaseSizeBeforeCreate);
        var returnedTenantAttachment = tenantAttachmentMapper.toEntity(returnedTenantAttachmentDTO);
        assertTenantAttachmentUpdatableFieldsEquals(returnedTenantAttachment, getPersistedTenantAttachment(returnedTenantAttachment));

        insertedTenantAttachment = returnedTenantAttachment;
    }

    @Test
    @Transactional
    void createTenantAttachmentWithExistingId() throws Exception {
        // Create the TenantAttachment with an existing ID
        tenantAttachment.setId(1L);
        TenantAttachmentDTO tenantAttachmentDTO = tenantAttachmentMapper.toDto(tenantAttachment);

        long databaseSizeBeforeCreate = getRepositoryCount();

        // An entity with an existing ID cannot be created, so this API call must fail
        restTenantAttachmentMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(tenantAttachmentDTO)))
            .andExpect(status().isBadRequest());

        // Validate the TenantAttachment in the database
        assertSameRepositoryCount(databaseSizeBeforeCreate);
    }

    @Test
    @Transactional
    void checkVersionIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        tenantAttachment.setVersion(null);

        // Create the TenantAttachment, which fails.
        TenantAttachmentDTO tenantAttachmentDTO = tenantAttachmentMapper.toDto(tenantAttachment);

        restTenantAttachmentMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(tenantAttachmentDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkUploadedAtIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        tenantAttachment.setUploadedAt(null);

        // Create the TenantAttachment, which fails.
        TenantAttachmentDTO tenantAttachmentDTO = tenantAttachmentMapper.toDto(tenantAttachment);

        restTenantAttachmentMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(tenantAttachmentDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkUpdatedAtIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        tenantAttachment.setUpdatedAt(null);

        // Create the TenantAttachment, which fails.
        TenantAttachmentDTO tenantAttachmentDTO = tenantAttachmentMapper.toDto(tenantAttachment);

        restTenantAttachmentMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(tenantAttachmentDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkIsDeletedIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        tenantAttachment.setIsDeleted(null);

        // Create the TenantAttachment, which fails.
        TenantAttachmentDTO tenantAttachmentDTO = tenantAttachmentMapper.toDto(tenantAttachment);

        restTenantAttachmentMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(tenantAttachmentDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void getAllTenantAttachments() throws Exception {
        // Initialize the database
        insertedTenantAttachment = tenantAttachmentRepository.saveAndFlush(tenantAttachment);

        // Get all the tenantAttachmentList
        restTenantAttachmentMockMvc
            .perform(get(ENTITY_API_URL + "?sort=id,desc"))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.[*].id").value(hasItem(tenantAttachment.getId().intValue())))
            .andExpect(jsonPath("$.[*].type").value(hasItem(DEFAULT_TYPE)))
            .andExpect(jsonPath("$.[*].fileUrl").value(hasItem(DEFAULT_FILE_URL)))
            .andExpect(jsonPath("$.[*].fileName").value(hasItem(DEFAULT_FILE_NAME)))
            .andExpect(jsonPath("$.[*].fileSize").value(hasItem(DEFAULT_FILE_SIZE.intValue())))
            .andExpect(jsonPath("$.[*].description").value(hasItem(DEFAULT_DESCRIPTION)))
            .andExpect(jsonPath("$.[*].metadata").value(hasItem(DEFAULT_METADATA)))
            .andExpect(jsonPath("$.[*].version").value(hasItem(DEFAULT_VERSION)))
            .andExpect(jsonPath("$.[*].uploadedBy").value(hasItem(DEFAULT_UPLOADED_BY)))
            .andExpect(jsonPath("$.[*].uploadedAt").value(hasItem(DEFAULT_UPLOADED_AT.toString())))
            .andExpect(jsonPath("$.[*].updatedBy").value(hasItem(DEFAULT_UPDATED_BY)))
            .andExpect(jsonPath("$.[*].updatedAt").value(hasItem(DEFAULT_UPDATED_AT.toString())))
            .andExpect(jsonPath("$.[*].isDeleted").value(hasItem(DEFAULT_IS_DELETED)));
    }

    @Test
    @Transactional
    void getTenantAttachment() throws Exception {
        // Initialize the database
        insertedTenantAttachment = tenantAttachmentRepository.saveAndFlush(tenantAttachment);

        // Get the tenantAttachment
        restTenantAttachmentMockMvc
            .perform(get(ENTITY_API_URL_ID, tenantAttachment.getId()))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.id").value(tenantAttachment.getId().intValue()))
            .andExpect(jsonPath("$.type").value(DEFAULT_TYPE))
            .andExpect(jsonPath("$.fileUrl").value(DEFAULT_FILE_URL))
            .andExpect(jsonPath("$.fileName").value(DEFAULT_FILE_NAME))
            .andExpect(jsonPath("$.fileSize").value(DEFAULT_FILE_SIZE.intValue()))
            .andExpect(jsonPath("$.description").value(DEFAULT_DESCRIPTION))
            .andExpect(jsonPath("$.metadata").value(DEFAULT_METADATA))
            .andExpect(jsonPath("$.version").value(DEFAULT_VERSION))
            .andExpect(jsonPath("$.uploadedBy").value(DEFAULT_UPLOADED_BY))
            .andExpect(jsonPath("$.uploadedAt").value(DEFAULT_UPLOADED_AT.toString()))
            .andExpect(jsonPath("$.updatedBy").value(DEFAULT_UPDATED_BY))
            .andExpect(jsonPath("$.updatedAt").value(DEFAULT_UPDATED_AT.toString()))
            .andExpect(jsonPath("$.isDeleted").value(DEFAULT_IS_DELETED));
    }

    @Test
    @Transactional
    void getNonExistingTenantAttachment() throws Exception {
        // Get the tenantAttachment
        restTenantAttachmentMockMvc.perform(get(ENTITY_API_URL_ID, Long.MAX_VALUE)).andExpect(status().isNotFound());
    }

    @Test
    @Transactional
    void putExistingTenantAttachment() throws Exception {
        // Initialize the database
        insertedTenantAttachment = tenantAttachmentRepository.saveAndFlush(tenantAttachment);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the tenantAttachment
        TenantAttachment updatedTenantAttachment = tenantAttachmentRepository.findById(tenantAttachment.getId()).orElseThrow();
        // Disconnect from session so that the updates on updatedTenantAttachment are not directly saved in db
        em.detach(updatedTenantAttachment);
        updatedTenantAttachment
            .type(UPDATED_TYPE)
            .fileUrl(UPDATED_FILE_URL)
            .fileName(UPDATED_FILE_NAME)
            .fileSize(UPDATED_FILE_SIZE)
            .description(UPDATED_DESCRIPTION)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .uploadedBy(UPDATED_UPLOADED_BY)
            .uploadedAt(UPDATED_UPLOADED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);
        TenantAttachmentDTO tenantAttachmentDTO = tenantAttachmentMapper.toDto(updatedTenantAttachment);

        restTenantAttachmentMockMvc
            .perform(
                put(ENTITY_API_URL_ID, tenantAttachmentDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(tenantAttachmentDTO))
            )
            .andExpect(status().isOk());

        // Validate the TenantAttachment in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertPersistedTenantAttachmentToMatchAllProperties(updatedTenantAttachment);
    }

    @Test
    @Transactional
    void putNonExistingTenantAttachment() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        tenantAttachment.setId(longCount.incrementAndGet());

        // Create the TenantAttachment
        TenantAttachmentDTO tenantAttachmentDTO = tenantAttachmentMapper.toDto(tenantAttachment);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restTenantAttachmentMockMvc
            .perform(
                put(ENTITY_API_URL_ID, tenantAttachmentDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(tenantAttachmentDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the TenantAttachment in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithIdMismatchTenantAttachment() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        tenantAttachment.setId(longCount.incrementAndGet());

        // Create the TenantAttachment
        TenantAttachmentDTO tenantAttachmentDTO = tenantAttachmentMapper.toDto(tenantAttachment);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restTenantAttachmentMockMvc
            .perform(
                put(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(tenantAttachmentDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the TenantAttachment in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithMissingIdPathParamTenantAttachment() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        tenantAttachment.setId(longCount.incrementAndGet());

        // Create the TenantAttachment
        TenantAttachmentDTO tenantAttachmentDTO = tenantAttachmentMapper.toDto(tenantAttachment);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restTenantAttachmentMockMvc
            .perform(put(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(tenantAttachmentDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the TenantAttachment in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void partialUpdateTenantAttachmentWithPatch() throws Exception {
        // Initialize the database
        insertedTenantAttachment = tenantAttachmentRepository.saveAndFlush(tenantAttachment);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the tenantAttachment using partial update
        TenantAttachment partialUpdatedTenantAttachment = new TenantAttachment();
        partialUpdatedTenantAttachment.setId(tenantAttachment.getId());

        partialUpdatedTenantAttachment
            .fileUrl(UPDATED_FILE_URL)
            .fileName(UPDATED_FILE_NAME)
            .fileSize(UPDATED_FILE_SIZE)
            .description(UPDATED_DESCRIPTION)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .uploadedAt(UPDATED_UPLOADED_AT);

        restTenantAttachmentMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedTenantAttachment.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedTenantAttachment))
            )
            .andExpect(status().isOk());

        // Validate the TenantAttachment in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertTenantAttachmentUpdatableFieldsEquals(
            createUpdateProxyForBean(partialUpdatedTenantAttachment, tenantAttachment),
            getPersistedTenantAttachment(tenantAttachment)
        );
    }

    @Test
    @Transactional
    void fullUpdateTenantAttachmentWithPatch() throws Exception {
        // Initialize the database
        insertedTenantAttachment = tenantAttachmentRepository.saveAndFlush(tenantAttachment);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the tenantAttachment using partial update
        TenantAttachment partialUpdatedTenantAttachment = new TenantAttachment();
        partialUpdatedTenantAttachment.setId(tenantAttachment.getId());

        partialUpdatedTenantAttachment
            .type(UPDATED_TYPE)
            .fileUrl(UPDATED_FILE_URL)
            .fileName(UPDATED_FILE_NAME)
            .fileSize(UPDATED_FILE_SIZE)
            .description(UPDATED_DESCRIPTION)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .uploadedBy(UPDATED_UPLOADED_BY)
            .uploadedAt(UPDATED_UPLOADED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);

        restTenantAttachmentMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedTenantAttachment.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedTenantAttachment))
            )
            .andExpect(status().isOk());

        // Validate the TenantAttachment in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertTenantAttachmentUpdatableFieldsEquals(
            partialUpdatedTenantAttachment,
            getPersistedTenantAttachment(partialUpdatedTenantAttachment)
        );
    }

    @Test
    @Transactional
    void patchNonExistingTenantAttachment() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        tenantAttachment.setId(longCount.incrementAndGet());

        // Create the TenantAttachment
        TenantAttachmentDTO tenantAttachmentDTO = tenantAttachmentMapper.toDto(tenantAttachment);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restTenantAttachmentMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, tenantAttachmentDTO.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(tenantAttachmentDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the TenantAttachment in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithIdMismatchTenantAttachment() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        tenantAttachment.setId(longCount.incrementAndGet());

        // Create the TenantAttachment
        TenantAttachmentDTO tenantAttachmentDTO = tenantAttachmentMapper.toDto(tenantAttachment);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restTenantAttachmentMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(tenantAttachmentDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the TenantAttachment in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithMissingIdPathParamTenantAttachment() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        tenantAttachment.setId(longCount.incrementAndGet());

        // Create the TenantAttachment
        TenantAttachmentDTO tenantAttachmentDTO = tenantAttachmentMapper.toDto(tenantAttachment);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restTenantAttachmentMockMvc
            .perform(patch(ENTITY_API_URL).contentType("application/merge-patch+json").content(om.writeValueAsBytes(tenantAttachmentDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the TenantAttachment in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void deleteTenantAttachment() throws Exception {
        // Initialize the database
        insertedTenantAttachment = tenantAttachmentRepository.saveAndFlush(tenantAttachment);

        long databaseSizeBeforeDelete = getRepositoryCount();

        // Delete the tenantAttachment
        restTenantAttachmentMockMvc
            .perform(delete(ENTITY_API_URL_ID, tenantAttachment.getId()).accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isNoContent());

        // Validate the database contains one less item
        assertDecrementedRepositoryCount(databaseSizeBeforeDelete);
    }

    protected long getRepositoryCount() {
        return tenantAttachmentRepository.count();
    }

    protected void assertIncrementedRepositoryCount(long countBefore) {
        assertThat(countBefore + 1).isEqualTo(getRepositoryCount());
    }

    protected void assertDecrementedRepositoryCount(long countBefore) {
        assertThat(countBefore - 1).isEqualTo(getRepositoryCount());
    }

    protected void assertSameRepositoryCount(long countBefore) {
        assertThat(countBefore).isEqualTo(getRepositoryCount());
    }

    protected TenantAttachment getPersistedTenantAttachment(TenantAttachment tenantAttachment) {
        return tenantAttachmentRepository.findById(tenantAttachment.getId()).orElseThrow();
    }

    protected void assertPersistedTenantAttachmentToMatchAllProperties(TenantAttachment expectedTenantAttachment) {
        assertTenantAttachmentAllPropertiesEquals(expectedTenantAttachment, getPersistedTenantAttachment(expectedTenantAttachment));
    }

    protected void assertPersistedTenantAttachmentToMatchUpdatableProperties(TenantAttachment expectedTenantAttachment) {
        assertTenantAttachmentAllUpdatablePropertiesEquals(
            expectedTenantAttachment,
            getPersistedTenantAttachment(expectedTenantAttachment)
        );
    }
}

package com.whiskerguard.organization.web.rest;

import static com.whiskerguard.organization.domain.ComplaintSuggestionAsserts.*;
import static com.whiskerguard.organization.web.rest.TestUtil.createUpdateProxyForBean;
import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.hasItem;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.whiskerguard.organization.IntegrationTest;
import com.whiskerguard.organization.domain.ComplaintSuggestion;
import com.whiskerguard.organization.repository.ComplaintSuggestionRepository;
import com.whiskerguard.organization.service.dto.ComplaintSuggestionDTO;
import com.whiskerguard.organization.service.mapper.ComplaintSuggestionMapper;
import jakarta.persistence.EntityManager;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Random;
import java.util.concurrent.atomic.AtomicLong;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

/**
 * Integration tests for the {@link ComplaintSuggestionResource} REST controller.
 */
@IntegrationTest
@AutoConfigureMockMvc
@WithMockUser
class ComplaintSuggestionResourceIT {

    private static final Long DEFAULT_TENANT_ID = 1L;
    private static final Long UPDATED_TENANT_ID = 2L;

    private static final Long DEFAULT_EMPLOYEE_ID = 1L;
    private static final Long UPDATED_EMPLOYEE_ID = 2L;

    private static final String DEFAULT_DETAIL = "AAAAAAAAAA";
    private static final String UPDATED_DETAIL = "BBBBBBBBBB";

    private static final Integer DEFAULT_IS_ANONYMOUS = 1;
    private static final Integer UPDATED_IS_ANONYMOUS = 2;

    private static final String DEFAULT_CONTACT_WAY = "AAAAAAAAAA";
    private static final String UPDATED_CONTACT_WAY = "BBBBBBBBBB";

    private static final String DEFAULT_METADATA = "AAAAAAAAAA";
    private static final String UPDATED_METADATA = "BBBBBBBBBB";

    private static final Integer DEFAULT_VERSION = 1;
    private static final Integer UPDATED_VERSION = 2;

    private static final String DEFAULT_CREATED_BY = "AAAAAAAAAA";
    private static final String UPDATED_CREATED_BY = "BBBBBBBBBB";

    private static final Instant DEFAULT_CREATED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_CREATED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final String DEFAULT_UPDATED_BY = "AAAAAAAAAA";
    private static final String UPDATED_UPDATED_BY = "BBBBBBBBBB";

    private static final Instant DEFAULT_UPDATED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_UPDATED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final Boolean DEFAULT_IS_DELETED = false;
    private static final Boolean UPDATED_IS_DELETED = true;

    private static final String ENTITY_API_URL = "/api/complaint-suggestions";
    private static final String ENTITY_API_URL_ID = ENTITY_API_URL + "/{id}";

    private static Random random = new Random();
    private static AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));

    @Autowired
    private ObjectMapper om;

    @Autowired
    private ComplaintSuggestionRepository complaintSuggestionRepository;

    @Autowired
    private ComplaintSuggestionMapper complaintSuggestionMapper;

    @Autowired
    private EntityManager em;

    @Autowired
    private MockMvc restComplaintSuggestionMockMvc;

    private ComplaintSuggestion complaintSuggestion;

    private ComplaintSuggestion insertedComplaintSuggestion;

    /**
     * Create an entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static ComplaintSuggestion createEntity() {
        return new ComplaintSuggestion()
            .tenantId(DEFAULT_TENANT_ID)
            .employeeId(DEFAULT_EMPLOYEE_ID)
            .detail(DEFAULT_DETAIL)
            .isAnonymous(DEFAULT_IS_ANONYMOUS)
            .contactWay(DEFAULT_CONTACT_WAY)
            .metadata(DEFAULT_METADATA)
            .version(DEFAULT_VERSION)
            .createdBy(DEFAULT_CREATED_BY)
            .createdAt(DEFAULT_CREATED_AT)
            .updatedBy(DEFAULT_UPDATED_BY)
            .updatedAt(DEFAULT_UPDATED_AT)
            .isDeleted(DEFAULT_IS_DELETED);
    }

    /**
     * Create an updated entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static ComplaintSuggestion createUpdatedEntity() {
        return new ComplaintSuggestion()
            .tenantId(UPDATED_TENANT_ID)
            .employeeId(UPDATED_EMPLOYEE_ID)
            .detail(UPDATED_DETAIL)
            .isAnonymous(UPDATED_IS_ANONYMOUS)
            .contactWay(UPDATED_CONTACT_WAY)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);
    }

    @BeforeEach
    void initTest() {
        complaintSuggestion = createEntity();
    }

    @AfterEach
    void cleanup() {
        if (insertedComplaintSuggestion != null) {
            complaintSuggestionRepository.delete(insertedComplaintSuggestion);
            insertedComplaintSuggestion = null;
        }
    }

    @Test
    @Transactional
    void createComplaintSuggestion() throws Exception {
        long databaseSizeBeforeCreate = getRepositoryCount();
        // Create the ComplaintSuggestion
        ComplaintSuggestionDTO complaintSuggestionDTO = complaintSuggestionMapper.toDto(complaintSuggestion);
        var returnedComplaintSuggestionDTO = om.readValue(
            restComplaintSuggestionMockMvc
                .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(complaintSuggestionDTO)))
                .andExpect(status().isCreated())
                .andReturn()
                .getResponse()
                .getContentAsString(),
            ComplaintSuggestionDTO.class
        );

        // Validate the ComplaintSuggestion in the database
        assertIncrementedRepositoryCount(databaseSizeBeforeCreate);
        var returnedComplaintSuggestion = complaintSuggestionMapper.toEntity(returnedComplaintSuggestionDTO);
        assertComplaintSuggestionUpdatableFieldsEquals(
            returnedComplaintSuggestion,
            getPersistedComplaintSuggestion(returnedComplaintSuggestion)
        );

        insertedComplaintSuggestion = returnedComplaintSuggestion;
    }

    @Test
    @Transactional
    void createComplaintSuggestionWithExistingId() throws Exception {
        // Create the ComplaintSuggestion with an existing ID
        complaintSuggestion.setId(1L);
        ComplaintSuggestionDTO complaintSuggestionDTO = complaintSuggestionMapper.toDto(complaintSuggestion);

        long databaseSizeBeforeCreate = getRepositoryCount();

        // An entity with an existing ID cannot be created, so this API call must fail
        restComplaintSuggestionMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(complaintSuggestionDTO)))
            .andExpect(status().isBadRequest());

        // Validate the ComplaintSuggestion in the database
        assertSameRepositoryCount(databaseSizeBeforeCreate);
    }

    @Test
    @Transactional
    void checkVersionIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        complaintSuggestion.setVersion(null);

        // Create the ComplaintSuggestion, which fails.
        ComplaintSuggestionDTO complaintSuggestionDTO = complaintSuggestionMapper.toDto(complaintSuggestion);

        restComplaintSuggestionMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(complaintSuggestionDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkCreatedAtIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        complaintSuggestion.setCreatedAt(null);

        // Create the ComplaintSuggestion, which fails.
        ComplaintSuggestionDTO complaintSuggestionDTO = complaintSuggestionMapper.toDto(complaintSuggestion);

        restComplaintSuggestionMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(complaintSuggestionDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkUpdatedAtIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        complaintSuggestion.setUpdatedAt(null);

        // Create the ComplaintSuggestion, which fails.
        ComplaintSuggestionDTO complaintSuggestionDTO = complaintSuggestionMapper.toDto(complaintSuggestion);

        restComplaintSuggestionMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(complaintSuggestionDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkIsDeletedIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        complaintSuggestion.setIsDeleted(null);

        // Create the ComplaintSuggestion, which fails.
        ComplaintSuggestionDTO complaintSuggestionDTO = complaintSuggestionMapper.toDto(complaintSuggestion);

        restComplaintSuggestionMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(complaintSuggestionDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void getAllComplaintSuggestions() throws Exception {
        // Initialize the database
        insertedComplaintSuggestion = complaintSuggestionRepository.saveAndFlush(complaintSuggestion);

        // Get all the complaintSuggestionList
        restComplaintSuggestionMockMvc
            .perform(get(ENTITY_API_URL + "?sort=id,desc"))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.[*].id").value(hasItem(complaintSuggestion.getId().intValue())))
            .andExpect(jsonPath("$.[*].tenantId").value(hasItem(DEFAULT_TENANT_ID.intValue())))
            .andExpect(jsonPath("$.[*].employeeId").value(hasItem(DEFAULT_EMPLOYEE_ID.intValue())))
            .andExpect(jsonPath("$.[*].detail").value(hasItem(DEFAULT_DETAIL)))
            .andExpect(jsonPath("$.[*].isAnonymous").value(hasItem(DEFAULT_IS_ANONYMOUS)))
            .andExpect(jsonPath("$.[*].contactWay").value(hasItem(DEFAULT_CONTACT_WAY)))
            .andExpect(jsonPath("$.[*].metadata").value(hasItem(DEFAULT_METADATA)))
            .andExpect(jsonPath("$.[*].version").value(hasItem(DEFAULT_VERSION)))
            .andExpect(jsonPath("$.[*].createdBy").value(hasItem(DEFAULT_CREATED_BY)))
            .andExpect(jsonPath("$.[*].createdAt").value(hasItem(DEFAULT_CREATED_AT.toString())))
            .andExpect(jsonPath("$.[*].updatedBy").value(hasItem(DEFAULT_UPDATED_BY)))
            .andExpect(jsonPath("$.[*].updatedAt").value(hasItem(DEFAULT_UPDATED_AT.toString())))
            .andExpect(jsonPath("$.[*].isDeleted").value(hasItem(DEFAULT_IS_DELETED)));
    }

    @Test
    @Transactional
    void getComplaintSuggestion() throws Exception {
        // Initialize the database
        insertedComplaintSuggestion = complaintSuggestionRepository.saveAndFlush(complaintSuggestion);

        // Get the complaintSuggestion
        restComplaintSuggestionMockMvc
            .perform(get(ENTITY_API_URL_ID, complaintSuggestion.getId()))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.id").value(complaintSuggestion.getId().intValue()))
            .andExpect(jsonPath("$.tenantId").value(DEFAULT_TENANT_ID.intValue()))
            .andExpect(jsonPath("$.employeeId").value(DEFAULT_EMPLOYEE_ID.intValue()))
            .andExpect(jsonPath("$.detail").value(DEFAULT_DETAIL))
            .andExpect(jsonPath("$.isAnonymous").value(DEFAULT_IS_ANONYMOUS))
            .andExpect(jsonPath("$.contactWay").value(DEFAULT_CONTACT_WAY))
            .andExpect(jsonPath("$.metadata").value(DEFAULT_METADATA))
            .andExpect(jsonPath("$.version").value(DEFAULT_VERSION))
            .andExpect(jsonPath("$.createdBy").value(DEFAULT_CREATED_BY))
            .andExpect(jsonPath("$.createdAt").value(DEFAULT_CREATED_AT.toString()))
            .andExpect(jsonPath("$.updatedBy").value(DEFAULT_UPDATED_BY))
            .andExpect(jsonPath("$.updatedAt").value(DEFAULT_UPDATED_AT.toString()))
            .andExpect(jsonPath("$.isDeleted").value(DEFAULT_IS_DELETED));
    }

    @Test
    @Transactional
    void getNonExistingComplaintSuggestion() throws Exception {
        // Get the complaintSuggestion
        restComplaintSuggestionMockMvc.perform(get(ENTITY_API_URL_ID, Long.MAX_VALUE)).andExpect(status().isNotFound());
    }

    @Test
    @Transactional
    void putExistingComplaintSuggestion() throws Exception {
        // Initialize the database
        insertedComplaintSuggestion = complaintSuggestionRepository.saveAndFlush(complaintSuggestion);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the complaintSuggestion
        ComplaintSuggestion updatedComplaintSuggestion = complaintSuggestionRepository.findById(complaintSuggestion.getId()).orElseThrow();
        // Disconnect from session so that the updates on updatedComplaintSuggestion are not directly saved in db
        em.detach(updatedComplaintSuggestion);
        updatedComplaintSuggestion
            .tenantId(UPDATED_TENANT_ID)
            .employeeId(UPDATED_EMPLOYEE_ID)
            .detail(UPDATED_DETAIL)
            .isAnonymous(UPDATED_IS_ANONYMOUS)
            .contactWay(UPDATED_CONTACT_WAY)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);
        ComplaintSuggestionDTO complaintSuggestionDTO = complaintSuggestionMapper.toDto(updatedComplaintSuggestion);

        restComplaintSuggestionMockMvc
            .perform(
                put(ENTITY_API_URL_ID, complaintSuggestionDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(complaintSuggestionDTO))
            )
            .andExpect(status().isOk());

        // Validate the ComplaintSuggestion in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertPersistedComplaintSuggestionToMatchAllProperties(updatedComplaintSuggestion);
    }

    @Test
    @Transactional
    void putNonExistingComplaintSuggestion() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        complaintSuggestion.setId(longCount.incrementAndGet());

        // Create the ComplaintSuggestion
        ComplaintSuggestionDTO complaintSuggestionDTO = complaintSuggestionMapper.toDto(complaintSuggestion);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restComplaintSuggestionMockMvc
            .perform(
                put(ENTITY_API_URL_ID, complaintSuggestionDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(complaintSuggestionDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the ComplaintSuggestion in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithIdMismatchComplaintSuggestion() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        complaintSuggestion.setId(longCount.incrementAndGet());

        // Create the ComplaintSuggestion
        ComplaintSuggestionDTO complaintSuggestionDTO = complaintSuggestionMapper.toDto(complaintSuggestion);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restComplaintSuggestionMockMvc
            .perform(
                put(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(complaintSuggestionDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the ComplaintSuggestion in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithMissingIdPathParamComplaintSuggestion() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        complaintSuggestion.setId(longCount.incrementAndGet());

        // Create the ComplaintSuggestion
        ComplaintSuggestionDTO complaintSuggestionDTO = complaintSuggestionMapper.toDto(complaintSuggestion);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restComplaintSuggestionMockMvc
            .perform(put(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(complaintSuggestionDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the ComplaintSuggestion in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void partialUpdateComplaintSuggestionWithPatch() throws Exception {
        // Initialize the database
        insertedComplaintSuggestion = complaintSuggestionRepository.saveAndFlush(complaintSuggestion);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the complaintSuggestion using partial update
        ComplaintSuggestion partialUpdatedComplaintSuggestion = new ComplaintSuggestion();
        partialUpdatedComplaintSuggestion.setId(complaintSuggestion.getId());

        partialUpdatedComplaintSuggestion
            .contactWay(UPDATED_CONTACT_WAY)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);

        restComplaintSuggestionMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedComplaintSuggestion.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedComplaintSuggestion))
            )
            .andExpect(status().isOk());

        // Validate the ComplaintSuggestion in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertComplaintSuggestionUpdatableFieldsEquals(
            createUpdateProxyForBean(partialUpdatedComplaintSuggestion, complaintSuggestion),
            getPersistedComplaintSuggestion(complaintSuggestion)
        );
    }

    @Test
    @Transactional
    void fullUpdateComplaintSuggestionWithPatch() throws Exception {
        // Initialize the database
        insertedComplaintSuggestion = complaintSuggestionRepository.saveAndFlush(complaintSuggestion);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the complaintSuggestion using partial update
        ComplaintSuggestion partialUpdatedComplaintSuggestion = new ComplaintSuggestion();
        partialUpdatedComplaintSuggestion.setId(complaintSuggestion.getId());

        partialUpdatedComplaintSuggestion
            .tenantId(UPDATED_TENANT_ID)
            .employeeId(UPDATED_EMPLOYEE_ID)
            .detail(UPDATED_DETAIL)
            .isAnonymous(UPDATED_IS_ANONYMOUS)
            .contactWay(UPDATED_CONTACT_WAY)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);

        restComplaintSuggestionMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedComplaintSuggestion.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedComplaintSuggestion))
            )
            .andExpect(status().isOk());

        // Validate the ComplaintSuggestion in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertComplaintSuggestionUpdatableFieldsEquals(
            partialUpdatedComplaintSuggestion,
            getPersistedComplaintSuggestion(partialUpdatedComplaintSuggestion)
        );
    }

    @Test
    @Transactional
    void patchNonExistingComplaintSuggestion() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        complaintSuggestion.setId(longCount.incrementAndGet());

        // Create the ComplaintSuggestion
        ComplaintSuggestionDTO complaintSuggestionDTO = complaintSuggestionMapper.toDto(complaintSuggestion);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restComplaintSuggestionMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, complaintSuggestionDTO.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(complaintSuggestionDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the ComplaintSuggestion in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithIdMismatchComplaintSuggestion() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        complaintSuggestion.setId(longCount.incrementAndGet());

        // Create the ComplaintSuggestion
        ComplaintSuggestionDTO complaintSuggestionDTO = complaintSuggestionMapper.toDto(complaintSuggestion);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restComplaintSuggestionMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(complaintSuggestionDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the ComplaintSuggestion in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithMissingIdPathParamComplaintSuggestion() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        complaintSuggestion.setId(longCount.incrementAndGet());

        // Create the ComplaintSuggestion
        ComplaintSuggestionDTO complaintSuggestionDTO = complaintSuggestionMapper.toDto(complaintSuggestion);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restComplaintSuggestionMockMvc
            .perform(
                patch(ENTITY_API_URL).contentType("application/merge-patch+json").content(om.writeValueAsBytes(complaintSuggestionDTO))
            )
            .andExpect(status().isMethodNotAllowed());

        // Validate the ComplaintSuggestion in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void deleteComplaintSuggestion() throws Exception {
        // Initialize the database
        insertedComplaintSuggestion = complaintSuggestionRepository.saveAndFlush(complaintSuggestion);

        long databaseSizeBeforeDelete = getRepositoryCount();

        // Delete the complaintSuggestion
        restComplaintSuggestionMockMvc
            .perform(delete(ENTITY_API_URL_ID, complaintSuggestion.getId()).accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isNoContent());

        // Validate the database contains one less item
        assertDecrementedRepositoryCount(databaseSizeBeforeDelete);
    }

    protected long getRepositoryCount() {
        return complaintSuggestionRepository.count();
    }

    protected void assertIncrementedRepositoryCount(long countBefore) {
        assertThat(countBefore + 1).isEqualTo(getRepositoryCount());
    }

    protected void assertDecrementedRepositoryCount(long countBefore) {
        assertThat(countBefore - 1).isEqualTo(getRepositoryCount());
    }

    protected void assertSameRepositoryCount(long countBefore) {
        assertThat(countBefore).isEqualTo(getRepositoryCount());
    }

    protected ComplaintSuggestion getPersistedComplaintSuggestion(ComplaintSuggestion complaintSuggestion) {
        return complaintSuggestionRepository.findById(complaintSuggestion.getId()).orElseThrow();
    }

    protected void assertPersistedComplaintSuggestionToMatchAllProperties(ComplaintSuggestion expectedComplaintSuggestion) {
        assertComplaintSuggestionAllPropertiesEquals(
            expectedComplaintSuggestion,
            getPersistedComplaintSuggestion(expectedComplaintSuggestion)
        );
    }

    protected void assertPersistedComplaintSuggestionToMatchUpdatableProperties(ComplaintSuggestion expectedComplaintSuggestion) {
        assertComplaintSuggestionAllUpdatablePropertiesEquals(
            expectedComplaintSuggestion,
            getPersistedComplaintSuggestion(expectedComplaintSuggestion)
        );
    }
}

package com.whiskerguard.organization.web.rest;

import static com.whiskerguard.organization.domain.ComplaintSuggestionAttachmentAsserts.*;
import static com.whiskerguard.organization.web.rest.TestUtil.createUpdateProxyForBean;
import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.hasItem;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.whiskerguard.organization.IntegrationTest;
import com.whiskerguard.organization.domain.ComplaintSuggestionAttachment;
import com.whiskerguard.organization.repository.ComplaintSuggestionAttachmentRepository;
import com.whiskerguard.organization.service.dto.ComplaintSuggestionAttachmentDTO;
import com.whiskerguard.organization.service.mapper.ComplaintSuggestionAttachmentMapper;
import jakarta.persistence.EntityManager;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Random;
import java.util.concurrent.atomic.AtomicLong;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

/**
 * Integration tests for the {@link ComplaintSuggestionAttachmentResource} REST controller.
 */
@IntegrationTest
@AutoConfigureMockMvc
@WithMockUser
class ComplaintSuggestionAttachmentResourceIT {

    private static final Long DEFAULT_TENANT_ID = 1L;
    private static final Long UPDATED_TENANT_ID = 2L;

    private static final Long DEFAULT_SUGGESTION_ID = 1L;
    private static final Long UPDATED_SUGGESTION_ID = 2L;

    private static final String DEFAULT_FILE_NAME = "AAAAAAAAAA";
    private static final String UPDATED_FILE_NAME = "BBBBBBBBBB";

    private static final String DEFAULT_FILE_PATH = "AAAAAAAAAA";
    private static final String UPDATED_FILE_PATH = "BBBBBBBBBB";

    private static final String DEFAULT_FILE_TYPE = "AAAAAAAAAA";
    private static final String UPDATED_FILE_TYPE = "BBBBBBBBBB";

    private static final String DEFAULT_FILE_SIZE = "AAAAAAAAAA";
    private static final String UPDATED_FILE_SIZE = "BBBBBBBBBB";

    private static final String DEFAULT_FILE_DESC = "AAAAAAAAAA";
    private static final String UPDATED_FILE_DESC = "BBBBBBBBBB";

    private static final String DEFAULT_METADATA = "AAAAAAAAAA";
    private static final String UPDATED_METADATA = "BBBBBBBBBB";

    private static final Integer DEFAULT_VERSION = 1;
    private static final Integer UPDATED_VERSION = 2;

    private static final String DEFAULT_UPLOADED_BY = "AAAAAAAAAA";
    private static final String UPDATED_UPLOADED_BY = "BBBBBBBBBB";

    private static final Instant DEFAULT_UPLOADED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_UPLOADED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final Boolean DEFAULT_IS_DELETED = false;
    private static final Boolean UPDATED_IS_DELETED = true;

    private static final String ENTITY_API_URL = "/api/complaint-suggestion-attachments";
    private static final String ENTITY_API_URL_ID = ENTITY_API_URL + "/{id}";

    private static Random random = new Random();
    private static AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));

    @Autowired
    private ObjectMapper om;

    @Autowired
    private ComplaintSuggestionAttachmentRepository complaintSuggestionAttachmentRepository;

    @Autowired
    private ComplaintSuggestionAttachmentMapper complaintSuggestionAttachmentMapper;

    @Autowired
    private EntityManager em;

    @Autowired
    private MockMvc restComplaintSuggestionAttachmentMockMvc;

    private ComplaintSuggestionAttachment complaintSuggestionAttachment;

    private ComplaintSuggestionAttachment insertedComplaintSuggestionAttachment;

    /**
     * Create an entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static ComplaintSuggestionAttachment createEntity() {
        return new ComplaintSuggestionAttachment()
            .tenantId(DEFAULT_TENANT_ID)
            .suggestionId(DEFAULT_SUGGESTION_ID)
            .fileName(DEFAULT_FILE_NAME)
            .filePath(DEFAULT_FILE_PATH)
            .fileType(DEFAULT_FILE_TYPE)
            .fileSize(DEFAULT_FILE_SIZE)
            .fileDesc(DEFAULT_FILE_DESC)
            .metadata(DEFAULT_METADATA)
            .version(DEFAULT_VERSION)
            .uploadedBy(DEFAULT_UPLOADED_BY)
            .uploadedAt(DEFAULT_UPLOADED_AT)
            .isDeleted(DEFAULT_IS_DELETED);
    }

    /**
     * Create an updated entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static ComplaintSuggestionAttachment createUpdatedEntity() {
        return new ComplaintSuggestionAttachment()
            .tenantId(UPDATED_TENANT_ID)
            .suggestionId(UPDATED_SUGGESTION_ID)
            .fileName(UPDATED_FILE_NAME)
            .filePath(UPDATED_FILE_PATH)
            .fileType(UPDATED_FILE_TYPE)
            .fileSize(UPDATED_FILE_SIZE)
            .fileDesc(UPDATED_FILE_DESC)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .uploadedBy(UPDATED_UPLOADED_BY)
            .uploadedAt(UPDATED_UPLOADED_AT)
            .isDeleted(UPDATED_IS_DELETED);
    }

    @BeforeEach
    void initTest() {
        complaintSuggestionAttachment = createEntity();
    }

    @AfterEach
    void cleanup() {
        if (insertedComplaintSuggestionAttachment != null) {
            complaintSuggestionAttachmentRepository.delete(insertedComplaintSuggestionAttachment);
            insertedComplaintSuggestionAttachment = null;
        }
    }

    @Test
    @Transactional
    void createComplaintSuggestionAttachment() throws Exception {
        long databaseSizeBeforeCreate = getRepositoryCount();
        // Create the ComplaintSuggestionAttachment
        ComplaintSuggestionAttachmentDTO complaintSuggestionAttachmentDTO = complaintSuggestionAttachmentMapper.toDto(
            complaintSuggestionAttachment
        );
        var returnedComplaintSuggestionAttachmentDTO = om.readValue(
            restComplaintSuggestionAttachmentMockMvc
                .perform(
                    post(ENTITY_API_URL)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(om.writeValueAsBytes(complaintSuggestionAttachmentDTO))
                )
                .andExpect(status().isCreated())
                .andReturn()
                .getResponse()
                .getContentAsString(),
            ComplaintSuggestionAttachmentDTO.class
        );

        // Validate the ComplaintSuggestionAttachment in the database
        assertIncrementedRepositoryCount(databaseSizeBeforeCreate);
        var returnedComplaintSuggestionAttachment = complaintSuggestionAttachmentMapper.toEntity(returnedComplaintSuggestionAttachmentDTO);
        assertComplaintSuggestionAttachmentUpdatableFieldsEquals(
            returnedComplaintSuggestionAttachment,
            getPersistedComplaintSuggestionAttachment(returnedComplaintSuggestionAttachment)
        );

        insertedComplaintSuggestionAttachment = returnedComplaintSuggestionAttachment;
    }

    @Test
    @Transactional
    void createComplaintSuggestionAttachmentWithExistingId() throws Exception {
        // Create the ComplaintSuggestionAttachment with an existing ID
        complaintSuggestionAttachment.setId(1L);
        ComplaintSuggestionAttachmentDTO complaintSuggestionAttachmentDTO = complaintSuggestionAttachmentMapper.toDto(
            complaintSuggestionAttachment
        );

        long databaseSizeBeforeCreate = getRepositoryCount();

        // An entity with an existing ID cannot be created, so this API call must fail
        restComplaintSuggestionAttachmentMockMvc
            .perform(
                post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(complaintSuggestionAttachmentDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the ComplaintSuggestionAttachment in the database
        assertSameRepositoryCount(databaseSizeBeforeCreate);
    }

    @Test
    @Transactional
    void checkSuggestionIdIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        complaintSuggestionAttachment.setSuggestionId(null);

        // Create the ComplaintSuggestionAttachment, which fails.
        ComplaintSuggestionAttachmentDTO complaintSuggestionAttachmentDTO = complaintSuggestionAttachmentMapper.toDto(
            complaintSuggestionAttachment
        );

        restComplaintSuggestionAttachmentMockMvc
            .perform(
                post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(complaintSuggestionAttachmentDTO))
            )
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkFileNameIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        complaintSuggestionAttachment.setFileName(null);

        // Create the ComplaintSuggestionAttachment, which fails.
        ComplaintSuggestionAttachmentDTO complaintSuggestionAttachmentDTO = complaintSuggestionAttachmentMapper.toDto(
            complaintSuggestionAttachment
        );

        restComplaintSuggestionAttachmentMockMvc
            .perform(
                post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(complaintSuggestionAttachmentDTO))
            )
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkFilePathIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        complaintSuggestionAttachment.setFilePath(null);

        // Create the ComplaintSuggestionAttachment, which fails.
        ComplaintSuggestionAttachmentDTO complaintSuggestionAttachmentDTO = complaintSuggestionAttachmentMapper.toDto(
            complaintSuggestionAttachment
        );

        restComplaintSuggestionAttachmentMockMvc
            .perform(
                post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(complaintSuggestionAttachmentDTO))
            )
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkFileTypeIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        complaintSuggestionAttachment.setFileType(null);

        // Create the ComplaintSuggestionAttachment, which fails.
        ComplaintSuggestionAttachmentDTO complaintSuggestionAttachmentDTO = complaintSuggestionAttachmentMapper.toDto(
            complaintSuggestionAttachment
        );

        restComplaintSuggestionAttachmentMockMvc
            .perform(
                post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(complaintSuggestionAttachmentDTO))
            )
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkUploadedByIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        complaintSuggestionAttachment.setUploadedBy(null);

        // Create the ComplaintSuggestionAttachment, which fails.
        ComplaintSuggestionAttachmentDTO complaintSuggestionAttachmentDTO = complaintSuggestionAttachmentMapper.toDto(
            complaintSuggestionAttachment
        );

        restComplaintSuggestionAttachmentMockMvc
            .perform(
                post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(complaintSuggestionAttachmentDTO))
            )
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void getAllComplaintSuggestionAttachments() throws Exception {
        // Initialize the database
        insertedComplaintSuggestionAttachment = complaintSuggestionAttachmentRepository.saveAndFlush(complaintSuggestionAttachment);

        // Get all the complaintSuggestionAttachmentList
        restComplaintSuggestionAttachmentMockMvc
            .perform(get(ENTITY_API_URL + "?sort=id,desc"))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.[*].id").value(hasItem(complaintSuggestionAttachment.getId().intValue())))
            .andExpect(jsonPath("$.[*].tenantId").value(hasItem(DEFAULT_TENANT_ID.intValue())))
            .andExpect(jsonPath("$.[*].suggestionId").value(hasItem(DEFAULT_SUGGESTION_ID.intValue())))
            .andExpect(jsonPath("$.[*].fileName").value(hasItem(DEFAULT_FILE_NAME)))
            .andExpect(jsonPath("$.[*].filePath").value(hasItem(DEFAULT_FILE_PATH)))
            .andExpect(jsonPath("$.[*].fileType").value(hasItem(DEFAULT_FILE_TYPE)))
            .andExpect(jsonPath("$.[*].fileSize").value(hasItem(DEFAULT_FILE_SIZE)))
            .andExpect(jsonPath("$.[*].fileDesc").value(hasItem(DEFAULT_FILE_DESC)))
            .andExpect(jsonPath("$.[*].metadata").value(hasItem(DEFAULT_METADATA)))
            .andExpect(jsonPath("$.[*].version").value(hasItem(DEFAULT_VERSION)))
            .andExpect(jsonPath("$.[*].uploadedBy").value(hasItem(DEFAULT_UPLOADED_BY)))
            .andExpect(jsonPath("$.[*].uploadedAt").value(hasItem(DEFAULT_UPLOADED_AT.toString())))
            .andExpect(jsonPath("$.[*].isDeleted").value(hasItem(DEFAULT_IS_DELETED)));
    }

    @Test
    @Transactional
    void getComplaintSuggestionAttachment() throws Exception {
        // Initialize the database
        insertedComplaintSuggestionAttachment = complaintSuggestionAttachmentRepository.saveAndFlush(complaintSuggestionAttachment);

        // Get the complaintSuggestionAttachment
        restComplaintSuggestionAttachmentMockMvc
            .perform(get(ENTITY_API_URL_ID, complaintSuggestionAttachment.getId()))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.id").value(complaintSuggestionAttachment.getId().intValue()))
            .andExpect(jsonPath("$.tenantId").value(DEFAULT_TENANT_ID.intValue()))
            .andExpect(jsonPath("$.suggestionId").value(DEFAULT_SUGGESTION_ID.intValue()))
            .andExpect(jsonPath("$.fileName").value(DEFAULT_FILE_NAME))
            .andExpect(jsonPath("$.filePath").value(DEFAULT_FILE_PATH))
            .andExpect(jsonPath("$.fileType").value(DEFAULT_FILE_TYPE))
            .andExpect(jsonPath("$.fileSize").value(DEFAULT_FILE_SIZE))
            .andExpect(jsonPath("$.fileDesc").value(DEFAULT_FILE_DESC))
            .andExpect(jsonPath("$.metadata").value(DEFAULT_METADATA))
            .andExpect(jsonPath("$.version").value(DEFAULT_VERSION))
            .andExpect(jsonPath("$.uploadedBy").value(DEFAULT_UPLOADED_BY))
            .andExpect(jsonPath("$.uploadedAt").value(DEFAULT_UPLOADED_AT.toString()))
            .andExpect(jsonPath("$.isDeleted").value(DEFAULT_IS_DELETED));
    }

    @Test
    @Transactional
    void getNonExistingComplaintSuggestionAttachment() throws Exception {
        // Get the complaintSuggestionAttachment
        restComplaintSuggestionAttachmentMockMvc.perform(get(ENTITY_API_URL_ID, Long.MAX_VALUE)).andExpect(status().isNotFound());
    }

    @Test
    @Transactional
    void putExistingComplaintSuggestionAttachment() throws Exception {
        // Initialize the database
        insertedComplaintSuggestionAttachment = complaintSuggestionAttachmentRepository.saveAndFlush(complaintSuggestionAttachment);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the complaintSuggestionAttachment
        ComplaintSuggestionAttachment updatedComplaintSuggestionAttachment = complaintSuggestionAttachmentRepository
            .findById(complaintSuggestionAttachment.getId())
            .orElseThrow();
        // Disconnect from session so that the updates on updatedComplaintSuggestionAttachment are not directly saved in db
        em.detach(updatedComplaintSuggestionAttachment);
        updatedComplaintSuggestionAttachment
            .tenantId(UPDATED_TENANT_ID)
            .suggestionId(UPDATED_SUGGESTION_ID)
            .fileName(UPDATED_FILE_NAME)
            .filePath(UPDATED_FILE_PATH)
            .fileType(UPDATED_FILE_TYPE)
            .fileSize(UPDATED_FILE_SIZE)
            .fileDesc(UPDATED_FILE_DESC)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .uploadedBy(UPDATED_UPLOADED_BY)
            .uploadedAt(UPDATED_UPLOADED_AT)
            .isDeleted(UPDATED_IS_DELETED);
        ComplaintSuggestionAttachmentDTO complaintSuggestionAttachmentDTO = complaintSuggestionAttachmentMapper.toDto(
            updatedComplaintSuggestionAttachment
        );

        restComplaintSuggestionAttachmentMockMvc
            .perform(
                put(ENTITY_API_URL_ID, complaintSuggestionAttachmentDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(complaintSuggestionAttachmentDTO))
            )
            .andExpect(status().isOk());

        // Validate the ComplaintSuggestionAttachment in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertPersistedComplaintSuggestionAttachmentToMatchAllProperties(updatedComplaintSuggestionAttachment);
    }

    @Test
    @Transactional
    void putNonExistingComplaintSuggestionAttachment() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        complaintSuggestionAttachment.setId(longCount.incrementAndGet());

        // Create the ComplaintSuggestionAttachment
        ComplaintSuggestionAttachmentDTO complaintSuggestionAttachmentDTO = complaintSuggestionAttachmentMapper.toDto(
            complaintSuggestionAttachment
        );

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restComplaintSuggestionAttachmentMockMvc
            .perform(
                put(ENTITY_API_URL_ID, complaintSuggestionAttachmentDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(complaintSuggestionAttachmentDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the ComplaintSuggestionAttachment in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithIdMismatchComplaintSuggestionAttachment() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        complaintSuggestionAttachment.setId(longCount.incrementAndGet());

        // Create the ComplaintSuggestionAttachment
        ComplaintSuggestionAttachmentDTO complaintSuggestionAttachmentDTO = complaintSuggestionAttachmentMapper.toDto(
            complaintSuggestionAttachment
        );

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restComplaintSuggestionAttachmentMockMvc
            .perform(
                put(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(complaintSuggestionAttachmentDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the ComplaintSuggestionAttachment in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithMissingIdPathParamComplaintSuggestionAttachment() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        complaintSuggestionAttachment.setId(longCount.incrementAndGet());

        // Create the ComplaintSuggestionAttachment
        ComplaintSuggestionAttachmentDTO complaintSuggestionAttachmentDTO = complaintSuggestionAttachmentMapper.toDto(
            complaintSuggestionAttachment
        );

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restComplaintSuggestionAttachmentMockMvc
            .perform(
                put(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(complaintSuggestionAttachmentDTO))
            )
            .andExpect(status().isMethodNotAllowed());

        // Validate the ComplaintSuggestionAttachment in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void partialUpdateComplaintSuggestionAttachmentWithPatch() throws Exception {
        // Initialize the database
        insertedComplaintSuggestionAttachment = complaintSuggestionAttachmentRepository.saveAndFlush(complaintSuggestionAttachment);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the complaintSuggestionAttachment using partial update
        ComplaintSuggestionAttachment partialUpdatedComplaintSuggestionAttachment = new ComplaintSuggestionAttachment();
        partialUpdatedComplaintSuggestionAttachment.setId(complaintSuggestionAttachment.getId());

        partialUpdatedComplaintSuggestionAttachment
            .tenantId(UPDATED_TENANT_ID)
            .filePath(UPDATED_FILE_PATH)
            .fileDesc(UPDATED_FILE_DESC)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .isDeleted(UPDATED_IS_DELETED);

        restComplaintSuggestionAttachmentMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedComplaintSuggestionAttachment.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedComplaintSuggestionAttachment))
            )
            .andExpect(status().isOk());

        // Validate the ComplaintSuggestionAttachment in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertComplaintSuggestionAttachmentUpdatableFieldsEquals(
            createUpdateProxyForBean(partialUpdatedComplaintSuggestionAttachment, complaintSuggestionAttachment),
            getPersistedComplaintSuggestionAttachment(complaintSuggestionAttachment)
        );
    }

    @Test
    @Transactional
    void fullUpdateComplaintSuggestionAttachmentWithPatch() throws Exception {
        // Initialize the database
        insertedComplaintSuggestionAttachment = complaintSuggestionAttachmentRepository.saveAndFlush(complaintSuggestionAttachment);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the complaintSuggestionAttachment using partial update
        ComplaintSuggestionAttachment partialUpdatedComplaintSuggestionAttachment = new ComplaintSuggestionAttachment();
        partialUpdatedComplaintSuggestionAttachment.setId(complaintSuggestionAttachment.getId());

        partialUpdatedComplaintSuggestionAttachment
            .tenantId(UPDATED_TENANT_ID)
            .suggestionId(UPDATED_SUGGESTION_ID)
            .fileName(UPDATED_FILE_NAME)
            .filePath(UPDATED_FILE_PATH)
            .fileType(UPDATED_FILE_TYPE)
            .fileSize(UPDATED_FILE_SIZE)
            .fileDesc(UPDATED_FILE_DESC)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .uploadedBy(UPDATED_UPLOADED_BY)
            .uploadedAt(UPDATED_UPLOADED_AT)
            .isDeleted(UPDATED_IS_DELETED);

        restComplaintSuggestionAttachmentMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedComplaintSuggestionAttachment.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedComplaintSuggestionAttachment))
            )
            .andExpect(status().isOk());

        // Validate the ComplaintSuggestionAttachment in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertComplaintSuggestionAttachmentUpdatableFieldsEquals(
            partialUpdatedComplaintSuggestionAttachment,
            getPersistedComplaintSuggestionAttachment(partialUpdatedComplaintSuggestionAttachment)
        );
    }

    @Test
    @Transactional
    void patchNonExistingComplaintSuggestionAttachment() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        complaintSuggestionAttachment.setId(longCount.incrementAndGet());

        // Create the ComplaintSuggestionAttachment
        ComplaintSuggestionAttachmentDTO complaintSuggestionAttachmentDTO = complaintSuggestionAttachmentMapper.toDto(
            complaintSuggestionAttachment
        );

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restComplaintSuggestionAttachmentMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, complaintSuggestionAttachmentDTO.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(complaintSuggestionAttachmentDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the ComplaintSuggestionAttachment in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithIdMismatchComplaintSuggestionAttachment() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        complaintSuggestionAttachment.setId(longCount.incrementAndGet());

        // Create the ComplaintSuggestionAttachment
        ComplaintSuggestionAttachmentDTO complaintSuggestionAttachmentDTO = complaintSuggestionAttachmentMapper.toDto(
            complaintSuggestionAttachment
        );

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restComplaintSuggestionAttachmentMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(complaintSuggestionAttachmentDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the ComplaintSuggestionAttachment in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithMissingIdPathParamComplaintSuggestionAttachment() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        complaintSuggestionAttachment.setId(longCount.incrementAndGet());

        // Create the ComplaintSuggestionAttachment
        ComplaintSuggestionAttachmentDTO complaintSuggestionAttachmentDTO = complaintSuggestionAttachmentMapper.toDto(
            complaintSuggestionAttachment
        );

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restComplaintSuggestionAttachmentMockMvc
            .perform(
                patch(ENTITY_API_URL)
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(complaintSuggestionAttachmentDTO))
            )
            .andExpect(status().isMethodNotAllowed());

        // Validate the ComplaintSuggestionAttachment in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void deleteComplaintSuggestionAttachment() throws Exception {
        // Initialize the database
        insertedComplaintSuggestionAttachment = complaintSuggestionAttachmentRepository.saveAndFlush(complaintSuggestionAttachment);

        long databaseSizeBeforeDelete = getRepositoryCount();

        // Delete the complaintSuggestionAttachment
        restComplaintSuggestionAttachmentMockMvc
            .perform(delete(ENTITY_API_URL_ID, complaintSuggestionAttachment.getId()).accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isNoContent());

        // Validate the database contains one less item
        assertDecrementedRepositoryCount(databaseSizeBeforeDelete);
    }

    protected long getRepositoryCount() {
        return complaintSuggestionAttachmentRepository.count();
    }

    protected void assertIncrementedRepositoryCount(long countBefore) {
        assertThat(countBefore + 1).isEqualTo(getRepositoryCount());
    }

    protected void assertDecrementedRepositoryCount(long countBefore) {
        assertThat(countBefore - 1).isEqualTo(getRepositoryCount());
    }

    protected void assertSameRepositoryCount(long countBefore) {
        assertThat(countBefore).isEqualTo(getRepositoryCount());
    }

    protected ComplaintSuggestionAttachment getPersistedComplaintSuggestionAttachment(
        ComplaintSuggestionAttachment complaintSuggestionAttachment
    ) {
        return complaintSuggestionAttachmentRepository.findById(complaintSuggestionAttachment.getId()).orElseThrow();
    }

    protected void assertPersistedComplaintSuggestionAttachmentToMatchAllProperties(
        ComplaintSuggestionAttachment expectedComplaintSuggestionAttachment
    ) {
        assertComplaintSuggestionAttachmentAllPropertiesEquals(
            expectedComplaintSuggestionAttachment,
            getPersistedComplaintSuggestionAttachment(expectedComplaintSuggestionAttachment)
        );
    }

    protected void assertPersistedComplaintSuggestionAttachmentToMatchUpdatableProperties(
        ComplaintSuggestionAttachment expectedComplaintSuggestionAttachment
    ) {
        assertComplaintSuggestionAttachmentAllUpdatablePropertiesEquals(
            expectedComplaintSuggestionAttachment,
            getPersistedComplaintSuggestionAttachment(expectedComplaintSuggestionAttachment)
        );
    }
}

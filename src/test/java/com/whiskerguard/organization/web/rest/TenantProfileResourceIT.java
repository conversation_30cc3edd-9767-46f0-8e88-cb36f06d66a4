package com.whiskerguard.organization.web.rest;

import static com.whiskerguard.organization.domain.TenantProfileAsserts.*;
import static com.whiskerguard.organization.web.rest.TestUtil.createUpdateProxyForBean;
import static com.whiskerguard.organization.web.rest.TestUtil.sameNumber;
import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.hasItem;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.whiskerguard.organization.IntegrationTest;
import com.whiskerguard.organization.domain.TenantProfile;
import com.whiskerguard.organization.repository.TenantProfileRepository;
import com.whiskerguard.organization.service.dto.TenantProfileDTO;
import com.whiskerguard.organization.service.mapper.TenantProfileMapper;
import jakarta.persistence.EntityManager;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.Random;
import java.util.concurrent.atomic.AtomicLong;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

/**
 * Integration tests for the {@link TenantProfileResource} REST controller.
 */
@IntegrationTest
@AutoConfigureMockMvc
@WithMockUser
class TenantProfileResourceIT {

    private static final String DEFAULT_REGISTRATION_NUMBER = "AAAAAAAAAA";
    private static final String UPDATED_REGISTRATION_NUMBER = "BBBBBBBBBB";

    private static final LocalDate DEFAULT_REGISTRATION_DATE = LocalDate.ofEpochDay(0L);
    private static final LocalDate UPDATED_REGISTRATION_DATE = LocalDate.now(ZoneId.systemDefault());

    private static final BigDecimal DEFAULT_REGISTERED_CAPITAL = new BigDecimal(1);
    private static final BigDecimal UPDATED_REGISTERED_CAPITAL = new BigDecimal(2);

    private static final String DEFAULT_COMPANY_TYPE = "AAAAAAAAAA";
    private static final String UPDATED_COMPANY_TYPE = "BBBBBBBBBB";

    private static final String DEFAULT_BUSINESS_SCOPE = "AAAAAAAAAA";
    private static final String UPDATED_BUSINESS_SCOPE = "BBBBBBBBBB";

    private static final String DEFAULT_INDUSTRY = "AAAAAAAAAA";
    private static final String UPDATED_INDUSTRY = "BBBBBBBBBB";

    private static final String DEFAULT_TAX_REGISTRATION_NUMBER = "AAAAAAAAAA";
    private static final String UPDATED_TAX_REGISTRATION_NUMBER = "BBBBBBBBBB";

    private static final String DEFAULT_ORGANIZATION_CODE = "AAAAAAAAAA";
    private static final String UPDATED_ORGANIZATION_CODE = "BBBBBBBBBB";

    private static final String DEFAULT_REGISTERED_ADDRESS = "AAAAAAAAAA";
    private static final String UPDATED_REGISTERED_ADDRESS = "BBBBBBBBBB";

    private static final String DEFAULT_POSTAL_CODE = "AAAAAAAAAA";
    private static final String UPDATED_POSTAL_CODE = "BBBBBBBBBB";

    private static final String DEFAULT_WEBSITE = "AAAAAAAAAA";
    private static final String UPDATED_WEBSITE = "BBBBBBBBBB";

    private static final String DEFAULT_FAX = "AAAAAAAAAA";
    private static final String UPDATED_FAX = "BBBBBBBBBB";

    private static final String DEFAULT_CONTACT_PERSON = "AAAAAAAAAA";
    private static final String UPDATED_CONTACT_PERSON = "BBBBBBBBBB";

    private static final String DEFAULT_CONTACT_MOBILE = "AAAAAAAAAA";
    private static final String UPDATED_CONTACT_MOBILE = "BBBBBBBBBB";

    private static final String DEFAULT_CONTACT_EMAIL = "AAAAAAAAAA";
    private static final String UPDATED_CONTACT_EMAIL = "BBBBBBBBBB";

    private static final String DEFAULT_BANK_NAME = "AAAAAAAAAA";
    private static final String UPDATED_BANK_NAME = "BBBBBBBBBB";

    private static final String DEFAULT_BANK_ACCOUNT = "AAAAAAAAAA";
    private static final String UPDATED_BANK_ACCOUNT = "BBBBBBBBBB";

    private static final String DEFAULT_BUSINESS_LICENSE_PATH = "AAAAAAAAAA";
    private static final String UPDATED_BUSINESS_LICENSE_PATH = "BBBBBBBBBB";

    private static final String DEFAULT_LEGAL_PERSON = "AAAAAAAAAA";
    private static final String UPDATED_LEGAL_PERSON = "BBBBBBBBBB";

    private static final String DEFAULT_LEGAL_PERSON_ID = "AAAAAAAAAA";
    private static final String UPDATED_LEGAL_PERSON_ID = "BBBBBBBBBB";

    private static final String DEFAULT_METADATA = "AAAAAAAAAA";
    private static final String UPDATED_METADATA = "BBBBBBBBBB";

    private static final Integer DEFAULT_VERSION = 1;
    private static final Integer UPDATED_VERSION = 2;

    private static final String DEFAULT_CREATED_BY = "AAAAAAAAAA";
    private static final String UPDATED_CREATED_BY = "BBBBBBBBBB";

    private static final Instant DEFAULT_CREATED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_CREATED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final String DEFAULT_UPDATED_BY = "AAAAAAAAAA";
    private static final String UPDATED_UPDATED_BY = "BBBBBBBBBB";

    private static final Instant DEFAULT_UPDATED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_UPDATED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final Boolean DEFAULT_IS_DELETED = false;
    private static final Boolean UPDATED_IS_DELETED = true;

    private static final String ENTITY_API_URL = "/api/tenant-profiles";
    private static final String ENTITY_API_URL_ID = ENTITY_API_URL + "/{id}";

    private static Random random = new Random();
    private static AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));

    @Autowired
    private ObjectMapper om;

    @Autowired
    private TenantProfileRepository tenantProfileRepository;

    @Autowired
    private TenantProfileMapper tenantProfileMapper;

    @Autowired
    private EntityManager em;

    @Autowired
    private MockMvc restTenantProfileMockMvc;

    private TenantProfile tenantProfile;

    private TenantProfile insertedTenantProfile;

    /**
     * Create an entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static TenantProfile createEntity() {
        return new TenantProfile()
            .registrationNumber(DEFAULT_REGISTRATION_NUMBER)
            .registrationDate(DEFAULT_REGISTRATION_DATE)
            .registeredCapital(DEFAULT_REGISTERED_CAPITAL)
            .companyType(DEFAULT_COMPANY_TYPE)
            .businessScope(DEFAULT_BUSINESS_SCOPE)
            .industry(DEFAULT_INDUSTRY)
            .taxRegistrationNumber(DEFAULT_TAX_REGISTRATION_NUMBER)
            .organizationCode(DEFAULT_ORGANIZATION_CODE)
            .registeredAddress(DEFAULT_REGISTERED_ADDRESS)
            .postalCode(DEFAULT_POSTAL_CODE)
            .website(DEFAULT_WEBSITE)
            .fax(DEFAULT_FAX)
            .contactPerson(DEFAULT_CONTACT_PERSON)
            .contactMobile(DEFAULT_CONTACT_MOBILE)
            .contactEmail(DEFAULT_CONTACT_EMAIL)
            .bankName(DEFAULT_BANK_NAME)
            .bankAccount(DEFAULT_BANK_ACCOUNT)
            .businessLicensePath(DEFAULT_BUSINESS_LICENSE_PATH)
            .legalPerson(DEFAULT_LEGAL_PERSON)
            .legalPersonId(DEFAULT_LEGAL_PERSON_ID)
            .metadata(DEFAULT_METADATA)
            .version(DEFAULT_VERSION)
            .createdBy(DEFAULT_CREATED_BY)
            .createdAt(DEFAULT_CREATED_AT)
            .updatedBy(DEFAULT_UPDATED_BY)
            .updatedAt(DEFAULT_UPDATED_AT)
            .isDeleted(DEFAULT_IS_DELETED);
    }

    /**
     * Create an updated entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static TenantProfile createUpdatedEntity() {
        return new TenantProfile()
            .registrationNumber(UPDATED_REGISTRATION_NUMBER)
            .registrationDate(UPDATED_REGISTRATION_DATE)
            .registeredCapital(UPDATED_REGISTERED_CAPITAL)
            .companyType(UPDATED_COMPANY_TYPE)
            .businessScope(UPDATED_BUSINESS_SCOPE)
            .industry(UPDATED_INDUSTRY)
            .taxRegistrationNumber(UPDATED_TAX_REGISTRATION_NUMBER)
            .organizationCode(UPDATED_ORGANIZATION_CODE)
            .registeredAddress(UPDATED_REGISTERED_ADDRESS)
            .postalCode(UPDATED_POSTAL_CODE)
            .website(UPDATED_WEBSITE)
            .fax(UPDATED_FAX)
            .contactPerson(UPDATED_CONTACT_PERSON)
            .contactMobile(UPDATED_CONTACT_MOBILE)
            .contactEmail(UPDATED_CONTACT_EMAIL)
            .bankName(UPDATED_BANK_NAME)
            .bankAccount(UPDATED_BANK_ACCOUNT)
            .businessLicensePath(UPDATED_BUSINESS_LICENSE_PATH)
            .legalPerson(UPDATED_LEGAL_PERSON)
            .legalPersonId(UPDATED_LEGAL_PERSON_ID)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);
    }

    @BeforeEach
    void initTest() {
        tenantProfile = createEntity();
    }

    @AfterEach
    void cleanup() {
        if (insertedTenantProfile != null) {
            tenantProfileRepository.delete(insertedTenantProfile);
            insertedTenantProfile = null;
        }
    }

    @Test
    @Transactional
    void createTenantProfile() throws Exception {
        long databaseSizeBeforeCreate = getRepositoryCount();
        // Create the TenantProfile
        TenantProfileDTO tenantProfileDTO = tenantProfileMapper.toDto(tenantProfile);
        var returnedTenantProfileDTO = om.readValue(
            restTenantProfileMockMvc
                .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(tenantProfileDTO)))
                .andExpect(status().isCreated())
                .andReturn()
                .getResponse()
                .getContentAsString(),
            TenantProfileDTO.class
        );

        // Validate the TenantProfile in the database
        assertIncrementedRepositoryCount(databaseSizeBeforeCreate);
        var returnedTenantProfile = tenantProfileMapper.toEntity(returnedTenantProfileDTO);
        assertTenantProfileUpdatableFieldsEquals(returnedTenantProfile, getPersistedTenantProfile(returnedTenantProfile));

        insertedTenantProfile = returnedTenantProfile;
    }

    @Test
    @Transactional
    void createTenantProfileWithExistingId() throws Exception {
        // Create the TenantProfile with an existing ID
        tenantProfile.setId(1L);
        TenantProfileDTO tenantProfileDTO = tenantProfileMapper.toDto(tenantProfile);

        long databaseSizeBeforeCreate = getRepositoryCount();

        // An entity with an existing ID cannot be created, so this API call must fail
        restTenantProfileMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(tenantProfileDTO)))
            .andExpect(status().isBadRequest());

        // Validate the TenantProfile in the database
        assertSameRepositoryCount(databaseSizeBeforeCreate);
    }

    @Test
    @Transactional
    void checkVersionIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        tenantProfile.setVersion(null);

        // Create the TenantProfile, which fails.
        TenantProfileDTO tenantProfileDTO = tenantProfileMapper.toDto(tenantProfile);

        restTenantProfileMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(tenantProfileDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkCreatedAtIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        tenantProfile.setCreatedAt(null);

        // Create the TenantProfile, which fails.
        TenantProfileDTO tenantProfileDTO = tenantProfileMapper.toDto(tenantProfile);

        restTenantProfileMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(tenantProfileDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkUpdatedAtIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        tenantProfile.setUpdatedAt(null);

        // Create the TenantProfile, which fails.
        TenantProfileDTO tenantProfileDTO = tenantProfileMapper.toDto(tenantProfile);

        restTenantProfileMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(tenantProfileDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkIsDeletedIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        tenantProfile.setIsDeleted(null);

        // Create the TenantProfile, which fails.
        TenantProfileDTO tenantProfileDTO = tenantProfileMapper.toDto(tenantProfile);

        restTenantProfileMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(tenantProfileDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void getAllTenantProfiles() throws Exception {
        // Initialize the database
        insertedTenantProfile = tenantProfileRepository.saveAndFlush(tenantProfile);

        // Get all the tenantProfileList
        restTenantProfileMockMvc
            .perform(get(ENTITY_API_URL + "?sort=id,desc"))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.[*].id").value(hasItem(tenantProfile.getId().intValue())))
            .andExpect(jsonPath("$.[*].registrationNumber").value(hasItem(DEFAULT_REGISTRATION_NUMBER)))
            .andExpect(jsonPath("$.[*].registrationDate").value(hasItem(DEFAULT_REGISTRATION_DATE.toString())))
            .andExpect(jsonPath("$.[*].registeredCapital").value(hasItem(sameNumber(DEFAULT_REGISTERED_CAPITAL))))
            .andExpect(jsonPath("$.[*].companyType").value(hasItem(DEFAULT_COMPANY_TYPE)))
            .andExpect(jsonPath("$.[*].businessScope").value(hasItem(DEFAULT_BUSINESS_SCOPE)))
            .andExpect(jsonPath("$.[*].industry").value(hasItem(DEFAULT_INDUSTRY)))
            .andExpect(jsonPath("$.[*].taxRegistrationNumber").value(hasItem(DEFAULT_TAX_REGISTRATION_NUMBER)))
            .andExpect(jsonPath("$.[*].organizationCode").value(hasItem(DEFAULT_ORGANIZATION_CODE)))
            .andExpect(jsonPath("$.[*].registeredAddress").value(hasItem(DEFAULT_REGISTERED_ADDRESS)))
            .andExpect(jsonPath("$.[*].postalCode").value(hasItem(DEFAULT_POSTAL_CODE)))
            .andExpect(jsonPath("$.[*].website").value(hasItem(DEFAULT_WEBSITE)))
            .andExpect(jsonPath("$.[*].fax").value(hasItem(DEFAULT_FAX)))
            .andExpect(jsonPath("$.[*].contactPerson").value(hasItem(DEFAULT_CONTACT_PERSON)))
            .andExpect(jsonPath("$.[*].contactMobile").value(hasItem(DEFAULT_CONTACT_MOBILE)))
            .andExpect(jsonPath("$.[*].contactEmail").value(hasItem(DEFAULT_CONTACT_EMAIL)))
            .andExpect(jsonPath("$.[*].bankName").value(hasItem(DEFAULT_BANK_NAME)))
            .andExpect(jsonPath("$.[*].bankAccount").value(hasItem(DEFAULT_BANK_ACCOUNT)))
            .andExpect(jsonPath("$.[*].businessLicensePath").value(hasItem(DEFAULT_BUSINESS_LICENSE_PATH)))
            .andExpect(jsonPath("$.[*].legalPerson").value(hasItem(DEFAULT_LEGAL_PERSON)))
            .andExpect(jsonPath("$.[*].legalPersonId").value(hasItem(DEFAULT_LEGAL_PERSON_ID)))
            .andExpect(jsonPath("$.[*].metadata").value(hasItem(DEFAULT_METADATA)))
            .andExpect(jsonPath("$.[*].version").value(hasItem(DEFAULT_VERSION)))
            .andExpect(jsonPath("$.[*].createdBy").value(hasItem(DEFAULT_CREATED_BY)))
            .andExpect(jsonPath("$.[*].createdAt").value(hasItem(DEFAULT_CREATED_AT.toString())))
            .andExpect(jsonPath("$.[*].updatedBy").value(hasItem(DEFAULT_UPDATED_BY)))
            .andExpect(jsonPath("$.[*].updatedAt").value(hasItem(DEFAULT_UPDATED_AT.toString())))
            .andExpect(jsonPath("$.[*].isDeleted").value(hasItem(DEFAULT_IS_DELETED)));
    }

    @Test
    @Transactional
    void getTenantProfile() throws Exception {
        // Initialize the database
        insertedTenantProfile = tenantProfileRepository.saveAndFlush(tenantProfile);

        // Get the tenantProfile
        restTenantProfileMockMvc
            .perform(get(ENTITY_API_URL_ID, tenantProfile.getId()))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.id").value(tenantProfile.getId().intValue()))
            .andExpect(jsonPath("$.registrationNumber").value(DEFAULT_REGISTRATION_NUMBER))
            .andExpect(jsonPath("$.registrationDate").value(DEFAULT_REGISTRATION_DATE.toString()))
            .andExpect(jsonPath("$.registeredCapital").value(sameNumber(DEFAULT_REGISTERED_CAPITAL)))
            .andExpect(jsonPath("$.companyType").value(DEFAULT_COMPANY_TYPE))
            .andExpect(jsonPath("$.businessScope").value(DEFAULT_BUSINESS_SCOPE))
            .andExpect(jsonPath("$.industry").value(DEFAULT_INDUSTRY))
            .andExpect(jsonPath("$.taxRegistrationNumber").value(DEFAULT_TAX_REGISTRATION_NUMBER))
            .andExpect(jsonPath("$.organizationCode").value(DEFAULT_ORGANIZATION_CODE))
            .andExpect(jsonPath("$.registeredAddress").value(DEFAULT_REGISTERED_ADDRESS))
            .andExpect(jsonPath("$.postalCode").value(DEFAULT_POSTAL_CODE))
            .andExpect(jsonPath("$.website").value(DEFAULT_WEBSITE))
            .andExpect(jsonPath("$.fax").value(DEFAULT_FAX))
            .andExpect(jsonPath("$.contactPerson").value(DEFAULT_CONTACT_PERSON))
            .andExpect(jsonPath("$.contactMobile").value(DEFAULT_CONTACT_MOBILE))
            .andExpect(jsonPath("$.contactEmail").value(DEFAULT_CONTACT_EMAIL))
            .andExpect(jsonPath("$.bankName").value(DEFAULT_BANK_NAME))
            .andExpect(jsonPath("$.bankAccount").value(DEFAULT_BANK_ACCOUNT))
            .andExpect(jsonPath("$.businessLicensePath").value(DEFAULT_BUSINESS_LICENSE_PATH))
            .andExpect(jsonPath("$.legalPerson").value(DEFAULT_LEGAL_PERSON))
            .andExpect(jsonPath("$.legalPersonId").value(DEFAULT_LEGAL_PERSON_ID))
            .andExpect(jsonPath("$.metadata").value(DEFAULT_METADATA))
            .andExpect(jsonPath("$.version").value(DEFAULT_VERSION))
            .andExpect(jsonPath("$.createdBy").value(DEFAULT_CREATED_BY))
            .andExpect(jsonPath("$.createdAt").value(DEFAULT_CREATED_AT.toString()))
            .andExpect(jsonPath("$.updatedBy").value(DEFAULT_UPDATED_BY))
            .andExpect(jsonPath("$.updatedAt").value(DEFAULT_UPDATED_AT.toString()))
            .andExpect(jsonPath("$.isDeleted").value(DEFAULT_IS_DELETED));
    }

    @Test
    @Transactional
    void getNonExistingTenantProfile() throws Exception {
        // Get the tenantProfile
        restTenantProfileMockMvc.perform(get(ENTITY_API_URL_ID, Long.MAX_VALUE)).andExpect(status().isNotFound());
    }

    @Test
    @Transactional
    void putExistingTenantProfile() throws Exception {
        // Initialize the database
        insertedTenantProfile = tenantProfileRepository.saveAndFlush(tenantProfile);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the tenantProfile
        TenantProfile updatedTenantProfile = tenantProfileRepository.findById(tenantProfile.getId()).orElseThrow();
        // Disconnect from session so that the updates on updatedTenantProfile are not directly saved in db
        em.detach(updatedTenantProfile);
        updatedTenantProfile
            .registrationNumber(UPDATED_REGISTRATION_NUMBER)
            .registrationDate(UPDATED_REGISTRATION_DATE)
            .registeredCapital(UPDATED_REGISTERED_CAPITAL)
            .companyType(UPDATED_COMPANY_TYPE)
            .businessScope(UPDATED_BUSINESS_SCOPE)
            .industry(UPDATED_INDUSTRY)
            .taxRegistrationNumber(UPDATED_TAX_REGISTRATION_NUMBER)
            .organizationCode(UPDATED_ORGANIZATION_CODE)
            .registeredAddress(UPDATED_REGISTERED_ADDRESS)
            .postalCode(UPDATED_POSTAL_CODE)
            .website(UPDATED_WEBSITE)
            .fax(UPDATED_FAX)
            .contactPerson(UPDATED_CONTACT_PERSON)
            .contactMobile(UPDATED_CONTACT_MOBILE)
            .contactEmail(UPDATED_CONTACT_EMAIL)
            .bankName(UPDATED_BANK_NAME)
            .bankAccount(UPDATED_BANK_ACCOUNT)
            .businessLicensePath(UPDATED_BUSINESS_LICENSE_PATH)
            .legalPerson(UPDATED_LEGAL_PERSON)
            .legalPersonId(UPDATED_LEGAL_PERSON_ID)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);
        TenantProfileDTO tenantProfileDTO = tenantProfileMapper.toDto(updatedTenantProfile);

        restTenantProfileMockMvc
            .perform(
                put(ENTITY_API_URL_ID, tenantProfileDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(tenantProfileDTO))
            )
            .andExpect(status().isOk());

        // Validate the TenantProfile in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertPersistedTenantProfileToMatchAllProperties(updatedTenantProfile);
    }

    @Test
    @Transactional
    void putNonExistingTenantProfile() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        tenantProfile.setId(longCount.incrementAndGet());

        // Create the TenantProfile
        TenantProfileDTO tenantProfileDTO = tenantProfileMapper.toDto(tenantProfile);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restTenantProfileMockMvc
            .perform(
                put(ENTITY_API_URL_ID, tenantProfileDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(tenantProfileDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the TenantProfile in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithIdMismatchTenantProfile() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        tenantProfile.setId(longCount.incrementAndGet());

        // Create the TenantProfile
        TenantProfileDTO tenantProfileDTO = tenantProfileMapper.toDto(tenantProfile);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restTenantProfileMockMvc
            .perform(
                put(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(tenantProfileDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the TenantProfile in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithMissingIdPathParamTenantProfile() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        tenantProfile.setId(longCount.incrementAndGet());

        // Create the TenantProfile
        TenantProfileDTO tenantProfileDTO = tenantProfileMapper.toDto(tenantProfile);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restTenantProfileMockMvc
            .perform(put(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(tenantProfileDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the TenantProfile in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void partialUpdateTenantProfileWithPatch() throws Exception {
        // Initialize the database
        insertedTenantProfile = tenantProfileRepository.saveAndFlush(tenantProfile);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the tenantProfile using partial update
        TenantProfile partialUpdatedTenantProfile = new TenantProfile();
        partialUpdatedTenantProfile.setId(tenantProfile.getId());

        partialUpdatedTenantProfile
            .registrationNumber(UPDATED_REGISTRATION_NUMBER)
            .registrationDate(UPDATED_REGISTRATION_DATE)
            .businessScope(UPDATED_BUSINESS_SCOPE)
            .industry(UPDATED_INDUSTRY)
            .taxRegistrationNumber(UPDATED_TAX_REGISTRATION_NUMBER)
            .website(UPDATED_WEBSITE)
            .fax(UPDATED_FAX)
            .contactPerson(UPDATED_CONTACT_PERSON)
            .contactMobile(UPDATED_CONTACT_MOBILE)
            .contactEmail(UPDATED_CONTACT_EMAIL)
            .bankName(UPDATED_BANK_NAME)
            .bankAccount(UPDATED_BANK_ACCOUNT)
            .businessLicensePath(UPDATED_BUSINESS_LICENSE_PATH)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .createdAt(UPDATED_CREATED_AT);

        restTenantProfileMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedTenantProfile.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedTenantProfile))
            )
            .andExpect(status().isOk());

        // Validate the TenantProfile in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertTenantProfileUpdatableFieldsEquals(
            createUpdateProxyForBean(partialUpdatedTenantProfile, tenantProfile),
            getPersistedTenantProfile(tenantProfile)
        );
    }

    @Test
    @Transactional
    void fullUpdateTenantProfileWithPatch() throws Exception {
        // Initialize the database
        insertedTenantProfile = tenantProfileRepository.saveAndFlush(tenantProfile);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the tenantProfile using partial update
        TenantProfile partialUpdatedTenantProfile = new TenantProfile();
        partialUpdatedTenantProfile.setId(tenantProfile.getId());

        partialUpdatedTenantProfile
            .registrationNumber(UPDATED_REGISTRATION_NUMBER)
            .registrationDate(UPDATED_REGISTRATION_DATE)
            .registeredCapital(UPDATED_REGISTERED_CAPITAL)
            .companyType(UPDATED_COMPANY_TYPE)
            .businessScope(UPDATED_BUSINESS_SCOPE)
            .industry(UPDATED_INDUSTRY)
            .taxRegistrationNumber(UPDATED_TAX_REGISTRATION_NUMBER)
            .organizationCode(UPDATED_ORGANIZATION_CODE)
            .registeredAddress(UPDATED_REGISTERED_ADDRESS)
            .postalCode(UPDATED_POSTAL_CODE)
            .website(UPDATED_WEBSITE)
            .fax(UPDATED_FAX)
            .contactPerson(UPDATED_CONTACT_PERSON)
            .contactMobile(UPDATED_CONTACT_MOBILE)
            .contactEmail(UPDATED_CONTACT_EMAIL)
            .bankName(UPDATED_BANK_NAME)
            .bankAccount(UPDATED_BANK_ACCOUNT)
            .businessLicensePath(UPDATED_BUSINESS_LICENSE_PATH)
            .legalPerson(UPDATED_LEGAL_PERSON)
            .legalPersonId(UPDATED_LEGAL_PERSON_ID)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);

        restTenantProfileMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedTenantProfile.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedTenantProfile))
            )
            .andExpect(status().isOk());

        // Validate the TenantProfile in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertTenantProfileUpdatableFieldsEquals(partialUpdatedTenantProfile, getPersistedTenantProfile(partialUpdatedTenantProfile));
    }

    @Test
    @Transactional
    void patchNonExistingTenantProfile() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        tenantProfile.setId(longCount.incrementAndGet());

        // Create the TenantProfile
        TenantProfileDTO tenantProfileDTO = tenantProfileMapper.toDto(tenantProfile);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restTenantProfileMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, tenantProfileDTO.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(tenantProfileDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the TenantProfile in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithIdMismatchTenantProfile() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        tenantProfile.setId(longCount.incrementAndGet());

        // Create the TenantProfile
        TenantProfileDTO tenantProfileDTO = tenantProfileMapper.toDto(tenantProfile);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restTenantProfileMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(tenantProfileDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the TenantProfile in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithMissingIdPathParamTenantProfile() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        tenantProfile.setId(longCount.incrementAndGet());

        // Create the TenantProfile
        TenantProfileDTO tenantProfileDTO = tenantProfileMapper.toDto(tenantProfile);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restTenantProfileMockMvc
            .perform(patch(ENTITY_API_URL).contentType("application/merge-patch+json").content(om.writeValueAsBytes(tenantProfileDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the TenantProfile in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void deleteTenantProfile() throws Exception {
        // Initialize the database
        insertedTenantProfile = tenantProfileRepository.saveAndFlush(tenantProfile);

        long databaseSizeBeforeDelete = getRepositoryCount();

        // Delete the tenantProfile
        restTenantProfileMockMvc
            .perform(delete(ENTITY_API_URL_ID, tenantProfile.getId()).accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isNoContent());

        // Validate the database contains one less item
        assertDecrementedRepositoryCount(databaseSizeBeforeDelete);
    }

    protected long getRepositoryCount() {
        return tenantProfileRepository.count();
    }

    protected void assertIncrementedRepositoryCount(long countBefore) {
        assertThat(countBefore + 1).isEqualTo(getRepositoryCount());
    }

    protected void assertDecrementedRepositoryCount(long countBefore) {
        assertThat(countBefore - 1).isEqualTo(getRepositoryCount());
    }

    protected void assertSameRepositoryCount(long countBefore) {
        assertThat(countBefore).isEqualTo(getRepositoryCount());
    }

    protected TenantProfile getPersistedTenantProfile(TenantProfile tenantProfile) {
        return tenantProfileRepository.findById(tenantProfile.getId()).orElseThrow();
    }

    protected void assertPersistedTenantProfileToMatchAllProperties(TenantProfile expectedTenantProfile) {
        assertTenantProfileAllPropertiesEquals(expectedTenantProfile, getPersistedTenantProfile(expectedTenantProfile));
    }

    protected void assertPersistedTenantProfileToMatchUpdatableProperties(TenantProfile expectedTenantProfile) {
        assertTenantProfileAllUpdatablePropertiesEquals(expectedTenantProfile, getPersistedTenantProfile(expectedTenantProfile));
    }
}

package com.whiskerguard.organization.web.rest;

import static com.whiskerguard.organization.domain.RiskModelAsserts.*;
import static com.whiskerguard.organization.web.rest.TestUtil.createUpdateProxyForBean;
import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.hasItem;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.whiskerguard.organization.IntegrationTest;
import com.whiskerguard.organization.domain.RiskModel;
import com.whiskerguard.organization.repository.RiskModelRepository;
import com.whiskerguard.organization.service.dto.RiskModelDTO;
import com.whiskerguard.organization.service.mapper.RiskModelMapper;
import jakarta.persistence.EntityManager;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Random;
import java.util.concurrent.atomic.AtomicLong;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

/**
 * Integration tests for the {@link RiskModelResource} REST controller.
 */
@IntegrationTest
@AutoConfigureMockMvc
@WithMockUser
class RiskModelResourceIT {

    private static final Long DEFAULT_TENANT_ID = 1L;
    private static final Long UPDATED_TENANT_ID = 2L;

    private static final String DEFAULT_NAME = "AAAAAAAAAA";
    private static final String UPDATED_NAME = "BBBBBBBBBB";

    private static final String DEFAULT_DESCRIPTION = "AAAAAAAAAA";
    private static final String UPDATED_DESCRIPTION = "BBBBBBBBBB";

    private static final Instant DEFAULT_EFFECTIVE_FROM = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_EFFECTIVE_FROM = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final Instant DEFAULT_EFFECTIVE_TO = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_EFFECTIVE_TO = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final Boolean DEFAULT_IS_DEFAULT = false;
    private static final Boolean UPDATED_IS_DEFAULT = true;

    private static final Integer DEFAULT_VERSION = 1;
    private static final Integer UPDATED_VERSION = 2;

    private static final String DEFAULT_METADATA = "AAAAAAAAAA";
    private static final String UPDATED_METADATA = "BBBBBBBBBB";

    private static final String DEFAULT_CREATED_BY = "AAAAAAAAAA";
    private static final String UPDATED_CREATED_BY = "BBBBBBBBBB";

    private static final Instant DEFAULT_CREATED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_CREATED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final String DEFAULT_UPDATED_BY = "AAAAAAAAAA";
    private static final String UPDATED_UPDATED_BY = "BBBBBBBBBB";

    private static final Instant DEFAULT_UPDATED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_UPDATED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final Boolean DEFAULT_IS_DELETED = false;
    private static final Boolean UPDATED_IS_DELETED = true;

    private static final String ENTITY_API_URL = "/api/risk-models";
    private static final String ENTITY_API_URL_ID = ENTITY_API_URL + "/{id}";

    private static Random random = new Random();
    private static AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));

    @Autowired
    private ObjectMapper om;

    @Autowired
    private RiskModelRepository riskModelRepository;

    @Autowired
    private RiskModelMapper riskModelMapper;

    @Autowired
    private EntityManager em;

    @Autowired
    private MockMvc restRiskModelMockMvc;

    private RiskModel riskModel;

    private RiskModel insertedRiskModel;

    /**
     * Create an entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static RiskModel createEntity() {
        return new RiskModel()
            .tenantId(DEFAULT_TENANT_ID)
            .name(DEFAULT_NAME)
            .description(DEFAULT_DESCRIPTION)
            .effectiveFrom(DEFAULT_EFFECTIVE_FROM)
            .effectiveTo(DEFAULT_EFFECTIVE_TO)
            .isDefault(DEFAULT_IS_DEFAULT)
            .version(DEFAULT_VERSION)
            .metadata(DEFAULT_METADATA)
            .createdBy(DEFAULT_CREATED_BY)
            .createdAt(DEFAULT_CREATED_AT)
            .updatedBy(DEFAULT_UPDATED_BY)
            .updatedAt(DEFAULT_UPDATED_AT)
            .isDeleted(DEFAULT_IS_DELETED);
    }

    /**
     * Create an updated entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static RiskModel createUpdatedEntity() {
        return new RiskModel()
            .tenantId(UPDATED_TENANT_ID)
            .name(UPDATED_NAME)
            .description(UPDATED_DESCRIPTION)
            .effectiveFrom(UPDATED_EFFECTIVE_FROM)
            .effectiveTo(UPDATED_EFFECTIVE_TO)
            .isDefault(UPDATED_IS_DEFAULT)
            .version(UPDATED_VERSION)
            .metadata(UPDATED_METADATA)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);
    }

    @BeforeEach
    void initTest() {
        riskModel = createEntity();
    }

    @AfterEach
    void cleanup() {
        if (insertedRiskModel != null) {
            riskModelRepository.delete(insertedRiskModel);
            insertedRiskModel = null;
        }
    }

    @Test
    @Transactional
    void createRiskModel() throws Exception {
        long databaseSizeBeforeCreate = getRepositoryCount();
        // Create the RiskModel
        RiskModelDTO riskModelDTO = riskModelMapper.toDto(riskModel);
        var returnedRiskModelDTO = om.readValue(
            restRiskModelMockMvc
                .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(riskModelDTO)))
                .andExpect(status().isCreated())
                .andReturn()
                .getResponse()
                .getContentAsString(),
            RiskModelDTO.class
        );

        // Validate the RiskModel in the database
        assertIncrementedRepositoryCount(databaseSizeBeforeCreate);
        var returnedRiskModel = riskModelMapper.toEntity(returnedRiskModelDTO);
        assertRiskModelUpdatableFieldsEquals(returnedRiskModel, getPersistedRiskModel(returnedRiskModel));

        insertedRiskModel = returnedRiskModel;
    }

    @Test
    @Transactional
    void createRiskModelWithExistingId() throws Exception {
        // Create the RiskModel with an existing ID
        riskModel.setId(1L);
        RiskModelDTO riskModelDTO = riskModelMapper.toDto(riskModel);

        long databaseSizeBeforeCreate = getRepositoryCount();

        // An entity with an existing ID cannot be created, so this API call must fail
        restRiskModelMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(riskModelDTO)))
            .andExpect(status().isBadRequest());

        // Validate the RiskModel in the database
        assertSameRepositoryCount(databaseSizeBeforeCreate);
    }

    @Test
    @Transactional
    void checkTenantIdIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        riskModel.setTenantId(null);

        // Create the RiskModel, which fails.
        RiskModelDTO riskModelDTO = riskModelMapper.toDto(riskModel);

        restRiskModelMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(riskModelDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkNameIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        riskModel.setName(null);

        // Create the RiskModel, which fails.
        RiskModelDTO riskModelDTO = riskModelMapper.toDto(riskModel);

        restRiskModelMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(riskModelDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkEffectiveFromIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        riskModel.setEffectiveFrom(null);

        // Create the RiskModel, which fails.
        RiskModelDTO riskModelDTO = riskModelMapper.toDto(riskModel);

        restRiskModelMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(riskModelDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkIsDefaultIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        riskModel.setIsDefault(null);

        // Create the RiskModel, which fails.
        RiskModelDTO riskModelDTO = riskModelMapper.toDto(riskModel);

        restRiskModelMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(riskModelDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkVersionIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        riskModel.setVersion(null);

        // Create the RiskModel, which fails.
        RiskModelDTO riskModelDTO = riskModelMapper.toDto(riskModel);

        restRiskModelMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(riskModelDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkCreatedAtIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        riskModel.setCreatedAt(null);

        // Create the RiskModel, which fails.
        RiskModelDTO riskModelDTO = riskModelMapper.toDto(riskModel);

        restRiskModelMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(riskModelDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkUpdatedAtIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        riskModel.setUpdatedAt(null);

        // Create the RiskModel, which fails.
        RiskModelDTO riskModelDTO = riskModelMapper.toDto(riskModel);

        restRiskModelMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(riskModelDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkIsDeletedIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        riskModel.setIsDeleted(null);

        // Create the RiskModel, which fails.
        RiskModelDTO riskModelDTO = riskModelMapper.toDto(riskModel);

        restRiskModelMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(riskModelDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void getAllRiskModels() throws Exception {
        // Initialize the database
        insertedRiskModel = riskModelRepository.saveAndFlush(riskModel);

        // Get all the riskModelList
        restRiskModelMockMvc
            .perform(get(ENTITY_API_URL + "?sort=id,desc"))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.[*].id").value(hasItem(riskModel.getId().intValue())))
            .andExpect(jsonPath("$.[*].tenantId").value(hasItem(DEFAULT_TENANT_ID.intValue())))
            .andExpect(jsonPath("$.[*].name").value(hasItem(DEFAULT_NAME)))
            .andExpect(jsonPath("$.[*].description").value(hasItem(DEFAULT_DESCRIPTION)))
            .andExpect(jsonPath("$.[*].effectiveFrom").value(hasItem(DEFAULT_EFFECTIVE_FROM.toString())))
            .andExpect(jsonPath("$.[*].effectiveTo").value(hasItem(DEFAULT_EFFECTIVE_TO.toString())))
            .andExpect(jsonPath("$.[*].isDefault").value(hasItem(DEFAULT_IS_DEFAULT)))
            .andExpect(jsonPath("$.[*].version").value(hasItem(DEFAULT_VERSION)))
            .andExpect(jsonPath("$.[*].metadata").value(hasItem(DEFAULT_METADATA)))
            .andExpect(jsonPath("$.[*].createdBy").value(hasItem(DEFAULT_CREATED_BY)))
            .andExpect(jsonPath("$.[*].createdAt").value(hasItem(DEFAULT_CREATED_AT.toString())))
            .andExpect(jsonPath("$.[*].updatedBy").value(hasItem(DEFAULT_UPDATED_BY)))
            .andExpect(jsonPath("$.[*].updatedAt").value(hasItem(DEFAULT_UPDATED_AT.toString())))
            .andExpect(jsonPath("$.[*].isDeleted").value(hasItem(DEFAULT_IS_DELETED)));
    }

    @Test
    @Transactional
    void getRiskModel() throws Exception {
        // Initialize the database
        insertedRiskModel = riskModelRepository.saveAndFlush(riskModel);

        // Get the riskModel
        restRiskModelMockMvc
            .perform(get(ENTITY_API_URL_ID, riskModel.getId()))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.id").value(riskModel.getId().intValue()))
            .andExpect(jsonPath("$.tenantId").value(DEFAULT_TENANT_ID.intValue()))
            .andExpect(jsonPath("$.name").value(DEFAULT_NAME))
            .andExpect(jsonPath("$.description").value(DEFAULT_DESCRIPTION))
            .andExpect(jsonPath("$.effectiveFrom").value(DEFAULT_EFFECTIVE_FROM.toString()))
            .andExpect(jsonPath("$.effectiveTo").value(DEFAULT_EFFECTIVE_TO.toString()))
            .andExpect(jsonPath("$.isDefault").value(DEFAULT_IS_DEFAULT))
            .andExpect(jsonPath("$.version").value(DEFAULT_VERSION))
            .andExpect(jsonPath("$.metadata").value(DEFAULT_METADATA))
            .andExpect(jsonPath("$.createdBy").value(DEFAULT_CREATED_BY))
            .andExpect(jsonPath("$.createdAt").value(DEFAULT_CREATED_AT.toString()))
            .andExpect(jsonPath("$.updatedBy").value(DEFAULT_UPDATED_BY))
            .andExpect(jsonPath("$.updatedAt").value(DEFAULT_UPDATED_AT.toString()))
            .andExpect(jsonPath("$.isDeleted").value(DEFAULT_IS_DELETED));
    }

    @Test
    @Transactional
    void getNonExistingRiskModel() throws Exception {
        // Get the riskModel
        restRiskModelMockMvc.perform(get(ENTITY_API_URL_ID, Long.MAX_VALUE)).andExpect(status().isNotFound());
    }

    @Test
    @Transactional
    void putExistingRiskModel() throws Exception {
        // Initialize the database
        insertedRiskModel = riskModelRepository.saveAndFlush(riskModel);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the riskModel
        RiskModel updatedRiskModel = riskModelRepository.findById(riskModel.getId()).orElseThrow();
        // Disconnect from session so that the updates on updatedRiskModel are not directly saved in db
        em.detach(updatedRiskModel);
        updatedRiskModel
            .tenantId(UPDATED_TENANT_ID)
            .name(UPDATED_NAME)
            .description(UPDATED_DESCRIPTION)
            .effectiveFrom(UPDATED_EFFECTIVE_FROM)
            .effectiveTo(UPDATED_EFFECTIVE_TO)
            .isDefault(UPDATED_IS_DEFAULT)
            .version(UPDATED_VERSION)
            .metadata(UPDATED_METADATA)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);
        RiskModelDTO riskModelDTO = riskModelMapper.toDto(updatedRiskModel);

        restRiskModelMockMvc
            .perform(
                put(ENTITY_API_URL_ID, riskModelDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(riskModelDTO))
            )
            .andExpect(status().isOk());

        // Validate the RiskModel in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertPersistedRiskModelToMatchAllProperties(updatedRiskModel);
    }

    @Test
    @Transactional
    void putNonExistingRiskModel() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        riskModel.setId(longCount.incrementAndGet());

        // Create the RiskModel
        RiskModelDTO riskModelDTO = riskModelMapper.toDto(riskModel);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restRiskModelMockMvc
            .perform(
                put(ENTITY_API_URL_ID, riskModelDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(riskModelDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the RiskModel in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithIdMismatchRiskModel() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        riskModel.setId(longCount.incrementAndGet());

        // Create the RiskModel
        RiskModelDTO riskModelDTO = riskModelMapper.toDto(riskModel);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restRiskModelMockMvc
            .perform(
                put(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(riskModelDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the RiskModel in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithMissingIdPathParamRiskModel() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        riskModel.setId(longCount.incrementAndGet());

        // Create the RiskModel
        RiskModelDTO riskModelDTO = riskModelMapper.toDto(riskModel);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restRiskModelMockMvc
            .perform(put(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(riskModelDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the RiskModel in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void partialUpdateRiskModelWithPatch() throws Exception {
        // Initialize the database
        insertedRiskModel = riskModelRepository.saveAndFlush(riskModel);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the riskModel using partial update
        RiskModel partialUpdatedRiskModel = new RiskModel();
        partialUpdatedRiskModel.setId(riskModel.getId());

        partialUpdatedRiskModel
            .tenantId(UPDATED_TENANT_ID)
            .description(UPDATED_DESCRIPTION)
            .effectiveFrom(UPDATED_EFFECTIVE_FROM)
            .effectiveTo(UPDATED_EFFECTIVE_TO)
            .version(UPDATED_VERSION)
            .metadata(UPDATED_METADATA)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedAt(UPDATED_UPDATED_AT);

        restRiskModelMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedRiskModel.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedRiskModel))
            )
            .andExpect(status().isOk());

        // Validate the RiskModel in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertRiskModelUpdatableFieldsEquals(
            createUpdateProxyForBean(partialUpdatedRiskModel, riskModel),
            getPersistedRiskModel(riskModel)
        );
    }

    @Test
    @Transactional
    void fullUpdateRiskModelWithPatch() throws Exception {
        // Initialize the database
        insertedRiskModel = riskModelRepository.saveAndFlush(riskModel);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the riskModel using partial update
        RiskModel partialUpdatedRiskModel = new RiskModel();
        partialUpdatedRiskModel.setId(riskModel.getId());

        partialUpdatedRiskModel
            .tenantId(UPDATED_TENANT_ID)
            .name(UPDATED_NAME)
            .description(UPDATED_DESCRIPTION)
            .effectiveFrom(UPDATED_EFFECTIVE_FROM)
            .effectiveTo(UPDATED_EFFECTIVE_TO)
            .isDefault(UPDATED_IS_DEFAULT)
            .version(UPDATED_VERSION)
            .metadata(UPDATED_METADATA)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);

        restRiskModelMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedRiskModel.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedRiskModel))
            )
            .andExpect(status().isOk());

        // Validate the RiskModel in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertRiskModelUpdatableFieldsEquals(partialUpdatedRiskModel, getPersistedRiskModel(partialUpdatedRiskModel));
    }

    @Test
    @Transactional
    void patchNonExistingRiskModel() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        riskModel.setId(longCount.incrementAndGet());

        // Create the RiskModel
        RiskModelDTO riskModelDTO = riskModelMapper.toDto(riskModel);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restRiskModelMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, riskModelDTO.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(riskModelDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the RiskModel in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithIdMismatchRiskModel() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        riskModel.setId(longCount.incrementAndGet());

        // Create the RiskModel
        RiskModelDTO riskModelDTO = riskModelMapper.toDto(riskModel);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restRiskModelMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(riskModelDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the RiskModel in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithMissingIdPathParamRiskModel() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        riskModel.setId(longCount.incrementAndGet());

        // Create the RiskModel
        RiskModelDTO riskModelDTO = riskModelMapper.toDto(riskModel);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restRiskModelMockMvc
            .perform(patch(ENTITY_API_URL).contentType("application/merge-patch+json").content(om.writeValueAsBytes(riskModelDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the RiskModel in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void deleteRiskModel() throws Exception {
        // Initialize the database
        insertedRiskModel = riskModelRepository.saveAndFlush(riskModel);

        long databaseSizeBeforeDelete = getRepositoryCount();

        // Delete the riskModel
        restRiskModelMockMvc
            .perform(delete(ENTITY_API_URL_ID, riskModel.getId()).accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isNoContent());

        // Validate the database contains one less item
        assertDecrementedRepositoryCount(databaseSizeBeforeDelete);
    }

    protected long getRepositoryCount() {
        return riskModelRepository.count();
    }

    protected void assertIncrementedRepositoryCount(long countBefore) {
        assertThat(countBefore + 1).isEqualTo(getRepositoryCount());
    }

    protected void assertDecrementedRepositoryCount(long countBefore) {
        assertThat(countBefore - 1).isEqualTo(getRepositoryCount());
    }

    protected void assertSameRepositoryCount(long countBefore) {
        assertThat(countBefore).isEqualTo(getRepositoryCount());
    }

    protected RiskModel getPersistedRiskModel(RiskModel riskModel) {
        return riskModelRepository.findById(riskModel.getId()).orElseThrow();
    }

    protected void assertPersistedRiskModelToMatchAllProperties(RiskModel expectedRiskModel) {
        assertRiskModelAllPropertiesEquals(expectedRiskModel, getPersistedRiskModel(expectedRiskModel));
    }

    protected void assertPersistedRiskModelToMatchUpdatableProperties(RiskModel expectedRiskModel) {
        assertRiskModelAllUpdatablePropertiesEquals(expectedRiskModel, getPersistedRiskModel(expectedRiskModel));
    }
}

package com.whiskerguard.organization.web.rest;

import static com.whiskerguard.organization.domain.EmployeeOrgAsserts.*;
import static com.whiskerguard.organization.web.rest.TestUtil.createUpdateProxyForBean;
import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.hasItem;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.whiskerguard.organization.IntegrationTest;
import com.whiskerguard.organization.domain.EmployeeOrg;
import com.whiskerguard.organization.repository.EmployeeOrgRepository;
import com.whiskerguard.organization.service.dto.EmployeeOrgDTO;
import com.whiskerguard.organization.service.mapper.EmployeeOrgMapper;
import jakarta.persistence.EntityManager;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.Random;
import java.util.concurrent.atomic.AtomicLong;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

/**
 * Integration tests for the {@link EmployeeOrgResource} REST controller.
 */
@IntegrationTest
@AutoConfigureMockMvc
@WithMockUser
class EmployeeOrgResourceIT {

    private static final Long DEFAULT_TENANT_ID = 1L;
    private static final Long UPDATED_TENANT_ID = 2L;

    private static final LocalDate DEFAULT_START_DATE = LocalDate.ofEpochDay(0L);
    private static final LocalDate UPDATED_START_DATE = LocalDate.now(ZoneId.systemDefault());

    private static final LocalDate DEFAULT_END_DATE = LocalDate.ofEpochDay(0L);
    private static final LocalDate UPDATED_END_DATE = LocalDate.now(ZoneId.systemDefault());

    private static final Boolean DEFAULT_IS_PRIMARY = false;
    private static final Boolean UPDATED_IS_PRIMARY = true;

    private static final Integer DEFAULT_VERSION = 1;
    private static final Integer UPDATED_VERSION = 2;

    private static final String DEFAULT_CREATED_BY = "AAAAAAAAAA";
    private static final String UPDATED_CREATED_BY = "BBBBBBBBBB";

    private static final Instant DEFAULT_CREATED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_CREATED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final String DEFAULT_UPDATED_BY = "AAAAAAAAAA";
    private static final String UPDATED_UPDATED_BY = "BBBBBBBBBB";

    private static final Instant DEFAULT_UPDATED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_UPDATED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final Boolean DEFAULT_IS_DELETED = false;
    private static final Boolean UPDATED_IS_DELETED = true;

    private static final String ENTITY_API_URL = "/api/employee-orgs";
    private static final String ENTITY_API_URL_ID = ENTITY_API_URL + "/{id}";

    private static Random random = new Random();
    private static AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));

    @Autowired
    private ObjectMapper om;

    @Autowired
    private EmployeeOrgRepository employeeOrgRepository;

    @Autowired
    private EmployeeOrgMapper employeeOrgMapper;

    @Autowired
    private EntityManager em;

    @Autowired
    private MockMvc restEmployeeOrgMockMvc;

    private EmployeeOrg employeeOrg;

    private EmployeeOrg insertedEmployeeOrg;

    /**
     * Create an entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static EmployeeOrg createEntity() {
        return new EmployeeOrg()
            .tenantId(DEFAULT_TENANT_ID)
            .startDate(DEFAULT_START_DATE)
            .endDate(DEFAULT_END_DATE)
            .isPrimary(DEFAULT_IS_PRIMARY)
            .version(DEFAULT_VERSION)
            .createdBy(DEFAULT_CREATED_BY)
            .createdAt(DEFAULT_CREATED_AT)
            .updatedBy(DEFAULT_UPDATED_BY)
            .updatedAt(DEFAULT_UPDATED_AT)
            .isDeleted(DEFAULT_IS_DELETED);
    }

    /**
     * Create an updated entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static EmployeeOrg createUpdatedEntity() {
        return new EmployeeOrg()
            .tenantId(UPDATED_TENANT_ID)
            .startDate(UPDATED_START_DATE)
            .endDate(UPDATED_END_DATE)
            .isPrimary(UPDATED_IS_PRIMARY)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);
    }

    @BeforeEach
    void initTest() {
        employeeOrg = createEntity();
    }

    @AfterEach
    void cleanup() {
        if (insertedEmployeeOrg != null) {
            employeeOrgRepository.delete(insertedEmployeeOrg);
            insertedEmployeeOrg = null;
        }
    }

    @Test
    @Transactional
    void createEmployeeOrg() throws Exception {
        long databaseSizeBeforeCreate = getRepositoryCount();
        // Create the EmployeeOrg
        EmployeeOrgDTO employeeOrgDTO = employeeOrgMapper.toDto(employeeOrg);
        var returnedEmployeeOrgDTO = om.readValue(
            restEmployeeOrgMockMvc
                .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(employeeOrgDTO)))
                .andExpect(status().isCreated())
                .andReturn()
                .getResponse()
                .getContentAsString(),
            EmployeeOrgDTO.class
        );

        // Validate the EmployeeOrg in the database
        assertIncrementedRepositoryCount(databaseSizeBeforeCreate);
        var returnedEmployeeOrg = employeeOrgMapper.toEntity(returnedEmployeeOrgDTO);
        assertEmployeeOrgUpdatableFieldsEquals(returnedEmployeeOrg, getPersistedEmployeeOrg(returnedEmployeeOrg));

        insertedEmployeeOrg = returnedEmployeeOrg;
    }

    @Test
    @Transactional
    void createEmployeeOrgWithExistingId() throws Exception {
        // Create the EmployeeOrg with an existing ID
        employeeOrg.setId(1L);
        EmployeeOrgDTO employeeOrgDTO = employeeOrgMapper.toDto(employeeOrg);

        long databaseSizeBeforeCreate = getRepositoryCount();

        // An entity with an existing ID cannot be created, so this API call must fail
        restEmployeeOrgMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(employeeOrgDTO)))
            .andExpect(status().isBadRequest());

        // Validate the EmployeeOrg in the database
        assertSameRepositoryCount(databaseSizeBeforeCreate);
    }

    @Test
    @Transactional
    void checkTenantIdIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        employeeOrg.setTenantId(null);

        // Create the EmployeeOrg, which fails.
        EmployeeOrgDTO employeeOrgDTO = employeeOrgMapper.toDto(employeeOrg);

        restEmployeeOrgMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(employeeOrgDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkIsPrimaryIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        employeeOrg.setIsPrimary(null);

        // Create the EmployeeOrg, which fails.
        EmployeeOrgDTO employeeOrgDTO = employeeOrgMapper.toDto(employeeOrg);

        restEmployeeOrgMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(employeeOrgDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkVersionIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        employeeOrg.setVersion(null);

        // Create the EmployeeOrg, which fails.
        EmployeeOrgDTO employeeOrgDTO = employeeOrgMapper.toDto(employeeOrg);

        restEmployeeOrgMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(employeeOrgDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkCreatedAtIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        employeeOrg.setCreatedAt(null);

        // Create the EmployeeOrg, which fails.
        EmployeeOrgDTO employeeOrgDTO = employeeOrgMapper.toDto(employeeOrg);

        restEmployeeOrgMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(employeeOrgDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkUpdatedAtIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        employeeOrg.setUpdatedAt(null);

        // Create the EmployeeOrg, which fails.
        EmployeeOrgDTO employeeOrgDTO = employeeOrgMapper.toDto(employeeOrg);

        restEmployeeOrgMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(employeeOrgDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkIsDeletedIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        employeeOrg.setIsDeleted(null);

        // Create the EmployeeOrg, which fails.
        EmployeeOrgDTO employeeOrgDTO = employeeOrgMapper.toDto(employeeOrg);

        restEmployeeOrgMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(employeeOrgDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void getAllEmployeeOrgs() throws Exception {
        // Initialize the database
        insertedEmployeeOrg = employeeOrgRepository.saveAndFlush(employeeOrg);

        // Get all the employeeOrgList
        restEmployeeOrgMockMvc
            .perform(get(ENTITY_API_URL + "?sort=id,desc"))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.[*].id").value(hasItem(employeeOrg.getId().intValue())))
            .andExpect(jsonPath("$.[*].tenantId").value(hasItem(DEFAULT_TENANT_ID.intValue())))
            .andExpect(jsonPath("$.[*].startDate").value(hasItem(DEFAULT_START_DATE.toString())))
            .andExpect(jsonPath("$.[*].endDate").value(hasItem(DEFAULT_END_DATE.toString())))
            .andExpect(jsonPath("$.[*].isPrimary").value(hasItem(DEFAULT_IS_PRIMARY)))
            .andExpect(jsonPath("$.[*].version").value(hasItem(DEFAULT_VERSION)))
            .andExpect(jsonPath("$.[*].createdBy").value(hasItem(DEFAULT_CREATED_BY)))
            .andExpect(jsonPath("$.[*].createdAt").value(hasItem(DEFAULT_CREATED_AT.toString())))
            .andExpect(jsonPath("$.[*].updatedBy").value(hasItem(DEFAULT_UPDATED_BY)))
            .andExpect(jsonPath("$.[*].updatedAt").value(hasItem(DEFAULT_UPDATED_AT.toString())))
            .andExpect(jsonPath("$.[*].isDeleted").value(hasItem(DEFAULT_IS_DELETED)));
    }

    @Test
    @Transactional
    void getEmployeeOrg() throws Exception {
        // Initialize the database
        insertedEmployeeOrg = employeeOrgRepository.saveAndFlush(employeeOrg);

        // Get the employeeOrg
        restEmployeeOrgMockMvc
            .perform(get(ENTITY_API_URL_ID, employeeOrg.getId()))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.id").value(employeeOrg.getId().intValue()))
            .andExpect(jsonPath("$.tenantId").value(DEFAULT_TENANT_ID.intValue()))
            .andExpect(jsonPath("$.startDate").value(DEFAULT_START_DATE.toString()))
            .andExpect(jsonPath("$.endDate").value(DEFAULT_END_DATE.toString()))
            .andExpect(jsonPath("$.isPrimary").value(DEFAULT_IS_PRIMARY))
            .andExpect(jsonPath("$.version").value(DEFAULT_VERSION))
            .andExpect(jsonPath("$.createdBy").value(DEFAULT_CREATED_BY))
            .andExpect(jsonPath("$.createdAt").value(DEFAULT_CREATED_AT.toString()))
            .andExpect(jsonPath("$.updatedBy").value(DEFAULT_UPDATED_BY))
            .andExpect(jsonPath("$.updatedAt").value(DEFAULT_UPDATED_AT.toString()))
            .andExpect(jsonPath("$.isDeleted").value(DEFAULT_IS_DELETED));
    }

    @Test
    @Transactional
    void getNonExistingEmployeeOrg() throws Exception {
        // Get the employeeOrg
        restEmployeeOrgMockMvc.perform(get(ENTITY_API_URL_ID, Long.MAX_VALUE)).andExpect(status().isNotFound());
    }

    @Test
    @Transactional
    void putExistingEmployeeOrg() throws Exception {
        // Initialize the database
        insertedEmployeeOrg = employeeOrgRepository.saveAndFlush(employeeOrg);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the employeeOrg
        EmployeeOrg updatedEmployeeOrg = employeeOrgRepository.findById(employeeOrg.getId()).orElseThrow();
        // Disconnect from session so that the updates on updatedEmployeeOrg are not directly saved in db
        em.detach(updatedEmployeeOrg);
        updatedEmployeeOrg
            .tenantId(UPDATED_TENANT_ID)
            .startDate(UPDATED_START_DATE)
            .endDate(UPDATED_END_DATE)
            .isPrimary(UPDATED_IS_PRIMARY)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);
        EmployeeOrgDTO employeeOrgDTO = employeeOrgMapper.toDto(updatedEmployeeOrg);

        restEmployeeOrgMockMvc
            .perform(
                put(ENTITY_API_URL_ID, employeeOrgDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(employeeOrgDTO))
            )
            .andExpect(status().isOk());

        // Validate the EmployeeOrg in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertPersistedEmployeeOrgToMatchAllProperties(updatedEmployeeOrg);
    }

    @Test
    @Transactional
    void putNonExistingEmployeeOrg() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        employeeOrg.setId(longCount.incrementAndGet());

        // Create the EmployeeOrg
        EmployeeOrgDTO employeeOrgDTO = employeeOrgMapper.toDto(employeeOrg);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restEmployeeOrgMockMvc
            .perform(
                put(ENTITY_API_URL_ID, employeeOrgDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(employeeOrgDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the EmployeeOrg in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithIdMismatchEmployeeOrg() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        employeeOrg.setId(longCount.incrementAndGet());

        // Create the EmployeeOrg
        EmployeeOrgDTO employeeOrgDTO = employeeOrgMapper.toDto(employeeOrg);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restEmployeeOrgMockMvc
            .perform(
                put(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(employeeOrgDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the EmployeeOrg in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithMissingIdPathParamEmployeeOrg() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        employeeOrg.setId(longCount.incrementAndGet());

        // Create the EmployeeOrg
        EmployeeOrgDTO employeeOrgDTO = employeeOrgMapper.toDto(employeeOrg);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restEmployeeOrgMockMvc
            .perform(put(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(employeeOrgDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the EmployeeOrg in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void partialUpdateEmployeeOrgWithPatch() throws Exception {
        // Initialize the database
        insertedEmployeeOrg = employeeOrgRepository.saveAndFlush(employeeOrg);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the employeeOrg using partial update
        EmployeeOrg partialUpdatedEmployeeOrg = new EmployeeOrg();
        partialUpdatedEmployeeOrg.setId(employeeOrg.getId());

        partialUpdatedEmployeeOrg
            .tenantId(UPDATED_TENANT_ID)
            .endDate(UPDATED_END_DATE)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY);

        restEmployeeOrgMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedEmployeeOrg.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedEmployeeOrg))
            )
            .andExpect(status().isOk());

        // Validate the EmployeeOrg in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertEmployeeOrgUpdatableFieldsEquals(
            createUpdateProxyForBean(partialUpdatedEmployeeOrg, employeeOrg),
            getPersistedEmployeeOrg(employeeOrg)
        );
    }

    @Test
    @Transactional
    void fullUpdateEmployeeOrgWithPatch() throws Exception {
        // Initialize the database
        insertedEmployeeOrg = employeeOrgRepository.saveAndFlush(employeeOrg);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the employeeOrg using partial update
        EmployeeOrg partialUpdatedEmployeeOrg = new EmployeeOrg();
        partialUpdatedEmployeeOrg.setId(employeeOrg.getId());

        partialUpdatedEmployeeOrg
            .tenantId(UPDATED_TENANT_ID)
            .startDate(UPDATED_START_DATE)
            .endDate(UPDATED_END_DATE)
            .isPrimary(UPDATED_IS_PRIMARY)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);

        restEmployeeOrgMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedEmployeeOrg.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedEmployeeOrg))
            )
            .andExpect(status().isOk());

        // Validate the EmployeeOrg in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertEmployeeOrgUpdatableFieldsEquals(partialUpdatedEmployeeOrg, getPersistedEmployeeOrg(partialUpdatedEmployeeOrg));
    }

    @Test
    @Transactional
    void patchNonExistingEmployeeOrg() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        employeeOrg.setId(longCount.incrementAndGet());

        // Create the EmployeeOrg
        EmployeeOrgDTO employeeOrgDTO = employeeOrgMapper.toDto(employeeOrg);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restEmployeeOrgMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, employeeOrgDTO.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(employeeOrgDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the EmployeeOrg in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithIdMismatchEmployeeOrg() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        employeeOrg.setId(longCount.incrementAndGet());

        // Create the EmployeeOrg
        EmployeeOrgDTO employeeOrgDTO = employeeOrgMapper.toDto(employeeOrg);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restEmployeeOrgMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(employeeOrgDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the EmployeeOrg in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithMissingIdPathParamEmployeeOrg() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        employeeOrg.setId(longCount.incrementAndGet());

        // Create the EmployeeOrg
        EmployeeOrgDTO employeeOrgDTO = employeeOrgMapper.toDto(employeeOrg);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restEmployeeOrgMockMvc
            .perform(patch(ENTITY_API_URL).contentType("application/merge-patch+json").content(om.writeValueAsBytes(employeeOrgDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the EmployeeOrg in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void deleteEmployeeOrg() throws Exception {
        // Initialize the database
        insertedEmployeeOrg = employeeOrgRepository.saveAndFlush(employeeOrg);

        long databaseSizeBeforeDelete = getRepositoryCount();

        // Delete the employeeOrg
        restEmployeeOrgMockMvc
            .perform(delete(ENTITY_API_URL_ID, employeeOrg.getId()).accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isNoContent());

        // Validate the database contains one less item
        assertDecrementedRepositoryCount(databaseSizeBeforeDelete);
    }

    protected long getRepositoryCount() {
        return employeeOrgRepository.count();
    }

    protected void assertIncrementedRepositoryCount(long countBefore) {
        assertThat(countBefore + 1).isEqualTo(getRepositoryCount());
    }

    protected void assertDecrementedRepositoryCount(long countBefore) {
        assertThat(countBefore - 1).isEqualTo(getRepositoryCount());
    }

    protected void assertSameRepositoryCount(long countBefore) {
        assertThat(countBefore).isEqualTo(getRepositoryCount());
    }

    protected EmployeeOrg getPersistedEmployeeOrg(EmployeeOrg employeeOrg) {
        return employeeOrgRepository.findById(employeeOrg.getId()).orElseThrow();
    }

    protected void assertPersistedEmployeeOrgToMatchAllProperties(EmployeeOrg expectedEmployeeOrg) {
        assertEmployeeOrgAllPropertiesEquals(expectedEmployeeOrg, getPersistedEmployeeOrg(expectedEmployeeOrg));
    }

    protected void assertPersistedEmployeeOrgToMatchUpdatableProperties(EmployeeOrg expectedEmployeeOrg) {
        assertEmployeeOrgAllUpdatablePropertiesEquals(expectedEmployeeOrg, getPersistedEmployeeOrg(expectedEmployeeOrg));
    }
}

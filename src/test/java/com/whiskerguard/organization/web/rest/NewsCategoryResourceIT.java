package com.whiskerguard.organization.web.rest;

import static com.whiskerguard.organization.domain.NewsCategoryAsserts.*;
import static com.whiskerguard.organization.web.rest.TestUtil.createUpdateProxyForBean;
import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.hasItem;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.whiskerguard.organization.IntegrationTest;
import com.whiskerguard.organization.domain.NewsCategory;
import com.whiskerguard.organization.domain.enumeration.NewsStatus;
import com.whiskerguard.organization.repository.NewsCategoryRepository;
import com.whiskerguard.organization.service.NewsCategoryService;
import com.whiskerguard.organization.service.dto.NewsCategoryDTO;
import com.whiskerguard.organization.service.mapper.NewsCategoryMapper;
import jakarta.persistence.EntityManager;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Random;
import java.util.concurrent.atomic.AtomicLong;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

/**
 * Integration tests for the {@link NewsCategoryResource} REST controller.
 */
@IntegrationTest
@ExtendWith(MockitoExtension.class)
@AutoConfigureMockMvc
@WithMockUser
class NewsCategoryResourceIT {

    private static final NewsStatus DEFAULT_STATUS = NewsStatus.DRAFT;
    private static final NewsStatus UPDATED_STATUS = NewsStatus.PENDING_REVIEW;

    private static final Integer DEFAULT_SORT_ORDER = 1;
    private static final Integer UPDATED_SORT_ORDER = 2;

    private static final String DEFAULT_NAME = "AAAAAAAAAA";
    private static final String UPDATED_NAME = "BBBBBBBBBB";

    private static final String DEFAULT_DESCRIPTION = "AAAAAAAAAA";
    private static final String UPDATED_DESCRIPTION = "BBBBBBBBBB";

    private static final String DEFAULT_COVER_IMAGE_URL = "AAAAAAAAAA";
    private static final String UPDATED_COVER_IMAGE_URL = "BBBBBBBBBB";

    private static final String DEFAULT_METADATA = "AAAAAAAAAA";
    private static final String UPDATED_METADATA = "BBBBBBBBBB";

    private static final Integer DEFAULT_VERSION = 1;
    private static final Integer UPDATED_VERSION = 2;

    private static final String DEFAULT_CREATED_BY = "AAAAAAAAAA";
    private static final String UPDATED_CREATED_BY = "BBBBBBBBBB";

    private static final Instant DEFAULT_CREATED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_CREATED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final String DEFAULT_UPDATED_BY = "AAAAAAAAAA";
    private static final String UPDATED_UPDATED_BY = "BBBBBBBBBB";

    private static final Instant DEFAULT_UPDATED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_UPDATED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final Boolean DEFAULT_IS_DELETED = false;
    private static final Boolean UPDATED_IS_DELETED = true;

    private static final String ENTITY_API_URL = "/api/news-categories";
    private static final String ENTITY_API_URL_ID = ENTITY_API_URL + "/{id}";

    private static Random random = new Random();
    private static AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));

    @Autowired
    private ObjectMapper om;

    @Autowired
    private NewsCategoryRepository newsCategoryRepository;

    @Mock
    private NewsCategoryRepository newsCategoryRepositoryMock;

    @Autowired
    private NewsCategoryMapper newsCategoryMapper;

    @Mock
    private NewsCategoryService newsCategoryServiceMock;

    @Autowired
    private EntityManager em;

    @Autowired
    private MockMvc restNewsCategoryMockMvc;

    private NewsCategory newsCategory;

    private NewsCategory insertedNewsCategory;

    /**
     * Create an entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static NewsCategory createEntity() {
        return new NewsCategory()
            .status(DEFAULT_STATUS)
            .sortOrder(DEFAULT_SORT_ORDER)
            .name(DEFAULT_NAME)
            .description(DEFAULT_DESCRIPTION)
            .coverImageUrl(DEFAULT_COVER_IMAGE_URL)
            .metadata(DEFAULT_METADATA)
            .version(DEFAULT_VERSION)
            .createdBy(DEFAULT_CREATED_BY)
            .createdAt(DEFAULT_CREATED_AT)
            .updatedBy(DEFAULT_UPDATED_BY)
            .updatedAt(DEFAULT_UPDATED_AT)
            .isDeleted(DEFAULT_IS_DELETED);
    }

    /**
     * Create an updated entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static NewsCategory createUpdatedEntity() {
        return new NewsCategory()
            .status(UPDATED_STATUS)
            .sortOrder(UPDATED_SORT_ORDER)
            .name(UPDATED_NAME)
            .description(UPDATED_DESCRIPTION)
            .coverImageUrl(UPDATED_COVER_IMAGE_URL)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);
    }

    @BeforeEach
    void initTest() {
        newsCategory = createEntity();
    }

    @AfterEach
    void cleanup() {
        if (insertedNewsCategory != null) {
            newsCategoryRepository.delete(insertedNewsCategory);
            insertedNewsCategory = null;
        }
    }

    @Test
    @Transactional
    void createNewsCategory() throws Exception {
        long databaseSizeBeforeCreate = getRepositoryCount();
        // Create the NewsCategory
        NewsCategoryDTO newsCategoryDTO = newsCategoryMapper.toDto(newsCategory);
        var returnedNewsCategoryDTO = om.readValue(
            restNewsCategoryMockMvc
                .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(newsCategoryDTO)))
                .andExpect(status().isCreated())
                .andReturn()
                .getResponse()
                .getContentAsString(),
            NewsCategoryDTO.class
        );

        // Validate the NewsCategory in the database
        assertIncrementedRepositoryCount(databaseSizeBeforeCreate);
        var returnedNewsCategory = newsCategoryMapper.toEntity(returnedNewsCategoryDTO);
        assertNewsCategoryUpdatableFieldsEquals(returnedNewsCategory, getPersistedNewsCategory(returnedNewsCategory));

        insertedNewsCategory = returnedNewsCategory;
    }

    @Test
    @Transactional
    void createNewsCategoryWithExistingId() throws Exception {
        // Create the NewsCategory with an existing ID
        newsCategory.setId(1L);
        NewsCategoryDTO newsCategoryDTO = newsCategoryMapper.toDto(newsCategory);

        long databaseSizeBeforeCreate = getRepositoryCount();

        // An entity with an existing ID cannot be created, so this API call must fail
        restNewsCategoryMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(newsCategoryDTO)))
            .andExpect(status().isBadRequest());

        // Validate the NewsCategory in the database
        assertSameRepositoryCount(databaseSizeBeforeCreate);
    }

    @Test
    @Transactional
    void checkStatusIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        newsCategory.setStatus(null);

        // Create the NewsCategory, which fails.
        NewsCategoryDTO newsCategoryDTO = newsCategoryMapper.toDto(newsCategory);

        restNewsCategoryMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(newsCategoryDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkNameIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        newsCategory.setName(null);

        // Create the NewsCategory, which fails.
        NewsCategoryDTO newsCategoryDTO = newsCategoryMapper.toDto(newsCategory);

        restNewsCategoryMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(newsCategoryDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkVersionIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        newsCategory.setVersion(null);

        // Create the NewsCategory, which fails.
        NewsCategoryDTO newsCategoryDTO = newsCategoryMapper.toDto(newsCategory);

        restNewsCategoryMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(newsCategoryDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkCreatedAtIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        newsCategory.setCreatedAt(null);

        // Create the NewsCategory, which fails.
        NewsCategoryDTO newsCategoryDTO = newsCategoryMapper.toDto(newsCategory);

        restNewsCategoryMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(newsCategoryDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkUpdatedAtIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        newsCategory.setUpdatedAt(null);

        // Create the NewsCategory, which fails.
        NewsCategoryDTO newsCategoryDTO = newsCategoryMapper.toDto(newsCategory);

        restNewsCategoryMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(newsCategoryDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkIsDeletedIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        newsCategory.setIsDeleted(null);

        // Create the NewsCategory, which fails.
        NewsCategoryDTO newsCategoryDTO = newsCategoryMapper.toDto(newsCategory);

        restNewsCategoryMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(newsCategoryDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void getAllNewsCategories() throws Exception {
        // Initialize the database
        insertedNewsCategory = newsCategoryRepository.saveAndFlush(newsCategory);

        // Get all the newsCategoryList
        restNewsCategoryMockMvc
            .perform(get(ENTITY_API_URL + "?sort=id,desc"))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.[*].id").value(hasItem(newsCategory.getId().intValue())))
            .andExpect(jsonPath("$.[*].status").value(hasItem(DEFAULT_STATUS.toString())))
            .andExpect(jsonPath("$.[*].sortOrder").value(hasItem(DEFAULT_SORT_ORDER)))
            .andExpect(jsonPath("$.[*].name").value(hasItem(DEFAULT_NAME)))
            .andExpect(jsonPath("$.[*].description").value(hasItem(DEFAULT_DESCRIPTION)))
            .andExpect(jsonPath("$.[*].coverImageUrl").value(hasItem(DEFAULT_COVER_IMAGE_URL)))
            .andExpect(jsonPath("$.[*].metadata").value(hasItem(DEFAULT_METADATA)))
            .andExpect(jsonPath("$.[*].version").value(hasItem(DEFAULT_VERSION)))
            .andExpect(jsonPath("$.[*].createdBy").value(hasItem(DEFAULT_CREATED_BY)))
            .andExpect(jsonPath("$.[*].createdAt").value(hasItem(DEFAULT_CREATED_AT.toString())))
            .andExpect(jsonPath("$.[*].updatedBy").value(hasItem(DEFAULT_UPDATED_BY)))
            .andExpect(jsonPath("$.[*].updatedAt").value(hasItem(DEFAULT_UPDATED_AT.toString())))
            .andExpect(jsonPath("$.[*].isDeleted").value(hasItem(DEFAULT_IS_DELETED)));
    }

    @SuppressWarnings({ "unchecked" })
    void getAllNewsCategoriesWithEagerRelationshipsIsEnabled() throws Exception {
        when(newsCategoryServiceMock.findAllWithEagerRelationships(any())).thenReturn(new PageImpl(new ArrayList<>()));

        restNewsCategoryMockMvc.perform(get(ENTITY_API_URL + "?eagerload=true")).andExpect(status().isOk());

        verify(newsCategoryServiceMock, times(1)).findAllWithEagerRelationships(any());
    }

    @SuppressWarnings({ "unchecked" })
    void getAllNewsCategoriesWithEagerRelationshipsIsNotEnabled() throws Exception {
        when(newsCategoryServiceMock.findAllWithEagerRelationships(any())).thenReturn(new PageImpl(new ArrayList<>()));

        restNewsCategoryMockMvc.perform(get(ENTITY_API_URL + "?eagerload=false")).andExpect(status().isOk());
        verify(newsCategoryRepositoryMock, times(1)).findAll(any(Pageable.class));
    }

    @Test
    @Transactional
    void getNewsCategory() throws Exception {
        // Initialize the database
        insertedNewsCategory = newsCategoryRepository.saveAndFlush(newsCategory);

        // Get the newsCategory
        restNewsCategoryMockMvc
            .perform(get(ENTITY_API_URL_ID, newsCategory.getId()))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.id").value(newsCategory.getId().intValue()))
            .andExpect(jsonPath("$.status").value(DEFAULT_STATUS.toString()))
            .andExpect(jsonPath("$.sortOrder").value(DEFAULT_SORT_ORDER))
            .andExpect(jsonPath("$.name").value(DEFAULT_NAME))
            .andExpect(jsonPath("$.description").value(DEFAULT_DESCRIPTION))
            .andExpect(jsonPath("$.coverImageUrl").value(DEFAULT_COVER_IMAGE_URL))
            .andExpect(jsonPath("$.metadata").value(DEFAULT_METADATA))
            .andExpect(jsonPath("$.version").value(DEFAULT_VERSION))
            .andExpect(jsonPath("$.createdBy").value(DEFAULT_CREATED_BY))
            .andExpect(jsonPath("$.createdAt").value(DEFAULT_CREATED_AT.toString()))
            .andExpect(jsonPath("$.updatedBy").value(DEFAULT_UPDATED_BY))
            .andExpect(jsonPath("$.updatedAt").value(DEFAULT_UPDATED_AT.toString()))
            .andExpect(jsonPath("$.isDeleted").value(DEFAULT_IS_DELETED));
    }

    @Test
    @Transactional
    void getNonExistingNewsCategory() throws Exception {
        // Get the newsCategory
        restNewsCategoryMockMvc.perform(get(ENTITY_API_URL_ID, Long.MAX_VALUE)).andExpect(status().isNotFound());
    }

    @Test
    @Transactional
    void putExistingNewsCategory() throws Exception {
        // Initialize the database
        insertedNewsCategory = newsCategoryRepository.saveAndFlush(newsCategory);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the newsCategory
        NewsCategory updatedNewsCategory = newsCategoryRepository.findById(newsCategory.getId()).orElseThrow();
        // Disconnect from session so that the updates on updatedNewsCategory are not directly saved in db
        em.detach(updatedNewsCategory);
        updatedNewsCategory
            .status(UPDATED_STATUS)
            .sortOrder(UPDATED_SORT_ORDER)
            .name(UPDATED_NAME)
            .description(UPDATED_DESCRIPTION)
            .coverImageUrl(UPDATED_COVER_IMAGE_URL)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);
        NewsCategoryDTO newsCategoryDTO = newsCategoryMapper.toDto(updatedNewsCategory);

        restNewsCategoryMockMvc
            .perform(
                put(ENTITY_API_URL_ID, newsCategoryDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(newsCategoryDTO))
            )
            .andExpect(status().isOk());

        // Validate the NewsCategory in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertPersistedNewsCategoryToMatchAllProperties(updatedNewsCategory);
    }

    @Test
    @Transactional
    void putNonExistingNewsCategory() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        newsCategory.setId(longCount.incrementAndGet());

        // Create the NewsCategory
        NewsCategoryDTO newsCategoryDTO = newsCategoryMapper.toDto(newsCategory);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restNewsCategoryMockMvc
            .perform(
                put(ENTITY_API_URL_ID, newsCategoryDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(newsCategoryDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the NewsCategory in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithIdMismatchNewsCategory() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        newsCategory.setId(longCount.incrementAndGet());

        // Create the NewsCategory
        NewsCategoryDTO newsCategoryDTO = newsCategoryMapper.toDto(newsCategory);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restNewsCategoryMockMvc
            .perform(
                put(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(newsCategoryDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the NewsCategory in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithMissingIdPathParamNewsCategory() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        newsCategory.setId(longCount.incrementAndGet());

        // Create the NewsCategory
        NewsCategoryDTO newsCategoryDTO = newsCategoryMapper.toDto(newsCategory);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restNewsCategoryMockMvc
            .perform(put(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(newsCategoryDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the NewsCategory in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void partialUpdateNewsCategoryWithPatch() throws Exception {
        // Initialize the database
        insertedNewsCategory = newsCategoryRepository.saveAndFlush(newsCategory);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the newsCategory using partial update
        NewsCategory partialUpdatedNewsCategory = new NewsCategory();
        partialUpdatedNewsCategory.setId(newsCategory.getId());

        partialUpdatedNewsCategory
            .status(UPDATED_STATUS)
            .name(UPDATED_NAME)
            .description(UPDATED_DESCRIPTION)
            .coverImageUrl(UPDATED_COVER_IMAGE_URL)
            .createdAt(UPDATED_CREATED_AT)
            .updatedAt(UPDATED_UPDATED_AT);

        restNewsCategoryMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedNewsCategory.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedNewsCategory))
            )
            .andExpect(status().isOk());

        // Validate the NewsCategory in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertNewsCategoryUpdatableFieldsEquals(
            createUpdateProxyForBean(partialUpdatedNewsCategory, newsCategory),
            getPersistedNewsCategory(newsCategory)
        );
    }

    @Test
    @Transactional
    void fullUpdateNewsCategoryWithPatch() throws Exception {
        // Initialize the database
        insertedNewsCategory = newsCategoryRepository.saveAndFlush(newsCategory);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the newsCategory using partial update
        NewsCategory partialUpdatedNewsCategory = new NewsCategory();
        partialUpdatedNewsCategory.setId(newsCategory.getId());

        partialUpdatedNewsCategory
            .status(UPDATED_STATUS)
            .sortOrder(UPDATED_SORT_ORDER)
            .name(UPDATED_NAME)
            .description(UPDATED_DESCRIPTION)
            .coverImageUrl(UPDATED_COVER_IMAGE_URL)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);

        restNewsCategoryMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedNewsCategory.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedNewsCategory))
            )
            .andExpect(status().isOk());

        // Validate the NewsCategory in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertNewsCategoryUpdatableFieldsEquals(partialUpdatedNewsCategory, getPersistedNewsCategory(partialUpdatedNewsCategory));
    }

    @Test
    @Transactional
    void patchNonExistingNewsCategory() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        newsCategory.setId(longCount.incrementAndGet());

        // Create the NewsCategory
        NewsCategoryDTO newsCategoryDTO = newsCategoryMapper.toDto(newsCategory);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restNewsCategoryMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, newsCategoryDTO.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(newsCategoryDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the NewsCategory in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithIdMismatchNewsCategory() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        newsCategory.setId(longCount.incrementAndGet());

        // Create the NewsCategory
        NewsCategoryDTO newsCategoryDTO = newsCategoryMapper.toDto(newsCategory);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restNewsCategoryMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(newsCategoryDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the NewsCategory in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithMissingIdPathParamNewsCategory() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        newsCategory.setId(longCount.incrementAndGet());

        // Create the NewsCategory
        NewsCategoryDTO newsCategoryDTO = newsCategoryMapper.toDto(newsCategory);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restNewsCategoryMockMvc
            .perform(patch(ENTITY_API_URL).contentType("application/merge-patch+json").content(om.writeValueAsBytes(newsCategoryDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the NewsCategory in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void deleteNewsCategory() throws Exception {
        // Initialize the database
        insertedNewsCategory = newsCategoryRepository.saveAndFlush(newsCategory);

        long databaseSizeBeforeDelete = getRepositoryCount();

        // Delete the newsCategory
        restNewsCategoryMockMvc
            .perform(delete(ENTITY_API_URL_ID, newsCategory.getId()).accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isNoContent());

        // Validate the database contains one less item
        assertDecrementedRepositoryCount(databaseSizeBeforeDelete);
    }

    protected long getRepositoryCount() {
        return newsCategoryRepository.count();
    }

    protected void assertIncrementedRepositoryCount(long countBefore) {
        assertThat(countBefore + 1).isEqualTo(getRepositoryCount());
    }

    protected void assertDecrementedRepositoryCount(long countBefore) {
        assertThat(countBefore - 1).isEqualTo(getRepositoryCount());
    }

    protected void assertSameRepositoryCount(long countBefore) {
        assertThat(countBefore).isEqualTo(getRepositoryCount());
    }

    protected NewsCategory getPersistedNewsCategory(NewsCategory newsCategory) {
        return newsCategoryRepository.findById(newsCategory.getId()).orElseThrow();
    }

    protected void assertPersistedNewsCategoryToMatchAllProperties(NewsCategory expectedNewsCategory) {
        assertNewsCategoryAllPropertiesEquals(expectedNewsCategory, getPersistedNewsCategory(expectedNewsCategory));
    }

    protected void assertPersistedNewsCategoryToMatchUpdatableProperties(NewsCategory expectedNewsCategory) {
        assertNewsCategoryAllUpdatablePropertiesEquals(expectedNewsCategory, getPersistedNewsCategory(expectedNewsCategory));
    }
}

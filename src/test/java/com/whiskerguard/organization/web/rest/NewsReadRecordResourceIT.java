package com.whiskerguard.organization.web.rest;

import static com.whiskerguard.organization.domain.NewsReadRecordAsserts.*;
import static com.whiskerguard.organization.web.rest.TestUtil.createUpdateProxyForBean;
import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.hasItem;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.whiskerguard.organization.IntegrationTest;
import com.whiskerguard.organization.domain.NewsReadRecord;
import com.whiskerguard.organization.repository.NewsReadRecordRepository;
import com.whiskerguard.organization.service.NewsReadRecordService;
import com.whiskerguard.organization.service.dto.NewsReadRecordDTO;
import com.whiskerguard.organization.service.mapper.NewsReadRecordMapper;
import jakarta.persistence.EntityManager;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Random;
import java.util.concurrent.atomic.AtomicLong;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

/**
 * Integration tests for the {@link NewsReadRecordResource} REST controller.
 */
@IntegrationTest
@ExtendWith(MockitoExtension.class)
@AutoConfigureMockMvc
@WithMockUser
class NewsReadRecordResourceIT {

    private static final Instant DEFAULT_READ_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_READ_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final String DEFAULT_SOURCE = "AAAAAAAAAA";
    private static final String UPDATED_SOURCE = "BBBBBBBBBB";

    private static final String DEFAULT_DEVICE = "AAAAAAAAAA";
    private static final String UPDATED_DEVICE = "BBBBBBBBBB";

    private static final Integer DEFAULT_DURATION = 1;
    private static final Integer UPDATED_DURATION = 2;

    private static final String DEFAULT_METADATA = "AAAAAAAAAA";
    private static final String UPDATED_METADATA = "BBBBBBBBBB";

    private static final Integer DEFAULT_VERSION = 1;
    private static final Integer UPDATED_VERSION = 2;

    private static final String DEFAULT_CREATED_BY = "AAAAAAAAAA";
    private static final String UPDATED_CREATED_BY = "BBBBBBBBBB";

    private static final Instant DEFAULT_CREATED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_CREATED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final String DEFAULT_UPDATED_BY = "AAAAAAAAAA";
    private static final String UPDATED_UPDATED_BY = "BBBBBBBBBB";

    private static final Instant DEFAULT_UPDATED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_UPDATED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final Boolean DEFAULT_IS_DELETED = false;
    private static final Boolean UPDATED_IS_DELETED = true;

    private static final String ENTITY_API_URL = "/api/news-read-records";
    private static final String ENTITY_API_URL_ID = ENTITY_API_URL + "/{id}";

    private static Random random = new Random();
    private static AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));

    @Autowired
    private ObjectMapper om;

    @Autowired
    private NewsReadRecordRepository newsReadRecordRepository;

    @Mock
    private NewsReadRecordRepository newsReadRecordRepositoryMock;

    @Autowired
    private NewsReadRecordMapper newsReadRecordMapper;

    @Mock
    private NewsReadRecordService newsReadRecordServiceMock;

    @Autowired
    private EntityManager em;

    @Autowired
    private MockMvc restNewsReadRecordMockMvc;

    private NewsReadRecord newsReadRecord;

    private NewsReadRecord insertedNewsReadRecord;

    /**
     * Create an entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static NewsReadRecord createEntity() {
        return new NewsReadRecord()
            .readAt(DEFAULT_READ_AT)
            .source(DEFAULT_SOURCE)
            .device(DEFAULT_DEVICE)
            .duration(DEFAULT_DURATION)
            .metadata(DEFAULT_METADATA)
            .version(DEFAULT_VERSION)
            .createdBy(DEFAULT_CREATED_BY)
            .createdAt(DEFAULT_CREATED_AT)
            .updatedBy(DEFAULT_UPDATED_BY)
            .updatedAt(DEFAULT_UPDATED_AT)
            .isDeleted(DEFAULT_IS_DELETED);
    }

    /**
     * Create an updated entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static NewsReadRecord createUpdatedEntity() {
        return new NewsReadRecord()
            .readAt(UPDATED_READ_AT)
            .source(UPDATED_SOURCE)
            .device(UPDATED_DEVICE)
            .duration(UPDATED_DURATION)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);
    }

    @BeforeEach
    void initTest() {
        newsReadRecord = createEntity();
    }

    @AfterEach
    void cleanup() {
        if (insertedNewsReadRecord != null) {
            newsReadRecordRepository.delete(insertedNewsReadRecord);
            insertedNewsReadRecord = null;
        }
    }

    @Test
    @Transactional
    void createNewsReadRecord() throws Exception {
        long databaseSizeBeforeCreate = getRepositoryCount();
        // Create the NewsReadRecord
        NewsReadRecordDTO newsReadRecordDTO = newsReadRecordMapper.toDto(newsReadRecord);
        var returnedNewsReadRecordDTO = om.readValue(
            restNewsReadRecordMockMvc
                .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(newsReadRecordDTO)))
                .andExpect(status().isCreated())
                .andReturn()
                .getResponse()
                .getContentAsString(),
            NewsReadRecordDTO.class
        );

        // Validate the NewsReadRecord in the database
        assertIncrementedRepositoryCount(databaseSizeBeforeCreate);
        var returnedNewsReadRecord = newsReadRecordMapper.toEntity(returnedNewsReadRecordDTO);
        assertNewsReadRecordUpdatableFieldsEquals(returnedNewsReadRecord, getPersistedNewsReadRecord(returnedNewsReadRecord));

        insertedNewsReadRecord = returnedNewsReadRecord;
    }

    @Test
    @Transactional
    void createNewsReadRecordWithExistingId() throws Exception {
        // Create the NewsReadRecord with an existing ID
        newsReadRecord.setId(1L);
        NewsReadRecordDTO newsReadRecordDTO = newsReadRecordMapper.toDto(newsReadRecord);

        long databaseSizeBeforeCreate = getRepositoryCount();

        // An entity with an existing ID cannot be created, so this API call must fail
        restNewsReadRecordMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(newsReadRecordDTO)))
            .andExpect(status().isBadRequest());

        // Validate the NewsReadRecord in the database
        assertSameRepositoryCount(databaseSizeBeforeCreate);
    }

    @Test
    @Transactional
    void checkReadAtIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        newsReadRecord.setReadAt(null);

        // Create the NewsReadRecord, which fails.
        NewsReadRecordDTO newsReadRecordDTO = newsReadRecordMapper.toDto(newsReadRecord);

        restNewsReadRecordMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(newsReadRecordDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkVersionIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        newsReadRecord.setVersion(null);

        // Create the NewsReadRecord, which fails.
        NewsReadRecordDTO newsReadRecordDTO = newsReadRecordMapper.toDto(newsReadRecord);

        restNewsReadRecordMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(newsReadRecordDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkCreatedAtIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        newsReadRecord.setCreatedAt(null);

        // Create the NewsReadRecord, which fails.
        NewsReadRecordDTO newsReadRecordDTO = newsReadRecordMapper.toDto(newsReadRecord);

        restNewsReadRecordMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(newsReadRecordDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkUpdatedAtIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        newsReadRecord.setUpdatedAt(null);

        // Create the NewsReadRecord, which fails.
        NewsReadRecordDTO newsReadRecordDTO = newsReadRecordMapper.toDto(newsReadRecord);

        restNewsReadRecordMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(newsReadRecordDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkIsDeletedIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        newsReadRecord.setIsDeleted(null);

        // Create the NewsReadRecord, which fails.
        NewsReadRecordDTO newsReadRecordDTO = newsReadRecordMapper.toDto(newsReadRecord);

        restNewsReadRecordMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(newsReadRecordDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void getAllNewsReadRecords() throws Exception {
        // Initialize the database
        insertedNewsReadRecord = newsReadRecordRepository.saveAndFlush(newsReadRecord);

        // Get all the newsReadRecordList
        restNewsReadRecordMockMvc
            .perform(get(ENTITY_API_URL + "?sort=id,desc"))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.[*].id").value(hasItem(newsReadRecord.getId().intValue())))
            .andExpect(jsonPath("$.[*].readAt").value(hasItem(DEFAULT_READ_AT.toString())))
            .andExpect(jsonPath("$.[*].source").value(hasItem(DEFAULT_SOURCE)))
            .andExpect(jsonPath("$.[*].device").value(hasItem(DEFAULT_DEVICE)))
            .andExpect(jsonPath("$.[*].duration").value(hasItem(DEFAULT_DURATION)))
            .andExpect(jsonPath("$.[*].metadata").value(hasItem(DEFAULT_METADATA)))
            .andExpect(jsonPath("$.[*].version").value(hasItem(DEFAULT_VERSION)))
            .andExpect(jsonPath("$.[*].createdBy").value(hasItem(DEFAULT_CREATED_BY)))
            .andExpect(jsonPath("$.[*].createdAt").value(hasItem(DEFAULT_CREATED_AT.toString())))
            .andExpect(jsonPath("$.[*].updatedBy").value(hasItem(DEFAULT_UPDATED_BY)))
            .andExpect(jsonPath("$.[*].updatedAt").value(hasItem(DEFAULT_UPDATED_AT.toString())))
            .andExpect(jsonPath("$.[*].isDeleted").value(hasItem(DEFAULT_IS_DELETED)));
    }

    @SuppressWarnings({ "unchecked" })
    void getAllNewsReadRecordsWithEagerRelationshipsIsEnabled() throws Exception {
        when(newsReadRecordServiceMock.findAllWithEagerRelationships(any())).thenReturn(new PageImpl(new ArrayList<>()));

        restNewsReadRecordMockMvc.perform(get(ENTITY_API_URL + "?eagerload=true")).andExpect(status().isOk());

        verify(newsReadRecordServiceMock, times(1)).findAllWithEagerRelationships(any());
    }

    @SuppressWarnings({ "unchecked" })
    void getAllNewsReadRecordsWithEagerRelationshipsIsNotEnabled() throws Exception {
        when(newsReadRecordServiceMock.findAllWithEagerRelationships(any())).thenReturn(new PageImpl(new ArrayList<>()));

        restNewsReadRecordMockMvc.perform(get(ENTITY_API_URL + "?eagerload=false")).andExpect(status().isOk());
        verify(newsReadRecordRepositoryMock, times(1)).findAll(any(Pageable.class));
    }

    @Test
    @Transactional
    void getNewsReadRecord() throws Exception {
        // Initialize the database
        insertedNewsReadRecord = newsReadRecordRepository.saveAndFlush(newsReadRecord);

        // Get the newsReadRecord
        restNewsReadRecordMockMvc
            .perform(get(ENTITY_API_URL_ID, newsReadRecord.getId()))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.id").value(newsReadRecord.getId().intValue()))
            .andExpect(jsonPath("$.readAt").value(DEFAULT_READ_AT.toString()))
            .andExpect(jsonPath("$.source").value(DEFAULT_SOURCE))
            .andExpect(jsonPath("$.device").value(DEFAULT_DEVICE))
            .andExpect(jsonPath("$.duration").value(DEFAULT_DURATION))
            .andExpect(jsonPath("$.metadata").value(DEFAULT_METADATA))
            .andExpect(jsonPath("$.version").value(DEFAULT_VERSION))
            .andExpect(jsonPath("$.createdBy").value(DEFAULT_CREATED_BY))
            .andExpect(jsonPath("$.createdAt").value(DEFAULT_CREATED_AT.toString()))
            .andExpect(jsonPath("$.updatedBy").value(DEFAULT_UPDATED_BY))
            .andExpect(jsonPath("$.updatedAt").value(DEFAULT_UPDATED_AT.toString()))
            .andExpect(jsonPath("$.isDeleted").value(DEFAULT_IS_DELETED));
    }

    @Test
    @Transactional
    void getNonExistingNewsReadRecord() throws Exception {
        // Get the newsReadRecord
        restNewsReadRecordMockMvc.perform(get(ENTITY_API_URL_ID, Long.MAX_VALUE)).andExpect(status().isNotFound());
    }

    @Test
    @Transactional
    void putExistingNewsReadRecord() throws Exception {
        // Initialize the database
        insertedNewsReadRecord = newsReadRecordRepository.saveAndFlush(newsReadRecord);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the newsReadRecord
        NewsReadRecord updatedNewsReadRecord = newsReadRecordRepository.findById(newsReadRecord.getId()).orElseThrow();
        // Disconnect from session so that the updates on updatedNewsReadRecord are not directly saved in db
        em.detach(updatedNewsReadRecord);
        updatedNewsReadRecord
            .readAt(UPDATED_READ_AT)
            .source(UPDATED_SOURCE)
            .device(UPDATED_DEVICE)
            .duration(UPDATED_DURATION)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);
        NewsReadRecordDTO newsReadRecordDTO = newsReadRecordMapper.toDto(updatedNewsReadRecord);

        restNewsReadRecordMockMvc
            .perform(
                put(ENTITY_API_URL_ID, newsReadRecordDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(newsReadRecordDTO))
            )
            .andExpect(status().isOk());

        // Validate the NewsReadRecord in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertPersistedNewsReadRecordToMatchAllProperties(updatedNewsReadRecord);
    }

    @Test
    @Transactional
    void putNonExistingNewsReadRecord() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        newsReadRecord.setId(longCount.incrementAndGet());

        // Create the NewsReadRecord
        NewsReadRecordDTO newsReadRecordDTO = newsReadRecordMapper.toDto(newsReadRecord);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restNewsReadRecordMockMvc
            .perform(
                put(ENTITY_API_URL_ID, newsReadRecordDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(newsReadRecordDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the NewsReadRecord in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithIdMismatchNewsReadRecord() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        newsReadRecord.setId(longCount.incrementAndGet());

        // Create the NewsReadRecord
        NewsReadRecordDTO newsReadRecordDTO = newsReadRecordMapper.toDto(newsReadRecord);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restNewsReadRecordMockMvc
            .perform(
                put(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(newsReadRecordDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the NewsReadRecord in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithMissingIdPathParamNewsReadRecord() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        newsReadRecord.setId(longCount.incrementAndGet());

        // Create the NewsReadRecord
        NewsReadRecordDTO newsReadRecordDTO = newsReadRecordMapper.toDto(newsReadRecord);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restNewsReadRecordMockMvc
            .perform(put(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(newsReadRecordDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the NewsReadRecord in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void partialUpdateNewsReadRecordWithPatch() throws Exception {
        // Initialize the database
        insertedNewsReadRecord = newsReadRecordRepository.saveAndFlush(newsReadRecord);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the newsReadRecord using partial update
        NewsReadRecord partialUpdatedNewsReadRecord = new NewsReadRecord();
        partialUpdatedNewsReadRecord.setId(newsReadRecord.getId());

        restNewsReadRecordMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedNewsReadRecord.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedNewsReadRecord))
            )
            .andExpect(status().isOk());

        // Validate the NewsReadRecord in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertNewsReadRecordUpdatableFieldsEquals(
            createUpdateProxyForBean(partialUpdatedNewsReadRecord, newsReadRecord),
            getPersistedNewsReadRecord(newsReadRecord)
        );
    }

    @Test
    @Transactional
    void fullUpdateNewsReadRecordWithPatch() throws Exception {
        // Initialize the database
        insertedNewsReadRecord = newsReadRecordRepository.saveAndFlush(newsReadRecord);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the newsReadRecord using partial update
        NewsReadRecord partialUpdatedNewsReadRecord = new NewsReadRecord();
        partialUpdatedNewsReadRecord.setId(newsReadRecord.getId());

        partialUpdatedNewsReadRecord
            .readAt(UPDATED_READ_AT)
            .source(UPDATED_SOURCE)
            .device(UPDATED_DEVICE)
            .duration(UPDATED_DURATION)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);

        restNewsReadRecordMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedNewsReadRecord.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedNewsReadRecord))
            )
            .andExpect(status().isOk());

        // Validate the NewsReadRecord in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertNewsReadRecordUpdatableFieldsEquals(partialUpdatedNewsReadRecord, getPersistedNewsReadRecord(partialUpdatedNewsReadRecord));
    }

    @Test
    @Transactional
    void patchNonExistingNewsReadRecord() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        newsReadRecord.setId(longCount.incrementAndGet());

        // Create the NewsReadRecord
        NewsReadRecordDTO newsReadRecordDTO = newsReadRecordMapper.toDto(newsReadRecord);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restNewsReadRecordMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, newsReadRecordDTO.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(newsReadRecordDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the NewsReadRecord in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithIdMismatchNewsReadRecord() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        newsReadRecord.setId(longCount.incrementAndGet());

        // Create the NewsReadRecord
        NewsReadRecordDTO newsReadRecordDTO = newsReadRecordMapper.toDto(newsReadRecord);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restNewsReadRecordMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(newsReadRecordDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the NewsReadRecord in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithMissingIdPathParamNewsReadRecord() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        newsReadRecord.setId(longCount.incrementAndGet());

        // Create the NewsReadRecord
        NewsReadRecordDTO newsReadRecordDTO = newsReadRecordMapper.toDto(newsReadRecord);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restNewsReadRecordMockMvc
            .perform(patch(ENTITY_API_URL).contentType("application/merge-patch+json").content(om.writeValueAsBytes(newsReadRecordDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the NewsReadRecord in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void deleteNewsReadRecord() throws Exception {
        // Initialize the database
        insertedNewsReadRecord = newsReadRecordRepository.saveAndFlush(newsReadRecord);

        long databaseSizeBeforeDelete = getRepositoryCount();

        // Delete the newsReadRecord
        restNewsReadRecordMockMvc
            .perform(delete(ENTITY_API_URL_ID, newsReadRecord.getId()).accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isNoContent());

        // Validate the database contains one less item
        assertDecrementedRepositoryCount(databaseSizeBeforeDelete);
    }

    protected long getRepositoryCount() {
        return newsReadRecordRepository.count();
    }

    protected void assertIncrementedRepositoryCount(long countBefore) {
        assertThat(countBefore + 1).isEqualTo(getRepositoryCount());
    }

    protected void assertDecrementedRepositoryCount(long countBefore) {
        assertThat(countBefore - 1).isEqualTo(getRepositoryCount());
    }

    protected void assertSameRepositoryCount(long countBefore) {
        assertThat(countBefore).isEqualTo(getRepositoryCount());
    }

    protected NewsReadRecord getPersistedNewsReadRecord(NewsReadRecord newsReadRecord) {
        return newsReadRecordRepository.findById(newsReadRecord.getId()).orElseThrow();
    }

    protected void assertPersistedNewsReadRecordToMatchAllProperties(NewsReadRecord expectedNewsReadRecord) {
        assertNewsReadRecordAllPropertiesEquals(expectedNewsReadRecord, getPersistedNewsReadRecord(expectedNewsReadRecord));
    }

    protected void assertPersistedNewsReadRecordToMatchUpdatableProperties(NewsReadRecord expectedNewsReadRecord) {
        assertNewsReadRecordAllUpdatablePropertiesEquals(expectedNewsReadRecord, getPersistedNewsReadRecord(expectedNewsReadRecord));
    }
}

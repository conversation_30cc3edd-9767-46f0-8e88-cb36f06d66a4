package com.whiskerguard.organization.web.rest;

import static com.whiskerguard.organization.domain.NewsAttachmentAsserts.*;
import static com.whiskerguard.organization.web.rest.TestUtil.createUpdateProxyForBean;
import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.hasItem;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.whiskerguard.organization.IntegrationTest;
import com.whiskerguard.organization.domain.NewsAttachment;
import com.whiskerguard.organization.repository.NewsAttachmentRepository;
import com.whiskerguard.organization.service.NewsAttachmentService;
import com.whiskerguard.organization.service.dto.NewsAttachmentDTO;
import com.whiskerguard.organization.service.mapper.NewsAttachmentMapper;
import jakarta.persistence.EntityManager;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Random;
import java.util.concurrent.atomic.AtomicLong;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

/**
 * Integration tests for the {@link NewsAttachmentResource} REST controller.
 */
@IntegrationTest
@ExtendWith(MockitoExtension.class)
@AutoConfigureMockMvc
@WithMockUser
class NewsAttachmentResourceIT {

    private static final String DEFAULT_NAME = "AAAAAAAAAA";
    private static final String UPDATED_NAME = "BBBBBBBBBB";

    private static final String DEFAULT_TYPE = "AAAAAAAAAA";
    private static final String UPDATED_TYPE = "BBBBBBBBBB";

    private static final String DEFAULT_URL = "AAAAAAAAAA";
    private static final String UPDATED_URL = "BBBBBBBBBB";

    private static final Long DEFAULT_FILE_SIZE = 1L;
    private static final Long UPDATED_FILE_SIZE = 2L;

    private static final Integer DEFAULT_SORT_ORDER = 1;
    private static final Integer UPDATED_SORT_ORDER = 2;

    private static final String DEFAULT_METADATA = "AAAAAAAAAA";
    private static final String UPDATED_METADATA = "BBBBBBBBBB";

    private static final Integer DEFAULT_VERSION = 1;
    private static final Integer UPDATED_VERSION = 2;

    private static final String DEFAULT_CREATED_BY = "AAAAAAAAAA";
    private static final String UPDATED_CREATED_BY = "BBBBBBBBBB";

    private static final Instant DEFAULT_CREATED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_CREATED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final String DEFAULT_UPDATED_BY = "AAAAAAAAAA";
    private static final String UPDATED_UPDATED_BY = "BBBBBBBBBB";

    private static final Instant DEFAULT_UPDATED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_UPDATED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final Boolean DEFAULT_IS_DELETED = false;
    private static final Boolean UPDATED_IS_DELETED = true;

    private static final String ENTITY_API_URL = "/api/news-attachments";
    private static final String ENTITY_API_URL_ID = ENTITY_API_URL + "/{id}";

    private static Random random = new Random();
    private static AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));

    @Autowired
    private ObjectMapper om;

    @Autowired
    private NewsAttachmentRepository newsAttachmentRepository;

    @Mock
    private NewsAttachmentRepository newsAttachmentRepositoryMock;

    @Autowired
    private NewsAttachmentMapper newsAttachmentMapper;

    @Mock
    private NewsAttachmentService newsAttachmentServiceMock;

    @Autowired
    private EntityManager em;

    @Autowired
    private MockMvc restNewsAttachmentMockMvc;

    private NewsAttachment newsAttachment;

    private NewsAttachment insertedNewsAttachment;

    /**
     * Create an entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static NewsAttachment createEntity() {
        return new NewsAttachment()
            .name(DEFAULT_NAME)
            .type(DEFAULT_TYPE)
            .url(DEFAULT_URL)
            .fileSize(DEFAULT_FILE_SIZE)
            .sortOrder(DEFAULT_SORT_ORDER)
            .metadata(DEFAULT_METADATA)
            .version(DEFAULT_VERSION)
            .createdBy(DEFAULT_CREATED_BY)
            .createdAt(DEFAULT_CREATED_AT)
            .updatedBy(DEFAULT_UPDATED_BY)
            .updatedAt(DEFAULT_UPDATED_AT)
            .isDeleted(DEFAULT_IS_DELETED);
    }

    /**
     * Create an updated entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static NewsAttachment createUpdatedEntity() {
        return new NewsAttachment()
            .name(UPDATED_NAME)
            .type(UPDATED_TYPE)
            .url(UPDATED_URL)
            .fileSize(UPDATED_FILE_SIZE)
            .sortOrder(UPDATED_SORT_ORDER)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);
    }

    @BeforeEach
    void initTest() {
        newsAttachment = createEntity();
    }

    @AfterEach
    void cleanup() {
        if (insertedNewsAttachment != null) {
            newsAttachmentRepository.delete(insertedNewsAttachment);
            insertedNewsAttachment = null;
        }
    }

    @Test
    @Transactional
    void createNewsAttachment() throws Exception {
        long databaseSizeBeforeCreate = getRepositoryCount();
        // Create the NewsAttachment
        NewsAttachmentDTO newsAttachmentDTO = newsAttachmentMapper.toDto(newsAttachment);
        var returnedNewsAttachmentDTO = om.readValue(
            restNewsAttachmentMockMvc
                .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(newsAttachmentDTO)))
                .andExpect(status().isCreated())
                .andReturn()
                .getResponse()
                .getContentAsString(),
            NewsAttachmentDTO.class
        );

        // Validate the NewsAttachment in the database
        assertIncrementedRepositoryCount(databaseSizeBeforeCreate);
        var returnedNewsAttachment = newsAttachmentMapper.toEntity(returnedNewsAttachmentDTO);
        assertNewsAttachmentUpdatableFieldsEquals(returnedNewsAttachment, getPersistedNewsAttachment(returnedNewsAttachment));

        insertedNewsAttachment = returnedNewsAttachment;
    }

    @Test
    @Transactional
    void createNewsAttachmentWithExistingId() throws Exception {
        // Create the NewsAttachment with an existing ID
        newsAttachment.setId(1L);
        NewsAttachmentDTO newsAttachmentDTO = newsAttachmentMapper.toDto(newsAttachment);

        long databaseSizeBeforeCreate = getRepositoryCount();

        // An entity with an existing ID cannot be created, so this API call must fail
        restNewsAttachmentMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(newsAttachmentDTO)))
            .andExpect(status().isBadRequest());

        // Validate the NewsAttachment in the database
        assertSameRepositoryCount(databaseSizeBeforeCreate);
    }

    @Test
    @Transactional
    void checkNameIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        newsAttachment.setName(null);

        // Create the NewsAttachment, which fails.
        NewsAttachmentDTO newsAttachmentDTO = newsAttachmentMapper.toDto(newsAttachment);

        restNewsAttachmentMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(newsAttachmentDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkTypeIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        newsAttachment.setType(null);

        // Create the NewsAttachment, which fails.
        NewsAttachmentDTO newsAttachmentDTO = newsAttachmentMapper.toDto(newsAttachment);

        restNewsAttachmentMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(newsAttachmentDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkUrlIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        newsAttachment.setUrl(null);

        // Create the NewsAttachment, which fails.
        NewsAttachmentDTO newsAttachmentDTO = newsAttachmentMapper.toDto(newsAttachment);

        restNewsAttachmentMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(newsAttachmentDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkVersionIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        newsAttachment.setVersion(null);

        // Create the NewsAttachment, which fails.
        NewsAttachmentDTO newsAttachmentDTO = newsAttachmentMapper.toDto(newsAttachment);

        restNewsAttachmentMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(newsAttachmentDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkCreatedAtIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        newsAttachment.setCreatedAt(null);

        // Create the NewsAttachment, which fails.
        NewsAttachmentDTO newsAttachmentDTO = newsAttachmentMapper.toDto(newsAttachment);

        restNewsAttachmentMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(newsAttachmentDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkUpdatedAtIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        newsAttachment.setUpdatedAt(null);

        // Create the NewsAttachment, which fails.
        NewsAttachmentDTO newsAttachmentDTO = newsAttachmentMapper.toDto(newsAttachment);

        restNewsAttachmentMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(newsAttachmentDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkIsDeletedIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        newsAttachment.setIsDeleted(null);

        // Create the NewsAttachment, which fails.
        NewsAttachmentDTO newsAttachmentDTO = newsAttachmentMapper.toDto(newsAttachment);

        restNewsAttachmentMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(newsAttachmentDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void getAllNewsAttachments() throws Exception {
        // Initialize the database
        insertedNewsAttachment = newsAttachmentRepository.saveAndFlush(newsAttachment);

        // Get all the newsAttachmentList
        restNewsAttachmentMockMvc
            .perform(get(ENTITY_API_URL + "?sort=id,desc"))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.[*].id").value(hasItem(newsAttachment.getId().intValue())))
            .andExpect(jsonPath("$.[*].name").value(hasItem(DEFAULT_NAME)))
            .andExpect(jsonPath("$.[*].type").value(hasItem(DEFAULT_TYPE)))
            .andExpect(jsonPath("$.[*].url").value(hasItem(DEFAULT_URL)))
            .andExpect(jsonPath("$.[*].fileSize").value(hasItem(DEFAULT_FILE_SIZE.intValue())))
            .andExpect(jsonPath("$.[*].sortOrder").value(hasItem(DEFAULT_SORT_ORDER)))
            .andExpect(jsonPath("$.[*].metadata").value(hasItem(DEFAULT_METADATA)))
            .andExpect(jsonPath("$.[*].version").value(hasItem(DEFAULT_VERSION)))
            .andExpect(jsonPath("$.[*].createdBy").value(hasItem(DEFAULT_CREATED_BY)))
            .andExpect(jsonPath("$.[*].createdAt").value(hasItem(DEFAULT_CREATED_AT.toString())))
            .andExpect(jsonPath("$.[*].updatedBy").value(hasItem(DEFAULT_UPDATED_BY)))
            .andExpect(jsonPath("$.[*].updatedAt").value(hasItem(DEFAULT_UPDATED_AT.toString())))
            .andExpect(jsonPath("$.[*].isDeleted").value(hasItem(DEFAULT_IS_DELETED)));
    }

    @SuppressWarnings({ "unchecked" })
    void getAllNewsAttachmentsWithEagerRelationshipsIsEnabled() throws Exception {
        when(newsAttachmentServiceMock.findAllWithEagerRelationships(any())).thenReturn(new PageImpl(new ArrayList<>()));

        restNewsAttachmentMockMvc.perform(get(ENTITY_API_URL + "?eagerload=true")).andExpect(status().isOk());

        verify(newsAttachmentServiceMock, times(1)).findAllWithEagerRelationships(any());
    }

    @SuppressWarnings({ "unchecked" })
    void getAllNewsAttachmentsWithEagerRelationshipsIsNotEnabled() throws Exception {
        when(newsAttachmentServiceMock.findAllWithEagerRelationships(any())).thenReturn(new PageImpl(new ArrayList<>()));

        restNewsAttachmentMockMvc.perform(get(ENTITY_API_URL + "?eagerload=false")).andExpect(status().isOk());
        verify(newsAttachmentRepositoryMock, times(1)).findAll(any(Pageable.class));
    }

    @Test
    @Transactional
    void getNewsAttachment() throws Exception {
        // Initialize the database
        insertedNewsAttachment = newsAttachmentRepository.saveAndFlush(newsAttachment);

        // Get the newsAttachment
        restNewsAttachmentMockMvc
            .perform(get(ENTITY_API_URL_ID, newsAttachment.getId()))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.id").value(newsAttachment.getId().intValue()))
            .andExpect(jsonPath("$.name").value(DEFAULT_NAME))
            .andExpect(jsonPath("$.type").value(DEFAULT_TYPE))
            .andExpect(jsonPath("$.url").value(DEFAULT_URL))
            .andExpect(jsonPath("$.fileSize").value(DEFAULT_FILE_SIZE.intValue()))
            .andExpect(jsonPath("$.sortOrder").value(DEFAULT_SORT_ORDER))
            .andExpect(jsonPath("$.metadata").value(DEFAULT_METADATA))
            .andExpect(jsonPath("$.version").value(DEFAULT_VERSION))
            .andExpect(jsonPath("$.createdBy").value(DEFAULT_CREATED_BY))
            .andExpect(jsonPath("$.createdAt").value(DEFAULT_CREATED_AT.toString()))
            .andExpect(jsonPath("$.updatedBy").value(DEFAULT_UPDATED_BY))
            .andExpect(jsonPath("$.updatedAt").value(DEFAULT_UPDATED_AT.toString()))
            .andExpect(jsonPath("$.isDeleted").value(DEFAULT_IS_DELETED));
    }

    @Test
    @Transactional
    void getNonExistingNewsAttachment() throws Exception {
        // Get the newsAttachment
        restNewsAttachmentMockMvc.perform(get(ENTITY_API_URL_ID, Long.MAX_VALUE)).andExpect(status().isNotFound());
    }

    @Test
    @Transactional
    void putExistingNewsAttachment() throws Exception {
        // Initialize the database
        insertedNewsAttachment = newsAttachmentRepository.saveAndFlush(newsAttachment);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the newsAttachment
        NewsAttachment updatedNewsAttachment = newsAttachmentRepository.findById(newsAttachment.getId()).orElseThrow();
        // Disconnect from session so that the updates on updatedNewsAttachment are not directly saved in db
        em.detach(updatedNewsAttachment);
        updatedNewsAttachment
            .name(UPDATED_NAME)
            .type(UPDATED_TYPE)
            .url(UPDATED_URL)
            .fileSize(UPDATED_FILE_SIZE)
            .sortOrder(UPDATED_SORT_ORDER)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);
        NewsAttachmentDTO newsAttachmentDTO = newsAttachmentMapper.toDto(updatedNewsAttachment);

        restNewsAttachmentMockMvc
            .perform(
                put(ENTITY_API_URL_ID, newsAttachmentDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(newsAttachmentDTO))
            )
            .andExpect(status().isOk());

        // Validate the NewsAttachment in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertPersistedNewsAttachmentToMatchAllProperties(updatedNewsAttachment);
    }

    @Test
    @Transactional
    void putNonExistingNewsAttachment() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        newsAttachment.setId(longCount.incrementAndGet());

        // Create the NewsAttachment
        NewsAttachmentDTO newsAttachmentDTO = newsAttachmentMapper.toDto(newsAttachment);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restNewsAttachmentMockMvc
            .perform(
                put(ENTITY_API_URL_ID, newsAttachmentDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(newsAttachmentDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the NewsAttachment in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithIdMismatchNewsAttachment() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        newsAttachment.setId(longCount.incrementAndGet());

        // Create the NewsAttachment
        NewsAttachmentDTO newsAttachmentDTO = newsAttachmentMapper.toDto(newsAttachment);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restNewsAttachmentMockMvc
            .perform(
                put(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(newsAttachmentDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the NewsAttachment in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithMissingIdPathParamNewsAttachment() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        newsAttachment.setId(longCount.incrementAndGet());

        // Create the NewsAttachment
        NewsAttachmentDTO newsAttachmentDTO = newsAttachmentMapper.toDto(newsAttachment);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restNewsAttachmentMockMvc
            .perform(put(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(newsAttachmentDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the NewsAttachment in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void partialUpdateNewsAttachmentWithPatch() throws Exception {
        // Initialize the database
        insertedNewsAttachment = newsAttachmentRepository.saveAndFlush(newsAttachment);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the newsAttachment using partial update
        NewsAttachment partialUpdatedNewsAttachment = new NewsAttachment();
        partialUpdatedNewsAttachment.setId(newsAttachment.getId());

        partialUpdatedNewsAttachment
            .type(UPDATED_TYPE)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .updatedBy(UPDATED_UPDATED_BY)
            .isDeleted(UPDATED_IS_DELETED);

        restNewsAttachmentMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedNewsAttachment.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedNewsAttachment))
            )
            .andExpect(status().isOk());

        // Validate the NewsAttachment in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertNewsAttachmentUpdatableFieldsEquals(
            createUpdateProxyForBean(partialUpdatedNewsAttachment, newsAttachment),
            getPersistedNewsAttachment(newsAttachment)
        );
    }

    @Test
    @Transactional
    void fullUpdateNewsAttachmentWithPatch() throws Exception {
        // Initialize the database
        insertedNewsAttachment = newsAttachmentRepository.saveAndFlush(newsAttachment);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the newsAttachment using partial update
        NewsAttachment partialUpdatedNewsAttachment = new NewsAttachment();
        partialUpdatedNewsAttachment.setId(newsAttachment.getId());

        partialUpdatedNewsAttachment
            .name(UPDATED_NAME)
            .type(UPDATED_TYPE)
            .url(UPDATED_URL)
            .fileSize(UPDATED_FILE_SIZE)
            .sortOrder(UPDATED_SORT_ORDER)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);

        restNewsAttachmentMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedNewsAttachment.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedNewsAttachment))
            )
            .andExpect(status().isOk());

        // Validate the NewsAttachment in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertNewsAttachmentUpdatableFieldsEquals(partialUpdatedNewsAttachment, getPersistedNewsAttachment(partialUpdatedNewsAttachment));
    }

    @Test
    @Transactional
    void patchNonExistingNewsAttachment() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        newsAttachment.setId(longCount.incrementAndGet());

        // Create the NewsAttachment
        NewsAttachmentDTO newsAttachmentDTO = newsAttachmentMapper.toDto(newsAttachment);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restNewsAttachmentMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, newsAttachmentDTO.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(newsAttachmentDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the NewsAttachment in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithIdMismatchNewsAttachment() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        newsAttachment.setId(longCount.incrementAndGet());

        // Create the NewsAttachment
        NewsAttachmentDTO newsAttachmentDTO = newsAttachmentMapper.toDto(newsAttachment);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restNewsAttachmentMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(newsAttachmentDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the NewsAttachment in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithMissingIdPathParamNewsAttachment() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        newsAttachment.setId(longCount.incrementAndGet());

        // Create the NewsAttachment
        NewsAttachmentDTO newsAttachmentDTO = newsAttachmentMapper.toDto(newsAttachment);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restNewsAttachmentMockMvc
            .perform(patch(ENTITY_API_URL).contentType("application/merge-patch+json").content(om.writeValueAsBytes(newsAttachmentDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the NewsAttachment in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void deleteNewsAttachment() throws Exception {
        // Initialize the database
        insertedNewsAttachment = newsAttachmentRepository.saveAndFlush(newsAttachment);

        long databaseSizeBeforeDelete = getRepositoryCount();

        // Delete the newsAttachment
        restNewsAttachmentMockMvc
            .perform(delete(ENTITY_API_URL_ID, newsAttachment.getId()).accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isNoContent());

        // Validate the database contains one less item
        assertDecrementedRepositoryCount(databaseSizeBeforeDelete);
    }

    protected long getRepositoryCount() {
        return newsAttachmentRepository.count();
    }

    protected void assertIncrementedRepositoryCount(long countBefore) {
        assertThat(countBefore + 1).isEqualTo(getRepositoryCount());
    }

    protected void assertDecrementedRepositoryCount(long countBefore) {
        assertThat(countBefore - 1).isEqualTo(getRepositoryCount());
    }

    protected void assertSameRepositoryCount(long countBefore) {
        assertThat(countBefore).isEqualTo(getRepositoryCount());
    }

    protected NewsAttachment getPersistedNewsAttachment(NewsAttachment newsAttachment) {
        return newsAttachmentRepository.findById(newsAttachment.getId()).orElseThrow();
    }

    protected void assertPersistedNewsAttachmentToMatchAllProperties(NewsAttachment expectedNewsAttachment) {
        assertNewsAttachmentAllPropertiesEquals(expectedNewsAttachment, getPersistedNewsAttachment(expectedNewsAttachment));
    }

    protected void assertPersistedNewsAttachmentToMatchUpdatableProperties(NewsAttachment expectedNewsAttachment) {
        assertNewsAttachmentAllUpdatablePropertiesEquals(expectedNewsAttachment, getPersistedNewsAttachment(expectedNewsAttachment));
    }
}

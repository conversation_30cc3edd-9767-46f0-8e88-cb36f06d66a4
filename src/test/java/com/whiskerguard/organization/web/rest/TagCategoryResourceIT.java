package com.whiskerguard.organization.web.rest;

import static com.whiskerguard.organization.domain.TagCategoryAsserts.*;
import static com.whiskerguard.organization.web.rest.TestUtil.createUpdateProxyForBean;
import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.hasItem;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.whiskerguard.organization.IntegrationTest;
import com.whiskerguard.organization.domain.TagCategory;
import com.whiskerguard.organization.repository.TagCategoryRepository;
import com.whiskerguard.organization.service.dto.TagCategoryDTO;
import com.whiskerguard.organization.service.mapper.TagCategoryMapper;
import jakarta.persistence.EntityManager;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Random;
import java.util.concurrent.atomic.AtomicLong;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

/**
 * Integration tests for the {@link TagCategoryResource} REST controller.
 */
@IntegrationTest
@AutoConfigureMockMvc
@WithMockUser
class TagCategoryResourceIT {

    private static final String DEFAULT_NAME = "AAAAAAAAAA";
    private static final String UPDATED_NAME = "BBBBBBBBBB";

    private static final String DEFAULT_DESCRIPTION = "AAAAAAAAAA";
    private static final String UPDATED_DESCRIPTION = "BBBBBBBBBB";

    private static final Integer DEFAULT_VERSION = 1;
    private static final Integer UPDATED_VERSION = 2;

    private static final String DEFAULT_CREATED_BY = "AAAAAAAAAA";
    private static final String UPDATED_CREATED_BY = "BBBBBBBBBB";

    private static final Instant DEFAULT_CREATED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_CREATED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final String DEFAULT_UPDATED_BY = "AAAAAAAAAA";
    private static final String UPDATED_UPDATED_BY = "BBBBBBBBBB";

    private static final Instant DEFAULT_UPDATED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_UPDATED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final Boolean DEFAULT_IS_DELETED = false;
    private static final Boolean UPDATED_IS_DELETED = true;

    private static final String ENTITY_API_URL = "/api/tag-categories";
    private static final String ENTITY_API_URL_ID = ENTITY_API_URL + "/{id}";

    private static Random random = new Random();
    private static AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));

    @Autowired
    private ObjectMapper om;

    @Autowired
    private TagCategoryRepository tagCategoryRepository;

    @Autowired
    private TagCategoryMapper tagCategoryMapper;

    @Autowired
    private EntityManager em;

    @Autowired
    private MockMvc restTagCategoryMockMvc;

    private TagCategory tagCategory;

    private TagCategory insertedTagCategory;

    /**
     * Create an entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static TagCategory createEntity() {
        return new TagCategory()
            .name(DEFAULT_NAME)
            .description(DEFAULT_DESCRIPTION)
            .version(DEFAULT_VERSION)
            .createdBy(DEFAULT_CREATED_BY)
            .createdAt(DEFAULT_CREATED_AT)
            .updatedBy(DEFAULT_UPDATED_BY)
            .updatedAt(DEFAULT_UPDATED_AT)
            .isDeleted(DEFAULT_IS_DELETED);
    }

    /**
     * Create an updated entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static TagCategory createUpdatedEntity() {
        return new TagCategory()
            .name(UPDATED_NAME)
            .description(UPDATED_DESCRIPTION)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);
    }

    @BeforeEach
    void initTest() {
        tagCategory = createEntity();
    }

    @AfterEach
    void cleanup() {
        if (insertedTagCategory != null) {
            tagCategoryRepository.delete(insertedTagCategory);
            insertedTagCategory = null;
        }
    }

    @Test
    @Transactional
    void createTagCategory() throws Exception {
        long databaseSizeBeforeCreate = getRepositoryCount();
        // Create the TagCategory
        TagCategoryDTO tagCategoryDTO = tagCategoryMapper.toDto(tagCategory);
        var returnedTagCategoryDTO = om.readValue(
            restTagCategoryMockMvc
                .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(tagCategoryDTO)))
                .andExpect(status().isCreated())
                .andReturn()
                .getResponse()
                .getContentAsString(),
            TagCategoryDTO.class
        );

        // Validate the TagCategory in the database
        assertIncrementedRepositoryCount(databaseSizeBeforeCreate);
        var returnedTagCategory = tagCategoryMapper.toEntity(returnedTagCategoryDTO);
        assertTagCategoryUpdatableFieldsEquals(returnedTagCategory, getPersistedTagCategory(returnedTagCategory));

        insertedTagCategory = returnedTagCategory;
    }

    @Test
    @Transactional
    void createTagCategoryWithExistingId() throws Exception {
        // Create the TagCategory with an existing ID
        tagCategory.setId(1L);
        TagCategoryDTO tagCategoryDTO = tagCategoryMapper.toDto(tagCategory);

        long databaseSizeBeforeCreate = getRepositoryCount();

        // An entity with an existing ID cannot be created, so this API call must fail
        restTagCategoryMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(tagCategoryDTO)))
            .andExpect(status().isBadRequest());

        // Validate the TagCategory in the database
        assertSameRepositoryCount(databaseSizeBeforeCreate);
    }

    @Test
    @Transactional
    void checkNameIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        tagCategory.setName(null);

        // Create the TagCategory, which fails.
        TagCategoryDTO tagCategoryDTO = tagCategoryMapper.toDto(tagCategory);

        restTagCategoryMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(tagCategoryDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkVersionIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        tagCategory.setVersion(null);

        // Create the TagCategory, which fails.
        TagCategoryDTO tagCategoryDTO = tagCategoryMapper.toDto(tagCategory);

        restTagCategoryMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(tagCategoryDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkCreatedAtIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        tagCategory.setCreatedAt(null);

        // Create the TagCategory, which fails.
        TagCategoryDTO tagCategoryDTO = tagCategoryMapper.toDto(tagCategory);

        restTagCategoryMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(tagCategoryDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkUpdatedAtIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        tagCategory.setUpdatedAt(null);

        // Create the TagCategory, which fails.
        TagCategoryDTO tagCategoryDTO = tagCategoryMapper.toDto(tagCategory);

        restTagCategoryMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(tagCategoryDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkIsDeletedIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        tagCategory.setIsDeleted(null);

        // Create the TagCategory, which fails.
        TagCategoryDTO tagCategoryDTO = tagCategoryMapper.toDto(tagCategory);

        restTagCategoryMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(tagCategoryDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void getAllTagCategories() throws Exception {
        // Initialize the database
        insertedTagCategory = tagCategoryRepository.saveAndFlush(tagCategory);

        // Get all the tagCategoryList
        restTagCategoryMockMvc
            .perform(get(ENTITY_API_URL + "?sort=id,desc"))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.[*].id").value(hasItem(tagCategory.getId().intValue())))
            .andExpect(jsonPath("$.[*].name").value(hasItem(DEFAULT_NAME)))
            .andExpect(jsonPath("$.[*].description").value(hasItem(DEFAULT_DESCRIPTION)))
            .andExpect(jsonPath("$.[*].version").value(hasItem(DEFAULT_VERSION)))
            .andExpect(jsonPath("$.[*].createdBy").value(hasItem(DEFAULT_CREATED_BY)))
            .andExpect(jsonPath("$.[*].createdAt").value(hasItem(DEFAULT_CREATED_AT.toString())))
            .andExpect(jsonPath("$.[*].updatedBy").value(hasItem(DEFAULT_UPDATED_BY)))
            .andExpect(jsonPath("$.[*].updatedAt").value(hasItem(DEFAULT_UPDATED_AT.toString())))
            .andExpect(jsonPath("$.[*].isDeleted").value(hasItem(DEFAULT_IS_DELETED)));
    }

    @Test
    @Transactional
    void getTagCategory() throws Exception {
        // Initialize the database
        insertedTagCategory = tagCategoryRepository.saveAndFlush(tagCategory);

        // Get the tagCategory
        restTagCategoryMockMvc
            .perform(get(ENTITY_API_URL_ID, tagCategory.getId()))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.id").value(tagCategory.getId().intValue()))
            .andExpect(jsonPath("$.name").value(DEFAULT_NAME))
            .andExpect(jsonPath("$.description").value(DEFAULT_DESCRIPTION))
            .andExpect(jsonPath("$.version").value(DEFAULT_VERSION))
            .andExpect(jsonPath("$.createdBy").value(DEFAULT_CREATED_BY))
            .andExpect(jsonPath("$.createdAt").value(DEFAULT_CREATED_AT.toString()))
            .andExpect(jsonPath("$.updatedBy").value(DEFAULT_UPDATED_BY))
            .andExpect(jsonPath("$.updatedAt").value(DEFAULT_UPDATED_AT.toString()))
            .andExpect(jsonPath("$.isDeleted").value(DEFAULT_IS_DELETED));
    }

    @Test
    @Transactional
    void getNonExistingTagCategory() throws Exception {
        // Get the tagCategory
        restTagCategoryMockMvc.perform(get(ENTITY_API_URL_ID, Long.MAX_VALUE)).andExpect(status().isNotFound());
    }

    @Test
    @Transactional
    void putExistingTagCategory() throws Exception {
        // Initialize the database
        insertedTagCategory = tagCategoryRepository.saveAndFlush(tagCategory);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the tagCategory
        TagCategory updatedTagCategory = tagCategoryRepository.findById(tagCategory.getId()).orElseThrow();
        // Disconnect from session so that the updates on updatedTagCategory are not directly saved in db
        em.detach(updatedTagCategory);
        updatedTagCategory
            .name(UPDATED_NAME)
            .description(UPDATED_DESCRIPTION)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);
        TagCategoryDTO tagCategoryDTO = tagCategoryMapper.toDto(updatedTagCategory);

        restTagCategoryMockMvc
            .perform(
                put(ENTITY_API_URL_ID, tagCategoryDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(tagCategoryDTO))
            )
            .andExpect(status().isOk());

        // Validate the TagCategory in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertPersistedTagCategoryToMatchAllProperties(updatedTagCategory);
    }

    @Test
    @Transactional
    void putNonExistingTagCategory() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        tagCategory.setId(longCount.incrementAndGet());

        // Create the TagCategory
        TagCategoryDTO tagCategoryDTO = tagCategoryMapper.toDto(tagCategory);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restTagCategoryMockMvc
            .perform(
                put(ENTITY_API_URL_ID, tagCategoryDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(tagCategoryDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the TagCategory in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithIdMismatchTagCategory() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        tagCategory.setId(longCount.incrementAndGet());

        // Create the TagCategory
        TagCategoryDTO tagCategoryDTO = tagCategoryMapper.toDto(tagCategory);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restTagCategoryMockMvc
            .perform(
                put(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(tagCategoryDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the TagCategory in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithMissingIdPathParamTagCategory() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        tagCategory.setId(longCount.incrementAndGet());

        // Create the TagCategory
        TagCategoryDTO tagCategoryDTO = tagCategoryMapper.toDto(tagCategory);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restTagCategoryMockMvc
            .perform(put(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(tagCategoryDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the TagCategory in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void partialUpdateTagCategoryWithPatch() throws Exception {
        // Initialize the database
        insertedTagCategory = tagCategoryRepository.saveAndFlush(tagCategory);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the tagCategory using partial update
        TagCategory partialUpdatedTagCategory = new TagCategory();
        partialUpdatedTagCategory.setId(tagCategory.getId());

        partialUpdatedTagCategory
            .name(UPDATED_NAME)
            .description(UPDATED_DESCRIPTION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT);

        restTagCategoryMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedTagCategory.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedTagCategory))
            )
            .andExpect(status().isOk());

        // Validate the TagCategory in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertTagCategoryUpdatableFieldsEquals(
            createUpdateProxyForBean(partialUpdatedTagCategory, tagCategory),
            getPersistedTagCategory(tagCategory)
        );
    }

    @Test
    @Transactional
    void fullUpdateTagCategoryWithPatch() throws Exception {
        // Initialize the database
        insertedTagCategory = tagCategoryRepository.saveAndFlush(tagCategory);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the tagCategory using partial update
        TagCategory partialUpdatedTagCategory = new TagCategory();
        partialUpdatedTagCategory.setId(tagCategory.getId());

        partialUpdatedTagCategory
            .name(UPDATED_NAME)
            .description(UPDATED_DESCRIPTION)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);

        restTagCategoryMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedTagCategory.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedTagCategory))
            )
            .andExpect(status().isOk());

        // Validate the TagCategory in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertTagCategoryUpdatableFieldsEquals(partialUpdatedTagCategory, getPersistedTagCategory(partialUpdatedTagCategory));
    }

    @Test
    @Transactional
    void patchNonExistingTagCategory() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        tagCategory.setId(longCount.incrementAndGet());

        // Create the TagCategory
        TagCategoryDTO tagCategoryDTO = tagCategoryMapper.toDto(tagCategory);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restTagCategoryMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, tagCategoryDTO.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(tagCategoryDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the TagCategory in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithIdMismatchTagCategory() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        tagCategory.setId(longCount.incrementAndGet());

        // Create the TagCategory
        TagCategoryDTO tagCategoryDTO = tagCategoryMapper.toDto(tagCategory);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restTagCategoryMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(tagCategoryDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the TagCategory in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithMissingIdPathParamTagCategory() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        tagCategory.setId(longCount.incrementAndGet());

        // Create the TagCategory
        TagCategoryDTO tagCategoryDTO = tagCategoryMapper.toDto(tagCategory);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restTagCategoryMockMvc
            .perform(patch(ENTITY_API_URL).contentType("application/merge-patch+json").content(om.writeValueAsBytes(tagCategoryDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the TagCategory in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void deleteTagCategory() throws Exception {
        // Initialize the database
        insertedTagCategory = tagCategoryRepository.saveAndFlush(tagCategory);

        long databaseSizeBeforeDelete = getRepositoryCount();

        // Delete the tagCategory
        restTagCategoryMockMvc
            .perform(delete(ENTITY_API_URL_ID, tagCategory.getId()).accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isNoContent());

        // Validate the database contains one less item
        assertDecrementedRepositoryCount(databaseSizeBeforeDelete);
    }

    protected long getRepositoryCount() {
        return tagCategoryRepository.count();
    }

    protected void assertIncrementedRepositoryCount(long countBefore) {
        assertThat(countBefore + 1).isEqualTo(getRepositoryCount());
    }

    protected void assertDecrementedRepositoryCount(long countBefore) {
        assertThat(countBefore - 1).isEqualTo(getRepositoryCount());
    }

    protected void assertSameRepositoryCount(long countBefore) {
        assertThat(countBefore).isEqualTo(getRepositoryCount());
    }

    protected TagCategory getPersistedTagCategory(TagCategory tagCategory) {
        return tagCategoryRepository.findById(tagCategory.getId()).orElseThrow();
    }

    protected void assertPersistedTagCategoryToMatchAllProperties(TagCategory expectedTagCategory) {
        assertTagCategoryAllPropertiesEquals(expectedTagCategory, getPersistedTagCategory(expectedTagCategory));
    }

    protected void assertPersistedTagCategoryToMatchUpdatableProperties(TagCategory expectedTagCategory) {
        assertTagCategoryAllUpdatablePropertiesEquals(expectedTagCategory, getPersistedTagCategory(expectedTagCategory));
    }
}

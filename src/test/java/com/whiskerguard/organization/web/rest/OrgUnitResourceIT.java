package com.whiskerguard.organization.web.rest;

import static com.whiskerguard.organization.domain.OrgUnitAsserts.*;
import static com.whiskerguard.organization.web.rest.TestUtil.createUpdateProxyForBean;
import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.hasItem;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.whiskerguard.organization.IntegrationTest;
import com.whiskerguard.organization.domain.OrgUnit;
import com.whiskerguard.organization.domain.enumeration.OrgUnitType;
import com.whiskerguard.organization.repository.OrgUnitRepository;
import com.whiskerguard.organization.service.dto.OrgUnitDTO;
import com.whiskerguard.organization.service.mapper.OrgUnitMapper;
import jakarta.persistence.EntityManager;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Random;
import java.util.concurrent.atomic.AtomicLong;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

/**
 * Integration tests for the {@link OrgUnitResource} REST controller.
 */
@IntegrationTest
@AutoConfigureMockMvc
@WithMockUser
class OrgUnitResourceIT {

    private static final Long DEFAULT_TENANT_ID = 1L;
    private static final Long UPDATED_TENANT_ID = 2L;

    private static final String DEFAULT_NAME = "AAAAAAAAAA";
    private static final String UPDATED_NAME = "BBBBBBBBBB";

    private static final String DEFAULT_CODE = "AAAAAAAAAA";
    private static final String UPDATED_CODE = "BBBBBBBBBB";

    private static final OrgUnitType DEFAULT_TYPE = OrgUnitType.COMPANY;
    private static final OrgUnitType UPDATED_TYPE = OrgUnitType.DEPARTMENT;

    private static final Integer DEFAULT_LEVEL = 1;
    private static final Integer UPDATED_LEVEL = 2;

    private static final Integer DEFAULT_STATUS = 1;
    private static final Integer UPDATED_STATUS = 2;

    private static final Integer DEFAULT_SORT_ORDER = 1;
    private static final Integer UPDATED_SORT_ORDER = 2;

    private static final String DEFAULT_DESCRIPTION = "AAAAAAAAAA";
    private static final String UPDATED_DESCRIPTION = "BBBBBBBBBB";

    private static final String DEFAULT_METADATA = "AAAAAAAAAA";
    private static final String UPDATED_METADATA = "BBBBBBBBBB";

    private static final Integer DEFAULT_VERSION = 1;
    private static final Integer UPDATED_VERSION = 2;

    private static final String DEFAULT_CREATED_BY = "AAAAAAAAAA";
    private static final String UPDATED_CREATED_BY = "BBBBBBBBBB";

    private static final Instant DEFAULT_CREATED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_CREATED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final String DEFAULT_UPDATED_BY = "AAAAAAAAAA";
    private static final String UPDATED_UPDATED_BY = "BBBBBBBBBB";

    private static final Instant DEFAULT_UPDATED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_UPDATED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final Boolean DEFAULT_IS_DELETED = false;
    private static final Boolean UPDATED_IS_DELETED = true;

    private static final String ENTITY_API_URL = "/api/org-units";
    private static final String ENTITY_API_URL_ID = ENTITY_API_URL + "/{id}";

    private static Random random = new Random();
    private static AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));

    @Autowired
    private ObjectMapper om;

    @Autowired
    private OrgUnitRepository orgUnitRepository;

    @Autowired
    private OrgUnitMapper orgUnitMapper;

    @Autowired
    private EntityManager em;

    @Autowired
    private MockMvc restOrgUnitMockMvc;

    private OrgUnit orgUnit;

    private OrgUnit insertedOrgUnit;

    /**
     * Create an entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static OrgUnit createEntity() {
        return new OrgUnit()
            .tenantId(DEFAULT_TENANT_ID)
            .name(DEFAULT_NAME)
            .code(DEFAULT_CODE)
            .type(DEFAULT_TYPE)
            .level(DEFAULT_LEVEL)
            .status(DEFAULT_STATUS)
            .sortOrder(DEFAULT_SORT_ORDER)
            .description(DEFAULT_DESCRIPTION)
            .metadata(DEFAULT_METADATA)
            .version(DEFAULT_VERSION)
            .createdBy(DEFAULT_CREATED_BY)
            .createdAt(DEFAULT_CREATED_AT)
            .updatedBy(DEFAULT_UPDATED_BY)
            .updatedAt(DEFAULT_UPDATED_AT)
            .isDeleted(DEFAULT_IS_DELETED);
    }

    /**
     * Create an updated entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static OrgUnit createUpdatedEntity() {
        return new OrgUnit()
            .tenantId(UPDATED_TENANT_ID)
            .name(UPDATED_NAME)
            .code(UPDATED_CODE)
            .type(UPDATED_TYPE)
            .level(UPDATED_LEVEL)
            .status(UPDATED_STATUS)
            .sortOrder(UPDATED_SORT_ORDER)
            .description(UPDATED_DESCRIPTION)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);
    }

    @BeforeEach
    void initTest() {
        orgUnit = createEntity();
    }

    @AfterEach
    void cleanup() {
        if (insertedOrgUnit != null) {
            orgUnitRepository.delete(insertedOrgUnit);
            insertedOrgUnit = null;
        }
    }

    @Test
    @Transactional
    void createOrgUnit() throws Exception {
        long databaseSizeBeforeCreate = getRepositoryCount();
        // Create the OrgUnit
        OrgUnitDTO orgUnitDTO = orgUnitMapper.toDto(orgUnit);
        var returnedOrgUnitDTO = om.readValue(
            restOrgUnitMockMvc
                .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(orgUnitDTO)))
                .andExpect(status().isCreated())
                .andReturn()
                .getResponse()
                .getContentAsString(),
            OrgUnitDTO.class
        );

        // Validate the OrgUnit in the database
        assertIncrementedRepositoryCount(databaseSizeBeforeCreate);
        var returnedOrgUnit = orgUnitMapper.toEntity(returnedOrgUnitDTO);
        assertOrgUnitUpdatableFieldsEquals(returnedOrgUnit, getPersistedOrgUnit(returnedOrgUnit));

        insertedOrgUnit = returnedOrgUnit;
    }

    @Test
    @Transactional
    void createOrgUnitWithExistingId() throws Exception {
        // Create the OrgUnit with an existing ID
        orgUnit.setId(1L);
        OrgUnitDTO orgUnitDTO = orgUnitMapper.toDto(orgUnit);

        long databaseSizeBeforeCreate = getRepositoryCount();

        // An entity with an existing ID cannot be created, so this API call must fail
        restOrgUnitMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(orgUnitDTO)))
            .andExpect(status().isBadRequest());

        // Validate the OrgUnit in the database
        assertSameRepositoryCount(databaseSizeBeforeCreate);
    }

    @Test
    @Transactional
    void checkTenantIdIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        orgUnit.setTenantId(null);

        // Create the OrgUnit, which fails.
        OrgUnitDTO orgUnitDTO = orgUnitMapper.toDto(orgUnit);

        restOrgUnitMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(orgUnitDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkNameIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        orgUnit.setName(null);

        // Create the OrgUnit, which fails.
        OrgUnitDTO orgUnitDTO = orgUnitMapper.toDto(orgUnit);

        restOrgUnitMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(orgUnitDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkCodeIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        orgUnit.setCode(null);

        // Create the OrgUnit, which fails.
        OrgUnitDTO orgUnitDTO = orgUnitMapper.toDto(orgUnit);

        restOrgUnitMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(orgUnitDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkTypeIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        orgUnit.setType(null);

        // Create the OrgUnit, which fails.
        OrgUnitDTO orgUnitDTO = orgUnitMapper.toDto(orgUnit);

        restOrgUnitMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(orgUnitDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkLevelIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        orgUnit.setLevel(null);

        // Create the OrgUnit, which fails.
        OrgUnitDTO orgUnitDTO = orgUnitMapper.toDto(orgUnit);

        restOrgUnitMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(orgUnitDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkStatusIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        orgUnit.setStatus(null);

        // Create the OrgUnit, which fails.
        OrgUnitDTO orgUnitDTO = orgUnitMapper.toDto(orgUnit);

        restOrgUnitMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(orgUnitDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkVersionIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        orgUnit.setVersion(null);

        // Create the OrgUnit, which fails.
        OrgUnitDTO orgUnitDTO = orgUnitMapper.toDto(orgUnit);

        restOrgUnitMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(orgUnitDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkCreatedAtIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        orgUnit.setCreatedAt(null);

        // Create the OrgUnit, which fails.
        OrgUnitDTO orgUnitDTO = orgUnitMapper.toDto(orgUnit);

        restOrgUnitMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(orgUnitDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkUpdatedAtIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        orgUnit.setUpdatedAt(null);

        // Create the OrgUnit, which fails.
        OrgUnitDTO orgUnitDTO = orgUnitMapper.toDto(orgUnit);

        restOrgUnitMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(orgUnitDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkIsDeletedIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        orgUnit.setIsDeleted(null);

        // Create the OrgUnit, which fails.
        OrgUnitDTO orgUnitDTO = orgUnitMapper.toDto(orgUnit);

        restOrgUnitMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(orgUnitDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void getAllOrgUnits() throws Exception {
        // Initialize the database
        insertedOrgUnit = orgUnitRepository.saveAndFlush(orgUnit);

        // Get all the orgUnitList
        restOrgUnitMockMvc
            .perform(get(ENTITY_API_URL + "?sort=id,desc"))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.[*].id").value(hasItem(orgUnit.getId().intValue())))
            .andExpect(jsonPath("$.[*].tenantId").value(hasItem(DEFAULT_TENANT_ID.intValue())))
            .andExpect(jsonPath("$.[*].name").value(hasItem(DEFAULT_NAME)))
            .andExpect(jsonPath("$.[*].code").value(hasItem(DEFAULT_CODE)))
            .andExpect(jsonPath("$.[*].type").value(hasItem(DEFAULT_TYPE.toString())))
            .andExpect(jsonPath("$.[*].level").value(hasItem(DEFAULT_LEVEL)))
            .andExpect(jsonPath("$.[*].status").value(hasItem(DEFAULT_STATUS)))
            .andExpect(jsonPath("$.[*].sortOrder").value(hasItem(DEFAULT_SORT_ORDER)))
            .andExpect(jsonPath("$.[*].description").value(hasItem(DEFAULT_DESCRIPTION)))
            .andExpect(jsonPath("$.[*].metadata").value(hasItem(DEFAULT_METADATA)))
            .andExpect(jsonPath("$.[*].version").value(hasItem(DEFAULT_VERSION)))
            .andExpect(jsonPath("$.[*].createdBy").value(hasItem(DEFAULT_CREATED_BY)))
            .andExpect(jsonPath("$.[*].createdAt").value(hasItem(DEFAULT_CREATED_AT.toString())))
            .andExpect(jsonPath("$.[*].updatedBy").value(hasItem(DEFAULT_UPDATED_BY)))
            .andExpect(jsonPath("$.[*].updatedAt").value(hasItem(DEFAULT_UPDATED_AT.toString())))
            .andExpect(jsonPath("$.[*].isDeleted").value(hasItem(DEFAULT_IS_DELETED)));
    }

    @Test
    @Transactional
    void getOrgUnit() throws Exception {
        // Initialize the database
        insertedOrgUnit = orgUnitRepository.saveAndFlush(orgUnit);

        // Get the orgUnit
        restOrgUnitMockMvc
            .perform(get(ENTITY_API_URL_ID, orgUnit.getId()))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.id").value(orgUnit.getId().intValue()))
            .andExpect(jsonPath("$.tenantId").value(DEFAULT_TENANT_ID.intValue()))
            .andExpect(jsonPath("$.name").value(DEFAULT_NAME))
            .andExpect(jsonPath("$.code").value(DEFAULT_CODE))
            .andExpect(jsonPath("$.type").value(DEFAULT_TYPE.toString()))
            .andExpect(jsonPath("$.level").value(DEFAULT_LEVEL))
            .andExpect(jsonPath("$.status").value(DEFAULT_STATUS))
            .andExpect(jsonPath("$.sortOrder").value(DEFAULT_SORT_ORDER))
            .andExpect(jsonPath("$.description").value(DEFAULT_DESCRIPTION))
            .andExpect(jsonPath("$.metadata").value(DEFAULT_METADATA))
            .andExpect(jsonPath("$.version").value(DEFAULT_VERSION))
            .andExpect(jsonPath("$.createdBy").value(DEFAULT_CREATED_BY))
            .andExpect(jsonPath("$.createdAt").value(DEFAULT_CREATED_AT.toString()))
            .andExpect(jsonPath("$.updatedBy").value(DEFAULT_UPDATED_BY))
            .andExpect(jsonPath("$.updatedAt").value(DEFAULT_UPDATED_AT.toString()))
            .andExpect(jsonPath("$.isDeleted").value(DEFAULT_IS_DELETED));
    }

    @Test
    @Transactional
    void getNonExistingOrgUnit() throws Exception {
        // Get the orgUnit
        restOrgUnitMockMvc.perform(get(ENTITY_API_URL_ID, Long.MAX_VALUE)).andExpect(status().isNotFound());
    }

    @Test
    @Transactional
    void putExistingOrgUnit() throws Exception {
        // Initialize the database
        insertedOrgUnit = orgUnitRepository.saveAndFlush(orgUnit);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the orgUnit
        OrgUnit updatedOrgUnit = orgUnitRepository.findById(orgUnit.getId()).orElseThrow();
        // Disconnect from session so that the updates on updatedOrgUnit are not directly saved in db
        em.detach(updatedOrgUnit);
        updatedOrgUnit
            .tenantId(UPDATED_TENANT_ID)
            .name(UPDATED_NAME)
            .code(UPDATED_CODE)
            .type(UPDATED_TYPE)
            .level(UPDATED_LEVEL)
            .status(UPDATED_STATUS)
            .sortOrder(UPDATED_SORT_ORDER)
            .description(UPDATED_DESCRIPTION)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);
        OrgUnitDTO orgUnitDTO = orgUnitMapper.toDto(updatedOrgUnit);

        restOrgUnitMockMvc
            .perform(
                put(ENTITY_API_URL_ID, orgUnitDTO.getId()).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(orgUnitDTO))
            )
            .andExpect(status().isOk());

        // Validate the OrgUnit in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertPersistedOrgUnitToMatchAllProperties(updatedOrgUnit);
    }

    @Test
    @Transactional
    void putNonExistingOrgUnit() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        orgUnit.setId(longCount.incrementAndGet());

        // Create the OrgUnit
        OrgUnitDTO orgUnitDTO = orgUnitMapper.toDto(orgUnit);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restOrgUnitMockMvc
            .perform(
                put(ENTITY_API_URL_ID, orgUnitDTO.getId()).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(orgUnitDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the OrgUnit in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithIdMismatchOrgUnit() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        orgUnit.setId(longCount.incrementAndGet());

        // Create the OrgUnit
        OrgUnitDTO orgUnitDTO = orgUnitMapper.toDto(orgUnit);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restOrgUnitMockMvc
            .perform(
                put(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(orgUnitDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the OrgUnit in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithMissingIdPathParamOrgUnit() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        orgUnit.setId(longCount.incrementAndGet());

        // Create the OrgUnit
        OrgUnitDTO orgUnitDTO = orgUnitMapper.toDto(orgUnit);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restOrgUnitMockMvc
            .perform(put(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(orgUnitDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the OrgUnit in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void partialUpdateOrgUnitWithPatch() throws Exception {
        // Initialize the database
        insertedOrgUnit = orgUnitRepository.saveAndFlush(orgUnit);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the orgUnit using partial update
        OrgUnit partialUpdatedOrgUnit = new OrgUnit();
        partialUpdatedOrgUnit.setId(orgUnit.getId());

        partialUpdatedOrgUnit
            .name(UPDATED_NAME)
            .code(UPDATED_CODE)
            .level(UPDATED_LEVEL)
            .status(UPDATED_STATUS)
            .sortOrder(UPDATED_SORT_ORDER)
            .description(UPDATED_DESCRIPTION)
            .version(UPDATED_VERSION)
            .updatedBy(UPDATED_UPDATED_BY)
            .isDeleted(UPDATED_IS_DELETED);

        restOrgUnitMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedOrgUnit.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedOrgUnit))
            )
            .andExpect(status().isOk());

        // Validate the OrgUnit in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertOrgUnitUpdatableFieldsEquals(createUpdateProxyForBean(partialUpdatedOrgUnit, orgUnit), getPersistedOrgUnit(orgUnit));
    }

    @Test
    @Transactional
    void fullUpdateOrgUnitWithPatch() throws Exception {
        // Initialize the database
        insertedOrgUnit = orgUnitRepository.saveAndFlush(orgUnit);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the orgUnit using partial update
        OrgUnit partialUpdatedOrgUnit = new OrgUnit();
        partialUpdatedOrgUnit.setId(orgUnit.getId());

        partialUpdatedOrgUnit
            .tenantId(UPDATED_TENANT_ID)
            .name(UPDATED_NAME)
            .code(UPDATED_CODE)
            .type(UPDATED_TYPE)
            .level(UPDATED_LEVEL)
            .status(UPDATED_STATUS)
            .sortOrder(UPDATED_SORT_ORDER)
            .description(UPDATED_DESCRIPTION)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);

        restOrgUnitMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedOrgUnit.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedOrgUnit))
            )
            .andExpect(status().isOk());

        // Validate the OrgUnit in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertOrgUnitUpdatableFieldsEquals(partialUpdatedOrgUnit, getPersistedOrgUnit(partialUpdatedOrgUnit));
    }

    @Test
    @Transactional
    void patchNonExistingOrgUnit() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        orgUnit.setId(longCount.incrementAndGet());

        // Create the OrgUnit
        OrgUnitDTO orgUnitDTO = orgUnitMapper.toDto(orgUnit);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restOrgUnitMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, orgUnitDTO.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(orgUnitDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the OrgUnit in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithIdMismatchOrgUnit() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        orgUnit.setId(longCount.incrementAndGet());

        // Create the OrgUnit
        OrgUnitDTO orgUnitDTO = orgUnitMapper.toDto(orgUnit);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restOrgUnitMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(orgUnitDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the OrgUnit in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithMissingIdPathParamOrgUnit() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        orgUnit.setId(longCount.incrementAndGet());

        // Create the OrgUnit
        OrgUnitDTO orgUnitDTO = orgUnitMapper.toDto(orgUnit);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restOrgUnitMockMvc
            .perform(patch(ENTITY_API_URL).contentType("application/merge-patch+json").content(om.writeValueAsBytes(orgUnitDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the OrgUnit in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void deleteOrgUnit() throws Exception {
        // Initialize the database
        insertedOrgUnit = orgUnitRepository.saveAndFlush(orgUnit);

        long databaseSizeBeforeDelete = getRepositoryCount();

        // Delete the orgUnit
        restOrgUnitMockMvc
            .perform(delete(ENTITY_API_URL_ID, orgUnit.getId()).accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isNoContent());

        // Validate the database contains one less item
        assertDecrementedRepositoryCount(databaseSizeBeforeDelete);
    }

    protected long getRepositoryCount() {
        return orgUnitRepository.count();
    }

    protected void assertIncrementedRepositoryCount(long countBefore) {
        assertThat(countBefore + 1).isEqualTo(getRepositoryCount());
    }

    protected void assertDecrementedRepositoryCount(long countBefore) {
        assertThat(countBefore - 1).isEqualTo(getRepositoryCount());
    }

    protected void assertSameRepositoryCount(long countBefore) {
        assertThat(countBefore).isEqualTo(getRepositoryCount());
    }

    protected OrgUnit getPersistedOrgUnit(OrgUnit orgUnit) {
        return orgUnitRepository.findById(orgUnit.getId()).orElseThrow();
    }

    protected void assertPersistedOrgUnitToMatchAllProperties(OrgUnit expectedOrgUnit) {
        assertOrgUnitAllPropertiesEquals(expectedOrgUnit, getPersistedOrgUnit(expectedOrgUnit));
    }

    protected void assertPersistedOrgUnitToMatchUpdatableProperties(OrgUnit expectedOrgUnit) {
        assertOrgUnitAllUpdatablePropertiesEquals(expectedOrgUnit, getPersistedOrgUnit(expectedOrgUnit));
    }
}

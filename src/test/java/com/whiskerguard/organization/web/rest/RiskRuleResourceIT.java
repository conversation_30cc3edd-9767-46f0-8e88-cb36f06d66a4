package com.whiskerguard.organization.web.rest;

import static com.whiskerguard.organization.domain.RiskRuleAsserts.*;
import static com.whiskerguard.organization.web.rest.TestUtil.createUpdateProxyForBean;
import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.hasItem;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.whiskerguard.organization.IntegrationTest;
import com.whiskerguard.organization.domain.RiskRule;
import com.whiskerguard.organization.domain.enumeration.RiskRuleType;
import com.whiskerguard.organization.repository.RiskRuleRepository;
import com.whiskerguard.organization.service.RiskRuleService;
import com.whiskerguard.organization.service.dto.RiskRuleDTO;
import com.whiskerguard.organization.service.mapper.RiskRuleMapper;
import jakarta.persistence.EntityManager;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Random;
import java.util.concurrent.atomic.AtomicLong;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

/**
 * Integration tests for the {@link RiskRuleResource} REST controller.
 */
@IntegrationTest
@ExtendWith(MockitoExtension.class)
@AutoConfigureMockMvc
@WithMockUser
class RiskRuleResourceIT {

    private static final Long DEFAULT_TENANT_ID = 1L;
    private static final Long UPDATED_TENANT_ID = 2L;

    private static final String DEFAULT_CODE = "AAAAAAAAAA";
    private static final String UPDATED_CODE = "BBBBBBBBBB";

    private static final String DEFAULT_NAME = "AAAAAAAAAA";
    private static final String UPDATED_NAME = "BBBBBBBBBB";

    private static final RiskRuleType DEFAULT_RULE_TYPE = RiskRuleType.THRESHOLD;
    private static final RiskRuleType UPDATED_RULE_TYPE = RiskRuleType.PATTERN;

    private static final String DEFAULT_PARAMS = "AAAAAAAAAA";
    private static final String UPDATED_PARAMS = "BBBBBBBBBB";

    private static final String DEFAULT_DESCRIPTION = "AAAAAAAAAA";
    private static final String UPDATED_DESCRIPTION = "BBBBBBBBBB";

    private static final Integer DEFAULT_SCORE = 1;
    private static final Integer UPDATED_SCORE = 2;

    private static final Integer DEFAULT_VERSION = 1;
    private static final Integer UPDATED_VERSION = 2;

    private static final String DEFAULT_METADATA = "AAAAAAAAAA";
    private static final String UPDATED_METADATA = "BBBBBBBBBB";

    private static final String DEFAULT_CREATED_BY = "AAAAAAAAAA";
    private static final String UPDATED_CREATED_BY = "BBBBBBBBBB";

    private static final Instant DEFAULT_CREATED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_CREATED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final String DEFAULT_UPDATED_BY = "AAAAAAAAAA";
    private static final String UPDATED_UPDATED_BY = "BBBBBBBBBB";

    private static final Instant DEFAULT_UPDATED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_UPDATED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final Boolean DEFAULT_IS_DELETED = false;
    private static final Boolean UPDATED_IS_DELETED = true;

    private static final String ENTITY_API_URL = "/api/risk-rules";
    private static final String ENTITY_API_URL_ID = ENTITY_API_URL + "/{id}";

    private static Random random = new Random();
    private static AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));

    @Autowired
    private ObjectMapper om;

    @Autowired
    private RiskRuleRepository riskRuleRepository;

    @Mock
    private RiskRuleRepository riskRuleRepositoryMock;

    @Autowired
    private RiskRuleMapper riskRuleMapper;

    @Mock
    private RiskRuleService riskRuleServiceMock;

    @Autowired
    private EntityManager em;

    @Autowired
    private MockMvc restRiskRuleMockMvc;

    private RiskRule riskRule;

    private RiskRule insertedRiskRule;

    /**
     * Create an entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static RiskRule createEntity() {
        return new RiskRule()
            .tenantId(DEFAULT_TENANT_ID)
            .code(DEFAULT_CODE)
            .name(DEFAULT_NAME)
            .ruleType(DEFAULT_RULE_TYPE)
            .params(DEFAULT_PARAMS)
            .description(DEFAULT_DESCRIPTION)
            .score(DEFAULT_SCORE)
            .version(DEFAULT_VERSION)
            .metadata(DEFAULT_METADATA)
            .createdBy(DEFAULT_CREATED_BY)
            .createdAt(DEFAULT_CREATED_AT)
            .updatedBy(DEFAULT_UPDATED_BY)
            .updatedAt(DEFAULT_UPDATED_AT)
            .isDeleted(DEFAULT_IS_DELETED);
    }

    /**
     * Create an updated entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static RiskRule createUpdatedEntity() {
        return new RiskRule()
            .tenantId(UPDATED_TENANT_ID)
            .code(UPDATED_CODE)
            .name(UPDATED_NAME)
            .ruleType(UPDATED_RULE_TYPE)
            .params(UPDATED_PARAMS)
            .description(UPDATED_DESCRIPTION)
            .score(UPDATED_SCORE)
            .version(UPDATED_VERSION)
            .metadata(UPDATED_METADATA)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);
    }

    @BeforeEach
    void initTest() {
        riskRule = createEntity();
    }

    @AfterEach
    void cleanup() {
        if (insertedRiskRule != null) {
            riskRuleRepository.delete(insertedRiskRule);
            insertedRiskRule = null;
        }
    }

    @Test
    @Transactional
    void createRiskRule() throws Exception {
        long databaseSizeBeforeCreate = getRepositoryCount();
        // Create the RiskRule
        RiskRuleDTO riskRuleDTO = riskRuleMapper.toDto(riskRule);
        var returnedRiskRuleDTO = om.readValue(
            restRiskRuleMockMvc
                .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(riskRuleDTO)))
                .andExpect(status().isCreated())
                .andReturn()
                .getResponse()
                .getContentAsString(),
            RiskRuleDTO.class
        );

        // Validate the RiskRule in the database
        assertIncrementedRepositoryCount(databaseSizeBeforeCreate);
        var returnedRiskRule = riskRuleMapper.toEntity(returnedRiskRuleDTO);
        assertRiskRuleUpdatableFieldsEquals(returnedRiskRule, getPersistedRiskRule(returnedRiskRule));

        insertedRiskRule = returnedRiskRule;
    }

    @Test
    @Transactional
    void createRiskRuleWithExistingId() throws Exception {
        // Create the RiskRule with an existing ID
        riskRule.setId(1L);
        RiskRuleDTO riskRuleDTO = riskRuleMapper.toDto(riskRule);

        long databaseSizeBeforeCreate = getRepositoryCount();

        // An entity with an existing ID cannot be created, so this API call must fail
        restRiskRuleMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(riskRuleDTO)))
            .andExpect(status().isBadRequest());

        // Validate the RiskRule in the database
        assertSameRepositoryCount(databaseSizeBeforeCreate);
    }

    @Test
    @Transactional
    void checkTenantIdIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        riskRule.setTenantId(null);

        // Create the RiskRule, which fails.
        RiskRuleDTO riskRuleDTO = riskRuleMapper.toDto(riskRule);

        restRiskRuleMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(riskRuleDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkCodeIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        riskRule.setCode(null);

        // Create the RiskRule, which fails.
        RiskRuleDTO riskRuleDTO = riskRuleMapper.toDto(riskRule);

        restRiskRuleMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(riskRuleDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkNameIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        riskRule.setName(null);

        // Create the RiskRule, which fails.
        RiskRuleDTO riskRuleDTO = riskRuleMapper.toDto(riskRule);

        restRiskRuleMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(riskRuleDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkRuleTypeIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        riskRule.setRuleType(null);

        // Create the RiskRule, which fails.
        RiskRuleDTO riskRuleDTO = riskRuleMapper.toDto(riskRule);

        restRiskRuleMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(riskRuleDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkVersionIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        riskRule.setVersion(null);

        // Create the RiskRule, which fails.
        RiskRuleDTO riskRuleDTO = riskRuleMapper.toDto(riskRule);

        restRiskRuleMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(riskRuleDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkCreatedAtIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        riskRule.setCreatedAt(null);

        // Create the RiskRule, which fails.
        RiskRuleDTO riskRuleDTO = riskRuleMapper.toDto(riskRule);

        restRiskRuleMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(riskRuleDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkUpdatedAtIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        riskRule.setUpdatedAt(null);

        // Create the RiskRule, which fails.
        RiskRuleDTO riskRuleDTO = riskRuleMapper.toDto(riskRule);

        restRiskRuleMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(riskRuleDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkIsDeletedIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        riskRule.setIsDeleted(null);

        // Create the RiskRule, which fails.
        RiskRuleDTO riskRuleDTO = riskRuleMapper.toDto(riskRule);

        restRiskRuleMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(riskRuleDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void getAllRiskRules() throws Exception {
        // Initialize the database
        insertedRiskRule = riskRuleRepository.saveAndFlush(riskRule);

        // Get all the riskRuleList
        restRiskRuleMockMvc
            .perform(get(ENTITY_API_URL + "?sort=id,desc"))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.[*].id").value(hasItem(riskRule.getId().intValue())))
            .andExpect(jsonPath("$.[*].tenantId").value(hasItem(DEFAULT_TENANT_ID.intValue())))
            .andExpect(jsonPath("$.[*].code").value(hasItem(DEFAULT_CODE)))
            .andExpect(jsonPath("$.[*].name").value(hasItem(DEFAULT_NAME)))
            .andExpect(jsonPath("$.[*].ruleType").value(hasItem(DEFAULT_RULE_TYPE.toString())))
            .andExpect(jsonPath("$.[*].params").value(hasItem(DEFAULT_PARAMS)))
            .andExpect(jsonPath("$.[*].description").value(hasItem(DEFAULT_DESCRIPTION)))
            .andExpect(jsonPath("$.[*].score").value(hasItem(DEFAULT_SCORE)))
            .andExpect(jsonPath("$.[*].version").value(hasItem(DEFAULT_VERSION)))
            .andExpect(jsonPath("$.[*].metadata").value(hasItem(DEFAULT_METADATA)))
            .andExpect(jsonPath("$.[*].createdBy").value(hasItem(DEFAULT_CREATED_BY)))
            .andExpect(jsonPath("$.[*].createdAt").value(hasItem(DEFAULT_CREATED_AT.toString())))
            .andExpect(jsonPath("$.[*].updatedBy").value(hasItem(DEFAULT_UPDATED_BY)))
            .andExpect(jsonPath("$.[*].updatedAt").value(hasItem(DEFAULT_UPDATED_AT.toString())))
            .andExpect(jsonPath("$.[*].isDeleted").value(hasItem(DEFAULT_IS_DELETED)));
    }

    @SuppressWarnings({ "unchecked" })
    void getAllRiskRulesWithEagerRelationshipsIsEnabled() throws Exception {
        when(riskRuleServiceMock.findAllWithEagerRelationships(any())).thenReturn(new PageImpl(new ArrayList<>()));

        restRiskRuleMockMvc.perform(get(ENTITY_API_URL + "?eagerload=true")).andExpect(status().isOk());

        verify(riskRuleServiceMock, times(1)).findAllWithEagerRelationships(any());
    }

    @SuppressWarnings({ "unchecked" })
    void getAllRiskRulesWithEagerRelationshipsIsNotEnabled() throws Exception {
        when(riskRuleServiceMock.findAllWithEagerRelationships(any())).thenReturn(new PageImpl(new ArrayList<>()));

        restRiskRuleMockMvc.perform(get(ENTITY_API_URL + "?eagerload=false")).andExpect(status().isOk());
        verify(riskRuleRepositoryMock, times(1)).findAll(any(Pageable.class));
    }

    @Test
    @Transactional
    void getRiskRule() throws Exception {
        // Initialize the database
        insertedRiskRule = riskRuleRepository.saveAndFlush(riskRule);

        // Get the riskRule
        restRiskRuleMockMvc
            .perform(get(ENTITY_API_URL_ID, riskRule.getId()))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.id").value(riskRule.getId().intValue()))
            .andExpect(jsonPath("$.tenantId").value(DEFAULT_TENANT_ID.intValue()))
            .andExpect(jsonPath("$.code").value(DEFAULT_CODE))
            .andExpect(jsonPath("$.name").value(DEFAULT_NAME))
            .andExpect(jsonPath("$.ruleType").value(DEFAULT_RULE_TYPE.toString()))
            .andExpect(jsonPath("$.params").value(DEFAULT_PARAMS))
            .andExpect(jsonPath("$.description").value(DEFAULT_DESCRIPTION))
            .andExpect(jsonPath("$.score").value(DEFAULT_SCORE))
            .andExpect(jsonPath("$.version").value(DEFAULT_VERSION))
            .andExpect(jsonPath("$.metadata").value(DEFAULT_METADATA))
            .andExpect(jsonPath("$.createdBy").value(DEFAULT_CREATED_BY))
            .andExpect(jsonPath("$.createdAt").value(DEFAULT_CREATED_AT.toString()))
            .andExpect(jsonPath("$.updatedBy").value(DEFAULT_UPDATED_BY))
            .andExpect(jsonPath("$.updatedAt").value(DEFAULT_UPDATED_AT.toString()))
            .andExpect(jsonPath("$.isDeleted").value(DEFAULT_IS_DELETED));
    }

    @Test
    @Transactional
    void getNonExistingRiskRule() throws Exception {
        // Get the riskRule
        restRiskRuleMockMvc.perform(get(ENTITY_API_URL_ID, Long.MAX_VALUE)).andExpect(status().isNotFound());
    }

    @Test
    @Transactional
    void putExistingRiskRule() throws Exception {
        // Initialize the database
        insertedRiskRule = riskRuleRepository.saveAndFlush(riskRule);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the riskRule
        RiskRule updatedRiskRule = riskRuleRepository.findById(riskRule.getId()).orElseThrow();
        // Disconnect from session so that the updates on updatedRiskRule are not directly saved in db
        em.detach(updatedRiskRule);
        updatedRiskRule
            .tenantId(UPDATED_TENANT_ID)
            .code(UPDATED_CODE)
            .name(UPDATED_NAME)
            .ruleType(UPDATED_RULE_TYPE)
            .params(UPDATED_PARAMS)
            .description(UPDATED_DESCRIPTION)
            .score(UPDATED_SCORE)
            .version(UPDATED_VERSION)
            .metadata(UPDATED_METADATA)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);
        RiskRuleDTO riskRuleDTO = riskRuleMapper.toDto(updatedRiskRule);

        restRiskRuleMockMvc
            .perform(
                put(ENTITY_API_URL_ID, riskRuleDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(riskRuleDTO))
            )
            .andExpect(status().isOk());

        // Validate the RiskRule in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertPersistedRiskRuleToMatchAllProperties(updatedRiskRule);
    }

    @Test
    @Transactional
    void putNonExistingRiskRule() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        riskRule.setId(longCount.incrementAndGet());

        // Create the RiskRule
        RiskRuleDTO riskRuleDTO = riskRuleMapper.toDto(riskRule);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restRiskRuleMockMvc
            .perform(
                put(ENTITY_API_URL_ID, riskRuleDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(riskRuleDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the RiskRule in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithIdMismatchRiskRule() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        riskRule.setId(longCount.incrementAndGet());

        // Create the RiskRule
        RiskRuleDTO riskRuleDTO = riskRuleMapper.toDto(riskRule);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restRiskRuleMockMvc
            .perform(
                put(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(riskRuleDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the RiskRule in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithMissingIdPathParamRiskRule() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        riskRule.setId(longCount.incrementAndGet());

        // Create the RiskRule
        RiskRuleDTO riskRuleDTO = riskRuleMapper.toDto(riskRule);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restRiskRuleMockMvc
            .perform(put(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(riskRuleDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the RiskRule in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void partialUpdateRiskRuleWithPatch() throws Exception {
        // Initialize the database
        insertedRiskRule = riskRuleRepository.saveAndFlush(riskRule);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the riskRule using partial update
        RiskRule partialUpdatedRiskRule = new RiskRule();
        partialUpdatedRiskRule.setId(riskRule.getId());

        partialUpdatedRiskRule
            .code(UPDATED_CODE)
            .name(UPDATED_NAME)
            .params(UPDATED_PARAMS)
            .score(UPDATED_SCORE)
            .version(UPDATED_VERSION)
            .metadata(UPDATED_METADATA)
            .updatedBy(UPDATED_UPDATED_BY)
            .isDeleted(UPDATED_IS_DELETED);

        restRiskRuleMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedRiskRule.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedRiskRule))
            )
            .andExpect(status().isOk());

        // Validate the RiskRule in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertRiskRuleUpdatableFieldsEquals(createUpdateProxyForBean(partialUpdatedRiskRule, riskRule), getPersistedRiskRule(riskRule));
    }

    @Test
    @Transactional
    void fullUpdateRiskRuleWithPatch() throws Exception {
        // Initialize the database
        insertedRiskRule = riskRuleRepository.saveAndFlush(riskRule);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the riskRule using partial update
        RiskRule partialUpdatedRiskRule = new RiskRule();
        partialUpdatedRiskRule.setId(riskRule.getId());

        partialUpdatedRiskRule
            .tenantId(UPDATED_TENANT_ID)
            .code(UPDATED_CODE)
            .name(UPDATED_NAME)
            .ruleType(UPDATED_RULE_TYPE)
            .params(UPDATED_PARAMS)
            .description(UPDATED_DESCRIPTION)
            .score(UPDATED_SCORE)
            .version(UPDATED_VERSION)
            .metadata(UPDATED_METADATA)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);

        restRiskRuleMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedRiskRule.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedRiskRule))
            )
            .andExpect(status().isOk());

        // Validate the RiskRule in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertRiskRuleUpdatableFieldsEquals(partialUpdatedRiskRule, getPersistedRiskRule(partialUpdatedRiskRule));
    }

    @Test
    @Transactional
    void patchNonExistingRiskRule() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        riskRule.setId(longCount.incrementAndGet());

        // Create the RiskRule
        RiskRuleDTO riskRuleDTO = riskRuleMapper.toDto(riskRule);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restRiskRuleMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, riskRuleDTO.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(riskRuleDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the RiskRule in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithIdMismatchRiskRule() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        riskRule.setId(longCount.incrementAndGet());

        // Create the RiskRule
        RiskRuleDTO riskRuleDTO = riskRuleMapper.toDto(riskRule);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restRiskRuleMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(riskRuleDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the RiskRule in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithMissingIdPathParamRiskRule() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        riskRule.setId(longCount.incrementAndGet());

        // Create the RiskRule
        RiskRuleDTO riskRuleDTO = riskRuleMapper.toDto(riskRule);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restRiskRuleMockMvc
            .perform(patch(ENTITY_API_URL).contentType("application/merge-patch+json").content(om.writeValueAsBytes(riskRuleDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the RiskRule in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void deleteRiskRule() throws Exception {
        // Initialize the database
        insertedRiskRule = riskRuleRepository.saveAndFlush(riskRule);

        long databaseSizeBeforeDelete = getRepositoryCount();

        // Delete the riskRule
        restRiskRuleMockMvc
            .perform(delete(ENTITY_API_URL_ID, riskRule.getId()).accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isNoContent());

        // Validate the database contains one less item
        assertDecrementedRepositoryCount(databaseSizeBeforeDelete);
    }

    protected long getRepositoryCount() {
        return riskRuleRepository.count();
    }

    protected void assertIncrementedRepositoryCount(long countBefore) {
        assertThat(countBefore + 1).isEqualTo(getRepositoryCount());
    }

    protected void assertDecrementedRepositoryCount(long countBefore) {
        assertThat(countBefore - 1).isEqualTo(getRepositoryCount());
    }

    protected void assertSameRepositoryCount(long countBefore) {
        assertThat(countBefore).isEqualTo(getRepositoryCount());
    }

    protected RiskRule getPersistedRiskRule(RiskRule riskRule) {
        return riskRuleRepository.findById(riskRule.getId()).orElseThrow();
    }

    protected void assertPersistedRiskRuleToMatchAllProperties(RiskRule expectedRiskRule) {
        assertRiskRuleAllPropertiesEquals(expectedRiskRule, getPersistedRiskRule(expectedRiskRule));
    }

    protected void assertPersistedRiskRuleToMatchUpdatableProperties(RiskRule expectedRiskRule) {
        assertRiskRuleAllUpdatablePropertiesEquals(expectedRiskRule, getPersistedRiskRule(expectedRiskRule));
    }
}

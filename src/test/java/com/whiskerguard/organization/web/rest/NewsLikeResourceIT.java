package com.whiskerguard.organization.web.rest;

import static com.whiskerguard.organization.domain.NewsLikeAsserts.*;
import static com.whiskerguard.organization.web.rest.TestUtil.createUpdateProxyForBean;
import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.hasItem;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.whiskerguard.organization.IntegrationTest;
import com.whiskerguard.organization.domain.NewsLike;
import com.whiskerguard.organization.repository.NewsLikeRepository;
import com.whiskerguard.organization.service.NewsLikeService;
import com.whiskerguard.organization.service.dto.NewsLikeDTO;
import com.whiskerguard.organization.service.mapper.NewsLikeMapper;
import jakarta.persistence.EntityManager;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Random;
import java.util.concurrent.atomic.AtomicLong;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

/**
 * Integration tests for the {@link NewsLikeResource} REST controller.
 */
@IntegrationTest
@ExtendWith(MockitoExtension.class)
@AutoConfigureMockMvc
@WithMockUser
class NewsLikeResourceIT {

    private static final String DEFAULT_METADATA = "AAAAAAAAAA";
    private static final String UPDATED_METADATA = "BBBBBBBBBB";

    private static final Integer DEFAULT_VERSION = 1;
    private static final Integer UPDATED_VERSION = 2;

    private static final String DEFAULT_CREATED_BY = "AAAAAAAAAA";
    private static final String UPDATED_CREATED_BY = "BBBBBBBBBB";

    private static final Instant DEFAULT_CREATED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_CREATED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final String DEFAULT_UPDATED_BY = "AAAAAAAAAA";
    private static final String UPDATED_UPDATED_BY = "BBBBBBBBBB";

    private static final Instant DEFAULT_UPDATED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_UPDATED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final Boolean DEFAULT_IS_DELETED = false;
    private static final Boolean UPDATED_IS_DELETED = true;

    private static final String ENTITY_API_URL = "/api/news-likes";
    private static final String ENTITY_API_URL_ID = ENTITY_API_URL + "/{id}";

    private static Random random = new Random();
    private static AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));

    @Autowired
    private ObjectMapper om;

    @Autowired
    private NewsLikeRepository newsLikeRepository;

    @Mock
    private NewsLikeRepository newsLikeRepositoryMock;

    @Autowired
    private NewsLikeMapper newsLikeMapper;

    @Mock
    private NewsLikeService newsLikeServiceMock;

    @Autowired
    private EntityManager em;

    @Autowired
    private MockMvc restNewsLikeMockMvc;

    private NewsLike newsLike;

    private NewsLike insertedNewsLike;

    /**
     * Create an entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static NewsLike createEntity() {
        return new NewsLike()
            .metadata(DEFAULT_METADATA)
            .version(DEFAULT_VERSION)
            .createdBy(DEFAULT_CREATED_BY)
            .createdAt(DEFAULT_CREATED_AT)
            .updatedBy(DEFAULT_UPDATED_BY)
            .updatedAt(DEFAULT_UPDATED_AT)
            .isDeleted(DEFAULT_IS_DELETED);
    }

    /**
     * Create an updated entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static NewsLike createUpdatedEntity() {
        return new NewsLike()
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);
    }

    @BeforeEach
    void initTest() {
        newsLike = createEntity();
    }

    @AfterEach
    void cleanup() {
        if (insertedNewsLike != null) {
            newsLikeRepository.delete(insertedNewsLike);
            insertedNewsLike = null;
        }
    }

    @Test
    @Transactional
    void createNewsLike() throws Exception {
        long databaseSizeBeforeCreate = getRepositoryCount();
        // Create the NewsLike
        NewsLikeDTO newsLikeDTO = newsLikeMapper.toDto(newsLike);
        var returnedNewsLikeDTO = om.readValue(
            restNewsLikeMockMvc
                .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(newsLikeDTO)))
                .andExpect(status().isCreated())
                .andReturn()
                .getResponse()
                .getContentAsString(),
            NewsLikeDTO.class
        );

        // Validate the NewsLike in the database
        assertIncrementedRepositoryCount(databaseSizeBeforeCreate);
        var returnedNewsLike = newsLikeMapper.toEntity(returnedNewsLikeDTO);
        assertNewsLikeUpdatableFieldsEquals(returnedNewsLike, getPersistedNewsLike(returnedNewsLike));

        insertedNewsLike = returnedNewsLike;
    }

    @Test
    @Transactional
    void createNewsLikeWithExistingId() throws Exception {
        // Create the NewsLike with an existing ID
        newsLike.setId(1L);
        NewsLikeDTO newsLikeDTO = newsLikeMapper.toDto(newsLike);

        long databaseSizeBeforeCreate = getRepositoryCount();

        // An entity with an existing ID cannot be created, so this API call must fail
        restNewsLikeMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(newsLikeDTO)))
            .andExpect(status().isBadRequest());

        // Validate the NewsLike in the database
        assertSameRepositoryCount(databaseSizeBeforeCreate);
    }

    @Test
    @Transactional
    void checkVersionIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        newsLike.setVersion(null);

        // Create the NewsLike, which fails.
        NewsLikeDTO newsLikeDTO = newsLikeMapper.toDto(newsLike);

        restNewsLikeMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(newsLikeDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkCreatedAtIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        newsLike.setCreatedAt(null);

        // Create the NewsLike, which fails.
        NewsLikeDTO newsLikeDTO = newsLikeMapper.toDto(newsLike);

        restNewsLikeMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(newsLikeDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkUpdatedAtIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        newsLike.setUpdatedAt(null);

        // Create the NewsLike, which fails.
        NewsLikeDTO newsLikeDTO = newsLikeMapper.toDto(newsLike);

        restNewsLikeMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(newsLikeDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkIsDeletedIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        newsLike.setIsDeleted(null);

        // Create the NewsLike, which fails.
        NewsLikeDTO newsLikeDTO = newsLikeMapper.toDto(newsLike);

        restNewsLikeMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(newsLikeDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void getAllNewsLikes() throws Exception {
        // Initialize the database
        insertedNewsLike = newsLikeRepository.saveAndFlush(newsLike);

        // Get all the newsLikeList
        restNewsLikeMockMvc
            .perform(get(ENTITY_API_URL + "?sort=id,desc"))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.[*].id").value(hasItem(newsLike.getId().intValue())))
            .andExpect(jsonPath("$.[*].metadata").value(hasItem(DEFAULT_METADATA)))
            .andExpect(jsonPath("$.[*].version").value(hasItem(DEFAULT_VERSION)))
            .andExpect(jsonPath("$.[*].createdBy").value(hasItem(DEFAULT_CREATED_BY)))
            .andExpect(jsonPath("$.[*].createdAt").value(hasItem(DEFAULT_CREATED_AT.toString())))
            .andExpect(jsonPath("$.[*].updatedBy").value(hasItem(DEFAULT_UPDATED_BY)))
            .andExpect(jsonPath("$.[*].updatedAt").value(hasItem(DEFAULT_UPDATED_AT.toString())))
            .andExpect(jsonPath("$.[*].isDeleted").value(hasItem(DEFAULT_IS_DELETED)));
    }

    @SuppressWarnings({ "unchecked" })
    void getAllNewsLikesWithEagerRelationshipsIsEnabled() throws Exception {
        when(newsLikeServiceMock.findAllWithEagerRelationships(any())).thenReturn(new PageImpl(new ArrayList<>()));

        restNewsLikeMockMvc.perform(get(ENTITY_API_URL + "?eagerload=true")).andExpect(status().isOk());

        verify(newsLikeServiceMock, times(1)).findAllWithEagerRelationships(any());
    }

    @SuppressWarnings({ "unchecked" })
    void getAllNewsLikesWithEagerRelationshipsIsNotEnabled() throws Exception {
        when(newsLikeServiceMock.findAllWithEagerRelationships(any())).thenReturn(new PageImpl(new ArrayList<>()));

        restNewsLikeMockMvc.perform(get(ENTITY_API_URL + "?eagerload=false")).andExpect(status().isOk());
        verify(newsLikeRepositoryMock, times(1)).findAll(any(Pageable.class));
    }

    @Test
    @Transactional
    void getNewsLike() throws Exception {
        // Initialize the database
        insertedNewsLike = newsLikeRepository.saveAndFlush(newsLike);

        // Get the newsLike
        restNewsLikeMockMvc
            .perform(get(ENTITY_API_URL_ID, newsLike.getId()))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.id").value(newsLike.getId().intValue()))
            .andExpect(jsonPath("$.metadata").value(DEFAULT_METADATA))
            .andExpect(jsonPath("$.version").value(DEFAULT_VERSION))
            .andExpect(jsonPath("$.createdBy").value(DEFAULT_CREATED_BY))
            .andExpect(jsonPath("$.createdAt").value(DEFAULT_CREATED_AT.toString()))
            .andExpect(jsonPath("$.updatedBy").value(DEFAULT_UPDATED_BY))
            .andExpect(jsonPath("$.updatedAt").value(DEFAULT_UPDATED_AT.toString()))
            .andExpect(jsonPath("$.isDeleted").value(DEFAULT_IS_DELETED));
    }

    @Test
    @Transactional
    void getNonExistingNewsLike() throws Exception {
        // Get the newsLike
        restNewsLikeMockMvc.perform(get(ENTITY_API_URL_ID, Long.MAX_VALUE)).andExpect(status().isNotFound());
    }

    @Test
    @Transactional
    void putExistingNewsLike() throws Exception {
        // Initialize the database
        insertedNewsLike = newsLikeRepository.saveAndFlush(newsLike);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the newsLike
        NewsLike updatedNewsLike = newsLikeRepository.findById(newsLike.getId()).orElseThrow();
        // Disconnect from session so that the updates on updatedNewsLike are not directly saved in db
        em.detach(updatedNewsLike);
        updatedNewsLike
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);
        NewsLikeDTO newsLikeDTO = newsLikeMapper.toDto(updatedNewsLike);

        restNewsLikeMockMvc
            .perform(
                put(ENTITY_API_URL_ID, newsLikeDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(newsLikeDTO))
            )
            .andExpect(status().isOk());

        // Validate the NewsLike in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertPersistedNewsLikeToMatchAllProperties(updatedNewsLike);
    }

    @Test
    @Transactional
    void putNonExistingNewsLike() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        newsLike.setId(longCount.incrementAndGet());

        // Create the NewsLike
        NewsLikeDTO newsLikeDTO = newsLikeMapper.toDto(newsLike);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restNewsLikeMockMvc
            .perform(
                put(ENTITY_API_URL_ID, newsLikeDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(newsLikeDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the NewsLike in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithIdMismatchNewsLike() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        newsLike.setId(longCount.incrementAndGet());

        // Create the NewsLike
        NewsLikeDTO newsLikeDTO = newsLikeMapper.toDto(newsLike);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restNewsLikeMockMvc
            .perform(
                put(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(newsLikeDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the NewsLike in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithMissingIdPathParamNewsLike() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        newsLike.setId(longCount.incrementAndGet());

        // Create the NewsLike
        NewsLikeDTO newsLikeDTO = newsLikeMapper.toDto(newsLike);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restNewsLikeMockMvc
            .perform(put(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(newsLikeDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the NewsLike in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void partialUpdateNewsLikeWithPatch() throws Exception {
        // Initialize the database
        insertedNewsLike = newsLikeRepository.saveAndFlush(newsLike);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the newsLike using partial update
        NewsLike partialUpdatedNewsLike = new NewsLike();
        partialUpdatedNewsLike.setId(newsLike.getId());

        partialUpdatedNewsLike.createdBy(UPDATED_CREATED_BY).updatedAt(UPDATED_UPDATED_AT).isDeleted(UPDATED_IS_DELETED);

        restNewsLikeMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedNewsLike.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedNewsLike))
            )
            .andExpect(status().isOk());

        // Validate the NewsLike in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertNewsLikeUpdatableFieldsEquals(createUpdateProxyForBean(partialUpdatedNewsLike, newsLike), getPersistedNewsLike(newsLike));
    }

    @Test
    @Transactional
    void fullUpdateNewsLikeWithPatch() throws Exception {
        // Initialize the database
        insertedNewsLike = newsLikeRepository.saveAndFlush(newsLike);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the newsLike using partial update
        NewsLike partialUpdatedNewsLike = new NewsLike();
        partialUpdatedNewsLike.setId(newsLike.getId());

        partialUpdatedNewsLike
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);

        restNewsLikeMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedNewsLike.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedNewsLike))
            )
            .andExpect(status().isOk());

        // Validate the NewsLike in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertNewsLikeUpdatableFieldsEquals(partialUpdatedNewsLike, getPersistedNewsLike(partialUpdatedNewsLike));
    }

    @Test
    @Transactional
    void patchNonExistingNewsLike() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        newsLike.setId(longCount.incrementAndGet());

        // Create the NewsLike
        NewsLikeDTO newsLikeDTO = newsLikeMapper.toDto(newsLike);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restNewsLikeMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, newsLikeDTO.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(newsLikeDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the NewsLike in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithIdMismatchNewsLike() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        newsLike.setId(longCount.incrementAndGet());

        // Create the NewsLike
        NewsLikeDTO newsLikeDTO = newsLikeMapper.toDto(newsLike);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restNewsLikeMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(newsLikeDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the NewsLike in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithMissingIdPathParamNewsLike() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        newsLike.setId(longCount.incrementAndGet());

        // Create the NewsLike
        NewsLikeDTO newsLikeDTO = newsLikeMapper.toDto(newsLike);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restNewsLikeMockMvc
            .perform(patch(ENTITY_API_URL).contentType("application/merge-patch+json").content(om.writeValueAsBytes(newsLikeDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the NewsLike in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void deleteNewsLike() throws Exception {
        // Initialize the database
        insertedNewsLike = newsLikeRepository.saveAndFlush(newsLike);

        long databaseSizeBeforeDelete = getRepositoryCount();

        // Delete the newsLike
        restNewsLikeMockMvc
            .perform(delete(ENTITY_API_URL_ID, newsLike.getId()).accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isNoContent());

        // Validate the database contains one less item
        assertDecrementedRepositoryCount(databaseSizeBeforeDelete);
    }

    protected long getRepositoryCount() {
        return newsLikeRepository.count();
    }

    protected void assertIncrementedRepositoryCount(long countBefore) {
        assertThat(countBefore + 1).isEqualTo(getRepositoryCount());
    }

    protected void assertDecrementedRepositoryCount(long countBefore) {
        assertThat(countBefore - 1).isEqualTo(getRepositoryCount());
    }

    protected void assertSameRepositoryCount(long countBefore) {
        assertThat(countBefore).isEqualTo(getRepositoryCount());
    }

    protected NewsLike getPersistedNewsLike(NewsLike newsLike) {
        return newsLikeRepository.findById(newsLike.getId()).orElseThrow();
    }

    protected void assertPersistedNewsLikeToMatchAllProperties(NewsLike expectedNewsLike) {
        assertNewsLikeAllPropertiesEquals(expectedNewsLike, getPersistedNewsLike(expectedNewsLike));
    }

    protected void assertPersistedNewsLikeToMatchUpdatableProperties(NewsLike expectedNewsLike) {
        assertNewsLikeAllUpdatablePropertiesEquals(expectedNewsLike, getPersistedNewsLike(expectedNewsLike));
    }
}

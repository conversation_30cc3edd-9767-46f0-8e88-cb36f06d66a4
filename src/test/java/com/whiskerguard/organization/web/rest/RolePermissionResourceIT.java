package com.whiskerguard.organization.web.rest;

import static com.whiskerguard.organization.domain.RolePermissionAsserts.*;
import static com.whiskerguard.organization.web.rest.TestUtil.createUpdateProxyForBean;
import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.hasItem;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.whiskerguard.organization.IntegrationTest;
import com.whiskerguard.organization.domain.RolePermission;
import com.whiskerguard.organization.repository.RolePermissionRepository;
import com.whiskerguard.organization.service.dto.RolePermissionDTO;
import com.whiskerguard.organization.service.mapper.RolePermissionMapper;
import jakarta.persistence.EntityManager;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Random;
import java.util.concurrent.atomic.AtomicLong;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

/**
 * Integration tests for the {@link RolePermissionResource} REST controller.
 */
@IntegrationTest
@AutoConfigureMockMvc
@WithMockUser
class RolePermissionResourceIT {

    private static final Long DEFAULT_TENANT_ID = 1L;
    private static final Long UPDATED_TENANT_ID = 2L;

    private static final Integer DEFAULT_VERSION = 1;
    private static final Integer UPDATED_VERSION = 2;

    private static final String DEFAULT_CREATED_BY = "AAAAAAAAAA";
    private static final String UPDATED_CREATED_BY = "BBBBBBBBBB";

    private static final Instant DEFAULT_CREATED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_CREATED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final String DEFAULT_UPDATED_BY = "AAAAAAAAAA";
    private static final String UPDATED_UPDATED_BY = "BBBBBBBBBB";

    private static final Instant DEFAULT_UPDATED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_UPDATED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final Boolean DEFAULT_IS_DELETED = false;
    private static final Boolean UPDATED_IS_DELETED = true;

    private static final String ENTITY_API_URL = "/api/role-permissions";
    private static final String ENTITY_API_URL_ID = ENTITY_API_URL + "/{id}";

    private static Random random = new Random();
    private static AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));

    @Autowired
    private ObjectMapper om;

    @Autowired
    private RolePermissionRepository rolePermissionRepository;

    @Autowired
    private RolePermissionMapper rolePermissionMapper;

    @Autowired
    private EntityManager em;

    @Autowired
    private MockMvc restRolePermissionMockMvc;

    private RolePermission rolePermission;

    private RolePermission insertedRolePermission;

    /**
     * Create an entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static RolePermission createEntity() {
        return new RolePermission()
            .tenantId(DEFAULT_TENANT_ID)
            .version(DEFAULT_VERSION)
            .createdBy(DEFAULT_CREATED_BY)
            .createdAt(DEFAULT_CREATED_AT)
            .updatedBy(DEFAULT_UPDATED_BY)
            .updatedAt(DEFAULT_UPDATED_AT)
            .isDeleted(DEFAULT_IS_DELETED);
    }

    /**
     * Create an updated entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static RolePermission createUpdatedEntity() {
        return new RolePermission()
            .tenantId(UPDATED_TENANT_ID)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);
    }

    @BeforeEach
    void initTest() {
        rolePermission = createEntity();
    }

    @AfterEach
    void cleanup() {
        if (insertedRolePermission != null) {
            rolePermissionRepository.delete(insertedRolePermission);
            insertedRolePermission = null;
        }
    }

    @Test
    @Transactional
    void createRolePermission() throws Exception {
        long databaseSizeBeforeCreate = getRepositoryCount();
        // Create the RolePermission
        RolePermissionDTO rolePermissionDTO = rolePermissionMapper.toDto(rolePermission);
        var returnedRolePermissionDTO = om.readValue(
            restRolePermissionMockMvc
                .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(rolePermissionDTO)))
                .andExpect(status().isCreated())
                .andReturn()
                .getResponse()
                .getContentAsString(),
            RolePermissionDTO.class
        );

        // Validate the RolePermission in the database
        assertIncrementedRepositoryCount(databaseSizeBeforeCreate);
        var returnedRolePermission = rolePermissionMapper.toEntity(returnedRolePermissionDTO);
        assertRolePermissionUpdatableFieldsEquals(returnedRolePermission, getPersistedRolePermission(returnedRolePermission));

        insertedRolePermission = returnedRolePermission;
    }

    @Test
    @Transactional
    void createRolePermissionWithExistingId() throws Exception {
        // Create the RolePermission with an existing ID
        rolePermission.setId(1L);
        RolePermissionDTO rolePermissionDTO = rolePermissionMapper.toDto(rolePermission);

        long databaseSizeBeforeCreate = getRepositoryCount();

        // An entity with an existing ID cannot be created, so this API call must fail
        restRolePermissionMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(rolePermissionDTO)))
            .andExpect(status().isBadRequest());

        // Validate the RolePermission in the database
        assertSameRepositoryCount(databaseSizeBeforeCreate);
    }

    @Test
    @Transactional
    void checkTenantIdIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        rolePermission.setTenantId(null);

        // Create the RolePermission, which fails.
        RolePermissionDTO rolePermissionDTO = rolePermissionMapper.toDto(rolePermission);

        restRolePermissionMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(rolePermissionDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkVersionIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        rolePermission.setVersion(null);

        // Create the RolePermission, which fails.
        RolePermissionDTO rolePermissionDTO = rolePermissionMapper.toDto(rolePermission);

        restRolePermissionMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(rolePermissionDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkCreatedAtIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        rolePermission.setCreatedAt(null);

        // Create the RolePermission, which fails.
        RolePermissionDTO rolePermissionDTO = rolePermissionMapper.toDto(rolePermission);

        restRolePermissionMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(rolePermissionDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkUpdatedAtIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        rolePermission.setUpdatedAt(null);

        // Create the RolePermission, which fails.
        RolePermissionDTO rolePermissionDTO = rolePermissionMapper.toDto(rolePermission);

        restRolePermissionMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(rolePermissionDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkIsDeletedIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        rolePermission.setIsDeleted(null);

        // Create the RolePermission, which fails.
        RolePermissionDTO rolePermissionDTO = rolePermissionMapper.toDto(rolePermission);

        restRolePermissionMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(rolePermissionDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void getAllRolePermissions() throws Exception {
        // Initialize the database
        insertedRolePermission = rolePermissionRepository.saveAndFlush(rolePermission);

        // Get all the rolePermissionList
        restRolePermissionMockMvc
            .perform(get(ENTITY_API_URL + "?sort=id,desc"))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.[*].id").value(hasItem(rolePermission.getId().intValue())))
            .andExpect(jsonPath("$.[*].tenantId").value(hasItem(DEFAULT_TENANT_ID.intValue())))
            .andExpect(jsonPath("$.[*].version").value(hasItem(DEFAULT_VERSION)))
            .andExpect(jsonPath("$.[*].createdBy").value(hasItem(DEFAULT_CREATED_BY)))
            .andExpect(jsonPath("$.[*].createdAt").value(hasItem(DEFAULT_CREATED_AT.toString())))
            .andExpect(jsonPath("$.[*].updatedBy").value(hasItem(DEFAULT_UPDATED_BY)))
            .andExpect(jsonPath("$.[*].updatedAt").value(hasItem(DEFAULT_UPDATED_AT.toString())))
            .andExpect(jsonPath("$.[*].isDeleted").value(hasItem(DEFAULT_IS_DELETED)));
    }

    @Test
    @Transactional
    void getRolePermission() throws Exception {
        // Initialize the database
        insertedRolePermission = rolePermissionRepository.saveAndFlush(rolePermission);

        // Get the rolePermission
        restRolePermissionMockMvc
            .perform(get(ENTITY_API_URL_ID, rolePermission.getId()))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.id").value(rolePermission.getId().intValue()))
            .andExpect(jsonPath("$.tenantId").value(DEFAULT_TENANT_ID.intValue()))
            .andExpect(jsonPath("$.version").value(DEFAULT_VERSION))
            .andExpect(jsonPath("$.createdBy").value(DEFAULT_CREATED_BY))
            .andExpect(jsonPath("$.createdAt").value(DEFAULT_CREATED_AT.toString()))
            .andExpect(jsonPath("$.updatedBy").value(DEFAULT_UPDATED_BY))
            .andExpect(jsonPath("$.updatedAt").value(DEFAULT_UPDATED_AT.toString()))
            .andExpect(jsonPath("$.isDeleted").value(DEFAULT_IS_DELETED));
    }

    @Test
    @Transactional
    void getNonExistingRolePermission() throws Exception {
        // Get the rolePermission
        restRolePermissionMockMvc.perform(get(ENTITY_API_URL_ID, Long.MAX_VALUE)).andExpect(status().isNotFound());
    }

    @Test
    @Transactional
    void putExistingRolePermission() throws Exception {
        // Initialize the database
        insertedRolePermission = rolePermissionRepository.saveAndFlush(rolePermission);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the rolePermission
        RolePermission updatedRolePermission = rolePermissionRepository.findById(rolePermission.getId()).orElseThrow();
        // Disconnect from session so that the updates on updatedRolePermission are not directly saved in db
        em.detach(updatedRolePermission);
        updatedRolePermission
            .tenantId(UPDATED_TENANT_ID)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);
        RolePermissionDTO rolePermissionDTO = rolePermissionMapper.toDto(updatedRolePermission);

        restRolePermissionMockMvc
            .perform(
                put(ENTITY_API_URL_ID, rolePermissionDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(rolePermissionDTO))
            )
            .andExpect(status().isOk());

        // Validate the RolePermission in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertPersistedRolePermissionToMatchAllProperties(updatedRolePermission);
    }

    @Test
    @Transactional
    void putNonExistingRolePermission() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        rolePermission.setId(longCount.incrementAndGet());

        // Create the RolePermission
        RolePermissionDTO rolePermissionDTO = rolePermissionMapper.toDto(rolePermission);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restRolePermissionMockMvc
            .perform(
                put(ENTITY_API_URL_ID, rolePermissionDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(rolePermissionDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the RolePermission in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithIdMismatchRolePermission() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        rolePermission.setId(longCount.incrementAndGet());

        // Create the RolePermission
        RolePermissionDTO rolePermissionDTO = rolePermissionMapper.toDto(rolePermission);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restRolePermissionMockMvc
            .perform(
                put(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(rolePermissionDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the RolePermission in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithMissingIdPathParamRolePermission() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        rolePermission.setId(longCount.incrementAndGet());

        // Create the RolePermission
        RolePermissionDTO rolePermissionDTO = rolePermissionMapper.toDto(rolePermission);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restRolePermissionMockMvc
            .perform(put(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(rolePermissionDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the RolePermission in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void partialUpdateRolePermissionWithPatch() throws Exception {
        // Initialize the database
        insertedRolePermission = rolePermissionRepository.saveAndFlush(rolePermission);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the rolePermission using partial update
        RolePermission partialUpdatedRolePermission = new RolePermission();
        partialUpdatedRolePermission.setId(rolePermission.getId());

        partialUpdatedRolePermission.version(UPDATED_VERSION).createdAt(UPDATED_CREATED_AT);

        restRolePermissionMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedRolePermission.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedRolePermission))
            )
            .andExpect(status().isOk());

        // Validate the RolePermission in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertRolePermissionUpdatableFieldsEquals(
            createUpdateProxyForBean(partialUpdatedRolePermission, rolePermission),
            getPersistedRolePermission(rolePermission)
        );
    }

    @Test
    @Transactional
    void fullUpdateRolePermissionWithPatch() throws Exception {
        // Initialize the database
        insertedRolePermission = rolePermissionRepository.saveAndFlush(rolePermission);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the rolePermission using partial update
        RolePermission partialUpdatedRolePermission = new RolePermission();
        partialUpdatedRolePermission.setId(rolePermission.getId());

        partialUpdatedRolePermission
            .tenantId(UPDATED_TENANT_ID)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);

        restRolePermissionMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedRolePermission.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedRolePermission))
            )
            .andExpect(status().isOk());

        // Validate the RolePermission in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertRolePermissionUpdatableFieldsEquals(partialUpdatedRolePermission, getPersistedRolePermission(partialUpdatedRolePermission));
    }

    @Test
    @Transactional
    void patchNonExistingRolePermission() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        rolePermission.setId(longCount.incrementAndGet());

        // Create the RolePermission
        RolePermissionDTO rolePermissionDTO = rolePermissionMapper.toDto(rolePermission);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restRolePermissionMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, rolePermissionDTO.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(rolePermissionDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the RolePermission in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithIdMismatchRolePermission() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        rolePermission.setId(longCount.incrementAndGet());

        // Create the RolePermission
        RolePermissionDTO rolePermissionDTO = rolePermissionMapper.toDto(rolePermission);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restRolePermissionMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(rolePermissionDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the RolePermission in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithMissingIdPathParamRolePermission() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        rolePermission.setId(longCount.incrementAndGet());

        // Create the RolePermission
        RolePermissionDTO rolePermissionDTO = rolePermissionMapper.toDto(rolePermission);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restRolePermissionMockMvc
            .perform(patch(ENTITY_API_URL).contentType("application/merge-patch+json").content(om.writeValueAsBytes(rolePermissionDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the RolePermission in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void deleteRolePermission() throws Exception {
        // Initialize the database
        insertedRolePermission = rolePermissionRepository.saveAndFlush(rolePermission);

        long databaseSizeBeforeDelete = getRepositoryCount();

        // Delete the rolePermission
        restRolePermissionMockMvc
            .perform(delete(ENTITY_API_URL_ID, rolePermission.getId()).accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isNoContent());

        // Validate the database contains one less item
        assertDecrementedRepositoryCount(databaseSizeBeforeDelete);
    }

    protected long getRepositoryCount() {
        return rolePermissionRepository.count();
    }

    protected void assertIncrementedRepositoryCount(long countBefore) {
        assertThat(countBefore + 1).isEqualTo(getRepositoryCount());
    }

    protected void assertDecrementedRepositoryCount(long countBefore) {
        assertThat(countBefore - 1).isEqualTo(getRepositoryCount());
    }

    protected void assertSameRepositoryCount(long countBefore) {
        assertThat(countBefore).isEqualTo(getRepositoryCount());
    }

    protected RolePermission getPersistedRolePermission(RolePermission rolePermission) {
        return rolePermissionRepository.findById(rolePermission.getId()).orElseThrow();
    }

    protected void assertPersistedRolePermissionToMatchAllProperties(RolePermission expectedRolePermission) {
        assertRolePermissionAllPropertiesEquals(expectedRolePermission, getPersistedRolePermission(expectedRolePermission));
    }

    protected void assertPersistedRolePermissionToMatchUpdatableProperties(RolePermission expectedRolePermission) {
        assertRolePermissionAllUpdatablePropertiesEquals(expectedRolePermission, getPersistedRolePermission(expectedRolePermission));
    }
}

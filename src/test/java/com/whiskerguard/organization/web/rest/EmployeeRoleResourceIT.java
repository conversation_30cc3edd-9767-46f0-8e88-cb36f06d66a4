package com.whiskerguard.organization.web.rest;

import static com.whiskerguard.organization.domain.EmployeeRoleAsserts.*;
import static com.whiskerguard.organization.web.rest.TestUtil.createUpdateProxyForBean;
import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.hasItem;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.whiskerguard.organization.IntegrationTest;
import com.whiskerguard.organization.domain.EmployeeRole;
import com.whiskerguard.organization.repository.EmployeeRoleRepository;
import com.whiskerguard.organization.service.dto.EmployeeRoleDTO;
import com.whiskerguard.organization.service.mapper.EmployeeRoleMapper;
import jakarta.persistence.EntityManager;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Random;
import java.util.concurrent.atomic.AtomicLong;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

/**
 * Integration tests for the {@link EmployeeRoleResource} REST controller.
 */
@IntegrationTest
@AutoConfigureMockMvc
@WithMockUser
class EmployeeRoleResourceIT {

    private static final Long DEFAULT_TENANT_ID = 1L;
    private static final Long UPDATED_TENANT_ID = 2L;

    private static final String DEFAULT_ASSIGNED_BY = "AAAAAAAAAA";
    private static final String UPDATED_ASSIGNED_BY = "BBBBBBBBBB";

    private static final Instant DEFAULT_ASSIGNED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_ASSIGNED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final Integer DEFAULT_VERSION = 1;
    private static final Integer UPDATED_VERSION = 2;

    private static final String DEFAULT_CREATED_BY = "AAAAAAAAAA";
    private static final String UPDATED_CREATED_BY = "BBBBBBBBBB";

    private static final Instant DEFAULT_CREATED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_CREATED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final String DEFAULT_UPDATED_BY = "AAAAAAAAAA";
    private static final String UPDATED_UPDATED_BY = "BBBBBBBBBB";

    private static final Instant DEFAULT_UPDATED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_UPDATED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final Boolean DEFAULT_IS_DELETED = false;
    private static final Boolean UPDATED_IS_DELETED = true;

    private static final String ENTITY_API_URL = "/api/employee-roles";
    private static final String ENTITY_API_URL_ID = ENTITY_API_URL + "/{id}";

    private static Random random = new Random();
    private static AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));

    @Autowired
    private ObjectMapper om;

    @Autowired
    private EmployeeRoleRepository employeeRoleRepository;

    @Autowired
    private EmployeeRoleMapper employeeRoleMapper;

    @Autowired
    private EntityManager em;

    @Autowired
    private MockMvc restEmployeeRoleMockMvc;

    private EmployeeRole employeeRole;

    private EmployeeRole insertedEmployeeRole;

    /**
     * Create an entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static EmployeeRole createEntity() {
        return new EmployeeRole()
            .tenantId(DEFAULT_TENANT_ID)
            .assignedBy(DEFAULT_ASSIGNED_BY)
            .assignedAt(DEFAULT_ASSIGNED_AT)
            .version(DEFAULT_VERSION)
            .createdBy(DEFAULT_CREATED_BY)
            .createdAt(DEFAULT_CREATED_AT)
            .updatedBy(DEFAULT_UPDATED_BY)
            .updatedAt(DEFAULT_UPDATED_AT)
            .isDeleted(DEFAULT_IS_DELETED);
    }

    /**
     * Create an updated entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static EmployeeRole createUpdatedEntity() {
        return new EmployeeRole()
            .tenantId(UPDATED_TENANT_ID)
            .assignedBy(UPDATED_ASSIGNED_BY)
            .assignedAt(UPDATED_ASSIGNED_AT)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);
    }

    @BeforeEach
    void initTest() {
        employeeRole = createEntity();
    }

    @AfterEach
    void cleanup() {
        if (insertedEmployeeRole != null) {
            employeeRoleRepository.delete(insertedEmployeeRole);
            insertedEmployeeRole = null;
        }
    }

    @Test
    @Transactional
    void createEmployeeRole() throws Exception {
        long databaseSizeBeforeCreate = getRepositoryCount();
        // Create the EmployeeRole
        EmployeeRoleDTO employeeRoleDTO = employeeRoleMapper.toDto(employeeRole);
        var returnedEmployeeRoleDTO = om.readValue(
            restEmployeeRoleMockMvc
                .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(employeeRoleDTO)))
                .andExpect(status().isCreated())
                .andReturn()
                .getResponse()
                .getContentAsString(),
            EmployeeRoleDTO.class
        );

        // Validate the EmployeeRole in the database
        assertIncrementedRepositoryCount(databaseSizeBeforeCreate);
        var returnedEmployeeRole = employeeRoleMapper.toEntity(returnedEmployeeRoleDTO);
        assertEmployeeRoleUpdatableFieldsEquals(returnedEmployeeRole, getPersistedEmployeeRole(returnedEmployeeRole));

        insertedEmployeeRole = returnedEmployeeRole;
    }

    @Test
    @Transactional
    void createEmployeeRoleWithExistingId() throws Exception {
        // Create the EmployeeRole with an existing ID
        employeeRole.setId(1L);
        EmployeeRoleDTO employeeRoleDTO = employeeRoleMapper.toDto(employeeRole);

        long databaseSizeBeforeCreate = getRepositoryCount();

        // An entity with an existing ID cannot be created, so this API call must fail
        restEmployeeRoleMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(employeeRoleDTO)))
            .andExpect(status().isBadRequest());

        // Validate the EmployeeRole in the database
        assertSameRepositoryCount(databaseSizeBeforeCreate);
    }

    @Test
    @Transactional
    void checkTenantIdIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        employeeRole.setTenantId(null);

        // Create the EmployeeRole, which fails.
        EmployeeRoleDTO employeeRoleDTO = employeeRoleMapper.toDto(employeeRole);

        restEmployeeRoleMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(employeeRoleDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkVersionIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        employeeRole.setVersion(null);

        // Create the EmployeeRole, which fails.
        EmployeeRoleDTO employeeRoleDTO = employeeRoleMapper.toDto(employeeRole);

        restEmployeeRoleMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(employeeRoleDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkCreatedAtIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        employeeRole.setCreatedAt(null);

        // Create the EmployeeRole, which fails.
        EmployeeRoleDTO employeeRoleDTO = employeeRoleMapper.toDto(employeeRole);

        restEmployeeRoleMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(employeeRoleDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkUpdatedAtIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        employeeRole.setUpdatedAt(null);

        // Create the EmployeeRole, which fails.
        EmployeeRoleDTO employeeRoleDTO = employeeRoleMapper.toDto(employeeRole);

        restEmployeeRoleMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(employeeRoleDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkIsDeletedIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        employeeRole.setIsDeleted(null);

        // Create the EmployeeRole, which fails.
        EmployeeRoleDTO employeeRoleDTO = employeeRoleMapper.toDto(employeeRole);

        restEmployeeRoleMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(employeeRoleDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void getAllEmployeeRoles() throws Exception {
        // Initialize the database
        insertedEmployeeRole = employeeRoleRepository.saveAndFlush(employeeRole);

        // Get all the employeeRoleList
        restEmployeeRoleMockMvc
            .perform(get(ENTITY_API_URL + "?sort=id,desc"))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.[*].id").value(hasItem(employeeRole.getId().intValue())))
            .andExpect(jsonPath("$.[*].tenantId").value(hasItem(DEFAULT_TENANT_ID.intValue())))
            .andExpect(jsonPath("$.[*].assignedBy").value(hasItem(DEFAULT_ASSIGNED_BY)))
            .andExpect(jsonPath("$.[*].assignedAt").value(hasItem(DEFAULT_ASSIGNED_AT.toString())))
            .andExpect(jsonPath("$.[*].version").value(hasItem(DEFAULT_VERSION)))
            .andExpect(jsonPath("$.[*].createdBy").value(hasItem(DEFAULT_CREATED_BY)))
            .andExpect(jsonPath("$.[*].createdAt").value(hasItem(DEFAULT_CREATED_AT.toString())))
            .andExpect(jsonPath("$.[*].updatedBy").value(hasItem(DEFAULT_UPDATED_BY)))
            .andExpect(jsonPath("$.[*].updatedAt").value(hasItem(DEFAULT_UPDATED_AT.toString())))
            .andExpect(jsonPath("$.[*].isDeleted").value(hasItem(DEFAULT_IS_DELETED)));
    }

    @Test
    @Transactional
    void getEmployeeRole() throws Exception {
        // Initialize the database
        insertedEmployeeRole = employeeRoleRepository.saveAndFlush(employeeRole);

        // Get the employeeRole
        restEmployeeRoleMockMvc
            .perform(get(ENTITY_API_URL_ID, employeeRole.getId()))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.id").value(employeeRole.getId().intValue()))
            .andExpect(jsonPath("$.tenantId").value(DEFAULT_TENANT_ID.intValue()))
            .andExpect(jsonPath("$.assignedBy").value(DEFAULT_ASSIGNED_BY))
            .andExpect(jsonPath("$.assignedAt").value(DEFAULT_ASSIGNED_AT.toString()))
            .andExpect(jsonPath("$.version").value(DEFAULT_VERSION))
            .andExpect(jsonPath("$.createdBy").value(DEFAULT_CREATED_BY))
            .andExpect(jsonPath("$.createdAt").value(DEFAULT_CREATED_AT.toString()))
            .andExpect(jsonPath("$.updatedBy").value(DEFAULT_UPDATED_BY))
            .andExpect(jsonPath("$.updatedAt").value(DEFAULT_UPDATED_AT.toString()))
            .andExpect(jsonPath("$.isDeleted").value(DEFAULT_IS_DELETED));
    }

    @Test
    @Transactional
    void getNonExistingEmployeeRole() throws Exception {
        // Get the employeeRole
        restEmployeeRoleMockMvc.perform(get(ENTITY_API_URL_ID, Long.MAX_VALUE)).andExpect(status().isNotFound());
    }

    @Test
    @Transactional
    void putExistingEmployeeRole() throws Exception {
        // Initialize the database
        insertedEmployeeRole = employeeRoleRepository.saveAndFlush(employeeRole);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the employeeRole
        EmployeeRole updatedEmployeeRole = employeeRoleRepository.findById(employeeRole.getId()).orElseThrow();
        // Disconnect from session so that the updates on updatedEmployeeRole are not directly saved in db
        em.detach(updatedEmployeeRole);
        updatedEmployeeRole
            .tenantId(UPDATED_TENANT_ID)
            .assignedBy(UPDATED_ASSIGNED_BY)
            .assignedAt(UPDATED_ASSIGNED_AT)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);
        EmployeeRoleDTO employeeRoleDTO = employeeRoleMapper.toDto(updatedEmployeeRole);

        restEmployeeRoleMockMvc
            .perform(
                put(ENTITY_API_URL_ID, employeeRoleDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(employeeRoleDTO))
            )
            .andExpect(status().isOk());

        // Validate the EmployeeRole in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertPersistedEmployeeRoleToMatchAllProperties(updatedEmployeeRole);
    }

    @Test
    @Transactional
    void putNonExistingEmployeeRole() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        employeeRole.setId(longCount.incrementAndGet());

        // Create the EmployeeRole
        EmployeeRoleDTO employeeRoleDTO = employeeRoleMapper.toDto(employeeRole);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restEmployeeRoleMockMvc
            .perform(
                put(ENTITY_API_URL_ID, employeeRoleDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(employeeRoleDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the EmployeeRole in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithIdMismatchEmployeeRole() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        employeeRole.setId(longCount.incrementAndGet());

        // Create the EmployeeRole
        EmployeeRoleDTO employeeRoleDTO = employeeRoleMapper.toDto(employeeRole);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restEmployeeRoleMockMvc
            .perform(
                put(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(employeeRoleDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the EmployeeRole in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithMissingIdPathParamEmployeeRole() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        employeeRole.setId(longCount.incrementAndGet());

        // Create the EmployeeRole
        EmployeeRoleDTO employeeRoleDTO = employeeRoleMapper.toDto(employeeRole);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restEmployeeRoleMockMvc
            .perform(put(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(employeeRoleDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the EmployeeRole in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void partialUpdateEmployeeRoleWithPatch() throws Exception {
        // Initialize the database
        insertedEmployeeRole = employeeRoleRepository.saveAndFlush(employeeRole);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the employeeRole using partial update
        EmployeeRole partialUpdatedEmployeeRole = new EmployeeRole();
        partialUpdatedEmployeeRole.setId(employeeRole.getId());

        partialUpdatedEmployeeRole
            .assignedBy(UPDATED_ASSIGNED_BY)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);

        restEmployeeRoleMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedEmployeeRole.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedEmployeeRole))
            )
            .andExpect(status().isOk());

        // Validate the EmployeeRole in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertEmployeeRoleUpdatableFieldsEquals(
            createUpdateProxyForBean(partialUpdatedEmployeeRole, employeeRole),
            getPersistedEmployeeRole(employeeRole)
        );
    }

    @Test
    @Transactional
    void fullUpdateEmployeeRoleWithPatch() throws Exception {
        // Initialize the database
        insertedEmployeeRole = employeeRoleRepository.saveAndFlush(employeeRole);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the employeeRole using partial update
        EmployeeRole partialUpdatedEmployeeRole = new EmployeeRole();
        partialUpdatedEmployeeRole.setId(employeeRole.getId());

        partialUpdatedEmployeeRole
            .tenantId(UPDATED_TENANT_ID)
            .assignedBy(UPDATED_ASSIGNED_BY)
            .assignedAt(UPDATED_ASSIGNED_AT)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);

        restEmployeeRoleMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedEmployeeRole.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedEmployeeRole))
            )
            .andExpect(status().isOk());

        // Validate the EmployeeRole in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertEmployeeRoleUpdatableFieldsEquals(partialUpdatedEmployeeRole, getPersistedEmployeeRole(partialUpdatedEmployeeRole));
    }

    @Test
    @Transactional
    void patchNonExistingEmployeeRole() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        employeeRole.setId(longCount.incrementAndGet());

        // Create the EmployeeRole
        EmployeeRoleDTO employeeRoleDTO = employeeRoleMapper.toDto(employeeRole);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restEmployeeRoleMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, employeeRoleDTO.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(employeeRoleDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the EmployeeRole in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithIdMismatchEmployeeRole() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        employeeRole.setId(longCount.incrementAndGet());

        // Create the EmployeeRole
        EmployeeRoleDTO employeeRoleDTO = employeeRoleMapper.toDto(employeeRole);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restEmployeeRoleMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(employeeRoleDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the EmployeeRole in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithMissingIdPathParamEmployeeRole() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        employeeRole.setId(longCount.incrementAndGet());

        // Create the EmployeeRole
        EmployeeRoleDTO employeeRoleDTO = employeeRoleMapper.toDto(employeeRole);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restEmployeeRoleMockMvc
            .perform(patch(ENTITY_API_URL).contentType("application/merge-patch+json").content(om.writeValueAsBytes(employeeRoleDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the EmployeeRole in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void deleteEmployeeRole() throws Exception {
        // Initialize the database
        insertedEmployeeRole = employeeRoleRepository.saveAndFlush(employeeRole);

        long databaseSizeBeforeDelete = getRepositoryCount();

        // Delete the employeeRole
        restEmployeeRoleMockMvc
            .perform(delete(ENTITY_API_URL_ID, employeeRole.getId()).accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isNoContent());

        // Validate the database contains one less item
        assertDecrementedRepositoryCount(databaseSizeBeforeDelete);
    }

    protected long getRepositoryCount() {
        return employeeRoleRepository.count();
    }

    protected void assertIncrementedRepositoryCount(long countBefore) {
        assertThat(countBefore + 1).isEqualTo(getRepositoryCount());
    }

    protected void assertDecrementedRepositoryCount(long countBefore) {
        assertThat(countBefore - 1).isEqualTo(getRepositoryCount());
    }

    protected void assertSameRepositoryCount(long countBefore) {
        assertThat(countBefore).isEqualTo(getRepositoryCount());
    }

    protected EmployeeRole getPersistedEmployeeRole(EmployeeRole employeeRole) {
        return employeeRoleRepository.findById(employeeRole.getId()).orElseThrow();
    }

    protected void assertPersistedEmployeeRoleToMatchAllProperties(EmployeeRole expectedEmployeeRole) {
        assertEmployeeRoleAllPropertiesEquals(expectedEmployeeRole, getPersistedEmployeeRole(expectedEmployeeRole));
    }

    protected void assertPersistedEmployeeRoleToMatchUpdatableProperties(EmployeeRole expectedEmployeeRole) {
        assertEmployeeRoleAllUpdatablePropertiesEquals(expectedEmployeeRole, getPersistedEmployeeRole(expectedEmployeeRole));
    }
}

package com.whiskerguard.organization.web.rest;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.whiskerguard.organization.IntegrationTest;
import com.whiskerguard.organization.domain.News;
import com.whiskerguard.organization.domain.enumeration.NewsStatus;
import com.whiskerguard.organization.repository.NewsRepository;
import com.whiskerguard.organization.service.NewsService;
import com.whiskerguard.organization.service.dto.NewsDTO;
import com.whiskerguard.organization.service.mapper.NewsMapper;
import jakarta.persistence.EntityManager;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Random;
import java.util.concurrent.atomic.AtomicLong;

import static com.whiskerguard.organization.domain.NewsAsserts.*;
import static com.whiskerguard.organization.web.rest.TestUtil.createUpdateProxyForBean;
import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.hasItem;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * Integration tests for the {@link NewsResource} REST controller.
 */
@IntegrationTest
@ExtendWith(MockitoExtension.class)
@AutoConfigureMockMvc
@WithMockUser
class NewsResourceIT {

    private static final NewsStatus DEFAULT_STATUS = NewsStatus.DRAFT;
    private static final NewsStatus UPDATED_STATUS = NewsStatus.PENDING_REVIEW;

    private static final Integer DEFAULT_SORT_ORDER = 1;
    private static final Integer UPDATED_SORT_ORDER = 2;

    private static final String DEFAULT_SUBTITLE = "AAAAAAAAAA";
    private static final String UPDATED_SUBTITLE = "BBBBBBBBBB";

    private static final String DEFAULT_TITLE = "AAAAAAAAAA";
    private static final String UPDATED_TITLE = "BBBBBBBBBB";

    private static final String DEFAULT_SUMMARY = "AAAAAAAAAA";
    private static final String UPDATED_SUMMARY = "BBBBBBBBBB";

    private static final String DEFAULT_KEYWORDS = "AAAAAAAAAA";
    private static final String UPDATED_KEYWORDS = "BBBBBBBBBB";

    private static final String DEFAULT_CONTENT = "AAAAAAAAAA";
    private static final String UPDATED_CONTENT = "BBBBBBBBBB";

    private static final Instant DEFAULT_PUBLISH_DATE = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_PUBLISH_DATE = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final Instant DEFAULT_PUBLISHED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_PUBLISHED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final Integer DEFAULT_VIEW_COUNT = 1;
    private static final Integer UPDATED_VIEW_COUNT = 2;

    private static final Integer DEFAULT_LIKE_COUNT = 1;
    private static final Integer UPDATED_LIKE_COUNT = 2;

    private static final Integer DEFAULT_COMMENT_COUNT = 1;
    private static final Integer UPDATED_COMMENT_COUNT = 2;

    private static final Integer DEFAULT_SHARE_COUNT = 1;
    private static final Integer UPDATED_SHARE_COUNT = 2;

    private static final String DEFAULT_COVER_IMAGE_URL = "AAAAAAAAAA";
    private static final String UPDATED_COVER_IMAGE_URL = "BBBBBBBBBB";

    private static final Boolean DEFAULT_IS_STICKY = false;
    private static final Boolean UPDATED_IS_STICKY = true;

    private static final Instant DEFAULT_STICKY_START_TIME = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_STICKY_START_TIME = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final Instant DEFAULT_STICKY_END_TIME = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_STICKY_END_TIME = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final String DEFAULT_METADATA = "AAAAAAAAAA";
    private static final String UPDATED_METADATA = "BBBBBBBBBB";

    private static final Integer DEFAULT_VERSION = 1;
    private static final Integer UPDATED_VERSION = 2;

    private static final String DEFAULT_CREATED_BY = "AAAAAAAAAA";
    private static final String UPDATED_CREATED_BY = "BBBBBBBBBB";

    private static final Instant DEFAULT_CREATED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_CREATED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final String DEFAULT_UPDATED_BY = "AAAAAAAAAA";
    private static final String UPDATED_UPDATED_BY = "BBBBBBBBBB";

    private static final Instant DEFAULT_UPDATED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_UPDATED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final Boolean DEFAULT_IS_DELETED = false;
    private static final Boolean UPDATED_IS_DELETED = true;

    private static final String ENTITY_API_URL = "/api/news";
    private static final String ENTITY_API_URL_ID = ENTITY_API_URL + "/{id}";

    private static Random random = new Random();
    private static AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));

    @Autowired
    private ObjectMapper om;

    @Autowired
    private NewsRepository newsRepository;

    @Mock
    private NewsRepository newsRepositoryMock;

    @Autowired
    private NewsMapper newsMapper;

    @Mock
    private NewsService newsServiceMock;

    @Autowired
    private EntityManager em;

    @Autowired
    private MockMvc restNewsMockMvc;

    private News news;

    private News insertedNews;

    /**
     * Create an entity for this test.
     * <p>
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static News createEntity() {
        return new News()
            .status(DEFAULT_STATUS)
            .sortOrder(DEFAULT_SORT_ORDER)
            .subtitle(DEFAULT_SUBTITLE)
            .title(DEFAULT_TITLE)
            .summary(DEFAULT_SUMMARY)
            .keywords(DEFAULT_KEYWORDS)
            .content(DEFAULT_CONTENT)
            .publishDate(DEFAULT_PUBLISH_DATE)
            .publishedAt(DEFAULT_PUBLISHED_AT)
            .viewCount(DEFAULT_VIEW_COUNT)
            .likeCount(DEFAULT_LIKE_COUNT)
            .commentCount(DEFAULT_COMMENT_COUNT)
            .shareCount(DEFAULT_SHARE_COUNT)
            .coverImageUrl(DEFAULT_COVER_IMAGE_URL)
            .isSticky(DEFAULT_IS_STICKY)
            .stickyStartTime(DEFAULT_STICKY_START_TIME)
            .stickyEndTime(DEFAULT_STICKY_END_TIME)
            .metadata(DEFAULT_METADATA)
            .version(DEFAULT_VERSION)
            .createdBy(DEFAULT_CREATED_BY)
            .createdAt(DEFAULT_CREATED_AT)
            .updatedBy(DEFAULT_UPDATED_BY)
            .updatedAt(DEFAULT_UPDATED_AT)
            .isDeleted(DEFAULT_IS_DELETED);
    }

    /**
     * Create an updated entity for this test.
     * <p>
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static News createUpdatedEntity() {
        return new News()
            .status(UPDATED_STATUS)
            .sortOrder(UPDATED_SORT_ORDER)
            .subtitle(UPDATED_SUBTITLE)
            .title(UPDATED_TITLE)
            .summary(UPDATED_SUMMARY)
            .keywords(UPDATED_KEYWORDS)
            .content(UPDATED_CONTENT)
            .publishDate(UPDATED_PUBLISH_DATE)
            .publishedAt(UPDATED_PUBLISHED_AT)
            .viewCount(UPDATED_VIEW_COUNT)
            .likeCount(UPDATED_LIKE_COUNT)
            .commentCount(UPDATED_COMMENT_COUNT)
            .shareCount(UPDATED_SHARE_COUNT)
            .coverImageUrl(UPDATED_COVER_IMAGE_URL)
            .isSticky(UPDATED_IS_STICKY)
            .stickyStartTime(UPDATED_STICKY_START_TIME)
            .stickyEndTime(UPDATED_STICKY_END_TIME)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);
    }

    @BeforeEach
    void initTest() {
        news = createEntity();
    }

    @AfterEach
    void cleanup() {
        if (insertedNews != null) {
            newsRepository.delete(insertedNews);
            insertedNews = null;
        }
    }

    @Test
    @Transactional
    void createNews() throws Exception {
        long databaseSizeBeforeCreate = getRepositoryCount();
        // Create the News
        NewsDTO newsDTO = newsMapper.toDto(news);
        var returnedNewsDTO = om.readValue(
            restNewsMockMvc
                .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(newsDTO)))
                .andExpect(status().isCreated())
                .andReturn()
                .getResponse()
                .getContentAsString(),
            NewsDTO.class
        );

        // Validate the News in the database
        assertIncrementedRepositoryCount(databaseSizeBeforeCreate);
        var returnedNews = newsMapper.toEntity(returnedNewsDTO);
        assertNewsUpdatableFieldsEquals(returnedNews, getPersistedNews(returnedNews));

        insertedNews = returnedNews;
    }

    @Test
    @Transactional
    void createNewsWithExistingId() throws Exception {
        // Create the News with an existing ID
        news.setId(1L);
        NewsDTO newsDTO = newsMapper.toDto(news);

        long databaseSizeBeforeCreate = getRepositoryCount();

        // An entity with an existing ID cannot be created, so this API call must fail
        restNewsMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(newsDTO)))
            .andExpect(status().isBadRequest());

        // Validate the News in the database
        assertSameRepositoryCount(databaseSizeBeforeCreate);
    }

    @Test
    @Transactional
    void checkStatusIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        news.setStatus(null);

        // Create the News, which fails.
        NewsDTO newsDTO = newsMapper.toDto(news);

        restNewsMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(newsDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkTitleIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        news.setTitle(null);

        // Create the News, which fails.
        NewsDTO newsDTO = newsMapper.toDto(news);

        restNewsMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(newsDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkVersionIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        news.setVersion(null);

        // Create the News, which fails.
        NewsDTO newsDTO = newsMapper.toDto(news);

        restNewsMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(newsDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkCreatedAtIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        news.setCreatedAt(null);

        // Create the News, which fails.
        NewsDTO newsDTO = newsMapper.toDto(news);

        restNewsMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(newsDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkUpdatedAtIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        news.setUpdatedAt(null);

        // Create the News, which fails.
        NewsDTO newsDTO = newsMapper.toDto(news);

        restNewsMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(newsDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkIsDeletedIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        news.setIsDeleted(null);

        // Create the News, which fails.
        NewsDTO newsDTO = newsMapper.toDto(news);

        restNewsMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(newsDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void getAllNews() throws Exception {
        // Initialize the database
        insertedNews = newsRepository.saveAndFlush(news);

        // Get all the newsList
        restNewsMockMvc
            .perform(get(ENTITY_API_URL + "?sort=id,desc"))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.[*].id").value(hasItem(news.getId().intValue())))
            .andExpect(jsonPath("$.[*].status").value(hasItem(DEFAULT_STATUS.toString())))
            .andExpect(jsonPath("$.[*].sortOrder").value(hasItem(DEFAULT_SORT_ORDER)))
            .andExpect(jsonPath("$.[*].subtitle").value(hasItem(DEFAULT_SUBTITLE)))
            .andExpect(jsonPath("$.[*].title").value(hasItem(DEFAULT_TITLE)))
            .andExpect(jsonPath("$.[*].summary").value(hasItem(DEFAULT_SUMMARY)))
            .andExpect(jsonPath("$.[*].keywords").value(hasItem(DEFAULT_KEYWORDS)))
            .andExpect(jsonPath("$.[*].content").value(hasItem(DEFAULT_CONTENT)))
            .andExpect(jsonPath("$.[*].publishDate").value(hasItem(DEFAULT_PUBLISH_DATE.toString())))
            .andExpect(jsonPath("$.[*].publishedAt").value(hasItem(DEFAULT_PUBLISHED_AT.toString())))
            .andExpect(jsonPath("$.[*].viewCount").value(hasItem(DEFAULT_VIEW_COUNT)))
            .andExpect(jsonPath("$.[*].likeCount").value(hasItem(DEFAULT_LIKE_COUNT)))
            .andExpect(jsonPath("$.[*].commentCount").value(hasItem(DEFAULT_COMMENT_COUNT)))
            .andExpect(jsonPath("$.[*].shareCount").value(hasItem(DEFAULT_SHARE_COUNT)))
            .andExpect(jsonPath("$.[*].coverImageUrl").value(hasItem(DEFAULT_COVER_IMAGE_URL)))
            .andExpect(jsonPath("$.[*].isSticky").value(hasItem(DEFAULT_IS_STICKY)))
            .andExpect(jsonPath("$.[*].stickyStartTime").value(hasItem(DEFAULT_STICKY_START_TIME.toString())))
            .andExpect(jsonPath("$.[*].stickyEndTime").value(hasItem(DEFAULT_STICKY_END_TIME.toString())))
            .andExpect(jsonPath("$.[*].metadata").value(hasItem(DEFAULT_METADATA)))
            .andExpect(jsonPath("$.[*].version").value(hasItem(DEFAULT_VERSION)))
            .andExpect(jsonPath("$.[*].createdBy").value(hasItem(DEFAULT_CREATED_BY)))
            .andExpect(jsonPath("$.[*].createdAt").value(hasItem(DEFAULT_CREATED_AT.toString())))
            .andExpect(jsonPath("$.[*].updatedBy").value(hasItem(DEFAULT_UPDATED_BY)))
            .andExpect(jsonPath("$.[*].updatedAt").value(hasItem(DEFAULT_UPDATED_AT.toString())))
            .andExpect(jsonPath("$.[*].isDeleted").value(hasItem(DEFAULT_IS_DELETED)));
    }

    @SuppressWarnings({"unchecked"})
    void getAllNewsWithEagerRelationshipsIsEnabled() throws Exception {
        when(newsServiceMock.findAllWithEagerRelationships(any(), categoryId)).thenReturn(new PageImpl(new ArrayList<>()));

        restNewsMockMvc.perform(get(ENTITY_API_URL + "?eagerload=true")).andExpect(status().isOk());

        verify(newsServiceMock, times(1)).findAllWithEagerRelationships(any(), categoryId);
    }

    @SuppressWarnings({"unchecked"})
    void getAllNewsWithEagerRelationshipsIsNotEnabled() throws Exception {
        when(newsServiceMock.findAllWithEagerRelationships(any(), categoryId)).thenReturn(new PageImpl(new ArrayList<>()));

        restNewsMockMvc.perform(get(ENTITY_API_URL + "?eagerload=false")).andExpect(status().isOk());
        verify(newsRepositoryMock, times(1)).findAll(any(Pageable.class));
    }

    @Test
    @Transactional
    void getNews() throws Exception {
        // Initialize the database
        insertedNews = newsRepository.saveAndFlush(news);

        // Get the news
        restNewsMockMvc
            .perform(get(ENTITY_API_URL_ID, news.getId()))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.id").value(news.getId().intValue()))
            .andExpect(jsonPath("$.status").value(DEFAULT_STATUS.toString()))
            .andExpect(jsonPath("$.sortOrder").value(DEFAULT_SORT_ORDER))
            .andExpect(jsonPath("$.subtitle").value(DEFAULT_SUBTITLE))
            .andExpect(jsonPath("$.title").value(DEFAULT_TITLE))
            .andExpect(jsonPath("$.summary").value(DEFAULT_SUMMARY))
            .andExpect(jsonPath("$.keywords").value(DEFAULT_KEYWORDS))
            .andExpect(jsonPath("$.content").value(DEFAULT_CONTENT))
            .andExpect(jsonPath("$.publishDate").value(DEFAULT_PUBLISH_DATE.toString()))
            .andExpect(jsonPath("$.publishedAt").value(DEFAULT_PUBLISHED_AT.toString()))
            .andExpect(jsonPath("$.viewCount").value(DEFAULT_VIEW_COUNT))
            .andExpect(jsonPath("$.likeCount").value(DEFAULT_LIKE_COUNT))
            .andExpect(jsonPath("$.commentCount").value(DEFAULT_COMMENT_COUNT))
            .andExpect(jsonPath("$.shareCount").value(DEFAULT_SHARE_COUNT))
            .andExpect(jsonPath("$.coverImageUrl").value(DEFAULT_COVER_IMAGE_URL))
            .andExpect(jsonPath("$.isSticky").value(DEFAULT_IS_STICKY))
            .andExpect(jsonPath("$.stickyStartTime").value(DEFAULT_STICKY_START_TIME.toString()))
            .andExpect(jsonPath("$.stickyEndTime").value(DEFAULT_STICKY_END_TIME.toString()))
            .andExpect(jsonPath("$.metadata").value(DEFAULT_METADATA))
            .andExpect(jsonPath("$.version").value(DEFAULT_VERSION))
            .andExpect(jsonPath("$.createdBy").value(DEFAULT_CREATED_BY))
            .andExpect(jsonPath("$.createdAt").value(DEFAULT_CREATED_AT.toString()))
            .andExpect(jsonPath("$.updatedBy").value(DEFAULT_UPDATED_BY))
            .andExpect(jsonPath("$.updatedAt").value(DEFAULT_UPDATED_AT.toString()))
            .andExpect(jsonPath("$.isDeleted").value(DEFAULT_IS_DELETED));
    }

    @Test
    @Transactional
    void getNonExistingNews() throws Exception {
        // Get the news
        restNewsMockMvc.perform(get(ENTITY_API_URL_ID, Long.MAX_VALUE)).andExpect(status().isNotFound());
    }

    @Test
    @Transactional
    void putExistingNews() throws Exception {
        // Initialize the database
        insertedNews = newsRepository.saveAndFlush(news);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the news
        News updatedNews = newsRepository.findById(news.getId()).orElseThrow();
        // Disconnect from session so that the updates on updatedNews are not directly saved in db
        em.detach(updatedNews);
        updatedNews
            .status(UPDATED_STATUS)
            .sortOrder(UPDATED_SORT_ORDER)
            .subtitle(UPDATED_SUBTITLE)
            .title(UPDATED_TITLE)
            .summary(UPDATED_SUMMARY)
            .keywords(UPDATED_KEYWORDS)
            .content(UPDATED_CONTENT)
            .publishDate(UPDATED_PUBLISH_DATE)
            .publishedAt(UPDATED_PUBLISHED_AT)
            .viewCount(UPDATED_VIEW_COUNT)
            .likeCount(UPDATED_LIKE_COUNT)
            .commentCount(UPDATED_COMMENT_COUNT)
            .shareCount(UPDATED_SHARE_COUNT)
            .coverImageUrl(UPDATED_COVER_IMAGE_URL)
            .isSticky(UPDATED_IS_STICKY)
            .stickyStartTime(UPDATED_STICKY_START_TIME)
            .stickyEndTime(UPDATED_STICKY_END_TIME)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);
        NewsDTO newsDTO = newsMapper.toDto(updatedNews);

        restNewsMockMvc
            .perform(put(ENTITY_API_URL_ID, newsDTO.getId()).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(newsDTO)))
            .andExpect(status().isOk());

        // Validate the News in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertPersistedNewsToMatchAllProperties(updatedNews);
    }

    @Test
    @Transactional
    void putNonExistingNews() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        news.setId(longCount.incrementAndGet());

        // Create the News
        NewsDTO newsDTO = newsMapper.toDto(news);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restNewsMockMvc
            .perform(put(ENTITY_API_URL_ID, newsDTO.getId()).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(newsDTO)))
            .andExpect(status().isBadRequest());

        // Validate the News in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithIdMismatchNews() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        news.setId(longCount.incrementAndGet());

        // Create the News
        NewsDTO newsDTO = newsMapper.toDto(news);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restNewsMockMvc
            .perform(
                put(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(newsDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the News in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithMissingIdPathParamNews() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        news.setId(longCount.incrementAndGet());

        // Create the News
        NewsDTO newsDTO = newsMapper.toDto(news);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restNewsMockMvc
            .perform(put(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(newsDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the News in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void partialUpdateNewsWithPatch() throws Exception {
        // Initialize the database
        insertedNews = newsRepository.saveAndFlush(news);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the news using partial update
        News partialUpdatedNews = new News();
        partialUpdatedNews.setId(news.getId());

        partialUpdatedNews
            .subtitle(UPDATED_SUBTITLE)
            .title(UPDATED_TITLE)
            .content(UPDATED_CONTENT)
            .likeCount(UPDATED_LIKE_COUNT)
            .stickyStartTime(UPDATED_STICKY_START_TIME)
            .metadata(UPDATED_METADATA)
            .createdBy(UPDATED_CREATED_BY);

        restNewsMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedNews.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedNews))
            )
            .andExpect(status().isOk());

        // Validate the News in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertNewsUpdatableFieldsEquals(createUpdateProxyForBean(partialUpdatedNews, news), getPersistedNews(news));
    }

    @Test
    @Transactional
    void fullUpdateNewsWithPatch() throws Exception {
        // Initialize the database
        insertedNews = newsRepository.saveAndFlush(news);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the news using partial update
        News partialUpdatedNews = new News();
        partialUpdatedNews.setId(news.getId());

        partialUpdatedNews
            .status(UPDATED_STATUS)
            .sortOrder(UPDATED_SORT_ORDER)
            .subtitle(UPDATED_SUBTITLE)
            .title(UPDATED_TITLE)
            .summary(UPDATED_SUMMARY)
            .keywords(UPDATED_KEYWORDS)
            .content(UPDATED_CONTENT)
            .publishDate(UPDATED_PUBLISH_DATE)
            .publishedAt(UPDATED_PUBLISHED_AT)
            .viewCount(UPDATED_VIEW_COUNT)
            .likeCount(UPDATED_LIKE_COUNT)
            .commentCount(UPDATED_COMMENT_COUNT)
            .shareCount(UPDATED_SHARE_COUNT)
            .coverImageUrl(UPDATED_COVER_IMAGE_URL)
            .isSticky(UPDATED_IS_STICKY)
            .stickyStartTime(UPDATED_STICKY_START_TIME)
            .stickyEndTime(UPDATED_STICKY_END_TIME)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);

        restNewsMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedNews.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedNews))
            )
            .andExpect(status().isOk());

        // Validate the News in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertNewsUpdatableFieldsEquals(partialUpdatedNews, getPersistedNews(partialUpdatedNews));
    }

    @Test
    @Transactional
    void patchNonExistingNews() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        news.setId(longCount.incrementAndGet());

        // Create the News
        NewsDTO newsDTO = newsMapper.toDto(news);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restNewsMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, newsDTO.getId()).contentType("application/merge-patch+json").content(om.writeValueAsBytes(newsDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the News in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithIdMismatchNews() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        news.setId(longCount.incrementAndGet());

        // Create the News
        NewsDTO newsDTO = newsMapper.toDto(news);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restNewsMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(newsDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the News in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithMissingIdPathParamNews() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        news.setId(longCount.incrementAndGet());

        // Create the News
        NewsDTO newsDTO = newsMapper.toDto(news);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restNewsMockMvc
            .perform(patch(ENTITY_API_URL).contentType("application/merge-patch+json").content(om.writeValueAsBytes(newsDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the News in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void deleteNews() throws Exception {
        // Initialize the database
        insertedNews = newsRepository.saveAndFlush(news);

        long databaseSizeBeforeDelete = getRepositoryCount();

        // Delete the news
        restNewsMockMvc
            .perform(delete(ENTITY_API_URL_ID, news.getId()).accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isNoContent());

        // Validate the database contains one less item
        assertDecrementedRepositoryCount(databaseSizeBeforeDelete);
    }

    protected long getRepositoryCount() {
        return newsRepository.count();
    }

    protected void assertIncrementedRepositoryCount(long countBefore) {
        assertThat(countBefore + 1).isEqualTo(getRepositoryCount());
    }

    protected void assertDecrementedRepositoryCount(long countBefore) {
        assertThat(countBefore - 1).isEqualTo(getRepositoryCount());
    }

    protected void assertSameRepositoryCount(long countBefore) {
        assertThat(countBefore).isEqualTo(getRepositoryCount());
    }

    protected News getPersistedNews(News news) {
        return newsRepository.findById(news.getId()).orElseThrow();
    }

    protected void assertPersistedNewsToMatchAllProperties(News expectedNews) {
        assertNewsAllPropertiesEquals(expectedNews, getPersistedNews(expectedNews));
    }

    protected void assertPersistedNewsToMatchUpdatableProperties(News expectedNews) {
        assertNewsAllUpdatablePropertiesEquals(expectedNews, getPersistedNews(expectedNews));
    }
}

package com.whiskerguard.organization.web.rest;

import static com.whiskerguard.organization.domain.TenantInitializeAsserts.*;
import static com.whiskerguard.organization.web.rest.TestUtil.createUpdateProxyForBean;
import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.hasItem;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.whiskerguard.organization.IntegrationTest;
import com.whiskerguard.organization.domain.TenantInitialize;
import com.whiskerguard.organization.repository.TenantInitializeRepository;
import com.whiskerguard.organization.service.dto.TenantInitializeDTO;
import com.whiskerguard.organization.service.mapper.TenantInitializeMapper;
import jakarta.persistence.EntityManager;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Random;
import java.util.concurrent.atomic.AtomicLong;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

/**
 * Integration tests for the {@link TenantInitializeResource} REST controller.
 */
@IntegrationTest
@AutoConfigureMockMvc
@WithMockUser
class TenantInitializeResourceIT {

    private static final Long DEFAULT_TENANT_ID = 1L;
    private static final Long UPDATED_TENANT_ID = 2L;

    private static final Integer DEFAULT_TYPE = 1;
    private static final Integer UPDATED_TYPE = 2;

    private static final String DEFAULT_NAME = "AAAAAAAAAA";
    private static final String UPDATED_NAME = "BBBBBBBBBB";

    private static final String DEFAULT_CODE = "AAAAAAAAAA";
    private static final String UPDATED_CODE = "BBBBBBBBBB";

    private static final String DEFAULT_DEPARTMENT_CODE = "AAAAAAAAAA";
    private static final String UPDATED_DEPARTMENT_CODE = "BBBBBBBBBB";

    private static final String DEFAULT_DESCRIPTION = "AAAAAAAAAA";
    private static final String UPDATED_DESCRIPTION = "BBBBBBBBBB";

    private static final String DEFAULT_METADATA = "AAAAAAAAAA";
    private static final String UPDATED_METADATA = "BBBBBBBBBB";

    private static final Integer DEFAULT_VERSION = 1;
    private static final Integer UPDATED_VERSION = 2;

    private static final String DEFAULT_CREATED_BY = "AAAAAAAAAA";
    private static final String UPDATED_CREATED_BY = "BBBBBBBBBB";

    private static final Instant DEFAULT_CREATED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_CREATED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final String DEFAULT_UPDATED_BY = "AAAAAAAAAA";
    private static final String UPDATED_UPDATED_BY = "BBBBBBBBBB";

    private static final Instant DEFAULT_UPDATED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_UPDATED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final Boolean DEFAULT_IS_DELETED = false;
    private static final Boolean UPDATED_IS_DELETED = true;

    private static final String ENTITY_API_URL = "/api/tenant-initializes";
    private static final String ENTITY_API_URL_ID = ENTITY_API_URL + "/{id}";

    private static Random random = new Random();
    private static AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));

    @Autowired
    private ObjectMapper om;

    @Autowired
    private TenantInitializeRepository tenantInitializeRepository;

    @Autowired
    private TenantInitializeMapper tenantInitializeMapper;

    @Autowired
    private EntityManager em;

    @Autowired
    private MockMvc restTenantInitializeMockMvc;

    private TenantInitialize tenantInitialize;

    private TenantInitialize insertedTenantInitialize;

    /**
     * Create an entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static TenantInitialize createEntity() {
        return new TenantInitialize()
            .tenantId(DEFAULT_TENANT_ID)
            .type(DEFAULT_TYPE)
            .name(DEFAULT_NAME)
            .code(DEFAULT_CODE)
            .departmentCode(DEFAULT_DEPARTMENT_CODE)
            .description(DEFAULT_DESCRIPTION)
            .metadata(DEFAULT_METADATA)
            .version(DEFAULT_VERSION)
            .createdBy(DEFAULT_CREATED_BY)
            .createdAt(DEFAULT_CREATED_AT)
            .updatedBy(DEFAULT_UPDATED_BY)
            .updatedAt(DEFAULT_UPDATED_AT)
            .isDeleted(DEFAULT_IS_DELETED);
    }

    /**
     * Create an updated entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static TenantInitialize createUpdatedEntity() {
        return new TenantInitialize()
            .tenantId(UPDATED_TENANT_ID)
            .type(UPDATED_TYPE)
            .name(UPDATED_NAME)
            .code(UPDATED_CODE)
            .departmentCode(UPDATED_DEPARTMENT_CODE)
            .description(UPDATED_DESCRIPTION)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);
    }

    @BeforeEach
    void initTest() {
        tenantInitialize = createEntity();
    }

    @AfterEach
    void cleanup() {
        if (insertedTenantInitialize != null) {
            tenantInitializeRepository.delete(insertedTenantInitialize);
            insertedTenantInitialize = null;
        }
    }

    @Test
    @Transactional
    void createTenantInitialize() throws Exception {
        long databaseSizeBeforeCreate = getRepositoryCount();
        // Create the TenantInitialize
        TenantInitializeDTO tenantInitializeDTO = tenantInitializeMapper.toDto(tenantInitialize);
        var returnedTenantInitializeDTO = om.readValue(
            restTenantInitializeMockMvc
                .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(tenantInitializeDTO)))
                .andExpect(status().isCreated())
                .andReturn()
                .getResponse()
                .getContentAsString(),
            TenantInitializeDTO.class
        );

        // Validate the TenantInitialize in the database
        assertIncrementedRepositoryCount(databaseSizeBeforeCreate);
        var returnedTenantInitialize = tenantInitializeMapper.toEntity(returnedTenantInitializeDTO);
        assertTenantInitializeUpdatableFieldsEquals(returnedTenantInitialize, getPersistedTenantInitialize(returnedTenantInitialize));

        insertedTenantInitialize = returnedTenantInitialize;
    }

    @Test
    @Transactional
    void createTenantInitializeWithExistingId() throws Exception {
        // Create the TenantInitialize with an existing ID
        tenantInitialize.setId(1L);
        TenantInitializeDTO tenantInitializeDTO = tenantInitializeMapper.toDto(tenantInitialize);

        long databaseSizeBeforeCreate = getRepositoryCount();

        // An entity with an existing ID cannot be created, so this API call must fail
        restTenantInitializeMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(tenantInitializeDTO)))
            .andExpect(status().isBadRequest());

        // Validate the TenantInitialize in the database
        assertSameRepositoryCount(databaseSizeBeforeCreate);
    }

    @Test
    @Transactional
    void checkTenantIdIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        tenantInitialize.setTenantId(null);

        // Create the TenantInitialize, which fails.
        TenantInitializeDTO tenantInitializeDTO = tenantInitializeMapper.toDto(tenantInitialize);

        restTenantInitializeMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(tenantInitializeDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkTypeIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        tenantInitialize.setType(null);

        // Create the TenantInitialize, which fails.
        TenantInitializeDTO tenantInitializeDTO = tenantInitializeMapper.toDto(tenantInitialize);

        restTenantInitializeMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(tenantInitializeDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkNameIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        tenantInitialize.setName(null);

        // Create the TenantInitialize, which fails.
        TenantInitializeDTO tenantInitializeDTO = tenantInitializeMapper.toDto(tenantInitialize);

        restTenantInitializeMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(tenantInitializeDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkCodeIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        tenantInitialize.setCode(null);

        // Create the TenantInitialize, which fails.
        TenantInitializeDTO tenantInitializeDTO = tenantInitializeMapper.toDto(tenantInitialize);

        restTenantInitializeMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(tenantInitializeDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkDepartmentCodeIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        tenantInitialize.setDepartmentCode(null);

        // Create the TenantInitialize, which fails.
        TenantInitializeDTO tenantInitializeDTO = tenantInitializeMapper.toDto(tenantInitialize);

        restTenantInitializeMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(tenantInitializeDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkVersionIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        tenantInitialize.setVersion(null);

        // Create the TenantInitialize, which fails.
        TenantInitializeDTO tenantInitializeDTO = tenantInitializeMapper.toDto(tenantInitialize);

        restTenantInitializeMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(tenantInitializeDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkCreatedAtIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        tenantInitialize.setCreatedAt(null);

        // Create the TenantInitialize, which fails.
        TenantInitializeDTO tenantInitializeDTO = tenantInitializeMapper.toDto(tenantInitialize);

        restTenantInitializeMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(tenantInitializeDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkUpdatedAtIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        tenantInitialize.setUpdatedAt(null);

        // Create the TenantInitialize, which fails.
        TenantInitializeDTO tenantInitializeDTO = tenantInitializeMapper.toDto(tenantInitialize);

        restTenantInitializeMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(tenantInitializeDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkIsDeletedIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        tenantInitialize.setIsDeleted(null);

        // Create the TenantInitialize, which fails.
        TenantInitializeDTO tenantInitializeDTO = tenantInitializeMapper.toDto(tenantInitialize);

        restTenantInitializeMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(tenantInitializeDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void getAllTenantInitializes() throws Exception {
        // Initialize the database
        insertedTenantInitialize = tenantInitializeRepository.saveAndFlush(tenantInitialize);

        // Get all the tenantInitializeList
        restTenantInitializeMockMvc
            .perform(get(ENTITY_API_URL + "?sort=id,desc"))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.[*].id").value(hasItem(tenantInitialize.getId().intValue())))
            .andExpect(jsonPath("$.[*].tenantId").value(hasItem(DEFAULT_TENANT_ID.intValue())))
            .andExpect(jsonPath("$.[*].type").value(hasItem(DEFAULT_TYPE)))
            .andExpect(jsonPath("$.[*].name").value(hasItem(DEFAULT_NAME)))
            .andExpect(jsonPath("$.[*].code").value(hasItem(DEFAULT_CODE)))
            .andExpect(jsonPath("$.[*].departmentCode").value(hasItem(DEFAULT_DEPARTMENT_CODE)))
            .andExpect(jsonPath("$.[*].description").value(hasItem(DEFAULT_DESCRIPTION)))
            .andExpect(jsonPath("$.[*].metadata").value(hasItem(DEFAULT_METADATA)))
            .andExpect(jsonPath("$.[*].version").value(hasItem(DEFAULT_VERSION)))
            .andExpect(jsonPath("$.[*].createdBy").value(hasItem(DEFAULT_CREATED_BY)))
            .andExpect(jsonPath("$.[*].createdAt").value(hasItem(DEFAULT_CREATED_AT.toString())))
            .andExpect(jsonPath("$.[*].updatedBy").value(hasItem(DEFAULT_UPDATED_BY)))
            .andExpect(jsonPath("$.[*].updatedAt").value(hasItem(DEFAULT_UPDATED_AT.toString())))
            .andExpect(jsonPath("$.[*].isDeleted").value(hasItem(DEFAULT_IS_DELETED)));
    }

    @Test
    @Transactional
    void getTenantInitialize() throws Exception {
        // Initialize the database
        insertedTenantInitialize = tenantInitializeRepository.saveAndFlush(tenantInitialize);

        // Get the tenantInitialize
        restTenantInitializeMockMvc
            .perform(get(ENTITY_API_URL_ID, tenantInitialize.getId()))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.id").value(tenantInitialize.getId().intValue()))
            .andExpect(jsonPath("$.tenantId").value(DEFAULT_TENANT_ID.intValue()))
            .andExpect(jsonPath("$.type").value(DEFAULT_TYPE))
            .andExpect(jsonPath("$.name").value(DEFAULT_NAME))
            .andExpect(jsonPath("$.code").value(DEFAULT_CODE))
            .andExpect(jsonPath("$.departmentCode").value(DEFAULT_DEPARTMENT_CODE))
            .andExpect(jsonPath("$.description").value(DEFAULT_DESCRIPTION))
            .andExpect(jsonPath("$.metadata").value(DEFAULT_METADATA))
            .andExpect(jsonPath("$.version").value(DEFAULT_VERSION))
            .andExpect(jsonPath("$.createdBy").value(DEFAULT_CREATED_BY))
            .andExpect(jsonPath("$.createdAt").value(DEFAULT_CREATED_AT.toString()))
            .andExpect(jsonPath("$.updatedBy").value(DEFAULT_UPDATED_BY))
            .andExpect(jsonPath("$.updatedAt").value(DEFAULT_UPDATED_AT.toString()))
            .andExpect(jsonPath("$.isDeleted").value(DEFAULT_IS_DELETED));
    }

    @Test
    @Transactional
    void getNonExistingTenantInitialize() throws Exception {
        // Get the tenantInitialize
        restTenantInitializeMockMvc.perform(get(ENTITY_API_URL_ID, Long.MAX_VALUE)).andExpect(status().isNotFound());
    }

    @Test
    @Transactional
    void putExistingTenantInitialize() throws Exception {
        // Initialize the database
        insertedTenantInitialize = tenantInitializeRepository.saveAndFlush(tenantInitialize);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the tenantInitialize
        TenantInitialize updatedTenantInitialize = tenantInitializeRepository.findById(tenantInitialize.getId()).orElseThrow();
        // Disconnect from session so that the updates on updatedTenantInitialize are not directly saved in db
        em.detach(updatedTenantInitialize);
        updatedTenantInitialize
            .tenantId(UPDATED_TENANT_ID)
            .type(UPDATED_TYPE)
            .name(UPDATED_NAME)
            .code(UPDATED_CODE)
            .departmentCode(UPDATED_DEPARTMENT_CODE)
            .description(UPDATED_DESCRIPTION)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);
        TenantInitializeDTO tenantInitializeDTO = tenantInitializeMapper.toDto(updatedTenantInitialize);

        restTenantInitializeMockMvc
            .perform(
                put(ENTITY_API_URL_ID, tenantInitializeDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(tenantInitializeDTO))
            )
            .andExpect(status().isOk());

        // Validate the TenantInitialize in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertPersistedTenantInitializeToMatchAllProperties(updatedTenantInitialize);
    }

    @Test
    @Transactional
    void putNonExistingTenantInitialize() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        tenantInitialize.setId(longCount.incrementAndGet());

        // Create the TenantInitialize
        TenantInitializeDTO tenantInitializeDTO = tenantInitializeMapper.toDto(tenantInitialize);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restTenantInitializeMockMvc
            .perform(
                put(ENTITY_API_URL_ID, tenantInitializeDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(tenantInitializeDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the TenantInitialize in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithIdMismatchTenantInitialize() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        tenantInitialize.setId(longCount.incrementAndGet());

        // Create the TenantInitialize
        TenantInitializeDTO tenantInitializeDTO = tenantInitializeMapper.toDto(tenantInitialize);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restTenantInitializeMockMvc
            .perform(
                put(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(tenantInitializeDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the TenantInitialize in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithMissingIdPathParamTenantInitialize() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        tenantInitialize.setId(longCount.incrementAndGet());

        // Create the TenantInitialize
        TenantInitializeDTO tenantInitializeDTO = tenantInitializeMapper.toDto(tenantInitialize);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restTenantInitializeMockMvc
            .perform(put(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(tenantInitializeDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the TenantInitialize in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void partialUpdateTenantInitializeWithPatch() throws Exception {
        // Initialize the database
        insertedTenantInitialize = tenantInitializeRepository.saveAndFlush(tenantInitialize);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the tenantInitialize using partial update
        TenantInitialize partialUpdatedTenantInitialize = new TenantInitialize();
        partialUpdatedTenantInitialize.setId(tenantInitialize.getId());

        partialUpdatedTenantInitialize
            .type(UPDATED_TYPE)
            .name(UPDATED_NAME)
            .code(UPDATED_CODE)
            .departmentCode(UPDATED_DEPARTMENT_CODE)
            .description(UPDATED_DESCRIPTION)
            .metadata(UPDATED_METADATA)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT);

        restTenantInitializeMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedTenantInitialize.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedTenantInitialize))
            )
            .andExpect(status().isOk());

        // Validate the TenantInitialize in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertTenantInitializeUpdatableFieldsEquals(
            createUpdateProxyForBean(partialUpdatedTenantInitialize, tenantInitialize),
            getPersistedTenantInitialize(tenantInitialize)
        );
    }

    @Test
    @Transactional
    void fullUpdateTenantInitializeWithPatch() throws Exception {
        // Initialize the database
        insertedTenantInitialize = tenantInitializeRepository.saveAndFlush(tenantInitialize);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the tenantInitialize using partial update
        TenantInitialize partialUpdatedTenantInitialize = new TenantInitialize();
        partialUpdatedTenantInitialize.setId(tenantInitialize.getId());

        partialUpdatedTenantInitialize
            .tenantId(UPDATED_TENANT_ID)
            .type(UPDATED_TYPE)
            .name(UPDATED_NAME)
            .code(UPDATED_CODE)
            .departmentCode(UPDATED_DEPARTMENT_CODE)
            .description(UPDATED_DESCRIPTION)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);

        restTenantInitializeMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedTenantInitialize.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedTenantInitialize))
            )
            .andExpect(status().isOk());

        // Validate the TenantInitialize in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertTenantInitializeUpdatableFieldsEquals(
            partialUpdatedTenantInitialize,
            getPersistedTenantInitialize(partialUpdatedTenantInitialize)
        );
    }

    @Test
    @Transactional
    void patchNonExistingTenantInitialize() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        tenantInitialize.setId(longCount.incrementAndGet());

        // Create the TenantInitialize
        TenantInitializeDTO tenantInitializeDTO = tenantInitializeMapper.toDto(tenantInitialize);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restTenantInitializeMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, tenantInitializeDTO.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(tenantInitializeDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the TenantInitialize in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithIdMismatchTenantInitialize() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        tenantInitialize.setId(longCount.incrementAndGet());

        // Create the TenantInitialize
        TenantInitializeDTO tenantInitializeDTO = tenantInitializeMapper.toDto(tenantInitialize);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restTenantInitializeMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(tenantInitializeDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the TenantInitialize in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithMissingIdPathParamTenantInitialize() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        tenantInitialize.setId(longCount.incrementAndGet());

        // Create the TenantInitialize
        TenantInitializeDTO tenantInitializeDTO = tenantInitializeMapper.toDto(tenantInitialize);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restTenantInitializeMockMvc
            .perform(patch(ENTITY_API_URL).contentType("application/merge-patch+json").content(om.writeValueAsBytes(tenantInitializeDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the TenantInitialize in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void deleteTenantInitialize() throws Exception {
        // Initialize the database
        insertedTenantInitialize = tenantInitializeRepository.saveAndFlush(tenantInitialize);

        long databaseSizeBeforeDelete = getRepositoryCount();

        // Delete the tenantInitialize
        restTenantInitializeMockMvc
            .perform(delete(ENTITY_API_URL_ID, tenantInitialize.getId()).accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isNoContent());

        // Validate the database contains one less item
        assertDecrementedRepositoryCount(databaseSizeBeforeDelete);
    }

    protected long getRepositoryCount() {
        return tenantInitializeRepository.count();
    }

    protected void assertIncrementedRepositoryCount(long countBefore) {
        assertThat(countBefore + 1).isEqualTo(getRepositoryCount());
    }

    protected void assertDecrementedRepositoryCount(long countBefore) {
        assertThat(countBefore - 1).isEqualTo(getRepositoryCount());
    }

    protected void assertSameRepositoryCount(long countBefore) {
        assertThat(countBefore).isEqualTo(getRepositoryCount());
    }

    protected TenantInitialize getPersistedTenantInitialize(TenantInitialize tenantInitialize) {
        return tenantInitializeRepository.findById(tenantInitialize.getId()).orElseThrow();
    }

    protected void assertPersistedTenantInitializeToMatchAllProperties(TenantInitialize expectedTenantInitialize) {
        assertTenantInitializeAllPropertiesEquals(expectedTenantInitialize, getPersistedTenantInitialize(expectedTenantInitialize));
    }

    protected void assertPersistedTenantInitializeToMatchUpdatableProperties(TenantInitialize expectedTenantInitialize) {
        assertTenantInitializeAllUpdatablePropertiesEquals(
            expectedTenantInitialize,
            getPersistedTenantInitialize(expectedTenantInitialize)
        );
    }
}

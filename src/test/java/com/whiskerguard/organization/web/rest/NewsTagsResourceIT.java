package com.whiskerguard.organization.web.rest;

import static com.whiskerguard.organization.domain.NewsTagsAsserts.*;
import static com.whiskerguard.organization.web.rest.TestUtil.createUpdateProxyForBean;
import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.hasItem;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.whiskerguard.organization.IntegrationTest;
import com.whiskerguard.organization.domain.News;
import com.whiskerguard.organization.domain.NewsTags;
import com.whiskerguard.organization.domain.Tag;
import com.whiskerguard.organization.repository.NewsTagsRepository;
import com.whiskerguard.organization.service.dto.NewsTagsDTO;
import com.whiskerguard.organization.service.mapper.NewsTagsMapper;
import jakarta.persistence.EntityManager;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Random;
import java.util.concurrent.atomic.AtomicLong;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

/**
 * Integration tests for the {@link NewsTagsResource} REST controller.
 */
@IntegrationTest
@AutoConfigureMockMvc
@WithMockUser
class NewsTagsResourceIT {

    private static final Long DEFAULT_TENANT_ID = 1L;
    private static final Long UPDATED_TENANT_ID = 2L;

    private static final String DEFAULT_METADATA = "AAAAAAAAAA";
    private static final String UPDATED_METADATA = "BBBBBBBBBB";

    private static final Integer DEFAULT_VERSION = 1;
    private static final Integer UPDATED_VERSION = 2;

    private static final String DEFAULT_CREATED_BY = "AAAAAAAAAA";
    private static final String UPDATED_CREATED_BY = "BBBBBBBBBB";

    private static final Instant DEFAULT_CREATED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_CREATED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final String DEFAULT_UPDATED_BY = "AAAAAAAAAA";
    private static final String UPDATED_UPDATED_BY = "BBBBBBBBBB";

    private static final Instant DEFAULT_UPDATED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_UPDATED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final Boolean DEFAULT_IS_DELETED = false;
    private static final Boolean UPDATED_IS_DELETED = true;

    private static final String ENTITY_API_URL = "/api/news-tags";
    private static final String ENTITY_API_URL_ID = ENTITY_API_URL + "/{id}";

    private static Random random = new Random();
    private static AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));

    @Autowired
    private ObjectMapper om;

    @Autowired
    private NewsTagsRepository newsTagsRepository;

    @Autowired
    private NewsTagsMapper newsTagsMapper;

    @Autowired
    private EntityManager em;

    @Autowired
    private MockMvc restNewsTagsMockMvc;

    private NewsTags newsTags;

    private NewsTags insertedNewsTags;

    /**
     * Create an entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static NewsTags createEntity(EntityManager em) {
        NewsTags newsTags = new NewsTags()
            .tenantId(DEFAULT_TENANT_ID)
            .metadata(DEFAULT_METADATA)
            .version(DEFAULT_VERSION)
            .createdBy(DEFAULT_CREATED_BY)
            .createdAt(DEFAULT_CREATED_AT)
            .updatedBy(DEFAULT_UPDATED_BY)
            .updatedAt(DEFAULT_UPDATED_AT)
            .isDeleted(DEFAULT_IS_DELETED);
        // Add required entity
        Tag tag;
        if (TestUtil.findAll(em, Tag.class).isEmpty()) {
            tag = TagResourceIT.createEntity();
            em.persist(tag);
            em.flush();
        } else {
            tag = TestUtil.findAll(em, Tag.class).get(0);
        }
        newsTags.setTags(tag);
        // Add required entity
        News news;
        if (TestUtil.findAll(em, News.class).isEmpty()) {
            news = NewsResourceIT.createEntity();
            em.persist(news);
            em.flush();
        } else {
            news = TestUtil.findAll(em, News.class).get(0);
        }
        newsTags.setNews(news);
        return newsTags;
    }

    /**
     * Create an updated entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static NewsTags createUpdatedEntity(EntityManager em) {
        NewsTags updatedNewsTags = new NewsTags()
            .tenantId(UPDATED_TENANT_ID)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);
        // Add required entity
        Tag tag;
        if (TestUtil.findAll(em, Tag.class).isEmpty()) {
            tag = TagResourceIT.createUpdatedEntity();
            em.persist(tag);
            em.flush();
        } else {
            tag = TestUtil.findAll(em, Tag.class).get(0);
        }
        updatedNewsTags.setTags(tag);
        // Add required entity
        News news;
        if (TestUtil.findAll(em, News.class).isEmpty()) {
            news = NewsResourceIT.createUpdatedEntity();
            em.persist(news);
            em.flush();
        } else {
            news = TestUtil.findAll(em, News.class).get(0);
        }
        updatedNewsTags.setNews(news);
        return updatedNewsTags;
    }

    @BeforeEach
    void initTest() {
        newsTags = createEntity(em);
    }

    @AfterEach
    void cleanup() {
        if (insertedNewsTags != null) {
            newsTagsRepository.delete(insertedNewsTags);
            insertedNewsTags = null;
        }
    }

    @Test
    @Transactional
    void createNewsTags() throws Exception {
        long databaseSizeBeforeCreate = getRepositoryCount();
        // Create the NewsTags
        NewsTagsDTO newsTagsDTO = newsTagsMapper.toDto(newsTags);
        var returnedNewsTagsDTO = om.readValue(
            restNewsTagsMockMvc
                .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(newsTagsDTO)))
                .andExpect(status().isCreated())
                .andReturn()
                .getResponse()
                .getContentAsString(),
            NewsTagsDTO.class
        );

        // Validate the NewsTags in the database
        assertIncrementedRepositoryCount(databaseSizeBeforeCreate);
        var returnedNewsTags = newsTagsMapper.toEntity(returnedNewsTagsDTO);
        assertNewsTagsUpdatableFieldsEquals(returnedNewsTags, getPersistedNewsTags(returnedNewsTags));

        insertedNewsTags = returnedNewsTags;
    }

    @Test
    @Transactional
    void createNewsTagsWithExistingId() throws Exception {
        // Create the NewsTags with an existing ID
        newsTags.setId(1L);
        NewsTagsDTO newsTagsDTO = newsTagsMapper.toDto(newsTags);

        long databaseSizeBeforeCreate = getRepositoryCount();

        // An entity with an existing ID cannot be created, so this API call must fail
        restNewsTagsMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(newsTagsDTO)))
            .andExpect(status().isBadRequest());

        // Validate the NewsTags in the database
        assertSameRepositoryCount(databaseSizeBeforeCreate);
    }

    @Test
    @Transactional
    void checkTenantIdIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        newsTags.setTenantId(null);

        // Create the NewsTags, which fails.
        NewsTagsDTO newsTagsDTO = newsTagsMapper.toDto(newsTags);

        restNewsTagsMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(newsTagsDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkVersionIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        newsTags.setVersion(null);

        // Create the NewsTags, which fails.
        NewsTagsDTO newsTagsDTO = newsTagsMapper.toDto(newsTags);

        restNewsTagsMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(newsTagsDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkCreatedAtIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        newsTags.setCreatedAt(null);

        // Create the NewsTags, which fails.
        NewsTagsDTO newsTagsDTO = newsTagsMapper.toDto(newsTags);

        restNewsTagsMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(newsTagsDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkUpdatedAtIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        newsTags.setUpdatedAt(null);

        // Create the NewsTags, which fails.
        NewsTagsDTO newsTagsDTO = newsTagsMapper.toDto(newsTags);

        restNewsTagsMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(newsTagsDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkIsDeletedIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        newsTags.setIsDeleted(null);

        // Create the NewsTags, which fails.
        NewsTagsDTO newsTagsDTO = newsTagsMapper.toDto(newsTags);

        restNewsTagsMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(newsTagsDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void getAllNewsTags() throws Exception {
        // Initialize the database
        insertedNewsTags = newsTagsRepository.saveAndFlush(newsTags);

        // Get all the newsTagsList
        restNewsTagsMockMvc
            .perform(get(ENTITY_API_URL + "?sort=id,desc"))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.[*].id").value(hasItem(newsTags.getId().intValue())))
            .andExpect(jsonPath("$.[*].tenantId").value(hasItem(DEFAULT_TENANT_ID.intValue())))
            .andExpect(jsonPath("$.[*].metadata").value(hasItem(DEFAULT_METADATA)))
            .andExpect(jsonPath("$.[*].version").value(hasItem(DEFAULT_VERSION)))
            .andExpect(jsonPath("$.[*].createdBy").value(hasItem(DEFAULT_CREATED_BY)))
            .andExpect(jsonPath("$.[*].createdAt").value(hasItem(DEFAULT_CREATED_AT.toString())))
            .andExpect(jsonPath("$.[*].updatedBy").value(hasItem(DEFAULT_UPDATED_BY)))
            .andExpect(jsonPath("$.[*].updatedAt").value(hasItem(DEFAULT_UPDATED_AT.toString())))
            .andExpect(jsonPath("$.[*].isDeleted").value(hasItem(DEFAULT_IS_DELETED)));
    }

    @Test
    @Transactional
    void getNewsTags() throws Exception {
        // Initialize the database
        insertedNewsTags = newsTagsRepository.saveAndFlush(newsTags);

        // Get the newsTags
        restNewsTagsMockMvc
            .perform(get(ENTITY_API_URL_ID, newsTags.getId()))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.id").value(newsTags.getId().intValue()))
            .andExpect(jsonPath("$.tenantId").value(DEFAULT_TENANT_ID.intValue()))
            .andExpect(jsonPath("$.metadata").value(DEFAULT_METADATA))
            .andExpect(jsonPath("$.version").value(DEFAULT_VERSION))
            .andExpect(jsonPath("$.createdBy").value(DEFAULT_CREATED_BY))
            .andExpect(jsonPath("$.createdAt").value(DEFAULT_CREATED_AT.toString()))
            .andExpect(jsonPath("$.updatedBy").value(DEFAULT_UPDATED_BY))
            .andExpect(jsonPath("$.updatedAt").value(DEFAULT_UPDATED_AT.toString()))
            .andExpect(jsonPath("$.isDeleted").value(DEFAULT_IS_DELETED));
    }

    @Test
    @Transactional
    void getNonExistingNewsTags() throws Exception {
        // Get the newsTags
        restNewsTagsMockMvc.perform(get(ENTITY_API_URL_ID, Long.MAX_VALUE)).andExpect(status().isNotFound());
    }

    @Test
    @Transactional
    void putExistingNewsTags() throws Exception {
        // Initialize the database
        insertedNewsTags = newsTagsRepository.saveAndFlush(newsTags);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the newsTags
        NewsTags updatedNewsTags = newsTagsRepository.findById(newsTags.getId()).orElseThrow();
        // Disconnect from session so that the updates on updatedNewsTags are not directly saved in db
        em.detach(updatedNewsTags);
        updatedNewsTags
            .tenantId(UPDATED_TENANT_ID)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);
        NewsTagsDTO newsTagsDTO = newsTagsMapper.toDto(updatedNewsTags);

        restNewsTagsMockMvc
            .perform(
                put(ENTITY_API_URL_ID, newsTagsDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(newsTagsDTO))
            )
            .andExpect(status().isOk());

        // Validate the NewsTags in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertPersistedNewsTagsToMatchAllProperties(updatedNewsTags);
    }

    @Test
    @Transactional
    void putNonExistingNewsTags() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        newsTags.setId(longCount.incrementAndGet());

        // Create the NewsTags
        NewsTagsDTO newsTagsDTO = newsTagsMapper.toDto(newsTags);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restNewsTagsMockMvc
            .perform(
                put(ENTITY_API_URL_ID, newsTagsDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(newsTagsDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the NewsTags in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithIdMismatchNewsTags() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        newsTags.setId(longCount.incrementAndGet());

        // Create the NewsTags
        NewsTagsDTO newsTagsDTO = newsTagsMapper.toDto(newsTags);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restNewsTagsMockMvc
            .perform(
                put(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(newsTagsDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the NewsTags in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithMissingIdPathParamNewsTags() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        newsTags.setId(longCount.incrementAndGet());

        // Create the NewsTags
        NewsTagsDTO newsTagsDTO = newsTagsMapper.toDto(newsTags);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restNewsTagsMockMvc
            .perform(put(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(newsTagsDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the NewsTags in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void partialUpdateNewsTagsWithPatch() throws Exception {
        // Initialize the database
        insertedNewsTags = newsTagsRepository.saveAndFlush(newsTags);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the newsTags using partial update
        NewsTags partialUpdatedNewsTags = new NewsTags();
        partialUpdatedNewsTags.setId(newsTags.getId());

        partialUpdatedNewsTags
            .version(UPDATED_VERSION)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);

        restNewsTagsMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedNewsTags.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedNewsTags))
            )
            .andExpect(status().isOk());

        // Validate the NewsTags in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertNewsTagsUpdatableFieldsEquals(createUpdateProxyForBean(partialUpdatedNewsTags, newsTags), getPersistedNewsTags(newsTags));
    }

    @Test
    @Transactional
    void fullUpdateNewsTagsWithPatch() throws Exception {
        // Initialize the database
        insertedNewsTags = newsTagsRepository.saveAndFlush(newsTags);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the newsTags using partial update
        NewsTags partialUpdatedNewsTags = new NewsTags();
        partialUpdatedNewsTags.setId(newsTags.getId());

        partialUpdatedNewsTags
            .tenantId(UPDATED_TENANT_ID)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);

        restNewsTagsMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedNewsTags.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedNewsTags))
            )
            .andExpect(status().isOk());

        // Validate the NewsTags in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertNewsTagsUpdatableFieldsEquals(partialUpdatedNewsTags, getPersistedNewsTags(partialUpdatedNewsTags));
    }

    @Test
    @Transactional
    void patchNonExistingNewsTags() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        newsTags.setId(longCount.incrementAndGet());

        // Create the NewsTags
        NewsTagsDTO newsTagsDTO = newsTagsMapper.toDto(newsTags);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restNewsTagsMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, newsTagsDTO.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(newsTagsDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the NewsTags in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithIdMismatchNewsTags() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        newsTags.setId(longCount.incrementAndGet());

        // Create the NewsTags
        NewsTagsDTO newsTagsDTO = newsTagsMapper.toDto(newsTags);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restNewsTagsMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(newsTagsDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the NewsTags in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithMissingIdPathParamNewsTags() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        newsTags.setId(longCount.incrementAndGet());

        // Create the NewsTags
        NewsTagsDTO newsTagsDTO = newsTagsMapper.toDto(newsTags);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restNewsTagsMockMvc
            .perform(patch(ENTITY_API_URL).contentType("application/merge-patch+json").content(om.writeValueAsBytes(newsTagsDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the NewsTags in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void deleteNewsTags() throws Exception {
        // Initialize the database
        insertedNewsTags = newsTagsRepository.saveAndFlush(newsTags);

        long databaseSizeBeforeDelete = getRepositoryCount();

        // Delete the newsTags
        restNewsTagsMockMvc
            .perform(delete(ENTITY_API_URL_ID, newsTags.getId()).accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isNoContent());

        // Validate the database contains one less item
        assertDecrementedRepositoryCount(databaseSizeBeforeDelete);
    }

    protected long getRepositoryCount() {
        return newsTagsRepository.count();
    }

    protected void assertIncrementedRepositoryCount(long countBefore) {
        assertThat(countBefore + 1).isEqualTo(getRepositoryCount());
    }

    protected void assertDecrementedRepositoryCount(long countBefore) {
        assertThat(countBefore - 1).isEqualTo(getRepositoryCount());
    }

    protected void assertSameRepositoryCount(long countBefore) {
        assertThat(countBefore).isEqualTo(getRepositoryCount());
    }

    protected NewsTags getPersistedNewsTags(NewsTags newsTags) {
        return newsTagsRepository.findById(newsTags.getId()).orElseThrow();
    }

    protected void assertPersistedNewsTagsToMatchAllProperties(NewsTags expectedNewsTags) {
        assertNewsTagsAllPropertiesEquals(expectedNewsTags, getPersistedNewsTags(expectedNewsTags));
    }

    protected void assertPersistedNewsTagsToMatchUpdatableProperties(NewsTags expectedNewsTags) {
        assertNewsTagsAllUpdatablePropertiesEquals(expectedNewsTags, getPersistedNewsTags(expectedNewsTags));
    }
}

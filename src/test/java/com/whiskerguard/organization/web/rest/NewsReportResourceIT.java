package com.whiskerguard.organization.web.rest;

import static com.whiskerguard.organization.domain.NewsReportAsserts.*;
import static com.whiskerguard.organization.web.rest.TestUtil.createUpdateProxyForBean;
import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.hasItem;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.whiskerguard.organization.IntegrationTest;
import com.whiskerguard.organization.domain.NewsReport;
import com.whiskerguard.organization.domain.enumeration.ReportStatus;
import com.whiskerguard.organization.repository.NewsReportRepository;
import com.whiskerguard.organization.service.NewsReportService;
import com.whiskerguard.organization.service.dto.NewsReportDTO;
import com.whiskerguard.organization.service.mapper.NewsReportMapper;
import jakarta.persistence.EntityManager;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Random;
import java.util.concurrent.atomic.AtomicLong;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

/**
 * Integration tests for the {@link NewsReportResource} REST controller.
 */
@IntegrationTest
@ExtendWith(MockitoExtension.class)
@AutoConfigureMockMvc
@WithMockUser
class NewsReportResourceIT {

    private static final String DEFAULT_REASON = "AAAAAAAAAA";
    private static final String UPDATED_REASON = "BBBBBBBBBB";

    private static final String DEFAULT_DETAILS = "AAAAAAAAAA";
    private static final String UPDATED_DETAILS = "BBBBBBBBBB";

    private static final ReportStatus DEFAULT_STATUS = ReportStatus.PENDING;
    private static final ReportStatus UPDATED_STATUS = ReportStatus.PROCESSING;

    private static final String DEFAULT_RESULT = "AAAAAAAAAA";
    private static final String UPDATED_RESULT = "BBBBBBBBBB";

    private static final String DEFAULT_METADATA = "AAAAAAAAAA";
    private static final String UPDATED_METADATA = "BBBBBBBBBB";

    private static final Integer DEFAULT_VERSION = 1;
    private static final Integer UPDATED_VERSION = 2;

    private static final String DEFAULT_CREATED_BY = "AAAAAAAAAA";
    private static final String UPDATED_CREATED_BY = "BBBBBBBBBB";

    private static final Instant DEFAULT_CREATED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_CREATED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final String DEFAULT_UPDATED_BY = "AAAAAAAAAA";
    private static final String UPDATED_UPDATED_BY = "BBBBBBBBBB";

    private static final Instant DEFAULT_UPDATED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_UPDATED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final Boolean DEFAULT_IS_DELETED = false;
    private static final Boolean UPDATED_IS_DELETED = true;

    private static final String ENTITY_API_URL = "/api/news-reports";
    private static final String ENTITY_API_URL_ID = ENTITY_API_URL + "/{id}";

    private static Random random = new Random();
    private static AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));

    @Autowired
    private ObjectMapper om;

    @Autowired
    private NewsReportRepository newsReportRepository;

    @Mock
    private NewsReportRepository newsReportRepositoryMock;

    @Autowired
    private NewsReportMapper newsReportMapper;

    @Mock
    private NewsReportService newsReportServiceMock;

    @Autowired
    private EntityManager em;

    @Autowired
    private MockMvc restNewsReportMockMvc;

    private NewsReport newsReport;

    private NewsReport insertedNewsReport;

    /**
     * Create an entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static NewsReport createEntity() {
        return new NewsReport()
            .reason(DEFAULT_REASON)
            .details(DEFAULT_DETAILS)
            .status(DEFAULT_STATUS)
            .result(DEFAULT_RESULT)
            .metadata(DEFAULT_METADATA)
            .version(DEFAULT_VERSION)
            .createdBy(DEFAULT_CREATED_BY)
            .createdAt(DEFAULT_CREATED_AT)
            .updatedBy(DEFAULT_UPDATED_BY)
            .updatedAt(DEFAULT_UPDATED_AT)
            .isDeleted(DEFAULT_IS_DELETED);
    }

    /**
     * Create an updated entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static NewsReport createUpdatedEntity() {
        return new NewsReport()
            .reason(UPDATED_REASON)
            .details(UPDATED_DETAILS)
            .status(UPDATED_STATUS)
            .result(UPDATED_RESULT)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);
    }

    @BeforeEach
    void initTest() {
        newsReport = createEntity();
    }

    @AfterEach
    void cleanup() {
        if (insertedNewsReport != null) {
            newsReportRepository.delete(insertedNewsReport);
            insertedNewsReport = null;
        }
    }

    @Test
    @Transactional
    void createNewsReport() throws Exception {
        long databaseSizeBeforeCreate = getRepositoryCount();
        // Create the NewsReport
        NewsReportDTO newsReportDTO = newsReportMapper.toDto(newsReport);
        var returnedNewsReportDTO = om.readValue(
            restNewsReportMockMvc
                .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(newsReportDTO)))
                .andExpect(status().isCreated())
                .andReturn()
                .getResponse()
                .getContentAsString(),
            NewsReportDTO.class
        );

        // Validate the NewsReport in the database
        assertIncrementedRepositoryCount(databaseSizeBeforeCreate);
        var returnedNewsReport = newsReportMapper.toEntity(returnedNewsReportDTO);
        assertNewsReportUpdatableFieldsEquals(returnedNewsReport, getPersistedNewsReport(returnedNewsReport));

        insertedNewsReport = returnedNewsReport;
    }

    @Test
    @Transactional
    void createNewsReportWithExistingId() throws Exception {
        // Create the NewsReport with an existing ID
        newsReport.setId(1L);
        NewsReportDTO newsReportDTO = newsReportMapper.toDto(newsReport);

        long databaseSizeBeforeCreate = getRepositoryCount();

        // An entity with an existing ID cannot be created, so this API call must fail
        restNewsReportMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(newsReportDTO)))
            .andExpect(status().isBadRequest());

        // Validate the NewsReport in the database
        assertSameRepositoryCount(databaseSizeBeforeCreate);
    }

    @Test
    @Transactional
    void checkReasonIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        newsReport.setReason(null);

        // Create the NewsReport, which fails.
        NewsReportDTO newsReportDTO = newsReportMapper.toDto(newsReport);

        restNewsReportMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(newsReportDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkStatusIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        newsReport.setStatus(null);

        // Create the NewsReport, which fails.
        NewsReportDTO newsReportDTO = newsReportMapper.toDto(newsReport);

        restNewsReportMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(newsReportDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkVersionIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        newsReport.setVersion(null);

        // Create the NewsReport, which fails.
        NewsReportDTO newsReportDTO = newsReportMapper.toDto(newsReport);

        restNewsReportMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(newsReportDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkCreatedAtIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        newsReport.setCreatedAt(null);

        // Create the NewsReport, which fails.
        NewsReportDTO newsReportDTO = newsReportMapper.toDto(newsReport);

        restNewsReportMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(newsReportDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkUpdatedAtIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        newsReport.setUpdatedAt(null);

        // Create the NewsReport, which fails.
        NewsReportDTO newsReportDTO = newsReportMapper.toDto(newsReport);

        restNewsReportMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(newsReportDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkIsDeletedIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        newsReport.setIsDeleted(null);

        // Create the NewsReport, which fails.
        NewsReportDTO newsReportDTO = newsReportMapper.toDto(newsReport);

        restNewsReportMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(newsReportDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void getAllNewsReports() throws Exception {
        // Initialize the database
        insertedNewsReport = newsReportRepository.saveAndFlush(newsReport);

        // Get all the newsReportList
        restNewsReportMockMvc
            .perform(get(ENTITY_API_URL + "?sort=id,desc"))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.[*].id").value(hasItem(newsReport.getId().intValue())))
            .andExpect(jsonPath("$.[*].reason").value(hasItem(DEFAULT_REASON)))
            .andExpect(jsonPath("$.[*].details").value(hasItem(DEFAULT_DETAILS)))
            .andExpect(jsonPath("$.[*].status").value(hasItem(DEFAULT_STATUS.toString())))
            .andExpect(jsonPath("$.[*].result").value(hasItem(DEFAULT_RESULT)))
            .andExpect(jsonPath("$.[*].metadata").value(hasItem(DEFAULT_METADATA)))
            .andExpect(jsonPath("$.[*].version").value(hasItem(DEFAULT_VERSION)))
            .andExpect(jsonPath("$.[*].createdBy").value(hasItem(DEFAULT_CREATED_BY)))
            .andExpect(jsonPath("$.[*].createdAt").value(hasItem(DEFAULT_CREATED_AT.toString())))
            .andExpect(jsonPath("$.[*].updatedBy").value(hasItem(DEFAULT_UPDATED_BY)))
            .andExpect(jsonPath("$.[*].updatedAt").value(hasItem(DEFAULT_UPDATED_AT.toString())))
            .andExpect(jsonPath("$.[*].isDeleted").value(hasItem(DEFAULT_IS_DELETED)));
    }

    @SuppressWarnings({ "unchecked" })
    void getAllNewsReportsWithEagerRelationshipsIsEnabled() throws Exception {
        when(newsReportServiceMock.findAllWithEagerRelationships(any())).thenReturn(new PageImpl(new ArrayList<>()));

        restNewsReportMockMvc.perform(get(ENTITY_API_URL + "?eagerload=true")).andExpect(status().isOk());

        verify(newsReportServiceMock, times(1)).findAllWithEagerRelationships(any());
    }

    @SuppressWarnings({ "unchecked" })
    void getAllNewsReportsWithEagerRelationshipsIsNotEnabled() throws Exception {
        when(newsReportServiceMock.findAllWithEagerRelationships(any())).thenReturn(new PageImpl(new ArrayList<>()));

        restNewsReportMockMvc.perform(get(ENTITY_API_URL + "?eagerload=false")).andExpect(status().isOk());
        verify(newsReportRepositoryMock, times(1)).findAll(any(Pageable.class));
    }

    @Test
    @Transactional
    void getNewsReport() throws Exception {
        // Initialize the database
        insertedNewsReport = newsReportRepository.saveAndFlush(newsReport);

        // Get the newsReport
        restNewsReportMockMvc
            .perform(get(ENTITY_API_URL_ID, newsReport.getId()))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.id").value(newsReport.getId().intValue()))
            .andExpect(jsonPath("$.reason").value(DEFAULT_REASON))
            .andExpect(jsonPath("$.details").value(DEFAULT_DETAILS))
            .andExpect(jsonPath("$.status").value(DEFAULT_STATUS.toString()))
            .andExpect(jsonPath("$.result").value(DEFAULT_RESULT))
            .andExpect(jsonPath("$.metadata").value(DEFAULT_METADATA))
            .andExpect(jsonPath("$.version").value(DEFAULT_VERSION))
            .andExpect(jsonPath("$.createdBy").value(DEFAULT_CREATED_BY))
            .andExpect(jsonPath("$.createdAt").value(DEFAULT_CREATED_AT.toString()))
            .andExpect(jsonPath("$.updatedBy").value(DEFAULT_UPDATED_BY))
            .andExpect(jsonPath("$.updatedAt").value(DEFAULT_UPDATED_AT.toString()))
            .andExpect(jsonPath("$.isDeleted").value(DEFAULT_IS_DELETED));
    }

    @Test
    @Transactional
    void getNonExistingNewsReport() throws Exception {
        // Get the newsReport
        restNewsReportMockMvc.perform(get(ENTITY_API_URL_ID, Long.MAX_VALUE)).andExpect(status().isNotFound());
    }

    @Test
    @Transactional
    void putExistingNewsReport() throws Exception {
        // Initialize the database
        insertedNewsReport = newsReportRepository.saveAndFlush(newsReport);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the newsReport
        NewsReport updatedNewsReport = newsReportRepository.findById(newsReport.getId()).orElseThrow();
        // Disconnect from session so that the updates on updatedNewsReport are not directly saved in db
        em.detach(updatedNewsReport);
        updatedNewsReport
            .reason(UPDATED_REASON)
            .details(UPDATED_DETAILS)
            .status(UPDATED_STATUS)
            .result(UPDATED_RESULT)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);
        NewsReportDTO newsReportDTO = newsReportMapper.toDto(updatedNewsReport);

        restNewsReportMockMvc
            .perform(
                put(ENTITY_API_URL_ID, newsReportDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(newsReportDTO))
            )
            .andExpect(status().isOk());

        // Validate the NewsReport in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertPersistedNewsReportToMatchAllProperties(updatedNewsReport);
    }

    @Test
    @Transactional
    void putNonExistingNewsReport() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        newsReport.setId(longCount.incrementAndGet());

        // Create the NewsReport
        NewsReportDTO newsReportDTO = newsReportMapper.toDto(newsReport);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restNewsReportMockMvc
            .perform(
                put(ENTITY_API_URL_ID, newsReportDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(newsReportDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the NewsReport in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithIdMismatchNewsReport() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        newsReport.setId(longCount.incrementAndGet());

        // Create the NewsReport
        NewsReportDTO newsReportDTO = newsReportMapper.toDto(newsReport);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restNewsReportMockMvc
            .perform(
                put(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(newsReportDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the NewsReport in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithMissingIdPathParamNewsReport() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        newsReport.setId(longCount.incrementAndGet());

        // Create the NewsReport
        NewsReportDTO newsReportDTO = newsReportMapper.toDto(newsReport);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restNewsReportMockMvc
            .perform(put(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(newsReportDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the NewsReport in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void partialUpdateNewsReportWithPatch() throws Exception {
        // Initialize the database
        insertedNewsReport = newsReportRepository.saveAndFlush(newsReport);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the newsReport using partial update
        NewsReport partialUpdatedNewsReport = new NewsReport();
        partialUpdatedNewsReport.setId(newsReport.getId());

        partialUpdatedNewsReport
            .reason(UPDATED_REASON)
            .details(UPDATED_DETAILS)
            .metadata(UPDATED_METADATA)
            .createdAt(UPDATED_CREATED_AT)
            .updatedAt(UPDATED_UPDATED_AT);

        restNewsReportMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedNewsReport.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedNewsReport))
            )
            .andExpect(status().isOk());

        // Validate the NewsReport in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertNewsReportUpdatableFieldsEquals(
            createUpdateProxyForBean(partialUpdatedNewsReport, newsReport),
            getPersistedNewsReport(newsReport)
        );
    }

    @Test
    @Transactional
    void fullUpdateNewsReportWithPatch() throws Exception {
        // Initialize the database
        insertedNewsReport = newsReportRepository.saveAndFlush(newsReport);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the newsReport using partial update
        NewsReport partialUpdatedNewsReport = new NewsReport();
        partialUpdatedNewsReport.setId(newsReport.getId());

        partialUpdatedNewsReport
            .reason(UPDATED_REASON)
            .details(UPDATED_DETAILS)
            .status(UPDATED_STATUS)
            .result(UPDATED_RESULT)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);

        restNewsReportMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedNewsReport.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedNewsReport))
            )
            .andExpect(status().isOk());

        // Validate the NewsReport in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertNewsReportUpdatableFieldsEquals(partialUpdatedNewsReport, getPersistedNewsReport(partialUpdatedNewsReport));
    }

    @Test
    @Transactional
    void patchNonExistingNewsReport() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        newsReport.setId(longCount.incrementAndGet());

        // Create the NewsReport
        NewsReportDTO newsReportDTO = newsReportMapper.toDto(newsReport);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restNewsReportMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, newsReportDTO.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(newsReportDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the NewsReport in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithIdMismatchNewsReport() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        newsReport.setId(longCount.incrementAndGet());

        // Create the NewsReport
        NewsReportDTO newsReportDTO = newsReportMapper.toDto(newsReport);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restNewsReportMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(newsReportDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the NewsReport in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithMissingIdPathParamNewsReport() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        newsReport.setId(longCount.incrementAndGet());

        // Create the NewsReport
        NewsReportDTO newsReportDTO = newsReportMapper.toDto(newsReport);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restNewsReportMockMvc
            .perform(patch(ENTITY_API_URL).contentType("application/merge-patch+json").content(om.writeValueAsBytes(newsReportDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the NewsReport in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void deleteNewsReport() throws Exception {
        // Initialize the database
        insertedNewsReport = newsReportRepository.saveAndFlush(newsReport);

        long databaseSizeBeforeDelete = getRepositoryCount();

        // Delete the newsReport
        restNewsReportMockMvc
            .perform(delete(ENTITY_API_URL_ID, newsReport.getId()).accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isNoContent());

        // Validate the database contains one less item
        assertDecrementedRepositoryCount(databaseSizeBeforeDelete);
    }

    protected long getRepositoryCount() {
        return newsReportRepository.count();
    }

    protected void assertIncrementedRepositoryCount(long countBefore) {
        assertThat(countBefore + 1).isEqualTo(getRepositoryCount());
    }

    protected void assertDecrementedRepositoryCount(long countBefore) {
        assertThat(countBefore - 1).isEqualTo(getRepositoryCount());
    }

    protected void assertSameRepositoryCount(long countBefore) {
        assertThat(countBefore).isEqualTo(getRepositoryCount());
    }

    protected NewsReport getPersistedNewsReport(NewsReport newsReport) {
        return newsReportRepository.findById(newsReport.getId()).orElseThrow();
    }

    protected void assertPersistedNewsReportToMatchAllProperties(NewsReport expectedNewsReport) {
        assertNewsReportAllPropertiesEquals(expectedNewsReport, getPersistedNewsReport(expectedNewsReport));
    }

    protected void assertPersistedNewsReportToMatchUpdatableProperties(NewsReport expectedNewsReport) {
        assertNewsReportAllUpdatablePropertiesEquals(expectedNewsReport, getPersistedNewsReport(expectedNewsReport));
    }
}

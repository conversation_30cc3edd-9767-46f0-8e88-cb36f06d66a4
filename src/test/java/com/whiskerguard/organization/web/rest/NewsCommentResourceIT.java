package com.whiskerguard.organization.web.rest;

import static com.whiskerguard.organization.domain.NewsCommentAsserts.*;
import static com.whiskerguard.organization.web.rest.TestUtil.createUpdateProxyForBean;
import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.hasItem;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.whiskerguard.organization.IntegrationTest;
import com.whiskerguard.organization.domain.NewsComment;
import com.whiskerguard.organization.domain.enumeration.CommentStatus;
import com.whiskerguard.organization.repository.NewsCommentRepository;
import com.whiskerguard.organization.service.NewsCommentService;
import com.whiskerguard.organization.service.dto.NewsCommentDTO;
import com.whiskerguard.organization.service.mapper.NewsCommentMapper;
import jakarta.persistence.EntityManager;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Random;
import java.util.concurrent.atomic.AtomicLong;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

/**
 * Integration tests for the {@link NewsCommentResource} REST controller.
 */
@IntegrationTest
@ExtendWith(MockitoExtension.class)
@AutoConfigureMockMvc
@WithMockUser
class NewsCommentResourceIT {

    private static final CommentStatus DEFAULT_STATUS = CommentStatus.PENDING_REVIEW;
    private static final CommentStatus UPDATED_STATUS = CommentStatus.APPROVED;

    private static final Integer DEFAULT_SORT_ORDER = 1;
    private static final Integer UPDATED_SORT_ORDER = 2;

    private static final String DEFAULT_CONTENT = "AAAAAAAAAA";
    private static final String UPDATED_CONTENT = "BBBBBBBBBB";

    private static final Integer DEFAULT_LIKE_COUNT = 1;
    private static final Integer UPDATED_LIKE_COUNT = 2;

    private static final String DEFAULT_METADATA = "AAAAAAAAAA";
    private static final String UPDATED_METADATA = "BBBBBBBBBB";

    private static final Integer DEFAULT_VERSION = 1;
    private static final Integer UPDATED_VERSION = 2;

    private static final String DEFAULT_CREATED_BY = "AAAAAAAAAA";
    private static final String UPDATED_CREATED_BY = "BBBBBBBBBB";

    private static final Instant DEFAULT_CREATED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_CREATED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final String DEFAULT_UPDATED_BY = "AAAAAAAAAA";
    private static final String UPDATED_UPDATED_BY = "BBBBBBBBBB";

    private static final Instant DEFAULT_UPDATED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_UPDATED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final Boolean DEFAULT_IS_DELETED = false;
    private static final Boolean UPDATED_IS_DELETED = true;

    private static final String ENTITY_API_URL = "/api/news-comments";
    private static final String ENTITY_API_URL_ID = ENTITY_API_URL + "/{id}";

    private static Random random = new Random();
    private static AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));

    @Autowired
    private ObjectMapper om;

    @Autowired
    private NewsCommentRepository newsCommentRepository;

    @Mock
    private NewsCommentRepository newsCommentRepositoryMock;

    @Autowired
    private NewsCommentMapper newsCommentMapper;

    @Mock
    private NewsCommentService newsCommentServiceMock;

    @Autowired
    private EntityManager em;

    @Autowired
    private MockMvc restNewsCommentMockMvc;

    private NewsComment newsComment;

    private NewsComment insertedNewsComment;

    /**
     * Create an entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static NewsComment createEntity() {
        return new NewsComment()
            .status(DEFAULT_STATUS)
            .sortOrder(DEFAULT_SORT_ORDER)
            .content(DEFAULT_CONTENT)
            .likeCount(DEFAULT_LIKE_COUNT)
            .metadata(DEFAULT_METADATA)
            .version(DEFAULT_VERSION)
            .createdBy(DEFAULT_CREATED_BY)
            .createdAt(DEFAULT_CREATED_AT)
            .updatedBy(DEFAULT_UPDATED_BY)
            .updatedAt(DEFAULT_UPDATED_AT)
            .isDeleted(DEFAULT_IS_DELETED);
    }

    /**
     * Create an updated entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static NewsComment createUpdatedEntity() {
        return new NewsComment()
            .status(UPDATED_STATUS)
            .sortOrder(UPDATED_SORT_ORDER)
            .content(UPDATED_CONTENT)
            .likeCount(UPDATED_LIKE_COUNT)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);
    }

    @BeforeEach
    void initTest() {
        newsComment = createEntity();
    }

    @AfterEach
    void cleanup() {
        if (insertedNewsComment != null) {
            newsCommentRepository.delete(insertedNewsComment);
            insertedNewsComment = null;
        }
    }

    @Test
    @Transactional
    void createNewsComment() throws Exception {
        long databaseSizeBeforeCreate = getRepositoryCount();
        // Create the NewsComment
        NewsCommentDTO newsCommentDTO = newsCommentMapper.toDto(newsComment);
        var returnedNewsCommentDTO = om.readValue(
            restNewsCommentMockMvc
                .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(newsCommentDTO)))
                .andExpect(status().isCreated())
                .andReturn()
                .getResponse()
                .getContentAsString(),
            NewsCommentDTO.class
        );

        // Validate the NewsComment in the database
        assertIncrementedRepositoryCount(databaseSizeBeforeCreate);
        var returnedNewsComment = newsCommentMapper.toEntity(returnedNewsCommentDTO);
        assertNewsCommentUpdatableFieldsEquals(returnedNewsComment, getPersistedNewsComment(returnedNewsComment));

        insertedNewsComment = returnedNewsComment;
    }

    @Test
    @Transactional
    void createNewsCommentWithExistingId() throws Exception {
        // Create the NewsComment with an existing ID
        newsComment.setId(1L);
        NewsCommentDTO newsCommentDTO = newsCommentMapper.toDto(newsComment);

        long databaseSizeBeforeCreate = getRepositoryCount();

        // An entity with an existing ID cannot be created, so this API call must fail
        restNewsCommentMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(newsCommentDTO)))
            .andExpect(status().isBadRequest());

        // Validate the NewsComment in the database
        assertSameRepositoryCount(databaseSizeBeforeCreate);
    }

    @Test
    @Transactional
    void checkStatusIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        newsComment.setStatus(null);

        // Create the NewsComment, which fails.
        NewsCommentDTO newsCommentDTO = newsCommentMapper.toDto(newsComment);

        restNewsCommentMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(newsCommentDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkContentIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        newsComment.setContent(null);

        // Create the NewsComment, which fails.
        NewsCommentDTO newsCommentDTO = newsCommentMapper.toDto(newsComment);

        restNewsCommentMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(newsCommentDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkVersionIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        newsComment.setVersion(null);

        // Create the NewsComment, which fails.
        NewsCommentDTO newsCommentDTO = newsCommentMapper.toDto(newsComment);

        restNewsCommentMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(newsCommentDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkCreatedAtIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        newsComment.setCreatedAt(null);

        // Create the NewsComment, which fails.
        NewsCommentDTO newsCommentDTO = newsCommentMapper.toDto(newsComment);

        restNewsCommentMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(newsCommentDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkUpdatedAtIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        newsComment.setUpdatedAt(null);

        // Create the NewsComment, which fails.
        NewsCommentDTO newsCommentDTO = newsCommentMapper.toDto(newsComment);

        restNewsCommentMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(newsCommentDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkIsDeletedIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        newsComment.setIsDeleted(null);

        // Create the NewsComment, which fails.
        NewsCommentDTO newsCommentDTO = newsCommentMapper.toDto(newsComment);

        restNewsCommentMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(newsCommentDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void getAllNewsComments() throws Exception {
        // Initialize the database
        insertedNewsComment = newsCommentRepository.saveAndFlush(newsComment);

        // Get all the newsCommentList
        restNewsCommentMockMvc
            .perform(get(ENTITY_API_URL + "?sort=id,desc"))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.[*].id").value(hasItem(newsComment.getId().intValue())))
            .andExpect(jsonPath("$.[*].status").value(hasItem(DEFAULT_STATUS.toString())))
            .andExpect(jsonPath("$.[*].sortOrder").value(hasItem(DEFAULT_SORT_ORDER)))
            .andExpect(jsonPath("$.[*].content").value(hasItem(DEFAULT_CONTENT)))
            .andExpect(jsonPath("$.[*].likeCount").value(hasItem(DEFAULT_LIKE_COUNT)))
            .andExpect(jsonPath("$.[*].metadata").value(hasItem(DEFAULT_METADATA)))
            .andExpect(jsonPath("$.[*].version").value(hasItem(DEFAULT_VERSION)))
            .andExpect(jsonPath("$.[*].createdBy").value(hasItem(DEFAULT_CREATED_BY)))
            .andExpect(jsonPath("$.[*].createdAt").value(hasItem(DEFAULT_CREATED_AT.toString())))
            .andExpect(jsonPath("$.[*].updatedBy").value(hasItem(DEFAULT_UPDATED_BY)))
            .andExpect(jsonPath("$.[*].updatedAt").value(hasItem(DEFAULT_UPDATED_AT.toString())))
            .andExpect(jsonPath("$.[*].isDeleted").value(hasItem(DEFAULT_IS_DELETED)));
    }

    @SuppressWarnings({ "unchecked" })
    void getAllNewsCommentsWithEagerRelationshipsIsEnabled() throws Exception {
        when(newsCommentServiceMock.findAllWithEagerRelationships(any())).thenReturn(new PageImpl(new ArrayList<>()));

        restNewsCommentMockMvc.perform(get(ENTITY_API_URL + "?eagerload=true")).andExpect(status().isOk());

        verify(newsCommentServiceMock, times(1)).findAllWithEagerRelationships(any());
    }

    @SuppressWarnings({ "unchecked" })
    void getAllNewsCommentsWithEagerRelationshipsIsNotEnabled() throws Exception {
        when(newsCommentServiceMock.findAllWithEagerRelationships(any())).thenReturn(new PageImpl(new ArrayList<>()));

        restNewsCommentMockMvc.perform(get(ENTITY_API_URL + "?eagerload=false")).andExpect(status().isOk());
        verify(newsCommentRepositoryMock, times(1)).findAll(any(Pageable.class));
    }

    @Test
    @Transactional
    void getNewsComment() throws Exception {
        // Initialize the database
        insertedNewsComment = newsCommentRepository.saveAndFlush(newsComment);

        // Get the newsComment
        restNewsCommentMockMvc
            .perform(get(ENTITY_API_URL_ID, newsComment.getId()))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.id").value(newsComment.getId().intValue()))
            .andExpect(jsonPath("$.status").value(DEFAULT_STATUS.toString()))
            .andExpect(jsonPath("$.sortOrder").value(DEFAULT_SORT_ORDER))
            .andExpect(jsonPath("$.content").value(DEFAULT_CONTENT))
            .andExpect(jsonPath("$.likeCount").value(DEFAULT_LIKE_COUNT))
            .andExpect(jsonPath("$.metadata").value(DEFAULT_METADATA))
            .andExpect(jsonPath("$.version").value(DEFAULT_VERSION))
            .andExpect(jsonPath("$.createdBy").value(DEFAULT_CREATED_BY))
            .andExpect(jsonPath("$.createdAt").value(DEFAULT_CREATED_AT.toString()))
            .andExpect(jsonPath("$.updatedBy").value(DEFAULT_UPDATED_BY))
            .andExpect(jsonPath("$.updatedAt").value(DEFAULT_UPDATED_AT.toString()))
            .andExpect(jsonPath("$.isDeleted").value(DEFAULT_IS_DELETED));
    }

    @Test
    @Transactional
    void getNonExistingNewsComment() throws Exception {
        // Get the newsComment
        restNewsCommentMockMvc.perform(get(ENTITY_API_URL_ID, Long.MAX_VALUE)).andExpect(status().isNotFound());
    }

    @Test
    @Transactional
    void putExistingNewsComment() throws Exception {
        // Initialize the database
        insertedNewsComment = newsCommentRepository.saveAndFlush(newsComment);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the newsComment
        NewsComment updatedNewsComment = newsCommentRepository.findById(newsComment.getId()).orElseThrow();
        // Disconnect from session so that the updates on updatedNewsComment are not directly saved in db
        em.detach(updatedNewsComment);
        updatedNewsComment
            .status(UPDATED_STATUS)
            .sortOrder(UPDATED_SORT_ORDER)
            .content(UPDATED_CONTENT)
            .likeCount(UPDATED_LIKE_COUNT)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);
        NewsCommentDTO newsCommentDTO = newsCommentMapper.toDto(updatedNewsComment);

        restNewsCommentMockMvc
            .perform(
                put(ENTITY_API_URL_ID, newsCommentDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(newsCommentDTO))
            )
            .andExpect(status().isOk());

        // Validate the NewsComment in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertPersistedNewsCommentToMatchAllProperties(updatedNewsComment);
    }

    @Test
    @Transactional
    void putNonExistingNewsComment() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        newsComment.setId(longCount.incrementAndGet());

        // Create the NewsComment
        NewsCommentDTO newsCommentDTO = newsCommentMapper.toDto(newsComment);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restNewsCommentMockMvc
            .perform(
                put(ENTITY_API_URL_ID, newsCommentDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(newsCommentDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the NewsComment in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithIdMismatchNewsComment() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        newsComment.setId(longCount.incrementAndGet());

        // Create the NewsComment
        NewsCommentDTO newsCommentDTO = newsCommentMapper.toDto(newsComment);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restNewsCommentMockMvc
            .perform(
                put(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(newsCommentDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the NewsComment in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithMissingIdPathParamNewsComment() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        newsComment.setId(longCount.incrementAndGet());

        // Create the NewsComment
        NewsCommentDTO newsCommentDTO = newsCommentMapper.toDto(newsComment);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restNewsCommentMockMvc
            .perform(put(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(newsCommentDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the NewsComment in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void partialUpdateNewsCommentWithPatch() throws Exception {
        // Initialize the database
        insertedNewsComment = newsCommentRepository.saveAndFlush(newsComment);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the newsComment using partial update
        NewsComment partialUpdatedNewsComment = new NewsComment();
        partialUpdatedNewsComment.setId(newsComment.getId());

        partialUpdatedNewsComment
            .status(UPDATED_STATUS)
            .likeCount(UPDATED_LIKE_COUNT)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedAt(UPDATED_UPDATED_AT);

        restNewsCommentMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedNewsComment.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedNewsComment))
            )
            .andExpect(status().isOk());

        // Validate the NewsComment in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertNewsCommentUpdatableFieldsEquals(
            createUpdateProxyForBean(partialUpdatedNewsComment, newsComment),
            getPersistedNewsComment(newsComment)
        );
    }

    @Test
    @Transactional
    void fullUpdateNewsCommentWithPatch() throws Exception {
        // Initialize the database
        insertedNewsComment = newsCommentRepository.saveAndFlush(newsComment);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the newsComment using partial update
        NewsComment partialUpdatedNewsComment = new NewsComment();
        partialUpdatedNewsComment.setId(newsComment.getId());

        partialUpdatedNewsComment
            .status(UPDATED_STATUS)
            .sortOrder(UPDATED_SORT_ORDER)
            .content(UPDATED_CONTENT)
            .likeCount(UPDATED_LIKE_COUNT)
            .metadata(UPDATED_METADATA)
            .version(UPDATED_VERSION)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);

        restNewsCommentMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedNewsComment.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedNewsComment))
            )
            .andExpect(status().isOk());

        // Validate the NewsComment in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertNewsCommentUpdatableFieldsEquals(partialUpdatedNewsComment, getPersistedNewsComment(partialUpdatedNewsComment));
    }

    @Test
    @Transactional
    void patchNonExistingNewsComment() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        newsComment.setId(longCount.incrementAndGet());

        // Create the NewsComment
        NewsCommentDTO newsCommentDTO = newsCommentMapper.toDto(newsComment);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restNewsCommentMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, newsCommentDTO.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(newsCommentDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the NewsComment in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithIdMismatchNewsComment() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        newsComment.setId(longCount.incrementAndGet());

        // Create the NewsComment
        NewsCommentDTO newsCommentDTO = newsCommentMapper.toDto(newsComment);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restNewsCommentMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(newsCommentDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the NewsComment in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithMissingIdPathParamNewsComment() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        newsComment.setId(longCount.incrementAndGet());

        // Create the NewsComment
        NewsCommentDTO newsCommentDTO = newsCommentMapper.toDto(newsComment);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restNewsCommentMockMvc
            .perform(patch(ENTITY_API_URL).contentType("application/merge-patch+json").content(om.writeValueAsBytes(newsCommentDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the NewsComment in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void deleteNewsComment() throws Exception {
        // Initialize the database
        insertedNewsComment = newsCommentRepository.saveAndFlush(newsComment);

        long databaseSizeBeforeDelete = getRepositoryCount();

        // Delete the newsComment
        restNewsCommentMockMvc
            .perform(delete(ENTITY_API_URL_ID, newsComment.getId()).accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isNoContent());

        // Validate the database contains one less item
        assertDecrementedRepositoryCount(databaseSizeBeforeDelete);
    }

    protected long getRepositoryCount() {
        return newsCommentRepository.count();
    }

    protected void assertIncrementedRepositoryCount(long countBefore) {
        assertThat(countBefore + 1).isEqualTo(getRepositoryCount());
    }

    protected void assertDecrementedRepositoryCount(long countBefore) {
        assertThat(countBefore - 1).isEqualTo(getRepositoryCount());
    }

    protected void assertSameRepositoryCount(long countBefore) {
        assertThat(countBefore).isEqualTo(getRepositoryCount());
    }

    protected NewsComment getPersistedNewsComment(NewsComment newsComment) {
        return newsCommentRepository.findById(newsComment.getId()).orElseThrow();
    }

    protected void assertPersistedNewsCommentToMatchAllProperties(NewsComment expectedNewsComment) {
        assertNewsCommentAllPropertiesEquals(expectedNewsComment, getPersistedNewsComment(expectedNewsComment));
    }

    protected void assertPersistedNewsCommentToMatchUpdatableProperties(NewsComment expectedNewsComment) {
        assertNewsCommentAllUpdatablePropertiesEquals(expectedNewsComment, getPersistedNewsComment(expectedNewsComment));
    }
}

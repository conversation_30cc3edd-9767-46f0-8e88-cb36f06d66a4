package com.whiskerguard.organization.web.rest;

import static com.whiskerguard.organization.domain.RiskCategoryAsserts.*;
import static com.whiskerguard.organization.web.rest.TestUtil.createUpdateProxyForBean;
import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.hasItem;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.whiskerguard.organization.IntegrationTest;
import com.whiskerguard.organization.domain.RiskCategory;
import com.whiskerguard.organization.domain.enumeration.RiskLevel;
import com.whiskerguard.organization.repository.RiskCategoryRepository;
import com.whiskerguard.organization.service.RiskCategoryService;
import com.whiskerguard.organization.service.dto.RiskCategoryDTO;
import com.whiskerguard.organization.service.mapper.RiskCategoryMapper;
import jakarta.persistence.EntityManager;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Random;
import java.util.concurrent.atomic.AtomicLong;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

/**
 * Integration tests for the {@link RiskCategoryResource} REST controller.
 */
@IntegrationTest
@ExtendWith(MockitoExtension.class)
@AutoConfigureMockMvc
@WithMockUser
class RiskCategoryResourceIT {

    private static final Long DEFAULT_TENANT_ID = 1L;
    private static final Long UPDATED_TENANT_ID = 2L;

    private static final String DEFAULT_NAME = "AAAAAAAAAA";
    private static final String UPDATED_NAME = "BBBBBBBBBB";

    private static final RiskLevel DEFAULT_LEVEL = RiskLevel.HIGH;
    private static final RiskLevel UPDATED_LEVEL = RiskLevel.MEDIUM;

    private static final String DEFAULT_EXPRESSION = "AAAAAAAAAA";
    private static final String UPDATED_EXPRESSION = "BBBBBBBBBB";

    private static final String DEFAULT_DESCRIPTION = "AAAAAAAAAA";
    private static final String UPDATED_DESCRIPTION = "BBBBBBBBBB";

    private static final Integer DEFAULT_VERSION = 1;
    private static final Integer UPDATED_VERSION = 2;

    private static final String DEFAULT_METADATA = "AAAAAAAAAA";
    private static final String UPDATED_METADATA = "BBBBBBBBBB";

    private static final String DEFAULT_CREATED_BY = "AAAAAAAAAA";
    private static final String UPDATED_CREATED_BY = "BBBBBBBBBB";

    private static final Instant DEFAULT_CREATED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_CREATED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final String DEFAULT_UPDATED_BY = "AAAAAAAAAA";
    private static final String UPDATED_UPDATED_BY = "BBBBBBBBBB";

    private static final Instant DEFAULT_UPDATED_AT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_UPDATED_AT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final Boolean DEFAULT_IS_DELETED = false;
    private static final Boolean UPDATED_IS_DELETED = true;

    private static final String ENTITY_API_URL = "/api/risk-categories";
    private static final String ENTITY_API_URL_ID = ENTITY_API_URL + "/{id}";

    private static Random random = new Random();
    private static AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));

    @Autowired
    private ObjectMapper om;

    @Autowired
    private RiskCategoryRepository riskCategoryRepository;

    @Mock
    private RiskCategoryRepository riskCategoryRepositoryMock;

    @Autowired
    private RiskCategoryMapper riskCategoryMapper;

    @Mock
    private RiskCategoryService riskCategoryServiceMock;

    @Autowired
    private EntityManager em;

    @Autowired
    private MockMvc restRiskCategoryMockMvc;

    private RiskCategory riskCategory;

    private RiskCategory insertedRiskCategory;

    /**
     * Create an entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static RiskCategory createEntity() {
        return new RiskCategory()
            .tenantId(DEFAULT_TENANT_ID)
            .name(DEFAULT_NAME)
            .level(DEFAULT_LEVEL)
            .expression(DEFAULT_EXPRESSION)
            .description(DEFAULT_DESCRIPTION)
            .version(DEFAULT_VERSION)
            .metadata(DEFAULT_METADATA)
            .createdBy(DEFAULT_CREATED_BY)
            .createdAt(DEFAULT_CREATED_AT)
            .updatedBy(DEFAULT_UPDATED_BY)
            .updatedAt(DEFAULT_UPDATED_AT)
            .isDeleted(DEFAULT_IS_DELETED);
    }

    /**
     * Create an updated entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static RiskCategory createUpdatedEntity() {
        return new RiskCategory()
            .tenantId(UPDATED_TENANT_ID)
            .name(UPDATED_NAME)
            .level(UPDATED_LEVEL)
            .expression(UPDATED_EXPRESSION)
            .description(UPDATED_DESCRIPTION)
            .version(UPDATED_VERSION)
            .metadata(UPDATED_METADATA)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);
    }

    @BeforeEach
    void initTest() {
        riskCategory = createEntity();
    }

    @AfterEach
    void cleanup() {
        if (insertedRiskCategory != null) {
            riskCategoryRepository.delete(insertedRiskCategory);
            insertedRiskCategory = null;
        }
    }

    @Test
    @Transactional
    void createRiskCategory() throws Exception {
        long databaseSizeBeforeCreate = getRepositoryCount();
        // Create the RiskCategory
        RiskCategoryDTO riskCategoryDTO = riskCategoryMapper.toDto(riskCategory);
        var returnedRiskCategoryDTO = om.readValue(
            restRiskCategoryMockMvc
                .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(riskCategoryDTO)))
                .andExpect(status().isCreated())
                .andReturn()
                .getResponse()
                .getContentAsString(),
            RiskCategoryDTO.class
        );

        // Validate the RiskCategory in the database
        assertIncrementedRepositoryCount(databaseSizeBeforeCreate);
        var returnedRiskCategory = riskCategoryMapper.toEntity(returnedRiskCategoryDTO);
        assertRiskCategoryUpdatableFieldsEquals(returnedRiskCategory, getPersistedRiskCategory(returnedRiskCategory));

        insertedRiskCategory = returnedRiskCategory;
    }

    @Test
    @Transactional
    void createRiskCategoryWithExistingId() throws Exception {
        // Create the RiskCategory with an existing ID
        riskCategory.setId(1L);
        RiskCategoryDTO riskCategoryDTO = riskCategoryMapper.toDto(riskCategory);

        long databaseSizeBeforeCreate = getRepositoryCount();

        // An entity with an existing ID cannot be created, so this API call must fail
        restRiskCategoryMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(riskCategoryDTO)))
            .andExpect(status().isBadRequest());

        // Validate the RiskCategory in the database
        assertSameRepositoryCount(databaseSizeBeforeCreate);
    }

    @Test
    @Transactional
    void checkTenantIdIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        riskCategory.setTenantId(null);

        // Create the RiskCategory, which fails.
        RiskCategoryDTO riskCategoryDTO = riskCategoryMapper.toDto(riskCategory);

        restRiskCategoryMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(riskCategoryDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkNameIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        riskCategory.setName(null);

        // Create the RiskCategory, which fails.
        RiskCategoryDTO riskCategoryDTO = riskCategoryMapper.toDto(riskCategory);

        restRiskCategoryMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(riskCategoryDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkLevelIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        riskCategory.setLevel(null);

        // Create the RiskCategory, which fails.
        RiskCategoryDTO riskCategoryDTO = riskCategoryMapper.toDto(riskCategory);

        restRiskCategoryMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(riskCategoryDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkExpressionIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        riskCategory.setExpression(null);

        // Create the RiskCategory, which fails.
        RiskCategoryDTO riskCategoryDTO = riskCategoryMapper.toDto(riskCategory);

        restRiskCategoryMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(riskCategoryDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkVersionIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        riskCategory.setVersion(null);

        // Create the RiskCategory, which fails.
        RiskCategoryDTO riskCategoryDTO = riskCategoryMapper.toDto(riskCategory);

        restRiskCategoryMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(riskCategoryDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkCreatedAtIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        riskCategory.setCreatedAt(null);

        // Create the RiskCategory, which fails.
        RiskCategoryDTO riskCategoryDTO = riskCategoryMapper.toDto(riskCategory);

        restRiskCategoryMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(riskCategoryDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkUpdatedAtIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        riskCategory.setUpdatedAt(null);

        // Create the RiskCategory, which fails.
        RiskCategoryDTO riskCategoryDTO = riskCategoryMapper.toDto(riskCategory);

        restRiskCategoryMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(riskCategoryDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkIsDeletedIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        riskCategory.setIsDeleted(null);

        // Create the RiskCategory, which fails.
        RiskCategoryDTO riskCategoryDTO = riskCategoryMapper.toDto(riskCategory);

        restRiskCategoryMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(riskCategoryDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void getAllRiskCategories() throws Exception {
        // Initialize the database
        insertedRiskCategory = riskCategoryRepository.saveAndFlush(riskCategory);

        // Get all the riskCategoryList
        restRiskCategoryMockMvc
            .perform(get(ENTITY_API_URL + "?sort=id,desc"))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.[*].id").value(hasItem(riskCategory.getId().intValue())))
            .andExpect(jsonPath("$.[*].tenantId").value(hasItem(DEFAULT_TENANT_ID.intValue())))
            .andExpect(jsonPath("$.[*].name").value(hasItem(DEFAULT_NAME)))
            .andExpect(jsonPath("$.[*].level").value(hasItem(DEFAULT_LEVEL.toString())))
            .andExpect(jsonPath("$.[*].expression").value(hasItem(DEFAULT_EXPRESSION)))
            .andExpect(jsonPath("$.[*].description").value(hasItem(DEFAULT_DESCRIPTION)))
            .andExpect(jsonPath("$.[*].version").value(hasItem(DEFAULT_VERSION)))
            .andExpect(jsonPath("$.[*].metadata").value(hasItem(DEFAULT_METADATA)))
            .andExpect(jsonPath("$.[*].createdBy").value(hasItem(DEFAULT_CREATED_BY)))
            .andExpect(jsonPath("$.[*].createdAt").value(hasItem(DEFAULT_CREATED_AT.toString())))
            .andExpect(jsonPath("$.[*].updatedBy").value(hasItem(DEFAULT_UPDATED_BY)))
            .andExpect(jsonPath("$.[*].updatedAt").value(hasItem(DEFAULT_UPDATED_AT.toString())))
            .andExpect(jsonPath("$.[*].isDeleted").value(hasItem(DEFAULT_IS_DELETED)));
    }

    @SuppressWarnings({ "unchecked" })
    void getAllRiskCategoriesWithEagerRelationshipsIsEnabled() throws Exception {
        when(riskCategoryServiceMock.findAllWithEagerRelationships(any())).thenReturn(new PageImpl(new ArrayList<>()));

        restRiskCategoryMockMvc.perform(get(ENTITY_API_URL + "?eagerload=true")).andExpect(status().isOk());

        verify(riskCategoryServiceMock, times(1)).findAllWithEagerRelationships(any());
    }

    @SuppressWarnings({ "unchecked" })
    void getAllRiskCategoriesWithEagerRelationshipsIsNotEnabled() throws Exception {
        when(riskCategoryServiceMock.findAllWithEagerRelationships(any())).thenReturn(new PageImpl(new ArrayList<>()));

        restRiskCategoryMockMvc.perform(get(ENTITY_API_URL + "?eagerload=false")).andExpect(status().isOk());
        verify(riskCategoryRepositoryMock, times(1)).findAll(any(Pageable.class));
    }

    @Test
    @Transactional
    void getRiskCategory() throws Exception {
        // Initialize the database
        insertedRiskCategory = riskCategoryRepository.saveAndFlush(riskCategory);

        // Get the riskCategory
        restRiskCategoryMockMvc
            .perform(get(ENTITY_API_URL_ID, riskCategory.getId()))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.id").value(riskCategory.getId().intValue()))
            .andExpect(jsonPath("$.tenantId").value(DEFAULT_TENANT_ID.intValue()))
            .andExpect(jsonPath("$.name").value(DEFAULT_NAME))
            .andExpect(jsonPath("$.level").value(DEFAULT_LEVEL.toString()))
            .andExpect(jsonPath("$.expression").value(DEFAULT_EXPRESSION))
            .andExpect(jsonPath("$.description").value(DEFAULT_DESCRIPTION))
            .andExpect(jsonPath("$.version").value(DEFAULT_VERSION))
            .andExpect(jsonPath("$.metadata").value(DEFAULT_METADATA))
            .andExpect(jsonPath("$.createdBy").value(DEFAULT_CREATED_BY))
            .andExpect(jsonPath("$.createdAt").value(DEFAULT_CREATED_AT.toString()))
            .andExpect(jsonPath("$.updatedBy").value(DEFAULT_UPDATED_BY))
            .andExpect(jsonPath("$.updatedAt").value(DEFAULT_UPDATED_AT.toString()))
            .andExpect(jsonPath("$.isDeleted").value(DEFAULT_IS_DELETED));
    }

    @Test
    @Transactional
    void getNonExistingRiskCategory() throws Exception {
        // Get the riskCategory
        restRiskCategoryMockMvc.perform(get(ENTITY_API_URL_ID, Long.MAX_VALUE)).andExpect(status().isNotFound());
    }

    @Test
    @Transactional
    void putExistingRiskCategory() throws Exception {
        // Initialize the database
        insertedRiskCategory = riskCategoryRepository.saveAndFlush(riskCategory);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the riskCategory
        RiskCategory updatedRiskCategory = riskCategoryRepository.findById(riskCategory.getId()).orElseThrow();
        // Disconnect from session so that the updates on updatedRiskCategory are not directly saved in db
        em.detach(updatedRiskCategory);
        updatedRiskCategory
            .tenantId(UPDATED_TENANT_ID)
            .name(UPDATED_NAME)
            .level(UPDATED_LEVEL)
            .expression(UPDATED_EXPRESSION)
            .description(UPDATED_DESCRIPTION)
            .version(UPDATED_VERSION)
            .metadata(UPDATED_METADATA)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);
        RiskCategoryDTO riskCategoryDTO = riskCategoryMapper.toDto(updatedRiskCategory);

        restRiskCategoryMockMvc
            .perform(
                put(ENTITY_API_URL_ID, riskCategoryDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(riskCategoryDTO))
            )
            .andExpect(status().isOk());

        // Validate the RiskCategory in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertPersistedRiskCategoryToMatchAllProperties(updatedRiskCategory);
    }

    @Test
    @Transactional
    void putNonExistingRiskCategory() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        riskCategory.setId(longCount.incrementAndGet());

        // Create the RiskCategory
        RiskCategoryDTO riskCategoryDTO = riskCategoryMapper.toDto(riskCategory);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restRiskCategoryMockMvc
            .perform(
                put(ENTITY_API_URL_ID, riskCategoryDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(riskCategoryDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the RiskCategory in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithIdMismatchRiskCategory() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        riskCategory.setId(longCount.incrementAndGet());

        // Create the RiskCategory
        RiskCategoryDTO riskCategoryDTO = riskCategoryMapper.toDto(riskCategory);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restRiskCategoryMockMvc
            .perform(
                put(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(riskCategoryDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the RiskCategory in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithMissingIdPathParamRiskCategory() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        riskCategory.setId(longCount.incrementAndGet());

        // Create the RiskCategory
        RiskCategoryDTO riskCategoryDTO = riskCategoryMapper.toDto(riskCategory);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restRiskCategoryMockMvc
            .perform(put(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(riskCategoryDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the RiskCategory in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void partialUpdateRiskCategoryWithPatch() throws Exception {
        // Initialize the database
        insertedRiskCategory = riskCategoryRepository.saveAndFlush(riskCategory);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the riskCategory using partial update
        RiskCategory partialUpdatedRiskCategory = new RiskCategory();
        partialUpdatedRiskCategory.setId(riskCategory.getId());

        partialUpdatedRiskCategory
            .tenantId(UPDATED_TENANT_ID)
            .level(UPDATED_LEVEL)
            .expression(UPDATED_EXPRESSION)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT);

        restRiskCategoryMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedRiskCategory.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedRiskCategory))
            )
            .andExpect(status().isOk());

        // Validate the RiskCategory in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertRiskCategoryUpdatableFieldsEquals(
            createUpdateProxyForBean(partialUpdatedRiskCategory, riskCategory),
            getPersistedRiskCategory(riskCategory)
        );
    }

    @Test
    @Transactional
    void fullUpdateRiskCategoryWithPatch() throws Exception {
        // Initialize the database
        insertedRiskCategory = riskCategoryRepository.saveAndFlush(riskCategory);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the riskCategory using partial update
        RiskCategory partialUpdatedRiskCategory = new RiskCategory();
        partialUpdatedRiskCategory.setId(riskCategory.getId());

        partialUpdatedRiskCategory
            .tenantId(UPDATED_TENANT_ID)
            .name(UPDATED_NAME)
            .level(UPDATED_LEVEL)
            .expression(UPDATED_EXPRESSION)
            .description(UPDATED_DESCRIPTION)
            .version(UPDATED_VERSION)
            .metadata(UPDATED_METADATA)
            .createdBy(UPDATED_CREATED_BY)
            .createdAt(UPDATED_CREATED_AT)
            .updatedBy(UPDATED_UPDATED_BY)
            .updatedAt(UPDATED_UPDATED_AT)
            .isDeleted(UPDATED_IS_DELETED);

        restRiskCategoryMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedRiskCategory.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedRiskCategory))
            )
            .andExpect(status().isOk());

        // Validate the RiskCategory in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertRiskCategoryUpdatableFieldsEquals(partialUpdatedRiskCategory, getPersistedRiskCategory(partialUpdatedRiskCategory));
    }

    @Test
    @Transactional
    void patchNonExistingRiskCategory() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        riskCategory.setId(longCount.incrementAndGet());

        // Create the RiskCategory
        RiskCategoryDTO riskCategoryDTO = riskCategoryMapper.toDto(riskCategory);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restRiskCategoryMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, riskCategoryDTO.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(riskCategoryDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the RiskCategory in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithIdMismatchRiskCategory() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        riskCategory.setId(longCount.incrementAndGet());

        // Create the RiskCategory
        RiskCategoryDTO riskCategoryDTO = riskCategoryMapper.toDto(riskCategory);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restRiskCategoryMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(riskCategoryDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the RiskCategory in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithMissingIdPathParamRiskCategory() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        riskCategory.setId(longCount.incrementAndGet());

        // Create the RiskCategory
        RiskCategoryDTO riskCategoryDTO = riskCategoryMapper.toDto(riskCategory);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restRiskCategoryMockMvc
            .perform(patch(ENTITY_API_URL).contentType("application/merge-patch+json").content(om.writeValueAsBytes(riskCategoryDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the RiskCategory in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void deleteRiskCategory() throws Exception {
        // Initialize the database
        insertedRiskCategory = riskCategoryRepository.saveAndFlush(riskCategory);

        long databaseSizeBeforeDelete = getRepositoryCount();

        // Delete the riskCategory
        restRiskCategoryMockMvc
            .perform(delete(ENTITY_API_URL_ID, riskCategory.getId()).accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isNoContent());

        // Validate the database contains one less item
        assertDecrementedRepositoryCount(databaseSizeBeforeDelete);
    }

    protected long getRepositoryCount() {
        return riskCategoryRepository.count();
    }

    protected void assertIncrementedRepositoryCount(long countBefore) {
        assertThat(countBefore + 1).isEqualTo(getRepositoryCount());
    }

    protected void assertDecrementedRepositoryCount(long countBefore) {
        assertThat(countBefore - 1).isEqualTo(getRepositoryCount());
    }

    protected void assertSameRepositoryCount(long countBefore) {
        assertThat(countBefore).isEqualTo(getRepositoryCount());
    }

    protected RiskCategory getPersistedRiskCategory(RiskCategory riskCategory) {
        return riskCategoryRepository.findById(riskCategory.getId()).orElseThrow();
    }

    protected void assertPersistedRiskCategoryToMatchAllProperties(RiskCategory expectedRiskCategory) {
        assertRiskCategoryAllPropertiesEquals(expectedRiskCategory, getPersistedRiskCategory(expectedRiskCategory));
    }

    protected void assertPersistedRiskCategoryToMatchUpdatableProperties(RiskCategory expectedRiskCategory) {
        assertRiskCategoryAllUpdatablePropertiesEquals(expectedRiskCategory, getPersistedRiskCategory(expectedRiskCategory));
    }
}

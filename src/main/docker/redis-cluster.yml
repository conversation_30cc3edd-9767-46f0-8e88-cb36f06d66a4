# This configuration is intended for development purpose, it's **your** responsibility to harden it for production
name: whiskerguardorgservice
services:
  redis:
    image: redis:7.4.2
    command:
      - 'redis-server'
      - '--port 6379'
      - '--cluster-enabled yes'
      - '--cluster-config-file nodes.conf'
      - '--cluster-node-timeout 5000'
      - '--appendonly yes'
    ports:
      - '6379:6379'
  redis-1:
    image: redis:7.4.2
    command:
      - 'redis-server'
      - '--port 6379'
      - '--cluster-enabled yes'
      - '--cluster-config-file nodes.conf'
      - '--cluster-node-timeout 5000'
      - '--appendonly yes'
    ports:
      - '16379:6379'
  redis-2:
    image: redis:7.4.2
    command:
      - 'redis-server'
      - '--port 6379'
      - '--cluster-enabled yes'
      - '--cluster-config-file nodes.conf'
      - '--cluster-node-timeout 5000'
      - '--appendonly yes'
    ports:
      - '26379:6379'
  redis-3:
    image: redis:7.4.2
    command:
      - 'redis-server'
      - '--port 6379'
      - '--cluster-enabled yes'
      - '--cluster-config-file nodes.conf'
      - '--cluster-node-timeout 5000'
      - '--appendonly yes'
    ports:
      - '36379:6379'
  redis-4:
    image: redis:7.4.2
    command:
      - 'redis-server'
      - '--port 6379'
      - '--cluster-enabled yes'
      - '--cluster-config-file nodes.conf'
      - '--cluster-node-timeout 5000'
      - '--appendonly yes'
    ports:
      - '46379:6379'
  redis-5:
    image: redis:7.4.2
    command:
      - 'redis-server'
      - '--port 6379'
      - '--cluster-enabled yes'
      - '--cluster-config-file nodes.conf'
      - '--cluster-node-timeout 5000'
      - '--appendonly yes'
    ports:
      - '56379:6379'
  redis-cluster-builder:
    build:
      context: .
      dockerfile: redis/Redis-Cluster.Dockerfile

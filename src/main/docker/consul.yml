# This configuration is intended for development purpose, it's **your** responsibility to harden it for production
name: whiskerguardorgservice
services:
  consul:
    image: docker.io/bitnami/consul:1.20.5
    # If you want to expose these ports outside your dev PC,
    # remove the "127.0.0.1:" prefix
    ports:
      - 127.0.0.1:8300:8300
      - 127.0.0.1:8500:8500
      - 127.0.0.1:8600:8600
    command: consul agent -dev -ui -client 0.0.0.0 -log-level=INFO
    labels:
      org.springframework.boot.ignore: true

  consul-config-loader:
    image: jhipster/consul-config-loader:v0.4.1
    volumes:
      - ./central-server-config:/config
    environment:
      - INIT_SLEEP_SECONDS=5
      - CONSUL_URL=consul
      - CONSUL_PORT=8500
      # Uncomment to load configuration into Consul from a Git repository
      # as configured in central-server-config/git2consul.json
      # Also set SPRING_CLOUD_CONSUL_CONFIG_FORMAT=files on your apps
      # - CONFIG_MODE=git
    labels:
      org.springframework.boot.ignore: true

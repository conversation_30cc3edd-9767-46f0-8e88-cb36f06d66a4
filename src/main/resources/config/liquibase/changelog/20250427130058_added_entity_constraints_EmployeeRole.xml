<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
  xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
  <!--
      Added the constraints for entity EmployeeRole.
  -->
  <changeSet id="20250427130058-2" author="jhipster">

    <addForeignKeyConstraint baseColumnNames="employee_id"
                             baseTableName="employee_role"
                             constraintName="fk_employee_role_employee_id"
                             referencedColumnNames="id"
                             referencedTableName="employee"
    />

    <addForeignKeyConstraint baseColumnNames="role_id"
                             baseTableName="employee_role"
                             constraintName="fk_employee_role_role_id"
                             referencedColumnNames="id"
                             referencedTableName="role"
    />

    <addForeignKeyConstraint baseColumnNames="org_unit_id"
                             baseTableName="employee_role"
                             constraintName="fk_employee_role_org_unit_id"
                             referencedColumnNames="id"
                             referencedTableName="org_unit"
    />
  </changeSet>
</databaseChangeLog>

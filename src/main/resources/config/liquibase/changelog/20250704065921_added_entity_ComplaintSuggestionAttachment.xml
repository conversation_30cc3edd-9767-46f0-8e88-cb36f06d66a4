<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd
                        http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd">

    <!--
        Added the entity ComplaintSuggestionAttachment.
    -->
    <changeSet id="20250704065921-1" author="jhipster">
        <createTable tableName="complaint_suggestion_attachment" remarks="投诉与建议附件表，用于存储合规案例相关的附件信息">
            <column name="id" type="bigint" autoIncrement="true" startWith="1500">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="tenant_id" type="bigint" remarks="租户ID">
                <constraints nullable="true" />
            </column>
            <column name="suggestion_id" type="bigint" remarks="关联投诉与建议ID">
                <constraints nullable="false" />
            </column>
            <column name="file_name" type="varchar(256)" remarks="附件名称">
                <constraints nullable="false" />
            </column>
            <column name="file_path" type="varchar(512)" remarks="附件存储路径或URL">
                <constraints nullable="false" />
            </column>
            <column name="file_type" type="varchar(32)" remarks="附件类型">
                <constraints nullable="false" />
            </column>
            <column name="file_size" type="varchar(32)" remarks="附件大小">
                <constraints nullable="true" />
            </column>
            <column name="file_desc" type="varchar(512)" remarks="附件描述">
                <constraints nullable="true" />
            </column>
            <column name="metadata" type="${clobType}" remarks="补充字段">
                <constraints nullable="true" />
            </column>
            <column name="version" type="integer" remarks="当前版本号">
                <constraints nullable="true" />
            </column>
            <column name="uploaded_by" type="varchar(64)" remarks="上传者">
                <constraints nullable="false" />
            </column>
            <column name="uploaded_at" type="${datetimeType}" remarks="上传时间">
                <constraints nullable="true" />
            </column>
            <column name="is_deleted" type="boolean" remarks="是否删除：0 表示正常 1 表示已删除">
                <constraints nullable="true" />
            </column>
            <!-- jhipster-needle-liquibase-add-column - JHipster will add columns here -->
        </createTable>
        <dropDefaultValue tableName="complaint_suggestion_attachment" columnName="uploaded_at" columnDataType="${datetimeType}"/>
    </changeSet>

    <!-- jhipster-needle-liquibase-add-changeset - JHipster will add changesets here -->

    <!--
        Load sample data generated with Faker.js
        - This data can be easily edited using a CSV editor (or even MS Excel) and
          is located in the 'src/main/resources/config/liquibase/fake-data' directory
        - By default this data is applied when running with the JHipster 'dev' profile.
          This can be customized by adding or removing 'faker' in the 'spring.liquibase.contexts'
          Spring Boot configuration key.
    -->
    <changeSet id="20250704065921-1-data" author="jhipster" context="faker">
        <loadData
                  file="config/liquibase/fake-data/complaint_suggestion_attachment.csv"
                  separator=";"
                  tableName="complaint_suggestion_attachment"
                  usePreparedStatements="true">
            <column name="id" type="numeric"/>
            <column name="tenant_id" type="numeric"/>
            <column name="suggestion_id" type="numeric"/>
            <column name="file_name" type="string"/>
            <column name="file_path" type="string"/>
            <column name="file_type" type="string"/>
            <column name="file_size" type="string"/>
            <column name="file_desc" type="string"/>
            <column name="metadata" type="clob"/>
            <column name="version" type="numeric"/>
            <column name="uploaded_by" type="string"/>
            <column name="uploaded_at" type="date"/>
            <column name="is_deleted" type="boolean"/>
            <!-- jhipster-needle-liquibase-add-loadcolumn - JHipster (and/or extensions) can add load columns here -->
        </loadData>
    </changeSet>
</databaseChangeLog>

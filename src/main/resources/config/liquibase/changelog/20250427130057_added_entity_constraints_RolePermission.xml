<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
  xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
  <!--
      Added the constraints for entity RolePermission.
  -->
  <changeSet id="20250427130057-2" author="jhipster">

    <addForeignKeyConstraint baseColumnNames="role_id"
                             baseTableName="role_permission"
                             constraintName="fk_role_permission_role_id"
                             referencedColumnNames="id"
                             referencedTableName="role"
    />

    <addForeignKeyConstraint baseColumnNames="permission_id"
                             baseTableName="role_permission"
                             constraintName="fk_role_permission_permission_id"
                             referencedColumnNames="id"
                             referencedTableName="permission"
    />
  </changeSet>
</databaseChangeLog>

<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd
                        http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd">

    <!--
        Added the entity Tenant.
    -->
    <changeSet id="20250427130100-1" author="jhipster">
        <createTable tableName="tenant" remarks="租户（Tenant）实体">
            <column name="id" type="bigint" remarks="主键 ID" autoIncrement="true" startWith="1500">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="tenant_code" type="varchar(64)" remarks="租户编码">
                <constraints nullable="false" />
            </column>
            <column name="name" type="varchar(128)" remarks="租户名称">
                <constraints nullable="false" />
            </column>
            <column name="status" type="integer" remarks="租户状态">
                <constraints nullable="false" />
            </column>
            <column name="subscription_plan" type="varchar(64)" remarks="套餐类型">
                <constraints nullable="true" />
            </column>
            <column name="subscription_start" type="date" remarks="套餐开始日期">
                <constraints nullable="true" />
            </column>
            <column name="subscription_end" type="date" remarks="套餐结束日期">
                <constraints nullable="true" />
            </column>
            <column name="contact_email" type="varchar(128)" remarks="联系人邮箱">
                <constraints nullable="true" />
            </column>
            <column name="contact_phone" type="varchar(32)" remarks="联系人电话">
                <constraints nullable="true" />
            </column>
            <column name="is_system" type="boolean" remarks="是否是系统租户">
                <constraints nullable="true" />
            </column>
            <column name="metadata" type="varchar(255)" remarks="扩展元数据">
                <constraints nullable="true" />
            </column>
            <column name="version" type="integer" remarks="乐观锁版本">
                <constraints nullable="false" />
            </column>
            <column name="created_by" type="varchar(255)" remarks="创建者">
                <constraints nullable="true" />
            </column>
            <column name="created_at" type="${datetimeType}" remarks="创建时间">
                <constraints nullable="false" />
            </column>
            <column name="updated_by" type="varchar(255)" remarks="更新者">
                <constraints nullable="true" />
            </column>
            <column name="updated_at" type="${datetimeType}" remarks="更新时间">
                <constraints nullable="false" />
            </column>
            <column name="is_deleted" type="boolean" remarks="软删除标志">
                <constraints nullable="false" />
            </column>
            <!-- jhipster-needle-liquibase-add-column - JHipster will add columns here -->
        </createTable>
        <dropDefaultValue tableName="tenant" columnName="created_at" columnDataType="${datetimeType}"/>
        <dropDefaultValue tableName="tenant" columnName="updated_at" columnDataType="${datetimeType}"/>
    </changeSet>

    <!-- jhipster-needle-liquibase-add-changeset - JHipster will add changesets here -->

    <!--
        Load sample data generated with Faker.js
        - This data can be easily edited using a CSV editor (or even MS Excel) and
          is located in the 'src/main/resources/config/liquibase/fake-data' directory
        - By default this data is applied when running with the JHipster 'dev' profile.
          This can be customized by adding or removing 'faker' in the 'spring.liquibase.contexts'
          Spring Boot configuration key.
    -->
    <changeSet id="20250427130100-1-data" author="jhipster" context="faker">
        <loadData
                  file="config/liquibase/fake-data/tenant.csv"
                  separator=";"
                  tableName="tenant"
                  usePreparedStatements="true">
            <column name="id" type="numeric"/>
            <column name="tenant_code" type="string"/>
            <column name="name" type="string"/>
            <column name="status" type="numeric"/>
            <column name="subscription_plan" type="string"/>
            <column name="subscription_start" type="date"/>
            <column name="subscription_end" type="date"/>
            <column name="contact_email" type="string"/>
            <column name="contact_phone" type="string"/>
            <column name="is_system" type="boolean"/>
            <column name="metadata" type="string"/>
            <column name="version" type="numeric"/>
            <column name="created_by" type="string"/>
            <column name="created_at" type="date"/>
            <column name="updated_by" type="string"/>
            <column name="updated_at" type="date"/>
            <column name="is_deleted" type="boolean"/>
            <!-- jhipster-needle-liquibase-add-loadcolumn - JHipster (and/or extensions) can add load columns here -->
        </loadData>
    </changeSet>
</databaseChangeLog>

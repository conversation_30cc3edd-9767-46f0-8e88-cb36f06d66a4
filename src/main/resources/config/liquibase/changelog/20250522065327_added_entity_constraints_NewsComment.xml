<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
  xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
  <!--
      Added the constraints for entity NewsComment.
  -->
  <changeSet id="20250522065327-2" author="jhipster">

    <addForeignKeyConstraint baseColumnNames="news_id"
                             baseTableName="news_comment"
                             constraintName="fk_news_comment_news_id"
                             referencedColumnNames="id"
                             referencedTableName="news"
    />

    <addForeignKeyConstraint baseColumnNames="parent_id"
                             baseTableName="news_comment"
                             constraintName="fk_news_comment_parent_id"
                             referencedColumnNames="id"
                             referencedTableName="news_comment"
    />

    <addForeignKeyConstraint baseColumnNames="commenter_id"
                             baseTableName="news_comment"
                             constraintName="fk_news_comment_commenter_id"
                             referencedColumnNames="id"
                             referencedTableName="employee"
    />
  </changeSet>
</databaseChangeLog>

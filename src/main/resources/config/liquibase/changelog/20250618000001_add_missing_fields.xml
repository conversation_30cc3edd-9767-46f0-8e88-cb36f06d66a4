<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd
                        http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd">

    <!--
        Add missing tenant_id and metadata fields to entities
    -->
    
    <!-- Add tenant_id field to News table -->
    <changeSet id="20250618000001-1" author="developer">
        <addColumn tableName="news">
            <column name="tenant_id" type="bigint" remarks="租户 ID">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>

    <!-- Add tenant_id field to NewsCategory table -->
    <changeSet id="20250618000001-2" author="developer">
        <addColumn tableName="news_category">
            <column name="tenant_id" type="bigint" remarks="租户 ID">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>

    <!-- Add tenant_id field to NewsComment table -->
    <changeSet id="20250618000001-3" author="developer">
        <addColumn tableName="news_comment">
            <column name="tenant_id" type="bigint" remarks="租户 ID">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>

    <!-- Add tenant_id field to NewsAttachment table -->
    <changeSet id="20250618000001-4" author="developer">
        <addColumn tableName="news_attachment">
            <column name="tenant_id" type="bigint" remarks="租户 ID">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>

    <!-- Add tenant_id field to NewsReport table -->
    <changeSet id="20250618000001-5" author="developer">
        <addColumn tableName="news_report">
            <column name="tenant_id" type="bigint" remarks="租户 ID">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>

    <!-- Add tenant_id field to NewsReadRecord table -->
    <changeSet id="20250618000001-6" author="developer">
        <addColumn tableName="news_read_record">
            <column name="tenant_id" type="bigint" remarks="租户 ID">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>

    <!-- Add tenant_id field to NewsLike table -->
    <changeSet id="20250618000001-7" author="developer">
        <addColumn tableName="news_like">
            <column name="tenant_id" type="bigint" remarks="租户 ID">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>

    <!-- Add tenant_id field to Tag table -->
    <changeSet id="20250618000001-8" author="developer">
        <addColumn tableName="tag">
            <column name="tenant_id" type="bigint" remarks="租户 ID">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>

    <!-- Add tenant_id field to TagCategory table -->
    <changeSet id="20250618000001-9" author="developer">
        <addColumn tableName="tag_category">
            <column name="tenant_id" type="bigint" remarks="租户 ID">
                <constraints nullable="false"/>
            </column>
        </addColumn>
    </changeSet>

    <!-- Add metadata field to AuditLog table -->
    <changeSet id="20250618000001-10" author="developer">
        <addColumn tableName="audit_log">
            <column name="metadata" type="${clobType}" remarks="扩展元数据">
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>

    <!-- Add metadata field to Tag table -->
    <changeSet id="20250618000001-11" author="developer">
        <addColumn tableName="tag">
            <column name="metadata" type="${clobType}" remarks="扩展元数据">
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>

    <!-- Add metadata field to TagCategory table -->
    <changeSet id="20250618000001-12" author="developer">
        <addColumn tableName="tag_category">
            <column name="metadata" type="${clobType}" remarks="扩展元数据">
                <constraints nullable="true"/>
            </column>
        </addColumn>
    </changeSet>

</databaseChangeLog>

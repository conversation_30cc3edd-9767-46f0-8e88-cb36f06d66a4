<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
  xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">

  <!--
      Added the entity NewsComment.
  -->
  <changeSet id="20250522065327-1" author="jhipster">
    <createTable tableName="news_comment">
      <column name="id" type="bigint" autoIncrement="true" startWith="1500">
        <constraints primaryKey="true" nullable="false"/>
      </column>
      <column name="status" type="varchar(255)" remarks="状态">
        <constraints nullable="false"/>
      </column>
      <column name="sort_order" type="integer" remarks="排序序号">
        <constraints nullable="true"/>
      </column>
      <column name="content" type="varchar(512)" remarks="评论内容">
        <constraints nullable="false"/>
      </column>
      <column name="like_count" type="integer" remarks="点赞数">
        <constraints nullable="true"/>
      </column>
      <column name="metadata" type="${clobType}" remarks="扩展元数据（JSONB）">
        <constraints nullable="true"/>
      </column>
      <column name="version" type="integer" remarks="乐观锁版本">
        <constraints nullable="false"/>
      </column>
      <column name="created_by" type="varchar(255)" remarks="创建者">
        <constraints nullable="true"/>
      </column>
      <column name="created_at" type="${datetimeType}" remarks="创建时间">
        <constraints nullable="false"/>
      </column>
      <column name="updated_by" type="varchar(255)" remarks="更新者">
        <constraints nullable="true"/>
      </column>
      <column name="updated_at" type="${datetimeType}" remarks="更新时间">
        <constraints nullable="false"/>
      </column>
      <column name="is_deleted" type="boolean" remarks="软删除标志">
        <constraints nullable="false"/>
      </column>
      <column name="news_id" type="bigint">
        <constraints nullable="true"/>
      </column>
      <column name="parent_id" type="bigint">
        <constraints nullable="true"/>
      </column>
      <column name="commenter_id" type="bigint">
        <constraints nullable="true"/>
      </column>
      <!-- jhipster-needle-liquibase-add-column - JHipster will add columns here -->
    </createTable>
    <dropDefaultValue tableName="news_comment" columnName="created_at" columnDataType="${datetimeType}"/>
    <dropDefaultValue tableName="news_comment" columnName="updated_at" columnDataType="${datetimeType}"/>
  </changeSet>

  <!-- jhipster-needle-liquibase-add-changeset - JHipster will add changesets here -->

  <!--
      Load sample data generated with Faker.js
      - This data can be easily edited using a CSV editor (or even MS Excel) and
        is located in the 'src/main/resources/config/liquibase/fake-data' directory
      - By default this data is applied when running with the JHipster 'dev' profile.
        This can be customized by adding or removing 'faker' in the 'spring.liquibase.contexts'
        Spring Boot configuration key.
  -->
  <changeSet id="20250522065327-1-data" author="jhipster" context="faker">
    <loadData
      file="config/liquibase/fake-data/news_comment.csv"
      separator=";"
      tableName="news_comment"
      usePreparedStatements="true">
      <column name="id" type="numeric"/>
      <column name="status" type="string"/>
      <column name="sort_order" type="numeric"/>
      <column name="content" type="string"/>
      <column name="like_count" type="numeric"/>
      <column name="metadata" type="string"/>
      <column name="version" type="numeric"/>
      <column name="created_by" type="string"/>
      <column name="created_at" type="date"/>
      <column name="updated_by" type="string"/>
      <column name="updated_at" type="date"/>
      <column name="is_deleted" type="boolean"/>
      <!-- jhipster-needle-liquibase-add-loadcolumn - JHipster (and/or extensions) can add load columns here -->
    </loadData>
  </changeSet>
</databaseChangeLog>

<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd
                        http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd">

    <!--
        Added the entity Employee.
    -->
    <changeSet id="**************-1" author="jhipster">
        <createTable tableName="employee" remarks="员工（Employee）实体">
            <column name="id" type="bigint" remarks="主键 ID" autoIncrement="true" startWith="1500">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="tenant_id" type="bigint" remarks="租户 ID">
                <constraints nullable="false" />
            </column>
            <column name="username" type="varchar(64)" remarks="登录用户名">
                <constraints nullable="false" />
            </column>
            <column name="password" type="varchar(128)" remarks="登录密码（加密存储）">
                <constraints nullable="false" />
            </column>
            <column name="salt" type="varchar(32)" remarks="密码盐值">
                <constraints nullable="true" />
            </column>
            <column name="real_name" type="varchar(128)" remarks="真实姓名">
                <constraints nullable="false" />
            </column>
            <column name="avatar" type="varchar(255)" remarks="头像">
                <constraints nullable="false" />
            </column>
            <column name="email" type="varchar(128)" remarks="邮箱地址">
                <constraints nullable="false" />
            </column>
            <column name="phone" type="varchar(32)" remarks="手机号">
                <constraints nullable="true" />
            </column>
            <column name="gender" type="varchar(255)" remarks="性别">
                <constraints nullable="true" />
            </column>
            <column name="birth_date" type="date" remarks="生日">
                <constraints nullable="true" />
            </column>
            <column name="id_card" type="varchar(18)" remarks="身份证号">
                <constraints nullable="true" />
            </column>
            <column name="employee_no" type="varchar(64)" remarks="员工编号（工号）">
                <constraints nullable="true" />
            </column>
            <column name="status" type="varchar(255)" remarks="员工状态">
                <constraints nullable="false" />
            </column>
            <column name="hire_date" type="date" remarks="入职日期">
                <constraints nullable="true" />
            </column>
            <column name="leave_date" type="date" remarks="离职日期">
                <constraints nullable="true" />
            </column>
            <column name="metadata" type="varchar(255)" remarks="扩展元数据">
                <constraints nullable="true" />
            </column>
            <column name="version" type="integer" remarks="乐观锁版本">
                <constraints nullable="false" />
            </column>
            <column name="created_by" type="varchar(255)" remarks="创建者">
                <constraints nullable="true" />
            </column>
            <column name="created_at" type="${datetimeType}" remarks="创建时间">
                <constraints nullable="false" />
            </column>
            <column name="updated_by" type="varchar(255)" remarks="更新者">
                <constraints nullable="true" />
            </column>
            <column name="updated_at" type="${datetimeType}" remarks="更新时间">
                <constraints nullable="false" />
            </column>
            <column name="is_deleted" type="boolean" remarks="软删除标志">
                <constraints nullable="false" />
            </column>
            <column name="last_login_time" type="${datetimeType}" remarks="最后登录时间">
                <constraints nullable="true" />
            </column>
            <column name="last_login_ip" type="varchar(64)" remarks="最后登录IP">
                <constraints nullable="true" />
            </column>
            <column name="login_failure_count" type="integer" remarks="登录失败次数">
                <constraints nullable="true" />
            </column>
            <column name="account_locked_time" type="${datetimeType}" remarks="账号锁定时间">
                <constraints nullable="true" />
            </column>
            <column name="password_changed_time" type="${datetimeType}" remarks="密码修改时间">
                <constraints nullable="true" />
            </column>
            <column name="password_expired_time" type="${datetimeType}" remarks="密码过期时间">
                <constraints nullable="true" />
            </column>
            <column name="is_first_login" type="boolean" remarks="是否首次登录">
                <constraints nullable="false" />
            </column>
            <column name="force_change_password" type="boolean" remarks="是否强制修改密码">
                <constraints nullable="false" />
            </column>
            <column name="wechat_open_id" type="varchar(64)" remarks="微信OpenID">
                <constraints nullable="true" />
            </column>
            <column name="wechat_union_id" type="varchar(64)" remarks="微信UnionID">
                <constraints nullable="true" />
            </column>
            <!-- jhipster-needle-liquibase-add-column - JHipster will add columns here -->
        </createTable>
        <dropDefaultValue tableName="employee" columnName="created_at" columnDataType="${datetimeType}"/>
        <dropDefaultValue tableName="employee" columnName="updated_at" columnDataType="${datetimeType}"/>
        <dropDefaultValue tableName="employee" columnName="last_login_time" columnDataType="${datetimeType}"/>
        <dropDefaultValue tableName="employee" columnName="account_locked_time" columnDataType="${datetimeType}"/>
        <dropDefaultValue tableName="employee" columnName="password_changed_time" columnDataType="${datetimeType}"/>
        <dropDefaultValue tableName="employee" columnName="password_expired_time" columnDataType="${datetimeType}"/>
    </changeSet>

    <!-- jhipster-needle-liquibase-add-changeset - JHipster will add changesets here -->

    <!--
        Load sample data generated with Faker.js
        - This data can be easily edited using a CSV editor (or even MS Excel) and
          is located in the 'src/main/resources/config/liquibase/fake-data' directory
        - By default this data is applied when running with the JHipster 'dev' profile.
          This can be customized by adding or removing 'faker' in the 'spring.liquibase.contexts'
          Spring Boot configuration key.
    -->
    <changeSet id="**************-1-data" author="jhipster" context="faker">
        <loadData
                  file="config/liquibase/fake-data/employee.csv"
                  separator=";"
                  tableName="employee"
                  usePreparedStatements="true">
            <column name="id" type="numeric"/>
            <column name="tenant_id" type="numeric"/>
            <column name="username" type="string"/>
            <column name="password" type="string"/>
            <column name="salt" type="string"/>
            <column name="real_name" type="string"/>
            <column name="avatar" type="string"/>
            <column name="email" type="string"/>
            <column name="phone" type="string"/>
            <column name="gender" type="string"/>
            <column name="birth_date" type="date"/>
            <column name="id_card" type="string"/>
            <column name="employee_no" type="string"/>
            <column name="status" type="string"/>
            <column name="hire_date" type="date"/>
            <column name="leave_date" type="date"/>
            <column name="metadata" type="string"/>
            <column name="version" type="numeric"/>
            <column name="created_by" type="string"/>
            <column name="created_at" type="date"/>
            <column name="updated_by" type="string"/>
            <column name="updated_at" type="date"/>
            <column name="is_deleted" type="boolean"/>
            <column name="last_login_time" type="date"/>
            <column name="last_login_ip" type="string"/>
            <column name="login_failure_count" type="numeric"/>
            <column name="account_locked_time" type="date"/>
            <column name="password_changed_time" type="date"/>
            <column name="password_expired_time" type="date"/>
            <column name="is_first_login" type="boolean"/>
            <column name="force_change_password" type="boolean"/>
            <column name="wechat_open_id" type="string"/>
            <column name="wechat_union_id" type="string"/>
            <!-- jhipster-needle-liquibase-add-loadcolumn - JHipster (and/or extensions) can add load columns here -->
        </loadData>
    </changeSet>
</databaseChangeLog>

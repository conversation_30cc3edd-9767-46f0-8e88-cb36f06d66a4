<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd
                        http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd">

    <!--
        Add is_available column to permission table.
        为permission表添加is_available字段。
    -->
    <changeSet id="20250620140000-1" author="system">
        <addColumn tableName="permission">
            <column name="is_available" type="tinyint" defaultValue="1">
                <constraints nullable="false"/>
            </column>
        </addColumn>
        
        <!-- Add comment for the new column -->
        <sql>
            ALTER TABLE permission MODIFY COLUMN is_available tinyint NOT NULL DEFAULT '1' COMMENT '是否租户可用 0、否 1、是'
        </sql>
        
        <!-- Update existing records to set is_available = 1 -->
        <update tableName="permission">
            <column name="is_available" value="1"/>
            <where>is_available IS NULL</where>
        </update>
    </changeSet>

</databaseChangeLog>

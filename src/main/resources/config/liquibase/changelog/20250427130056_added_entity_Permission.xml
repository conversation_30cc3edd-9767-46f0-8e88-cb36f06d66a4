<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd
                        http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd">

    <!--
        Added the entity Permission.
    -->
    <changeSet id="20250427130056-1" author="jhipster">
        <createTable tableName="permission" remarks="权限（Permission）实体">
            <column name="id" type="bigint" remarks="主键 ID" autoIncrement="true" startWith="1500">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="tenant_id" type="bigint" remarks="租户 ID">
                <constraints nullable="false" />
            </column>
            <column name="service_name" type="varchar(64)" remarks="服务名称">
                <constraints nullable="false" />
            </column>
            <column name="code" type="varchar(64)" remarks="权限编码">
                <constraints nullable="false" />
            </column>
            <column name="name" type="varchar(128)" remarks="权限名称">
                <constraints nullable="false" />
            </column>
            <column name="resource_type" type="varchar(255)" remarks="资源类型">
                <constraints nullable="false" />
            </column>
            <column name="url_pattern" type="varchar(256)" remarks="URL 模式">
                <constraints nullable="true" />
            </column>
            <column name="method" type="varchar(16)" remarks="HTTP 方法">
                <constraints nullable="true" />
            </column>
            <column name="frontend_route" type="varchar(128)" remarks="前端路由">
                <constraints nullable="true" />
            </column>
            <column name="backend_url" type="varchar(128)" remarks="后端接口">
                <constraints nullable="true" />
            </column>
            <column name="icon" type="varchar(64)" remarks="图标">
                <constraints nullable="true" />
            </column>
            <column name="sort_order" type="integer" remarks="排序">
                <constraints nullable="true" />
            </column>
            <column name="component" type="varchar(255)" remarks="组件路径">
                <constraints nullable="true" />
            </column>
            <column name="redirect" type="varchar(255)" remarks="重定向">
                <constraints nullable="true" />
            </column>
            <column name="description" type="varchar(255)" remarks="描述">
                <constraints nullable="true" />
            </column>
            <column name="metadata" type="${clobType}" remarks="扩展元数据">
                <constraints nullable="true" />
            </column>
            <column name="version" type="integer" remarks="乐观锁版本">
                <constraints nullable="false" />
            </column>
            <column name="created_by" type="varchar(255)" remarks="创建者">
                <constraints nullable="true" />
            </column>
            <column name="created_at" type="${datetimeType}" remarks="创建时间">
                <constraints nullable="false" />
            </column>
            <column name="updated_by" type="varchar(255)" remarks="更新者">
                <constraints nullable="true" />
            </column>
            <column name="updated_at" type="${datetimeType}" remarks="更新时间">
                <constraints nullable="false" />
            </column>
            <column name="is_deleted" type="boolean" remarks="软删除标志">
                <constraints nullable="false" />
            </column>
            <!-- jhipster-needle-liquibase-add-column - JHipster will add columns here -->
        </createTable>
        <dropDefaultValue tableName="permission" columnName="created_at" columnDataType="${datetimeType}"/>
        <dropDefaultValue tableName="permission" columnName="updated_at" columnDataType="${datetimeType}"/>
    </changeSet>

    <!-- jhipster-needle-liquibase-add-changeset - JHipster will add changesets here -->

    <!--
        Load sample data generated with Faker.js
        - This data can be easily edited using a CSV editor (or even MS Excel) and
          is located in the 'src/main/resources/config/liquibase/fake-data' directory
        - By default this data is applied when running with the JHipster 'dev' profile.
          This can be customized by adding or removing 'faker' in the 'spring.liquibase.contexts'
          Spring Boot configuration key.
    -->
    <changeSet id="20250427130056-1-data" author="jhipster" context="faker">
        <loadData
                  file="config/liquibase/fake-data/permission.csv"
                  separator=";"
                  tableName="permission"
                  usePreparedStatements="true">
            <column name="id" type="numeric"/>
            <column name="tenant_id" type="numeric"/>
            <column name="service_name" type="string"/>
            <column name="code" type="string"/>
            <column name="name" type="string"/>
            <column name="resource_type" type="string"/>
            <column name="url_pattern" type="string"/>
            <column name="method" type="string"/>
            <column name="frontend_route" type="string"/>
            <column name="backend_url" type="string"/>
            <column name="icon" type="string"/>
            <column name="sort_order" type="numeric"/>
            <column name="component" type="string"/>
            <column name="redirect" type="string"/>
            <column name="description" type="string"/>
            <column name="metadata" type="string"/>
            <column name="version" type="numeric"/>
            <column name="created_by" type="string"/>
            <column name="created_at" type="date"/>
            <column name="updated_by" type="string"/>
            <column name="updated_at" type="date"/>
            <column name="is_deleted" type="boolean"/>
            <!-- jhipster-needle-liquibase-add-loadcolumn - JHipster (and/or extensions) can add load columns here -->
        </loadData>
    </changeSet>
</databaseChangeLog>

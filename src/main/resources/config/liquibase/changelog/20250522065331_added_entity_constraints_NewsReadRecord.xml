<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
  xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
  <!--
      Added the constraints for entity NewsReadRecord.
  -->
  <changeSet id="20250522065331-2" author="jhipster">

    <addForeignKeyConstraint baseColumnNames="news_id"
                             baseTableName="news_read_record"
                             constraintName="fk_news_read_record_news_id"
                             referencedColumnNames="id"
                             referencedTableName="news"
    />

    <addForeignKeyConstraint baseColumnNames="reader_id"
                             baseTableName="news_read_record"
                             constraintName="fk_news_read_record_reader_id"
                             referencedColumnNames="id"
                             referencedTableName="employee"
    />
  </changeSet>
</databaseChangeLog>

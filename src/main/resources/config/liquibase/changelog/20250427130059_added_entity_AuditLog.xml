<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd
                        http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd">

    <!--
        Added the entity AuditLog.
    -->
    <changeSet id="20250427130059-1" author="jhipster">
        <createTable tableName="audit_log" remarks="审计日志（AuditLog）实体">
            <column name="id" type="bigint" remarks="主键 ID" autoIncrement="true" startWith="1500">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="tenant_id" type="bigint" remarks="租户 ID">
                <constraints nullable="false" />
            </column>
            <column name="entity_name" type="varchar(64)" remarks="实体名称">
                <constraints nullable="false" />
            </column>
            <column name="entity_id" type="bigint" remarks="实体 ID">
                <constraints nullable="false" />
            </column>
            <column name="operation" type="varchar(16)" remarks="操作类型">
                <constraints nullable="false" />
            </column>
            <column name="operator" type="varchar(64)" remarks="操作者">
                <constraints nullable="true" />
            </column>
            <column name="timestamp" type="${datetimeType}" remarks="操作时间">
                <constraints nullable="false" />
            </column>
            <column name="diff" type="varchar(255)" remarks="差异 (JSON)">
                <constraints nullable="true" />
            </column>
            <column name="version" type="integer" remarks="乐观锁版本">
                <constraints nullable="false" />
            </column>
            <column name="created_by" type="varchar(255)" remarks="创建者">
                <constraints nullable="true" />
            </column>
            <column name="created_at" type="${datetimeType}" remarks="创建时间">
                <constraints nullable="false" />
            </column>
            <column name="updated_by" type="varchar(255)" remarks="更新者">
                <constraints nullable="true" />
            </column>
            <column name="updated_at" type="${datetimeType}" remarks="更新时间">
                <constraints nullable="false" />
            </column>
            <column name="is_deleted" type="boolean" remarks="软删除标志">
                <constraints nullable="false" />
            </column>
            <!-- jhipster-needle-liquibase-add-column - JHipster will add columns here -->
        </createTable>
        <dropDefaultValue tableName="audit_log" columnName="timestamp" columnDataType="${datetimeType}"/>
        <dropDefaultValue tableName="audit_log" columnName="created_at" columnDataType="${datetimeType}"/>
        <dropDefaultValue tableName="audit_log" columnName="updated_at" columnDataType="${datetimeType}"/>
    </changeSet>

    <!-- jhipster-needle-liquibase-add-changeset - JHipster will add changesets here -->

    <!--
        Load sample data generated with Faker.js
        - This data can be easily edited using a CSV editor (or even MS Excel) and
          is located in the 'src/main/resources/config/liquibase/fake-data' directory
        - By default this data is applied when running with the JHipster 'dev' profile.
          This can be customized by adding or removing 'faker' in the 'spring.liquibase.contexts'
          Spring Boot configuration key.
    -->
    <changeSet id="20250427130059-1-data" author="jhipster" context="faker">
        <loadData
                  file="config/liquibase/fake-data/audit_log.csv"
                  separator=";"
                  tableName="audit_log"
                  usePreparedStatements="true">
            <column name="id" type="numeric"/>
            <column name="tenant_id" type="numeric"/>
            <column name="entity_name" type="string"/>
            <column name="entity_id" type="numeric"/>
            <column name="operation" type="string"/>
            <column name="operator" type="string"/>
            <column name="timestamp" type="date"/>
            <column name="diff" type="string"/>
            <column name="version" type="numeric"/>
            <column name="created_by" type="string"/>
            <column name="created_at" type="date"/>
            <column name="updated_by" type="string"/>
            <column name="updated_at" type="date"/>
            <column name="is_deleted" type="boolean"/>
            <!-- jhipster-needle-liquibase-add-loadcolumn - JHipster (and/or extensions) can add load columns here -->
        </loadData>
    </changeSet>
</databaseChangeLog>

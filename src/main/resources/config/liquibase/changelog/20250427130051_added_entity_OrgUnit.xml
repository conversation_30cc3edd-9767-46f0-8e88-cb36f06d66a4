<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd
                        http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd">

    <!--
        Added the entity OrgUnit.
    -->
    <changeSet id="20250427130051-1" author="jhipster">
        <createTable tableName="org_unit" remarks="组织单元（OrgUnit）实体\n管理公司、部门、事业群、团队等组织层级，并维护树形结构">
            <column name="id" type="bigint" remarks="主键 ID" autoIncrement="true" startWith="1500">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="tenant_id" type="bigint" remarks="租户 ID，标识不同租户的数据隔离">
                <constraints nullable="false" />
            </column>
            <column name="name" type="varchar(128)" remarks="组织单位名称">
                <constraints nullable="false" />
            </column>
            <column name="code" type="varchar(64)" remarks="唯一编码，用于外部系统对接或导入映射">
                <constraints nullable="false" />
            </column>
            <column name="type" type="varchar(255)" remarks="组织单元类型">
                <constraints nullable="false" />
            </column>
            <column name="level" type="integer" remarks="层级深度，根节点为 1">
                <constraints nullable="false" />
            </column>
            <column name="status" type="integer" remarks="状态：1=启用，0=禁用">
                <constraints nullable="false" />
            </column>
            <column name="sort_order" type="integer" remarks="排序序号">
                <constraints nullable="true" />
            </column>
            <column name="description" type="varchar(255)" remarks="描述信息">
                <constraints nullable="true" />
            </column>
            <column name="metadata" type="${clobType}" remarks="扩展元数据（JSON）">
                <constraints nullable="true" />
            </column>
            <column name="version" type="integer" remarks="乐观锁版本">
                <constraints nullable="false" />
            </column>
            <column name="created_by" type="varchar(255)" remarks="创建者">
                <constraints nullable="true" />
            </column>
            <column name="created_at" type="${datetimeType}" remarks="创建时间">
                <constraints nullable="false" />
            </column>
            <column name="updated_by" type="varchar(255)" remarks="更新者">
                <constraints nullable="true" />
            </column>
            <column name="updated_at" type="${datetimeType}" remarks="更新时间">
                <constraints nullable="false" />
            </column>
            <column name="is_deleted" type="boolean" remarks="软删除标志">
                <constraints nullable="false" />
            </column>
            <column name="parent_id" type="bigint">
                <constraints nullable="true" />
            </column>
            <!-- jhipster-needle-liquibase-add-column - JHipster will add columns here -->
        </createTable>
        <dropDefaultValue tableName="org_unit" columnName="created_at" columnDataType="${datetimeType}"/>
        <dropDefaultValue tableName="org_unit" columnName="updated_at" columnDataType="${datetimeType}"/>
    </changeSet>

    <!-- jhipster-needle-liquibase-add-changeset - JHipster will add changesets here -->

    <!--
        Load sample data generated with Faker.js
        - This data can be easily edited using a CSV editor (or even MS Excel) and
          is located in the 'src/main/resources/config/liquibase/fake-data' directory
        - By default this data is applied when running with the JHipster 'dev' profile.
          This can be customized by adding or removing 'faker' in the 'spring.liquibase.contexts'
          Spring Boot configuration key.
    -->
    <changeSet id="20250427130051-1-data" author="jhipster" context="faker">
        <loadData
                  file="config/liquibase/fake-data/org_unit.csv"
                  separator=";"
                  tableName="org_unit"
                  usePreparedStatements="true">
            <column name="id" type="numeric"/>
            <column name="tenant_id" type="numeric"/>
            <column name="name" type="string"/>
            <column name="code" type="string"/>
            <column name="type" type="string"/>
            <column name="level" type="numeric"/>
            <column name="status" type="numeric"/>
            <column name="sort_order" type="numeric"/>
            <column name="description" type="string"/>
            <column name="metadata" type="string"/>
            <column name="version" type="numeric"/>
            <column name="created_by" type="string"/>
            <column name="created_at" type="date"/>
            <column name="updated_by" type="string"/>
            <column name="updated_at" type="date"/>
            <column name="is_deleted" type="boolean"/>
            <!-- jhipster-needle-liquibase-add-loadcolumn - JHipster (and/or extensions) can add load columns here -->
        </loadData>
    </changeSet>
</databaseChangeLog>

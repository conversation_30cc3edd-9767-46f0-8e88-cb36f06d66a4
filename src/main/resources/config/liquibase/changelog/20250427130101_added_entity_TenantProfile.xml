<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd
                        http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd">

    <!--
        Added the entity TenantProfile.
    -->
    <changeSet id="20250427130101-1" author="jhipster">
        <createTable tableName="tenant_profile" remarks="租户详情（TenantProfile）实体">
            <column name="id" type="bigint" remarks="主键 ID" autoIncrement="true" startWith="1500">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="registration_number" type="varchar(255)" remarks="工商注册号">
                <constraints nullable="true" />
            </column>
            <column name="registration_date" type="date" remarks="注册日期">
                <constraints nullable="true" />
            </column>
            <column name="registered_capital" type="decimal(21,2)" remarks="注册资本">
                <constraints nullable="true" />
            </column>
            <column name="company_type" type="varchar(255)" remarks="公司类型">
                <constraints nullable="true" />
            </column>
            <column name="business_scope" type="varchar(255)" remarks="经营范围">
                <constraints nullable="true" />
            </column>
            <column name="industry" type="varchar(255)" remarks="所属行业">
                <constraints nullable="true" />
            </column>
            <column name="tax_registration_number" type="varchar(255)" remarks="税务登记号">
                <constraints nullable="true" />
            </column>
            <column name="organization_code" type="varchar(255)" remarks="组织机构代码">
                <constraints nullable="true" />
            </column>
            <column name="registered_address" type="varchar(255)" remarks="注册地址">
                <constraints nullable="true" />
            </column>
            <column name="postal_code" type="varchar(255)" remarks="邮政编码">
                <constraints nullable="true" />
            </column>
            <column name="website" type="varchar(255)" remarks="官网">
                <constraints nullable="true" />
            </column>
            <column name="fax" type="varchar(255)" remarks="传真">
                <constraints nullable="true" />
            </column>
            <column name="contact_person" type="varchar(255)" remarks="联系人">
                <constraints nullable="true" />
            </column>
            <column name="contact_mobile" type="varchar(255)" remarks="联系人手机">
                <constraints nullable="true" />
            </column>
            <column name="contact_email" type="varchar(255)" remarks="联系人邮箱">
                <constraints nullable="true" />
            </column>
            <column name="bank_name" type="varchar(255)" remarks="开户行">
                <constraints nullable="true" />
            </column>
            <column name="bank_account" type="varchar(255)" remarks="银行账号">
                <constraints nullable="true" />
            </column>
            <column name="business_license_path" type="varchar(255)" remarks="营业执照路径">
                <constraints nullable="true" />
            </column>
            <column name="legal_person" type="varchar(255)" remarks="法人代表">
                <constraints nullable="true" />
            </column>
            <column name="legal_person_id" type="varchar(255)" remarks="法人证件号">
                <constraints nullable="true" />
            </column>
            <column name="metadata" type="${clobType}" remarks="扩展元数据">
                <constraints nullable="true" />
            </column>
            <column name="version" type="integer" remarks="乐观锁版本">
                <constraints nullable="false" />
            </column>
            <column name="created_by" type="varchar(255)" remarks="创建者">
                <constraints nullable="true" />
            </column>
            <column name="created_at" type="${datetimeType}" remarks="创建时间">
                <constraints nullable="false" />
            </column>
            <column name="updated_by" type="varchar(255)" remarks="更新者">
                <constraints nullable="true" />
            </column>
            <column name="updated_at" type="${datetimeType}" remarks="更新时间">
                <constraints nullable="false" />
            </column>
            <column name="is_deleted" type="boolean" remarks="软删除标志">
                <constraints nullable="false" />
            </column>
            <column name="tenant_id" type="bigint">
                <constraints nullable="true" />
            </column>
            <!-- jhipster-needle-liquibase-add-column - JHipster will add columns here -->
        </createTable>
        <dropDefaultValue tableName="tenant_profile" columnName="created_at" columnDataType="${datetimeType}"/>
        <dropDefaultValue tableName="tenant_profile" columnName="updated_at" columnDataType="${datetimeType}"/>
    </changeSet>

    <!-- jhipster-needle-liquibase-add-changeset - JHipster will add changesets here -->

    <!--
        Load sample data generated with Faker.js
        - This data can be easily edited using a CSV editor (or even MS Excel) and
          is located in the 'src/main/resources/config/liquibase/fake-data' directory
        - By default this data is applied when running with the JHipster 'dev' profile.
          This can be customized by adding or removing 'faker' in the 'spring.liquibase.contexts'
          Spring Boot configuration key.
    -->
    <changeSet id="20250427130101-1-data" author="jhipster" context="faker">
        <loadData
                  file="config/liquibase/fake-data/tenant_profile.csv"
                  separator=";"
                  tableName="tenant_profile"
                  usePreparedStatements="true">
            <column name="id" type="numeric"/>
            <column name="registration_number" type="string"/>
            <column name="registration_date" type="date"/>
            <column name="registered_capital" type="numeric"/>
            <column name="company_type" type="string"/>
            <column name="business_scope" type="string"/>
            <column name="industry" type="string"/>
            <column name="tax_registration_number" type="string"/>
            <column name="organization_code" type="string"/>
            <column name="registered_address" type="string"/>
            <column name="postal_code" type="string"/>
            <column name="website" type="string"/>
            <column name="fax" type="string"/>
            <column name="contact_person" type="string"/>
            <column name="contact_mobile" type="string"/>
            <column name="contact_email" type="string"/>
            <column name="bank_name" type="string"/>
            <column name="bank_account" type="string"/>
            <column name="business_license_path" type="string"/>
            <column name="legal_person" type="string"/>
            <column name="legal_person_id" type="string"/>
            <column name="metadata" type="string"/>
            <column name="version" type="numeric"/>
            <column name="created_by" type="string"/>
            <column name="created_at" type="date"/>
            <column name="updated_by" type="string"/>
            <column name="updated_at" type="date"/>
            <column name="is_deleted" type="boolean"/>
            <!-- jhipster-needle-liquibase-add-loadcolumn - JHipster (and/or extensions) can add load columns here -->
        </loadData>
    </changeSet>
</databaseChangeLog>

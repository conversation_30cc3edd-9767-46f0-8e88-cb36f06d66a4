<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
  xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">

  <!--
      Added the entity News.
  -->
  <changeSet id="20250522065326-1" author="jhipster">
    <createTable tableName="news">
      <column name="id" type="bigint" autoIncrement="true" startWith="1500">
        <constraints primaryKey="true" nullable="false"/>
      </column>
      <column name="status" type="varchar(255)" remarks="状态">
        <constraints nullable="false"/>
      </column>
      <column name="sort_order" type="integer" remarks="排序序号">
        <constraints nullable="true"/>
      </column>
      <column name="subtitle" type="varchar(255)" remarks="副标题">
        <constraints nullable="true"/>
      </column>
      <column name="title" type="varchar(255)" remarks="标题">
        <constraints nullable="false"/>
      </column>
      <column name="summary" type="varchar(255)" remarks="摘要">
        <constraints nullable="true"/>
      </column>
      <column name="keywords" type="varchar(255)" remarks="关键词（用于SEO，全局搜索）">
        <constraints nullable="true"/>
      </column>
      <column name="content" type="${clobType}" remarks="正文内容">
        <constraints nullable="true"/>
      </column>
      <column name="publish_date" type="${datetimeType}" remarks="发布时间">
        <constraints nullable="true"/>
      </column>
      <column name="published_at" type="${datetimeType}" remarks="正式发布时戳">
        <constraints nullable="true"/>
      </column>
      <column name="view_count" type="integer" remarks="浏览量">
        <constraints nullable="true"/>
      </column>
      <column name="like_count" type="integer" remarks="点赞数">
        <constraints nullable="true"/>
      </column>
      <column name="comment_count" type="integer" remarks="评论数">
        <constraints nullable="true"/>
      </column>
      <column name="share_count" type="integer" remarks="分享数">
        <constraints nullable="true"/>
      </column>
      <column name="cover_image_url" type="varchar(255)" remarks="封面图 URL">
        <constraints nullable="true"/>
      </column>
      <column name="is_sticky" type="boolean" remarks="是否置顶">
        <constraints nullable="true"/>
      </column>
      <column name="sticky_start_time" type="${datetimeType}" remarks="置顶开始时间">
        <constraints nullable="true"/>
      </column>
      <column name="sticky_end_time" type="${datetimeType}" remarks="置顶结束时间">
        <constraints nullable="true"/>
      </column>
      <column name="metadata" type="${clobType}" remarks="扩展元数据（JSONB）">
        <constraints nullable="true"/>
      </column>
      <column name="version" type="integer" remarks="乐观锁版本">
        <constraints nullable="false"/>
      </column>
      <column name="created_by" type="varchar(255)" remarks="创建者">
        <constraints nullable="true"/>
      </column>
      <column name="created_at" type="${datetimeType}" remarks="创建时间">
        <constraints nullable="false"/>
      </column>
      <column name="updated_by" type="varchar(255)" remarks="更新者">
        <constraints nullable="true"/>
      </column>
      <column name="updated_at" type="${datetimeType}" remarks="更新时间">
        <constraints nullable="false"/>
      </column>
      <column name="is_deleted" type="boolean" remarks="软删除标志">
        <constraints nullable="false"/>
      </column>
      <column name="category_id" type="bigint">
        <constraints nullable="true"/>
      </column>
      <column name="org_unit_id" type="bigint">
        <constraints nullable="true"/>
      </column>
      <column name="author_id" type="bigint">
        <constraints nullable="true"/>
      </column>
      <!-- jhipster-needle-liquibase-add-column - JHipster will add columns here -->
    </createTable>
    <dropDefaultValue tableName="news" columnName="publish_date" columnDataType="${datetimeType}"/>
    <dropDefaultValue tableName="news" columnName="published_at" columnDataType="${datetimeType}"/>
    <dropDefaultValue tableName="news" columnName="sticky_start_time" columnDataType="${datetimeType}"/>
    <dropDefaultValue tableName="news" columnName="sticky_end_time" columnDataType="${datetimeType}"/>
    <dropDefaultValue tableName="news" columnName="created_at" columnDataType="${datetimeType}"/>
    <dropDefaultValue tableName="news" columnName="updated_at" columnDataType="${datetimeType}"/>
  </changeSet>

  <changeSet id="20250522065326-1-relations" author="jhipster">

    <createTable tableName="news_tags">
      <column name="tags_id" type="bigint">
        <constraints nullable="false"/>
      </column>
      <column name="news_id" type="bigint">
        <constraints nullable="false"/>
      </column>
    </createTable>

    <addPrimaryKey columnNames="news_id, tags_id" tableName="news_tags"/>
  </changeSet>

  <!-- jhipster-needle-liquibase-add-changeset - JHipster will add changesets here -->

  <!--
      Load sample data generated with Faker.js
      - This data can be easily edited using a CSV editor (or even MS Excel) and
        is located in the 'src/main/resources/config/liquibase/fake-data' directory
      - By default this data is applied when running with the JHipster 'dev' profile.
        This can be customized by adding or removing 'faker' in the 'spring.liquibase.contexts'
        Spring Boot configuration key.
  -->
  <changeSet id="20250522065326-1-data" author="jhipster" context="faker">
    <loadData
      file="config/liquibase/fake-data/news.csv"
      separator=";"
      tableName="news"
      usePreparedStatements="true">
      <column name="id" type="numeric"/>
      <column name="status" type="string"/>
      <column name="sort_order" type="numeric"/>
      <column name="subtitle" type="string"/>
      <column name="title" type="string"/>
      <column name="summary" type="string"/>
      <column name="keywords" type="string"/>
      <column name="content" type="string"/>
      <column name="publish_date" type="date"/>
      <column name="published_at" type="date"/>
      <column name="view_count" type="numeric"/>
      <column name="like_count" type="numeric"/>
      <column name="comment_count" type="numeric"/>
      <column name="share_count" type="numeric"/>
      <column name="cover_image_url" type="string"/>
      <column name="is_sticky" type="boolean"/>
      <column name="sticky_start_time" type="date"/>
      <column name="sticky_end_time" type="date"/>
      <column name="metadata" type="string"/>
      <column name="version" type="numeric"/>
      <column name="created_by" type="string"/>
      <column name="created_at" type="date"/>
      <column name="updated_by" type="string"/>
      <column name="updated_at" type="date"/>
      <column name="is_deleted" type="boolean"/>
      <!-- jhipster-needle-liquibase-add-loadcolumn - JHipster (and/or extensions) can add load columns here -->
    </loadData>
  </changeSet>
</databaseChangeLog>

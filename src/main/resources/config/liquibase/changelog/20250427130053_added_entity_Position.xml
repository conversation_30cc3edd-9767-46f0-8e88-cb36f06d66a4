<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
  xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">

  <!--
      Added the entity Position.
  -->
  <changeSet id="20250427130053-1" author="jhipster">
    <createTable tableName="position" remarks="岗位/职位（Position）实体">
      <column name="id" type="bigint" remarks="主键 ID" autoIncrement="true" startWith="1500">
        <constraints primaryKey="true" nullable="false"/>
      </column>
      <column name="tenant_id" type="bigint" remarks="租户 ID">
        <constraints nullable="false"/>
      </column>
      <column name="code" type="varchar(64)" remarks="岗位编码">
        <constraints nullable="false" unique="true" uniqueConstraintName="ux_position_code"/>
      </column>
      <column name="name" type="varchar(128)" remarks="岗位名称">
        <constraints nullable="false"/>
      </column>
      <column name="level" type="integer" remarks="岗位级别">
        <constraints nullable="true"/>
      </column>
      <column name="category" type="varchar(255)" remarks="岗位分类">
        <constraints nullable="true"/>
      </column>
      <column name="description" type="varchar(255)" remarks="描述信息">
        <constraints nullable="true"/>
      </column>
      <column name="metadata" type="${clobType}" remarks="扩展元数据">
        <constraints nullable="true"/>
      </column>
      <column name="version" type="integer" remarks="乐观锁版本">
        <constraints nullable="false"/>
      </column>
      <column name="created_by" type="varchar(255)" remarks="创建者">
        <constraints nullable="true"/>
      </column>
      <column name="created_at" type="${datetimeType}" remarks="创建时间">
        <constraints nullable="false"/>
      </column>
      <column name="updated_by" type="varchar(255)" remarks="更新者">
        <constraints nullable="true"/>
      </column>
      <column name="updated_at" type="${datetimeType}" remarks="更新时间">
        <constraints nullable="false"/>
      </column>
      <column name="is_deleted" type="boolean" remarks="软删除标志">
        <constraints nullable="false"/>
      </column>
      <column name="org_unit_id" type="bigint">
        <constraints nullable="true"/>
      </column>
      <!-- jhipster-needle-liquibase-add-column - JHipster will add columns here -->
    </createTable>
    <dropDefaultValue tableName="position" columnName="created_at" columnDataType="${datetimeType}"/>
    <dropDefaultValue tableName="position" columnName="updated_at" columnDataType="${datetimeType}"/>
  </changeSet>

  <!-- jhipster-needle-liquibase-add-changeset - JHipster will add changesets here -->

  <!--
      Load sample data generated with Faker.js
      - This data can be easily edited using a CSV editor (or even MS Excel) and
        is located in the 'src/main/resources/config/liquibase/fake-data' directory
      - By default this data is applied when running with the JHipster 'dev' profile.
        This can be customized by adding or removing 'faker' in the 'spring.liquibase.contexts'
        Spring Boot configuration key.
  -->
  <changeSet id="20250427130053-1-data" author="jhipster" context="faker">
    <loadData
      file="config/liquibase/fake-data/position.csv"
      separator=";"
      tableName="position"
      usePreparedStatements="true">
      <column name="id" type="numeric"/>
      <column name="tenant_id" type="numeric"/>
      <column name="code" type="string"/>
      <column name="name" type="string"/>
      <column name="level" type="numeric"/>
      <column name="category" type="string"/>
      <column name="description" type="string"/>
      <column name="metadata" type="string"/>
      <column name="version" type="numeric"/>
      <column name="created_by" type="string"/>
      <column name="created_at" type="date"/>
      <column name="updated_by" type="string"/>
      <column name="updated_at" type="date"/>
      <column name="is_deleted" type="boolean"/>
      <!-- jhipster-needle-liquibase-add-loadcolumn - JHipster (and/or extensions) can add load columns here -->
    </loadData>
  </changeSet>
</databaseChangeLog>

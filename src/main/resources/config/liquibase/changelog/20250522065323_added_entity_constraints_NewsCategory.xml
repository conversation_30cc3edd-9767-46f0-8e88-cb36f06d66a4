<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
  xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
  <!--
      Added the constraints for entity NewsCategory.
  -->
  <changeSet id="20250522065323-2" author="jhipster">

    <addForeignKeyConstraint baseColumnNames="org_unit_id"
                             baseTableName="news_category"
                             constraintName="fk_news_category_org_unit_id"
                             referencedColumnNames="id"
                             referencedTableName="org_unit"
    />

    <addForeignKeyConstraint baseColumnNames="parent_id"
                             baseTableName="news_category"
                             constraintName="fk_news_category_parent_id"
                             referencedColumnNames="id"
                             referencedTableName="news_category"
    />
  </changeSet>
</databaseChangeLog>

<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
  xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
  <!--
      Added the constraints for entity News.
  -->
  <changeSet id="20250522065326-2" author="jhipster">

    <addForeignKeyConstraint baseColumnNames="category_id"
                             baseTableName="news"
                             constraintName="fk_news_category_id"
                             referencedColumnNames="id"
                             referencedTableName="news_category"
    />

    <addForeignKeyConstraint baseColumnNames="org_unit_id"
                             baseTableName="news"
                             constraintName="fk_news_org_unit_id"
                             referencedColumnNames="id"
                             referencedTableName="org_unit"
    />

    <addForeignKeyConstraint baseColumnNames="author_id"
                             baseTableName="news"
                             constraintName="fk_news_author_id"
                             referencedColumnNames="id"
                             referencedTableName="employee"
    />

    <addForeignKeyConstraint baseColumnNames="news_id"
                             baseTableName="news_tags"
                             constraintName="fk_news_tags_news_id"
                             referencedColumnNames="id"
                             referencedTableName="news"
    />

    <addForeignKeyConstraint baseColumnNames="tags_id"
                             baseTableName="news_tags"
                             constraintName="fk_news_tags_tags_id"
                             referencedColumnNames="id"
                             referencedTableName="tag"
    />
  </changeSet>
</databaseChangeLog>

<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
  xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">

  <!--
      Added the entity LetterCommitment.
  -->
  <changeSet id="20250714022501-1" author="jhipster">
    <createTable tableName="letter_commitment"
                 remarks="承诺书（LetterCommitment）实体\n管理员工的各类承诺书，包括合规承诺等">
      <column name="id" type="bigint" remarks="主键 ID" autoIncrement="true" startWith="1500">
        <constraints primaryKey="true" nullable="false"/>
      </column>
      <column name="tenant_id" type="bigint" remarks="租户 ID，标识不同租户的数据隔离">
        <constraints nullable="false"/>
      </column>
      <column name="employee_id" type="bigint" remarks="员工 ID">
        <constraints nullable="false"/>
      </column>
      <column name="type" type="integer" remarks="类别：1、合规承诺">
        <constraints nullable="false"/>
      </column>
      <column name="file_path" type="varchar(255)" remarks="文件地址">
        <constraints nullable="false"/>
      </column>
      <column name="is_signed" type="boolean" remarks="是否已签名：0、否 1、是">
        <constraints nullable="false"/>
      </column>
      <column name="metadata" type="varchar(255)" remarks="扩展元数据（JSON）">
        <constraints nullable="true"/>
      </column>
      <column name="version" type="integer" remarks="乐观锁版本">
        <constraints nullable="false"/>
      </column>
      <column name="created_by" type="varchar(255)" remarks="创建者">
        <constraints nullable="true"/>
      </column>
      <column name="created_at" type="${datetimeType}" remarks="创建时间">
        <constraints nullable="false"/>
      </column>
      <column name="updated_by" type="varchar(255)" remarks="更新者">
        <constraints nullable="true"/>
      </column>
      <column name="updated_at" type="${datetimeType}" remarks="更新时间">
        <constraints nullable="false"/>
      </column>
      <column name="is_deleted" type="boolean" remarks="软删除标志">
        <constraints nullable="false"/>
      </column>
      <!-- jhipster-needle-liquibase-add-column - JHipster will add columns here -->
    </createTable>
    <dropDefaultValue tableName="letter_commitment" columnName="created_at" columnDataType="${datetimeType}"/>
    <dropDefaultValue tableName="letter_commitment" columnName="updated_at" columnDataType="${datetimeType}"/>
  </changeSet>

  <!-- jhipster-needle-liquibase-add-changeset - JHipster will add changesets here -->

  <!--
      Load sample data generated with Faker.js
      - This data can be easily edited using a CSV editor (or even MS Excel) and
        is located in the 'src/main/resources/config/liquibase/fake-data' directory
      - By default this data is applied when running with the JHipster 'dev' profile.
        This can be customized by adding or removing 'faker' in the 'spring.liquibase.contexts'
        Spring Boot configuration key.
  -->
  <changeSet id="20250714022501-1-data" author="jhipster" context="faker">
    <loadData
      file="config/liquibase/fake-data/letter_commitment.csv"
      separator=";"
      tableName="letter_commitment"
      usePreparedStatements="true">
      <column name="id" type="numeric"/>
      <column name="tenant_id" type="numeric"/>
      <column name="employee_id" type="numeric"/>
      <column name="type" type="numeric"/>
      <column name="file_path" type="string"/>
      <column name="is_signed" type="boolean"/>
      <column name="metadata" type="string"/>
      <column name="version" type="numeric"/>
      <column name="created_by" type="string"/>
      <column name="created_at" type="date"/>
      <column name="updated_by" type="string"/>
      <column name="updated_at" type="date"/>
      <column name="is_deleted" type="boolean"/>
      <!-- jhipster-needle-liquibase-add-loadcolumn - JHipster (and/or extensions) can add load columns here -->
    </loadData>
  </changeSet>
</databaseChangeLog>

<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
  xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
  <!--
      Added the constraints for entity NewsReport.
  -->
  <changeSet id="20250522065330-2" author="jhipster">

    <addForeignKeyConstraint baseColumnNames="news_id"
                             baseTableName="news_report"
                             constraintName="fk_news_report_news_id"
                             referencedColumnNames="id"
                             referencedTableName="news"
    />

    <addForeignKeyConstraint baseColumnNames="comment_id"
                             baseTableName="news_report"
                             constraintName="fk_news_report_comment_id"
                             referencedColumnNames="id"
                             referencedTableName="news_comment"
    />

    <addForeignKeyConstraint baseColumnNames="reporter_id"
                             baseTableName="news_report"
                             constraintName="fk_news_report_reporter_id"
                             referencedColumnNames="id"
                             referencedTableName="employee"
    />
  </changeSet>
</databaseChangeLog>

<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd
                        http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd">

    <!--
        Added the entity Reservation.
    -->
    <changeSet id="20250717101428-1" author="jhipster">
        <createTable tableName="reservation" remarks="预约体验（Reservation）实体">
            <column name="id" type="bigint" remarks="主键ID" autoIncrement="true" startWith="1500">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="name" type="varchar(64)" remarks="您的姓名">
                <constraints nullable="false" />
            </column>
            <column name="position" type="varchar(64)" remarks="职位">
                <constraints nullable="false" />
            </column>
            <column name="mobile" type="varchar(32)" remarks="手机号码">
                <constraints nullable="false" />
            </column>
            <column name="email" type="varchar(128)" remarks="电子邮箱">
                <constraints nullable="false" />
            </column>
            <column name="company" type="varchar(128)" remarks="公司名称">
                <constraints nullable="false" />
            </column>
            <column name="industry" type="varchar(64)" remarks="所属行业">
                <constraints nullable="false" />
            </column>
            <column name="company_size" type="varchar(255)" remarks="企业规模">
                <constraints nullable="false" />
            </column>
            <column name="focus_need" type="varchar(255)" remarks="最关注合规管理需求">
                <constraints nullable="false" />
            </column>
            <column name="other_desc" type="varchar(255)" remarks="其他需求说明">
                <constraints nullable="true" />
            </column>
            <column name="metadata" type="varchar(255)" remarks="补充字段">
                <constraints nullable="true" />
            </column>
            <column name="version" type="integer" remarks="当前版本号">
                <constraints nullable="false" />
            </column>
            <column name="created_by" type="varchar(64)" remarks="创建者账号或姓名">
                <constraints nullable="true" />
            </column>
            <column name="created_at" type="${datetimeType}" remarks="创建时间">
                <constraints nullable="false" />
            </column>
            <column name="updated_by" type="varchar(64)" remarks="最后修改者">
                <constraints nullable="true" />
            </column>
            <column name="updated_at" type="${datetimeType}" remarks="最后更新时间">
                <constraints nullable="false" />
            </column>
            <column name="is_deleted" type="boolean" remarks="是否删除：0 表示正常 1 表示已删除">
                <constraints nullable="false" />
            </column>
            <!-- jhipster-needle-liquibase-add-column - JHipster will add columns here -->
        </createTable>
        <dropDefaultValue tableName="reservation" columnName="created_at" columnDataType="${datetimeType}"/>
        <dropDefaultValue tableName="reservation" columnName="updated_at" columnDataType="${datetimeType}"/>
    </changeSet>

    <!-- jhipster-needle-liquibase-add-changeset - JHipster will add changesets here -->

    <!--
        Load sample data generated with Faker.js
        - This data can be easily edited using a CSV editor (or even MS Excel) and
          is located in the 'src/main/resources/config/liquibase/fake-data' directory
        - By default this data is applied when running with the JHipster 'dev' profile.
          This can be customized by adding or removing 'faker' in the 'spring.liquibase.contexts'
          Spring Boot configuration key.
    -->
    <changeSet id="20250717101428-1-data" author="jhipster" context="faker">
        <loadData
                  file="config/liquibase/fake-data/reservation.csv"
                  separator=";"
                  tableName="reservation"
                  usePreparedStatements="true">
            <column name="id" type="numeric"/>
            <column name="name" type="string"/>
            <column name="position" type="string"/>
            <column name="mobile" type="string"/>
            <column name="email" type="string"/>
            <column name="company" type="string"/>
            <column name="industry" type="string"/>
            <column name="company_size" type="string"/>
            <column name="focus_need" type="string"/>
            <column name="other_desc" type="string"/>
            <column name="metadata" type="string"/>
            <column name="version" type="numeric"/>
            <column name="created_by" type="string"/>
            <column name="created_at" type="date"/>
            <column name="updated_by" type="string"/>
            <column name="updated_at" type="date"/>
            <column name="is_deleted" type="boolean"/>
            <!-- jhipster-needle-liquibase-add-loadcolumn - JHipster (and/or extensions) can add load columns here -->
        </loadData>
    </changeSet>
</databaseChangeLog>

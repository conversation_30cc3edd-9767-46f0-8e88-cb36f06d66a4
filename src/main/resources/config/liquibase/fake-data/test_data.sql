-- 清理现有数据
SET FOREIGN_KEY_CHECKS = 0;
TRUNCATE TABLE tenant;
TRUNCATE TABLE tenant_profile;
TRUNCATE TABLE org_unit;
TRUNCATE TABLE position;
TRUNCATE TABLE employee;
TRUNCATE TABLE employee_org;
TRUNCATE TABLE role;
TRUNCATE TABLE permission;
TRUNCATE TABLE role_permission;
TRUNCATE TABLE employee_role;
SET FOREIGN_KEY_CHECKS = 1;

-- 插入租户数据
INSERT INTO tenant (
    id, tenant_code, name, status, subscription_plan, subscription_start, subscription_end,
    contact_email, contact_phone, version, created_by, created_at, updated_by, updated_at, is_deleted
) VALUES
(1, 'CAT_UNCLE', '猫伯伯科技有限公司', 1, 'ENTERPRISE', '2023-05-21', '2025-12-31',
    '<EMAIL>', '************', 1, 'system', NOW(), 'system', NOW(), false),
(2, 'STATE_ENERGY', '央企能源集团有限公司', 1, 'ULTIMATE', '2023-05-21', '2025-12-31',
    '<EMAIL>', '************', 1, 'system', NOW(), 'system', NOW(), false);

-- 插入租户详情配置
INSERT INTO tenant_profile (
    id, tenant_id, registration_number, registration_date, registered_capital, company_type,
    business_scope, organization_code, registered_address, contact_person, contact_mobile,
    contact_email, legal_person, version, created_by, created_at, updated_by, updated_at, is_deleted
) VALUES
(1, 1, '91110105MA002XYZ3A', '2023-01-01', 10000000.00, '有限责任公司',
    '技术开发、技术咨询、技术服务', '12345678-9', '北京市朝阳区科技园区888号',
    '猫总', '***********', '<EMAIL>', '猫伯伯', 1, 'system', NOW(), 'system', NOW(), false),
(2, 2, '91110000MA003ABC1B', '1998-05-18', 1000000000.00, '国有控股企业',
    '能源开发、电力供应、新能源技术服务', '98765432-1', '北京市西城区国资大厦666号',
    '李董事长', '***********', '<EMAIL>', '李董', 1, 'system', NOW(), 'system', NOW(), false);

-- 猫伯伯科技组织架构
INSERT INTO org_unit (
    id, tenant_id, name, code, type, level, status, sort_order,
    description, parent_id, version, created_by, created_at, updated_by, updated_at, is_deleted
) VALUES
(1, 1, '猫伯伯科技总部', 'CAT_HQ', 'COMPANY', 1, 1, 1,
    '猫伯伯科技有限公司总部', NULL, 1, 'system', NOW(), 'system', NOW(), false),
(2, 1, '研发中心', 'RD_CENTER', 'DEPARTMENT', 2, 1, 1,
    '负责公司技术研发工作', 1, 1, 'system', NOW(), 'system', NOW(), false),
(3, 1, '后端开发部', 'BACKEND', 'DEPARTMENT', 3, 1, 1,
    '负责后端系统开发', 2, 1, 'system', NOW(), 'system', NOW(), false),
(4, 1, '前端开发部', 'FRONTEND', 'DEPARTMENT', 3, 1, 2,
    '负责前端界面开发', 2, 1, 'system', NOW(), 'system', NOW(), false),
(5, 1, '产品设计部', 'PRODUCT', 'DEPARTMENT', 2, 1, 2,
    '负责产品设计规划', 1, 1, 'system', NOW(), 'system', NOW(), false),
(6, 1, '运营部', 'OPERATION', 'DEPARTMENT', 2, 1, 3,
    '负责产品运营推广', 1, 1, 'system', NOW(), 'system', NOW(), false),
(7, 1, '人力资源部', 'HR', 'DEPARTMENT', 2, 1, 4,
    '负责人力资源管理', 1, 1, 'system', NOW(), 'system', NOW(), false),
(8, 1, '财务部', 'FINANCE', 'DEPARTMENT', 2, 1, 5,
    '负责财务管理', 1, 1, 'system', NOW(), 'system', NOW(), false);

-- 央企能源组织架构
INSERT INTO org_unit (
    id, tenant_id, name, code, type, level, status, sort_order,
    description, parent_id, version, created_by, created_at, updated_by, updated_at, is_deleted
) VALUES
(101, 2, '央企能源集团总部', 'STATE_HQ', 'COMPANY', 1, 1, 1,
    '央企能源集团有限公司总部', NULL, 1, 'system', NOW(), 'system', NOW(), false),
(102, 2, '战略发展部', 'STRATEGY', 'DEPARTMENT', 2, 1, 1,
    '负责集团战略规划', 101, 1, 'system', NOW(), 'system', NOW(), false),
(103, 2, '生产运营部', 'PRODUCTION', 'DEPARTMENT', 2, 1, 2,
    '负责生产运营管理', 101, 1, 'system', NOW(), 'system', NOW(), false),
(104, 2, '安全管理部', 'SAFETY', 'DEPARTMENT', 2, 1, 3,
    '负责安全生产监督', 101, 1, 'system', NOW(), 'system', NOW(), false),
(105, 2, '人力资源部', 'HR', 'DEPARTMENT', 2, 1, 4,
    '负责人力资源管理', 101, 1, 'system', NOW(), 'system', NOW(), false),
(106, 2, '财务管理部', 'FINANCE', 'DEPARTMENT', 2, 1, 5,
    '负责财务管理', 101, 1, 'system', NOW(), 'system', NOW(), false),
(107, 2, '信息技术部', 'IT', 'DEPARTMENT', 2, 1, 6,
    '负责信息化建设', 101, 1, 'system', NOW(), 'system', NOW(), false);

-- 猫伯伯科技职位
INSERT INTO position (
    id, tenant_id, code, name, level, category, description,
    version, created_by, created_at, updated_by, updated_at, is_deleted
) VALUES
(1, 1, 'CTO', '首席技术官', 10, 'TECHNICAL', '负责公司整体技术方向',
    1, 'system', NOW(), 'system', NOW(), false),
(2, 1, 'RD_DIRECTOR', '研发总监', 9, 'TECHNICAL', '负责研发中心管理',
    1, 'system', NOW(), 'system', NOW(), false),
(3, 1, 'TECH_MANAGER', '技术经理', 8, 'TECHNICAL', '负责技术团队管理',
    1, 'system', NOW(), 'system', NOW(), false),
(4, 1, 'SENIOR_DEV', '高级开发工程师', 7, 'TECHNICAL', '负责核心功能开发',
    1, 'system', NOW(), 'system', NOW(), false),
(5, 1, 'DEV', '开发工程师', 6, 'TECHNICAL', '负责功能开发',
    1, 'system', NOW(), 'system', NOW(), false),
(6, 1, 'PRODUCT_DIRECTOR', '产品总监', 9, 'PRODUCT', '负责产品规划和管理',
    1, 'system', NOW(), 'system', NOW(), false),
(7, 1, 'PRODUCT_MANAGER', '产品经理', 7, 'PRODUCT', '负责产品设计和规划',
    1, 'system', NOW(), 'system', NOW(), false),
(8, 1, 'OPERATION_DIRECTOR', '运营总监', 8, 'OPERATION', '负责运营体系管理',
    1, 'system', NOW(), 'system', NOW(), false),
(9, 1, 'HR_DIRECTOR', 'HR总监', 8, 'ADMINISTRATIVE', '负责人力资源管理',
    1, 'system', NOW(), 'system', NOW(), false),
(10, 1, 'CFO', '财务总监', 9, 'FINANCIAL', '负责财务管理',
    1, 'system', NOW(), 'system', NOW(), false),
(11, 1, 'FINANCE_DIRECTOR', '财务总监', 8, 'ADMINISTRATIVE', '负责财务管理',
    1, 'system', NOW(), 'system', NOW(), false);

-- 央企能源职位
INSERT INTO position (
    id, tenant_id, code, name, level, category, description,
    version, created_by, created_at, updated_by, updated_at, is_deleted
) VALUES
(101, 2, 'VICE_PRESIDENT', '集团副总经理', 13, 'MANAGEMENT', '负责集团日常经营管理',
    1, 'system', NOW(), 'system', NOW(), false),
(102, 2, 'STRATEGY_DIRECTOR', '战略总监', 12, 'MANAGEMENT', '负责集团战略规划',
    1, 'system', NOW(), 'system', NOW(), false),
(103, 2, 'PRODUCTION_DIRECTOR', '生产总监', 12, 'TECHNICAL', '负责生产运营管理',
    1, 'system', NOW(), 'system', NOW(), false),
(104, 2, 'SAFETY_DIRECTOR', '安全总监', 12, 'TECHNICAL', '负责安全生产管理',
    1, 'system', NOW(), 'system', NOW(), false),
(105, 2, 'HR_DIRECTOR', 'HR总监', 12, 'ADMINISTRATIVE', '负责人力资源管理',
    1, 'system', NOW(), 'system', NOW(), false),
(106, 2, 'FINANCE_DIRECTOR', '财务总监', 12, 'FINANCIAL', '负责财务管理',
    1, 'system', NOW(), 'system', NOW(), false),
(107, 2, 'IT_DIRECTOR', 'IT总监', 12, 'TECHNICAL', '负责信息化建设',
    1, 'system', NOW(), 'system', NOW(), false);

-- 猫伯伯科技角色
INSERT INTO role (
    id, tenant_id, name, code, description, status,
    version, created_by, created_at, updated_by, updated_at, is_deleted
) VALUES
(1, 1, '超级管理员', 'SUPER_ADMIN', '系统最高权限管理员', 1,
    1, 'system', NOW(), 'system', NOW(), false),
(2, 1, '系统管理员', 'ADMIN', '系统管理员', 1,
    1, 'system', NOW(), 'system', NOW(), false),
(3, 1, '技术主管', 'TECH_LEAD', '技术部门主管', 1,
    1, 'system', NOW(), 'system', NOW(), false),
(4, 1, '部门经理', 'DEPT_MANAGER', '各部门经理', 1,
    1, 'system', NOW(), 'system', NOW(), false),
(5, 1, '普通员工', 'EMPLOYEE', '普通员工角色', 1,
    1, 'system', NOW(), 'system', NOW(), false);

-- 央企能源角色
INSERT INTO role (
    id, tenant_id, name, code, description, status,
    version, created_by, created_at, updated_by, updated_at, is_deleted
) VALUES
(101, 2, '超级管理员', 'SUPER_ADMIN', '系统最高权限管理员', 1,
    1, 'system', NOW(), 'system', NOW(), false),
(102, 2, '系统管理员', 'ADMIN', '系统管理员', 1,
    1, 'system', NOW(), 'system', NOW(), false),
(103, 2, '部门总监', 'DIRECTOR', '部门最高负责人', 1,
    1, 'system', NOW(), 'system', NOW(), false),
(104, 2, '部门经理', 'MANAGER', '部门管理人员', 1,
    1, 'system', NOW(), 'system', NOW(), false),
(105, 2, '普通员工', 'EMPLOYEE', '普通员工角色', 1,
    1, 'system', NOW(), 'system', NOW(), false);

-- 猫伯伯科技权限数据
INSERT INTO permission (
    id, tenant_id, name, code, resource_type, description, status,
    version, created_by, created_at, updated_by, updated_at, is_deleted
) VALUES
(1, 1, '组织管理', 'ORG_MANAGE', 'MENU', '组织架构管理功能', 1,
    1, 'system', NOW(), 'system', NOW(), false),
(2, 1, '员工查看', 'EMPLOYEE_VIEW', 'FUNCTION', '查看员工信息', 1,
    1, 'system', NOW(), 'system', NOW(), false),
(3, 1, '员工管理', 'EMPLOYEE_MANAGE', 'FUNCTION', '管理员工信息', 1,
    1, 'system', NOW(), 'system', NOW(), false),
(4, 1, '角色查看', 'ROLE_VIEW', 'FUNCTION', '查看角色信息', 1,
    1, 'system', NOW(), 'system', NOW(), false),
(5, 1, '角色管理', 'ROLE_MANAGE', 'FUNCTION', '管理角色信息', 1,
    1, 'system', NOW(), 'system', NOW(), false),
(6, 1, '权限查看', 'PERMISSION_VIEW', 'FUNCTION', '查看权限信息', 1,
    1, 'system', NOW(), 'system', NOW(), false),
(7, 1, '权限管理', 'PERMISSION_MANAGE', 'FUNCTION', '管理权限信息', 1,
    1, 'system', NOW(), 'system', NOW(), false),
(8, 1, '组织单元查看', 'ORG_UNIT_VIEW', 'FUNCTION', '查看组织单元信息', 1,
    1, 'system', NOW(), 'system', NOW(), false),
(9, 1, '组织单元管理', 'ORG_UNIT_MANAGE', 'FUNCTION', '管理组织单元信息', 1,
    1, 'system', NOW(), 'system', NOW(), false),
(10, 1, '职位查看', 'POSITION_VIEW', 'FUNCTION', '查看职位信息', 1,
    1, 'system', NOW(), 'system', NOW(), false),
(11, 1, '职位管理', 'POSITION_MANAGE', 'FUNCTION', '管理职位信息', 1,
    1, 'system', NOW(), 'system', NOW(), false),
(12, 1, '系统设置', 'SYSTEM_SETTINGS', 'MENU', '系统设置功能', 1,
    1, 'system', NOW(), 'system', NOW(), false),
(13, 1, '日志查看', 'LOG_VIEW', 'FUNCTION', '查看系统日志', 1,
    1, 'system', NOW(), 'system', NOW(), false),
(14, 1, '租户管理', 'TENANT_MANAGE', 'FUNCTION', '管理租户信息', 1,
    1, 'system', NOW(), 'system', NOW(), false),
(15, 1, '租户查看', 'TENANT_VIEW', 'FUNCTION', '查看租户信息', 1,
    1, 'system', NOW(), 'system', NOW(), false);

-- 央企能源权限数据
INSERT INTO permission (
    id, tenant_id, name, code, resource_type, description, status,
    version, created_by, created_at, updated_by, updated_at, is_deleted
) VALUES
(101, 2, '组织管理', 'ORG_MANAGE', 'MENU', '组织架构管理功能', 1,
    1, 'system', NOW(), 'system', NOW(), false),
(102, 2, '员工查看', 'EMPLOYEE_VIEW', 'FUNCTION', '查看员工信息', 1,
    1, 'system', NOW(), 'system', NOW(), false),
(103, 2, '员工管理', 'EMPLOYEE_MANAGE', 'FUNCTION', '管理员工信息', 1,
    1, 'system', NOW(), 'system', NOW(), false),
(104, 2, '角色查看', 'ROLE_VIEW', 'FUNCTION', '查看角色信息', 1,
    1, 'system', NOW(), 'system', NOW(), false),
(105, 2, '角色管理', 'ROLE_MANAGE', 'FUNCTION', '管理角色信息', 1,
    1, 'system', NOW(), 'system', NOW(), false),
(106, 2, '权限查看', 'PERMISSION_VIEW', 'FUNCTION', '查看权限信息', 1,
    1, 'system', NOW(), 'system', NOW(), false),
(107, 2, '权限管理', 'PERMISSION_MANAGE', 'FUNCTION', '管理权限信息', 1,
    1, 'system', NOW(), 'system', NOW(), false),
(108, 2, '组织单元查看', 'ORG_UNIT_VIEW', 'FUNCTION', '查看组织单元信息', 1,
    1, 'system', NOW(), 'system', NOW(), false),
(109, 2, '组织单元管理', 'ORG_UNIT_MANAGE', 'FUNCTION', '管理组织单元信息', 1,
    1, 'system', NOW(), 'system', NOW(), false),
(110, 2, '职位查看', 'POSITION_VIEW', 'FUNCTION', '查看职位信息', 1,
    1, 'system', NOW(), 'system', NOW(), false),
(111, 2, '职位管理', 'POSITION_MANAGE', 'FUNCTION', '管理职位信息', 1,
    1, 'system', NOW(), 'system', NOW(), false),
(112, 2, '系统设置', 'SYSTEM_SETTINGS', 'MENU', '系统设置功能', 1,
    1, 'system', NOW(), 'system', NOW(), false),
(113, 2, '日志查看', 'LOG_VIEW', 'FUNCTION', '查看系统日志', 1,
    1, 'system', NOW(), 'system', NOW(), false),
(114, 2, '租户管理', 'TENANT_MANAGE', 'FUNCTION', '管理租户信息', 1,
    1, 'system', NOW(), 'system', NOW(), false),
(115, 2, '租户查看', 'TENANT_VIEW', 'FUNCTION', '查看租户信息', 1,
    1, 'system', NOW(), 'system', NOW(), false);

-- 猫伯伯科技角色-权限关联
INSERT INTO role_permission (
    id, tenant_id, role_id, permission_id, version, created_by, created_at, updated_by, updated_at, is_deleted
) VALUES
-- 超级管理员拥有所有权限
(1, 1, 1, 1, 1, 'system', NOW(), 'system', NOW(), false),
(2, 1, 1, 2, 1, 'system', NOW(), 'system', NOW(), false),
(3, 1, 1, 3, 1, 'system', NOW(), 'system', NOW(), false),
(4, 1, 1, 4, 1, 'system', NOW(), 'system', NOW(), false),
(5, 1, 1, 5, 1, 'system', NOW(), 'system', NOW(), false),
(6, 1, 1, 6, 1, 'system', NOW(), 'system', NOW(), false),
(7, 1, 1, 7, 1, 'system', NOW(), 'system', NOW(), false),
(8, 1, 1, 8, 1, 'system', NOW(), 'system', NOW(), false),
(9, 1, 1, 9, 1, 'system', NOW(), 'system', NOW(), false),
(10, 1, 1, 10, 1, 'system', NOW(), 'system', NOW(), false),
(11, 1, 1, 11, 1, 'system', NOW(), 'system', NOW(), false),
(12, 1, 1, 12, 1, 'system', NOW(), 'system', NOW(), false),
(13, 1, 1, 13, 1, 'system', NOW(), 'system', NOW(), false),
(14, 1, 1, 14, 1, 'system', NOW(), 'system', NOW(), false),
(15, 1, 1, 15, 1, 'system', NOW(), 'system', NOW(), false),

-- 系统管理员权限
(16, 1, 2, 1, 1, 'system', NOW(), 'system', NOW(), false),
(17, 1, 2, 2, 1, 'system', NOW(), 'system', NOW(), false),
(18, 1, 2, 3, 1, 'system', NOW(), 'system', NOW(), false),
(19, 1, 2, 4, 1, 'system', NOW(), 'system', NOW(), false),
(20, 1, 2, 5, 1, 'system', NOW(), 'system', NOW(), false),
(21, 1, 2, 8, 1, 'system', NOW(), 'system', NOW(), false),
(22, 1, 2, 9, 1, 'system', NOW(), 'system', NOW(), false),
(23, 1, 2, 10, 1, 'system', NOW(), 'system', NOW(), false),
(24, 1, 2, 11, 1, 'system', NOW(), 'system', NOW(), false),
(25, 1, 2, 13, 1, 'system', NOW(), 'system', NOW(), false),

-- 技术主管权限
(26, 1, 3, 1, 1, 'system', NOW(), 'system', NOW(), false),
(27, 1, 3, 2, 1, 'system', NOW(), 'system', NOW(), false),
(28, 1, 3, 3, 1, 'system', NOW(), 'system', NOW(), false),
(29, 1, 3, 8, 1, 'system', NOW(), 'system', NOW(), false),
(30, 1, 3, 10, 1, 'system', NOW(), 'system', NOW(), false),

-- 部门经理权限
(31, 1, 4, 1, 1, 'system', NOW(), 'system', NOW(), false),
(32, 1, 4, 2, 1, 'system', NOW(), 'system', NOW(), false),
(33, 1, 4, 8, 1, 'system', NOW(), 'system', NOW(), false),
(34, 1, 4, 10, 1, 'system', NOW(), 'system', NOW(), false),

-- 普通员工权限
(35, 1, 5, 2, 1, 'system', NOW(), 'system', NOW(), false),
(36, 1, 5, 8, 1, 'system', NOW(), 'system', NOW(), false),
(37, 1, 5, 10, 1, 'system', NOW(), 'system', NOW(), false);

-- 央企能源角色-权限关联
INSERT INTO role_permission (
    id, tenant_id, role_id, permission_id, version, created_by, created_at, updated_by, updated_at, is_deleted
) VALUES
-- 超级管理员拥有所有权限
(101, 2, 101, 101, 1, 'system', NOW(), 'system', NOW(), false),
(102, 2, 101, 102, 1, 'system', NOW(), 'system', NOW(), false),
(103, 2, 101, 103, 1, 'system', NOW(), 'system', NOW(), false),
(104, 2, 101, 104, 1, 'system', NOW(), 'system', NOW(), false),
(105, 2, 101, 105, 1, 'system', NOW(), 'system', NOW(), false),
(106, 2, 101, 106, 1, 'system', NOW(), 'system', NOW(), false),
(107, 2, 101, 107, 1, 'system', NOW(), 'system', NOW(), false),
(108, 2, 101, 108, 1, 'system', NOW(), 'system', NOW(), false),
(109, 2, 101, 109, 1, 'system', NOW(), 'system', NOW(), false),
(110, 2, 101, 110, 1, 'system', NOW(), 'system', NOW(), false),
(111, 2, 101, 111, 1, 'system', NOW(), 'system', NOW(), false),
(112, 2, 101, 112, 1, 'system', NOW(), 'system', NOW(), false),
(113, 2, 101, 113, 1, 'system', NOW(), 'system', NOW(), false),
(114, 2, 101, 114, 1, 'system', NOW(), 'system', NOW(), false),
(115, 2, 101, 115, 1, 'system', NOW(), 'system', NOW(), false),

-- 系统管理员权限
(116, 2, 102, 101, 1, 'system', NOW(), 'system', NOW(), false),
(117, 2, 102, 102, 1, 'system', NOW(), 'system', NOW(), false),
(118, 2, 102, 103, 1, 'system', NOW(), 'system', NOW(), false),
(119, 2, 102, 104, 1, 'system', NOW(), 'system', NOW(), false),
(120, 2, 102, 105, 1, 'system', NOW(), 'system', NOW(), false),
(121, 2, 102, 108, 1, 'system', NOW(), 'system', NOW(), false),
(122, 2, 102, 109, 1, 'system', NOW(), 'system', NOW(), false),
(123, 2, 102, 110, 1, 'system', NOW(), 'system', NOW(), false),
(124, 2, 102, 111, 1, 'system', NOW(), 'system', NOW(), false),
(125, 2, 102, 113, 1, 'system', NOW(), 'system', NOW(), false),

-- 部门总监权限
(126, 2, 103, 101, 1, 'system', NOW(), 'system', NOW(), false),
(127, 2, 103, 102, 1, 'system', NOW(), 'system', NOW(), false),
(128, 2, 103, 103, 1, 'system', NOW(), 'system', NOW(), false),
(129, 2, 103, 108, 1, 'system', NOW(), 'system', NOW(), false),
(130, 2, 103, 109, 1, 'system', NOW(), 'system', NOW(), false),
(131, 2, 103, 110, 1, 'system', NOW(), 'system', NOW(), false),

-- 部门经理权限
(132, 2, 104, 101, 1, 'system', NOW(), 'system', NOW(), false),
(133, 2, 104, 102, 1, 'system', NOW(), 'system', NOW(), false),
(134, 2, 104, 108, 1, 'system', NOW(), 'system', NOW(), false),
(135, 2, 104, 110, 1, 'system', NOW(), 'system', NOW(), false),

-- 普通员工权限
(136, 2, 105, 102, 1, 'system', NOW(), 'system', NOW(), false),
(137, 2, 105, 108, 1, 'system', NOW(), 'system', NOW(), false),
(138, 2, 105, 110, 1, 'system', NOW(), 'system', NOW(), false);

-- 猫伯伯科技员工数据
INSERT INTO employee (
    id, tenant_id, username, password, salt, name, email, mobile, gender, avatar, status,
    version, created_by, created_at, updated_by, updated_at, is_deleted
) VALUES
(1, 1, 'admin', '$2a$10$gC.wRiI0MEVDtGGk9pZzB.UQIrwLQGc1V5fXzCRVY0Uo1hT/4qJI6', 'salt123', '系统管理员', '<EMAIL>', '***********', 1, 'avatar/admin.jpg', 1,
    1, 'system', NOW(), 'system', NOW(), false),
(2, 1, 'caotao', '$2a$10$gC.wRiI0MEVDtGGk9pZzB.UQIrwLQGc1V5fXzCRVY0Uo1hT/4qJI6', 'salt123', '曹涛', '<EMAIL>', '13800000002', 1, 'avatar/caotao.jpg', 1,
    1, 'system', NOW(), 'system', NOW(), false),
(3, 1, 'zhangsan', '$2a$10$gC.wRiI0MEVDtGGk9pZzB.UQIrwLQGc1V5fXzCRVY0Uo1hT/4qJI6', 'salt123', '张三', '<EMAIL>', '13800000003', 1, 'avatar/zhangsan.jpg', 1,
    1, 'system', NOW(), 'system', NOW(), false),
(4, 1, 'lisi', '$2a$10$gC.wRiI0MEVDtGGk9pZzB.UQIrwLQGc1V5fXzCRVY0Uo1hT/4qJI6', 'salt123', '李四', '<EMAIL>', '13800000004', 1, 'avatar/lisi.jpg', 1,
    1, 'system', NOW(), 'system', NOW(), false),
(5, 1, 'wangwu', '$2a$10$gC.wRiI0MEVDtGGk9pZzB.UQIrwLQGc1V5fXzCRVY0Uo1hT/4qJI6', 'salt123', '王五', '<EMAIL>', '13800000005', 1, 'avatar/wangwu.jpg', 1,
    1, 'system', NOW(), 'system', NOW(), false),
(6, 1, 'zhaoliu', '$2a$10$gC.wRiI0MEVDtGGk9pZzB.UQIrwLQGc1V5fXzCRVY0Uo1hT/4qJI6', 'salt123', '赵六', '<EMAIL>', '13800000006', 1, 'avatar/zhaoliu.jpg', 1,
    1, 'system', NOW(), 'system', NOW(), false),
(7, 1, 'sunqi', '$2a$10$gC.wRiI0MEVDtGGk9pZzB.UQIrwLQGc1V5fXzCRVY0Uo1hT/4qJI6', 'salt123', '孙七', '<EMAIL>', '13800000007', 2, 'avatar/sunqi.jpg', 1,
    1, 'system', NOW(), 'system', NOW(), false),
(8, 1, 'zhouba', '$2a$10$gC.wRiI0MEVDtGGk9pZzB.UQIrwLQGc1V5fXzCRVY0Uo1hT/4qJI6', 'salt123', '周八', '<EMAIL>', '13800000008', 1, 'avatar/zhouba.jpg', 1,
    1, 'system', NOW(), 'system', NOW(), false),
(9, 1, 'wujiu', '$2a$10$gC.wRiI0MEVDtGGk9pZzB.UQIrwLQGc1V5fXzCRVY0Uo1hT/4qJI6', 'salt123', '吴九', '<EMAIL>', '13800000009', 1, 'avatar/wujiu.jpg', 1,
    1, 'system', NOW(), 'system', NOW(), false),
(10, 1, 'zhengshi', '$2a$10$gC.wRiI0MEVDtGGk9pZzB.UQIrwLQGc1V5fXzCRVY0Uo1hT/4qJI6', 'salt123', '郑十', '<EMAIL>', '13800000010', 2, 'avatar/zhengshi.jpg', 1,
    1, 'system', NOW(), 'system', NOW(), false);

-- 央企能源员工数据
INSERT INTO employee (
    id, tenant_id, username, password, salt, name, email, mobile, gender, avatar, status,
    version, created_by, created_at, updated_by, updated_at, is_deleted
) VALUES
(101, 2, 'admin2', '$2a$10$gC.wRiI0MEVDtGGk9pZzB.UQIrwLQGc1V5fXzCRVY0Uo1hT/4qJI6', 'salt123', '系统管理员', '<EMAIL>', '***********', 1, 'avatar/admin2.jpg', 1,
    1, 'system', NOW(), 'system', NOW(), false),
(102, 2, 'ligang', '$2a$10$gC.wRiI0MEVDtGGk9pZzB.UQIrwLQGc1V5fXzCRVY0Uo1hT/4qJI6', 'salt123', '李刚', '<EMAIL>', '13900000002', 1, 'avatar/ligang.jpg', 1,
    1, 'system', NOW(), 'system', NOW(), false),
(103, 2, 'wangming', '$2a$10$gC.wRiI0MEVDtGGk9pZzB.UQIrwLQGc1V5fXzCRVY0Uo1hT/4qJI6', 'salt123', '王明', '<EMAIL>', '13900000003', 1, 'avatar/wangming.jpg', 1,
    1, 'system', NOW(), 'system', NOW(), false),
(104, 2, 'zhangjie', '$2a$10$gC.wRiI0MEVDtGGk9pZzB.UQIrwLQGc1V5fXzCRVY0Uo1hT/4qJI6', 'salt123', '张杰', '<EMAIL>', '13900000004', 1, 'avatar/zhangjie.jpg', 1,
    1, 'system', NOW(), 'system', NOW(), false),
(105, 2, 'liumei', '$2a$10$gC.wRiI0MEVDtGGk9pZzB.UQIrwLQGc1V5fXzCRVY0Uo1hT/4qJI6', 'salt123', '刘梅', '<EMAIL>', '13900000005', 2, 'avatar/liumei.jpg', 1,
    1, 'system', NOW(), 'system', NOW(), false),
(106, 2, 'chenfeng', '$2a$10$gC.wRiI0MEVDtGGk9pZzB.UQIrwLQGc1V5fXzCRVY0Uo1hT/4qJI6', 'salt123', '陈峰', '<EMAIL>', '13900000006', 1, 'avatar/chenfeng.jpg', 1,
    1, 'system', NOW(), 'system', NOW(), false);

-- 猫伯伯科技员工-组织关联
INSERT INTO employee_org (
    id, tenant_id, employee_id, org_unit_id, position_id, is_primary, version, created_by, created_at, updated_by, updated_at, is_deleted
) VALUES
(1, 1, 1, 1, 1, true, 1, 'system', NOW(), 'system', NOW(), false),
(2, 1, 2, 2, 2, true, 1, 'system', NOW(), 'system', NOW(), false),
(3, 1, 3, 3, 3, true, 1, 'system', NOW(), 'system', NOW(), false),
(4, 1, 4, 3, 4, true, 1, 'system', NOW(), 'system', NOW(), false),
(5, 1, 5, 3, 5, true, 1, 'system', NOW(), 'system', NOW(), false),
(6, 1, 6, 4, 3, true, 1, 'system', NOW(), 'system', NOW(), false),
(7, 1, 7, 4, 4, true, 1, 'system', NOW(), 'system', NOW(), false),
(8, 1, 8, 5, 6, true, 1, 'system', NOW(), 'system', NOW(), false),
(9, 1, 9, 5, 7, true, 1, 'system', NOW(), 'system', NOW(), false),
(10, 1, 10, 6, 8, true, 1, 'system', NOW(), 'system', NOW(), false);

-- 央企能源员工-组织关联
INSERT INTO employee_org (
    id, tenant_id, employee_id, org_unit_id, position_id, is_primary, version, created_by, created_at, updated_by, updated_at, is_deleted
) VALUES
(101, 2, 101, 101, 101, true, 1, 'system', NOW(), 'system', NOW(), false),
(102, 2, 102, 102, 102, true, 1, 'system', NOW(), 'system', NOW(), false),
(103, 2, 103, 103, 103, true, 1, 'system', NOW(), 'system', NOW(), false),
(104, 2, 104, 104, 104, true, 1, 'system', NOW(), 'system', NOW(), false),
(105, 2, 105, 105, 105, true, 1, 'system', NOW(), 'system', NOW(), false),
(106, 2, 106, 107, 107, true, 1, 'system', NOW(), 'system', NOW(), false);

-- 猫伯伯科技员工-角色关联
INSERT INTO employee_role (
    id, tenant_id, employee_id, role_id, version, created_by, created_at, updated_by, updated_at, is_deleted
) VALUES
(1, 1, 1, 1, 1, 'system', NOW(), 'system', NOW(), false),
(2, 1, 2, 2, 1, 'system', NOW(), 'system', NOW(), false),
(3, 1, 3, 3, 1, 'system', NOW(), 'system', NOW(), false),
(4, 1, 4, 5, 1, 'system', NOW(), 'system', NOW(), false),
(5, 1, 5, 5, 1, 'system', NOW(), 'system', NOW(), false),
(6, 1, 6, 3, 1, 'system', NOW(), 'system', NOW(), false),
(7, 1, 7, 5, 1, 'system', NOW(), 'system', NOW(), false),
(8, 1, 8, 4, 1, 'system', NOW(), 'system', NOW(), false),
(9, 1, 9, 5, 1, 'system', NOW(), 'system', NOW(), false),
(10, 1, 10, 4, 1, 'system', NOW(), 'system', NOW(), false);

-- 央企能源员工-角色关联
INSERT INTO employee_role (
    id, tenant_id, employee_id, role_id, version, created_by, created_at, updated_by, updated_at, is_deleted
) VALUES
(101, 2, 101, 101, 1, 'system', NOW(), 'system', NOW(), false),
(102, 2, 102, 103, 1, 'system', NOW(), 'system', NOW(), false),
(103, 2, 103, 103, 1, 'system', NOW(), 'system', NOW(), false),
(104, 2, 104, 103, 1, 'system', NOW(), 'system', NOW(), false),
(105, 2, 105, 104, 1, 'system', NOW(), 'system', NOW(), false),
(106, 2, 106, 104, 1, 'system', NOW(), 'system', NOW(), false);





<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
  xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
  <property name="now" value="now()" dbms="mysql"/>
  <property name="floatType" value="float" dbms="mysql"/>
  <property name="clobType" value="mediumtext" dbms="mysql"/>
  <property name="blobType" value="longtext" dbms="mysql"/>
  <property name="uuidType" value="varchar(36)" dbms="mysql"/>
  <property name="datetimeType" value="datetime(6)" dbms="mysql"/>
  <property name="timeType" value="time(6)" dbms="mysql"/>

  <include file="config/liquibase/changelog/00000000000000_initial_schema.xml" relativeToChangelogFile="false"/>
  <include file="config/liquibase/changelog/20250427130051_added_entity_OrgUnit.xml" relativeToChangelogFile="false"/>
  <include file="config/liquibase/changelog/20250427130052_added_entity_Employee.xml" relativeToChangelogFile="false"/>
  <include file="config/liquibase/changelog/20250427130053_added_entity_Position.xml" relativeToChangelogFile="false"/>
  <include file="config/liquibase/changelog/20250427130054_added_entity_EmployeeOrg.xml"
           relativeToChangelogFile="false"/>
  <include file="config/liquibase/changelog/20250427130055_added_entity_Role.xml" relativeToChangelogFile="false"/>
  <include file="config/liquibase/changelog/20250427130056_added_entity_Permission.xml"
           relativeToChangelogFile="false"/>
  <include file="config/liquibase/changelog/20250427130057_added_entity_RolePermission.xml"
           relativeToChangelogFile="false"/>
  <include file="config/liquibase/changelog/20250427130058_added_entity_EmployeeRole.xml"
           relativeToChangelogFile="false"/>
  <include file="config/liquibase/changelog/20250427130059_added_entity_AuditLog.xml" relativeToChangelogFile="false"/>
  <include file="config/liquibase/changelog/20250427130100_added_entity_Tenant.xml" relativeToChangelogFile="false"/>
  <include file="config/liquibase/changelog/20250427130101_added_entity_TenantProfile.xml"
           relativeToChangelogFile="false"/>
  <include file="config/liquibase/changelog/20250427130102_added_entity_TenantAttachment.xml"
           relativeToChangelogFile="false"/>
  <include file="config/liquibase/changelog/20250522065323_added_entity_NewsCategory.xml"
           relativeToChangelogFile="false"/>
  <include file="config/liquibase/changelog/20250522065324_added_entity_TagCategory.xml"
           relativeToChangelogFile="false"/>
  <include file="config/liquibase/changelog/20250522065325_added_entity_Tag.xml" relativeToChangelogFile="false"/>
  <include file="config/liquibase/changelog/20250522065326_added_entity_News.xml" relativeToChangelogFile="false"/>
  <include file="config/liquibase/changelog/20250522065327_added_entity_NewsComment.xml"
           relativeToChangelogFile="false"/>
  <include file="config/liquibase/changelog/20250522065328_added_entity_NewsLike.xml" relativeToChangelogFile="false"/>
  <include file="config/liquibase/changelog/20250522065329_added_entity_NewsAttachment.xml"
           relativeToChangelogFile="false"/>
  <include file="config/liquibase/changelog/20250522065330_added_entity_NewsReport.xml"
           relativeToChangelogFile="false"/>
  <include file="config/liquibase/changelog/20250522065331_added_entity_NewsReadRecord.xml"
           relativeToChangelogFile="false"/>
  <include file="config/liquibase/changelog/20250618000001_add_missing_fields.xml" relativeToChangelogFile="false"/>
  <include file="config/liquibase/changelog/20250620072401_added_entity_TenantInitialize.xml"
           relativeToChangelogFile="false"/>
  <include file="config/liquibase/changelog/20250620140000_add_is_available_to_permission.xml"
           relativeToChangelogFile="false"/>
  <include file="config/liquibase/changelog/20250704013855_added_entity_RiskModel.xml" relativeToChangelogFile="false"/>
  <include file="config/liquibase/changelog/20250704013856_added_entity_RiskCategory.xml"
           relativeToChangelogFile="false"/>
  <include file="config/liquibase/changelog/20250704013857_added_entity_RiskRule.xml" relativeToChangelogFile="false"/>
  <include file="config/liquibase/changelog/20250704065920_added_entity_ComplaintSuggestion.xml"
           relativeToChangelogFile="false"/>
  <include file="config/liquibase/changelog/20250704065921_added_entity_ComplaintSuggestionAttachment.xml"
           relativeToChangelogFile="false"/>
  <include file="config/liquibase/changelog/20250714022501_added_entity_LetterCommitment.xml"
           relativeToChangelogFile="false"/>
  <include file="config/liquibase/changelog/20250715060316_added_entity_NewsTags.xml" relativeToChangelogFile="false"/>
  <include file="config/liquibase/changelog/20250717101428_added_entity_Reservation.xml" relativeToChangelogFile="false"/>
  <include file="config/liquibase/changelog/20250728000001_add_enterprise_type_to_tenant_profile.xml" relativeToChangelogFile="false"/>
  <!-- jhipster-needle-liquibase-add-changelog - JHipster will add liquibase changelogs here -->
  <include file="config/liquibase/changelog/20250427130051_added_entity_constraints_OrgUnit.xml"
           relativeToChangelogFile="false"/>
  <include file="config/liquibase/changelog/20250427130054_added_entity_constraints_EmployeeOrg.xml"
           relativeToChangelogFile="false"/>
  <include file="config/liquibase/changelog/20250427130055_added_entity_constraints_Role.xml"
           relativeToChangelogFile="false"/>
  <include file="config/liquibase/changelog/20250427130056_added_entity_constraints_Permission.xml"
           relativeToChangelogFile="false"/>
  <include file="config/liquibase/changelog/20250427130057_added_entity_constraints_RolePermission.xml"
           relativeToChangelogFile="false"/>
  <include file="config/liquibase/changelog/20250427130058_added_entity_constraints_EmployeeRole.xml"
           relativeToChangelogFile="false"/>
  <include file="config/liquibase/changelog/20250427130101_added_entity_constraints_TenantProfile.xml"
           relativeToChangelogFile="false"/>
  <include file="config/liquibase/changelog/20250427130102_added_entity_constraints_TenantAttachment.xml"
           relativeToChangelogFile="false"/>
  <include file="config/liquibase/changelog/20250522065323_added_entity_constraints_NewsCategory.xml"
           relativeToChangelogFile="false"/>
  <include file="config/liquibase/changelog/20250522065325_added_entity_constraints_Tag.xml"
           relativeToChangelogFile="false"/>
  <include file="config/liquibase/changelog/20250522065326_added_entity_constraints_News.xml"
           relativeToChangelogFile="false"/>
  <include file="config/liquibase/changelog/20250522065327_added_entity_constraints_NewsComment.xml"
           relativeToChangelogFile="false"/>
  <include file="config/liquibase/changelog/20250522065328_added_entity_constraints_NewsLike.xml"
           relativeToChangelogFile="false"/>
  <include file="config/liquibase/changelog/20250522065329_added_entity_constraints_NewsAttachment.xml"
           relativeToChangelogFile="false"/>
  <include file="config/liquibase/changelog/20250522065330_added_entity_constraints_NewsReport.xml"
           relativeToChangelogFile="false"/>
  <include file="config/liquibase/changelog/20250522065331_added_entity_constraints_NewsReadRecord.xml"
           relativeToChangelogFile="false"/>
  <include file="config/liquibase/changelog/20250427130053_added_entity_constraints_Position.xml"
           relativeToChangelogFile="false"/>
  <include file="config/liquibase/changelog/20250704013856_added_entity_constraints_RiskCategory.xml"
           relativeToChangelogFile="false"/>
  <include file="config/liquibase/changelog/20250704013857_added_entity_constraints_RiskRule.xml"
           relativeToChangelogFile="false"/>
  <include file="config/liquibase/changelog/20250715060316_added_entity_constraints_NewsTags.xml"
           relativeToChangelogFile="false"/>
  <!-- jhipster-needle-liquibase-add-constraints-changelog - JHipster will add liquibase constraints changelogs here -->
  <!-- jhipster-needle-liquibase-add-incremental-changelog - JHipster will add incremental liquibase changelogs here -->
</databaseChangeLog>

package com.whiskerguard.organization.util;

import java.security.SecureRandom;
import java.util.regex.Pattern;

public class PasswordUtils {

    private static final String PASSWORD_PATTERN = "^(?=.*[0-9])(?=.*[a-z])(?=.*[A-Z]).{8,}$";
    private static final Pattern pattern = Pattern.compile(PASSWORD_PATTERN);
    private static final SecureRandom secureRandom = new SecureRandom();
    private static final String SALT_CHARS = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
    private static final int SALT_LENGTH = 32;

    /**
     * 检查密码复杂度
     *
     * @param password 密码
     * @return 是否满足复杂度要求
     */
    public static boolean isPasswordValid(String password) {
        return pattern.matcher(password).matches();
    }

    /**
     * 生成随机盐值
     *
     * @return 随机盐值
     */
    public static String generateSalt() {
        StringBuilder salt = new StringBuilder();
        for (int i = 0; i < SALT_LENGTH; i++) {
            salt.append(SALT_CHARS.charAt(secureRandom.nextInt(SALT_CHARS.length())));
        }
        return salt.toString();
    }

    /**
     * 获取密码复杂度要求描述
     *
     * @return 密码复杂度要求描述
     */
    public static String getPasswordRequirements() {
        return (
            """
                密码必须包含：
                - 至少8个字符
                - 至少一个大写字母
                - 至少一个小写字母
                - 至少一个数字
                - 不能包含空格"""
        );
    }
    
}

package com.whiskerguard.organization.util;

import com.whiskerguard.common.util.WgStringUtil;
import com.whiskerguard.organization.service.dto.EmployeeImportDTO;
import com.whiskerguard.organization.service.dto.RoleImportDTO;
import com.whiskerguard.organization.service.dto.TenantImportDTO;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.List;

/**
 * 导入数据验证工具类
 * 提供字段级别的验证和错误信息格式化
 */
public class ImportValidationUtil {

    // 日期格式
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    /**
     * 格式化错误信息
     *
     * @param rowNumber    行号（从1开始，Excel中的实际行号）
     * @param fieldName    字段名称
     * @param errorMessage 错误信息
     * @return 格式化后的错误信息
     */
    public static String formatErrorMessage(int rowNumber, String fieldName, String errorMessage) {
        return String.format("第%d行，%s：%s", rowNumber, fieldName, errorMessage);
    }

    /**
     * 验证员工导入数据
     *
     * @param importDTO 员工导入DTO
     * @param rowNumber 行号
     * @return 错误信息列表，如果为空则验证通过
     */
    public static List<String> validateEmployeeImportData(EmployeeImportDTO importDTO, int rowNumber) {
        List<String> errors = new ArrayList<>();

        // 验证用户名
        if (!StringUtils.hasText(importDTO.getUsername())) {
            errors.add(formatErrorMessage(rowNumber, "登录用户名", "不能为空"));
        } else if (importDTO.getUsername().length() < 3 || importDTO.getUsername().length() > 50) {
            errors.add(formatErrorMessage(rowNumber, "登录用户名", "长度必须在3-50个字符之间"));
        }

        // 验证真实姓名
        if (!StringUtils.hasText(importDTO.getRealName())) {
            errors.add(formatErrorMessage(rowNumber, "真实姓名", "不能为空"));
        } else if (importDTO.getRealName().length() > 100) {
            errors.add(formatErrorMessage(rowNumber, "真实姓名", "长度不能超过100个字符"));
        }

        // 验证邮箱
        if (StringUtils.hasText(importDTO.getEmail())) {
            if (!WgStringUtil.isValidEmail(importDTO.getEmail())) {
                errors.add(formatErrorMessage(rowNumber, "邮箱地址", "格式不正确"));
            }
        }

        // 验证手机号
        if (StringUtils.hasText(importDTO.getPhone())) {
            if (!WgStringUtil.isValidMobile(importDTO.getPhone())) {
                errors.add(formatErrorMessage(rowNumber, "手机号", "格式不正确，请输入11位有效手机号"));
            }
        }

        // 验证性别
        if (StringUtils.hasText(importDTO.getGender())) {
            if (!"MALE".equals(importDTO.getGender()) && !"FEMALE".equals(importDTO.getGender()) &&
                !"UNKNOWN".equals(importDTO.getGender())) {
                errors.add(formatErrorMessage(rowNumber, "性别", "只能填写MALE/FEMALE/UNKNOWN"));
            }
        }

        // 验证生日
        if (StringUtils.hasText(importDTO.getBirthDate())) {
            try {
                LocalDate.parse(importDTO.getBirthDate(), DATE_FORMATTER);
            } catch (DateTimeParseException e) {
                errors.add(formatErrorMessage(rowNumber, "生日", "日期格式不正确，请使用yyyy-MM-dd格式"));
            }
        }

        // 验证身份证号
        if (StringUtils.hasText(importDTO.getIdCard())) {
            if (!WgStringUtil.isValidIdCard(importDTO.getIdCard())) {
                errors.add(formatErrorMessage(rowNumber, "身份证号", "格式不正确"));
            }
        }

        return errors;
    }

    /**
     * 验证角色导入数据
     *
     * @param importDTO 角色导入DTO
     * @param rowNumber 行号
     * @return 错误信息列表，如果为空则验证通过
     */
    public static List<String> validateRoleImportData(RoleImportDTO importDTO, int rowNumber) {
        List<String> errors = new ArrayList<>();

        // 验证角色编码
        if (!StringUtils.hasText(importDTO.getCode())) {
            errors.add(formatErrorMessage(rowNumber, "角色编码", "不能为空"));
        } else if (importDTO.getCode().length() > 50) {
            errors.add(formatErrorMessage(rowNumber, "角色编码", "长度不能超过50个字符"));
        }

        // 验证角色名称
        if (!StringUtils.hasText(importDTO.getName())) {
            errors.add(formatErrorMessage(rowNumber, "角色名称", "不能为空"));
        } else if (importDTO.getName().length() > 100) {
            errors.add(formatErrorMessage(rowNumber, "角色名称", "长度不能超过100个字符"));
        }

        // 验证角色描述
        if (StringUtils.hasText(importDTO.getDescription()) && importDTO.getDescription().length() > 500) {
            errors.add(formatErrorMessage(rowNumber, "角色描述", "长度不能超过500个字符"));
        }

        return errors;
    }

    /**
     * 验证租户导入数据
     *
     * @param importDTO 租户导入DTO
     * @param rowNumber 行号
     * @return 错误信息列表，如果为空则验证通过
     */
    public static List<String> validateTenantImportData(TenantImportDTO importDTO, int rowNumber) {
        List<String> errors = new ArrayList<>();

        // 验证租户名称
        if (!StringUtils.hasText(importDTO.getName())) {
            errors.add(formatErrorMessage(rowNumber, "租户名称", "不能为空"));
        } else if (importDTO.getName().length() > 100) {
            errors.add(formatErrorMessage(rowNumber, "租户名称", "长度不能超过100个字符"));
        }

        // 验证联系邮箱
        if (StringUtils.hasText(importDTO.getContactEmail())) {
            if (!WgStringUtil.isValidEmail(importDTO.getContactEmail())) {
                errors.add(formatErrorMessage(rowNumber, "联系邮箱", "格式不正确"));
            }
        }

        // 验证联系电话
        if (StringUtils.hasText(importDTO.getContactPhone())) {
            if (!WgStringUtil.isValidMobile(importDTO.getContactPhone())) {
                errors.add(formatErrorMessage(rowNumber, "联系电话", "格式不正确，请输入11位有效手机号"));
            }
        }

        // 验证工商注册号
        if (WgStringUtil.isValidBusinessLicense(importDTO.getRegistrationNumber())) {
            errors.add(formatErrorMessage(rowNumber, "工商注册号", "长度不能超过50个字符"));
        }

        // 验证注册日期
        if (StringUtils.hasText(importDTO.getRegistrationDate())) {
            try {
                LocalDate.parse(importDTO.getRegistrationDate(), DATE_FORMATTER);
            } catch (DateTimeParseException e) {
                errors.add(formatErrorMessage(rowNumber, "注册日期", "日期格式不正确，请使用yyyy-MM-dd格式"));
            }
        }

        // 验证注册资本
        if (importDTO.getRegisteredCapital() != null) {
            if (importDTO.getRegisteredCapital().compareTo(BigDecimal.ZERO) <= 0) {
                errors.add(formatErrorMessage(rowNumber, "注册资本", "必须大于0"));
            }
        }

        return errors;
    }
}

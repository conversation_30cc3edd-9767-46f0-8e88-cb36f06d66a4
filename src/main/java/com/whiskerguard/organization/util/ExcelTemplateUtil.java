package com.whiskerguard.organization.util;

import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.lang.reflect.Field;
import java.util.List;

/**
 * Excel模板生成工具类
 */
public class ExcelTemplateUtil {

    /**
     * 生成Excel模板
     *
     * @param data         模板数据
     * @param clazz        数据类型
     * @param sheetName    工作表名称
     * @param outputStream 输出流
     * @throws IOException IO异常
     */
    public static <T> void exportToExcel(List<T> data, Class<T> clazz, String sheetName, ByteArrayOutputStream outputStream) throws IOException {
        exportToExcel(data, clazz, sheetName, null, outputStream);
    }

    /**
     * 生成Excel模板（带详细说明）
     *
     * @param data         模板数据
     * @param clazz        数据类型
     * @param sheetName    工作表名称
     * @param instructions 导入说明列表
     * @param outputStream 输出流
     * @throws IOException IO异常
     */
    public static <T> void exportToExcel(List<T> data, Class<T> clazz, String sheetName, List<String> instructions, ByteArrayOutputStream outputStream) throws IOException {
        try (Workbook workbook = new XSSFWorkbook()) {

            // 1. 创建数据Sheet（第一个sheet）
            createDataSheet(workbook, data, clazz, sheetName);

            // 2. 创建说明Sheet（第二个sheet）
            if (instructions != null && !instructions.isEmpty()) {
                createInstructionSheet(workbook, instructions);
            }

            workbook.write(outputStream);
        }
    }

    /**
     * 创建说明Sheet
     */
    private static void createInstructionSheet(Workbook workbook, List<String> instructions) {
        Sheet instructionSheet = workbook.createSheet("导入说明");

        // 创建样式
        CellStyle instructionStyle = createInstructionCellStyle(workbook);

        // 创建一个单元格包含所有说明
        Row instructionRow = instructionSheet.createRow(0);
        // 自动行高
        instructionRow.setHeight((short) -1);
        Cell instructionCell = instructionRow.createCell(0);

        // 组合所有说明文本
        StringBuilder allInstructions = new StringBuilder();
        allInstructions.append("数据导入说明 - 请仔细阅读以下导入说明：\n\n");

        for (int i = 0; i < instructions.size(); i++) {
            allInstructions.append(instructions.get(i));
            if (i < instructions.size() - 1) {
                allInstructions.append("\n");
            }
        }

        instructionCell.setCellValue(allInstructions.toString());
        instructionCell.setCellStyle(instructionStyle);

        // 设置列宽为很宽，以容纳所有文本
        instructionSheet.setColumnWidth(0, 25000);

        // 设置打印属性
        instructionSheet.setPrintGridlines(false);
        instructionSheet.setDisplayGridlines(false);
    }

    /**
     * 创建数据Sheet
     */
    private static <T> void createDataSheet(Workbook workbook, List<T> data, Class<T> clazz, String sheetName) {
        Sheet dataSheet = workbook.createSheet(sheetName);

        Field[] fields = clazz.getDeclaredFields();

        // 创建样式
        CellStyle headerStyle = createHeaderStyle(workbook);
        CellStyle dataStyle = createDataStyle(workbook);

        int currentRow = 0;

        // 创建表头行
        Row headerRow = dataSheet.createRow(currentRow++);
        headerRow.setHeight((short) 500);

        int colIndex = 0;
        for (Field field : fields) {
            if (!field.getName().equals("serialVersionUID")) {
                Cell cell = headerRow.createCell(colIndex);
                String displayName = getFieldDisplayName(field.getName());
                cell.setCellValue(displayName);
                cell.setCellStyle(headerStyle);

                // 设置列宽
                int columnWidth = calculateColumnWidth(displayName, field.getName());
                dataSheet.setColumnWidth(colIndex, columnWidth);

                colIndex++;
            }
        }

        // 填充数据行
        if (data != null && !data.isEmpty()) {
            for (int i = 0; i < data.size(); i++) {
                Row dataRow = dataSheet.createRow(currentRow + i);
                dataRow.setHeight((short) 400);
                T item = data.get(i);

                colIndex = 0;
                for (Field field : fields) {
                    if (!field.getName().equals("serialVersionUID")) {
                        field.setAccessible(true);
                        try {
                            Object value = field.get(item);
                            Cell cell = dataRow.createCell(colIndex);
                            if (value != null) {
                                cell.setCellValue(value.toString());
                            }
                            cell.setCellStyle(dataStyle);
                            colIndex++;
                        } catch (IllegalAccessException e) {
                            // 忽略无法访问的字段
                            colIndex++;
                        }
                    }
                }
            }
        }

        // 设置打印属性
        dataSheet.setPrintGridlines(true);
        dataSheet.setDisplayGridlines(true);
        dataSheet.setFitToPage(true);

        // 冻结表头行
        dataSheet.createFreezePane(0, 1);
    }

    /**
     * 创建表头样式
     */
    private static CellStyle createHeaderStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        Font font = workbook.createFont();

        // 字体设置
        font.setFontName("微软雅黑");
        font.setFontHeightInPoints((short) 11);
        font.setBold(true);
        font.setColor(IndexedColors.WHITE.getIndex());

        // 样式设置
        style.setFont(font);
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        style.setFillForegroundColor(IndexedColors.DARK_BLUE.getIndex());
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);

        // 边框设置
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setTopBorderColor(IndexedColors.WHITE.getIndex());
        style.setBottomBorderColor(IndexedColors.WHITE.getIndex());
        style.setLeftBorderColor(IndexedColors.WHITE.getIndex());
        style.setRightBorderColor(IndexedColors.WHITE.getIndex());

        return style;
    }

    /**
     * 创建数据样式
     */
    private static CellStyle createDataStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        Font font = workbook.createFont();

        // 字体设置
        font.setFontName("微软雅黑");
        font.setFontHeightInPoints((short) 10);

        // 样式设置
        style.setFont(font);
        style.setAlignment(HorizontalAlignment.LEFT);
        style.setVerticalAlignment(VerticalAlignment.CENTER);

        // 边框设置
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setTopBorderColor(IndexedColors.GREY_40_PERCENT.getIndex());
        style.setBottomBorderColor(IndexedColors.GREY_40_PERCENT.getIndex());
        style.setLeftBorderColor(IndexedColors.GREY_40_PERCENT.getIndex());
        style.setRightBorderColor(IndexedColors.GREY_40_PERCENT.getIndex());

        // 自动换行
        style.setWrapText(true);

        return style;
    }

    /**
     * 创建说明单元格样式
     */
    private static CellStyle createInstructionCellStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        Font font = workbook.createFont();

        // 字体设置
        font.setFontName("微软雅黑");
        font.setFontHeightInPoints((short) 11);
        font.setColor(IndexedColors.BLACK.getIndex());

        // 样式设置
        style.setFont(font);
        style.setAlignment(HorizontalAlignment.LEFT);
        style.setVerticalAlignment(VerticalAlignment.TOP);
        style.setWrapText(true); // 自动换行

        // 边框设置
        style.setBorderTop(BorderStyle.MEDIUM);
        style.setBorderBottom(BorderStyle.MEDIUM);
        style.setBorderLeft(BorderStyle.MEDIUM);
        style.setBorderRight(BorderStyle.MEDIUM);
        style.setTopBorderColor(IndexedColors.DARK_BLUE.getIndex());
        style.setBottomBorderColor(IndexedColors.DARK_BLUE.getIndex());
        style.setLeftBorderColor(IndexedColors.DARK_BLUE.getIndex());
        style.setRightBorderColor(IndexedColors.DARK_BLUE.getIndex());
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);

        return style;
    }

    /**
     * 计算列宽
     */
    private static int calculateColumnWidth(String displayName, String fieldName) {
        // 基础宽度
        int baseWidth = 3000;

        // 根据字段类型调整宽度
        switch (fieldName) {
            case "name", "businessScope", "registeredAddress", "description" -> {
                return 8000; // 较宽的文本字段
            }
            case "contactEmail", "profileContactEmail", "website" -> {
                return 6000; // 邮箱和网址字段
            }
            case "registrationNumber", "taxRegistrationNumber", "bankAccount", "idCard" -> {
                return 5000; // 长编号字段
            }
            case "phone", "contactPhone", "contactMobile", "fax" -> {
                return 4000; // 电话号码字段
            }
            case "code", "level", "category", "gender" -> {
                return 3000; // 短编码字段
            }
            default -> {
                // 根据显示名称长度计算
                int nameLength = displayName.length();
                return Math.max(baseWidth, nameLength * 300 + 3000);
            }
        }
    }

    /**
     * 获取字段显示名称
     */
    private static String getFieldDisplayName(String fieldName) {
        return switch (fieldName) {

            // 员工相关字段
            case "username" -> "*登录用户名";
            case "realName" -> "*真实姓名";
            case "email" -> "邮箱地址";
            case "phone" -> "手机号码";
            case "gender" -> "性别";
            case "birthDate" -> "生日";
            case "idCard" -> "身份证号";
            case "employeeNo" -> "员工编号（工号）";
            case "hireDate" -> "入职日期";
            case "startDate" -> "任职开始日期";
            case "roleNames" -> "角色名称列表";

            // 角色相关字段
            case "code" -> "*角色编码";
            case "name" -> "*角色名称";
            case "description" -> "角色描述";
            case "permissionCodes" -> "权限编码列表";

            // 组织架构相关字段
            case "dataType" -> "*数据类型";
            case "orgUnitCode" -> "组织单元编码";
            case "orgUnitName" -> "组织单元名称";
            case "orgUnitType" -> "组织单元类型";
            case "parentOrgUnitCode" -> "父级组织单元编码";
            case "orgUnitLevel" -> "组织单元层级";
            case "orgUnitSortOrder" -> "组织单元排序";
            case "orgUnitDescription" -> "组织单元描述";
            case "positionCode" -> "岗位编码";
            case "positionName" -> "岗位名称";
            case "positionLevel" -> "岗位级别";
            case "positionCategory" -> "岗位分类";
            case "belongsToOrgUnitCode" -> "所属组织单元编码";
            case "positionDescription" -> "岗位描述";

            // 租户企业相关字段（带必填标记）
            case "contactEmail" -> "企业联系邮箱";
            case "contactPhone" -> "企业联系电话";
            case "registrationNumber" -> "*工商注册号";
            case "registrationDate" -> "*注册日期";
            case "registeredCapital" -> "*注册资本(元)";
            case "companyType" -> "*企业类型";
            case "businessScope" -> "*经营范围";
            case "industry" -> "*所属行业";
            case "taxRegistrationNumber" -> "*税务登记号";
            case "organizationCode" -> "*组织机构代码";
            case "registeredAddress" -> "*注册地址";
            case "postalCode" -> "*邮政编码";
            case "website" -> "企业官网";
            case "fax" -> "传真号码";
            case "contactPerson" -> "联系人姓名";
            case "contactMobile" -> "联系人手机";
            case "profileContactEmail" -> "联系人邮箱";
            case "bankName" -> "开户银行";
            case "bankAccount" -> "银行账号";
            case "businessLicensePath" -> "营业执照";
            case "legalPerson" -> "法定代表人";
            case "legalPersonId" -> "法人身份证号";

            // 其他字段
            case "parentCode" -> "父级编码";

            default -> fieldName;
        };
    }
}

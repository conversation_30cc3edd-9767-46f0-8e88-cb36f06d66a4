package com.whiskerguard.organization.util;

import java.security.SecureRandom;


/**
 * 描述：租户编码工具类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/17
 */
public class TenantCodeGenerator {
    private static final String PREFIX = "mbb";
    private static final int NUMBER_LENGTH = 5;
    private static final int LETTER_LENGTH = 5;
    private static final String LETTERS = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
    private static final SecureRandom RANDOM = new SecureRandom();

    /**
     * 生成租户编码，格式：mbb + 5位数字 + 5位大写字母
     */
    public static String generate() {
        // 1~99999
        int number = RANDOM.nextInt(99999) + 1;
        String numberPart = String.format("%0" + NUMBER_LENGTH + "d", number);
        StringBuilder letterPart = new StringBuilder();
        for (int i = 0; i < LETTER_LENGTH; i++) {
            int idx = RANDOM.nextInt(LETTERS.length());
            letterPart.append(LETTERS.charAt(idx));
        }
        return PREFIX + numberPart + letterPart;
    }
}

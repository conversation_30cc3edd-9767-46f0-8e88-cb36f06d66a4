package com.whiskerguard.organization.config;

import jakarta.servlet.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import org.springframework.util.CollectionUtils;
import org.springframework.web.WebApplicationInitializer;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import org.springframework.web.filter.CorsFilter;
import tech.jhipster.config.JHipsterProperties;

/**
 * Configuration of web application with Servlet 3.0 APIs.
 * WebConfigurer 负责Web应用的相关配置，包括Servlet初始化和CORS跨域设置。
 */
@Configuration
public class WebConfigurer implements WebApplicationInitializer {

    private static final Logger LOG = LoggerFactory.getLogger(WebConfigurer.class);

    // Spring环境变量
    private final Environment env;

    // JHipster相关配置属性
    private final JHipsterProperties jHipsterProperties;

    /**
     * 构造方法，注入环境变量和JHipster配置
     * @param env Spring环境
     * @param jHipsterProperties JHipster配置
     */
    public WebConfigurer(Environment env, JHipsterProperties jHipsterProperties) {
        this.env = env;
        this.jHipsterProperties = jHipsterProperties;
    }

    /**
     * Servlet容器启动时的回调方法
     * @param servletContext Servlet上下文
     * @throws ServletException 启动异常
     */
    @Override
    public void onStartup(ServletContext servletContext) throws ServletException {
        // 如果有激活的Spring profile，则输出日志
        if (env.getActiveProfiles().length != 0) {
            LOG.info("Web application configuration, using profiles: {}", (Object[]) env.getActiveProfiles());
        }

        LOG.info("Web application fully configured");
    }

    /**
     * 配置CORS跨域过滤器
     * @return CorsFilter实例
     */
    @Bean
    public CorsFilter corsFilter() {
        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        CorsConfiguration config = jHipsterProperties.getCors();
        // 如果配置了允许的跨域来源，则注册CORS过滤规则
        if (!CollectionUtils.isEmpty(config.getAllowedOrigins()) || !CollectionUtils.isEmpty(config.getAllowedOriginPatterns())) {
            LOG.debug("Registering CORS filter");
            source.registerCorsConfiguration("/api/**", config);
            source.registerCorsConfiguration("/management/**", config);
            source.registerCorsConfiguration("/v3/api-docs", config);
            source.registerCorsConfiguration("/swagger-ui/**", config);
        }
        return new CorsFilter(source);
    }
}

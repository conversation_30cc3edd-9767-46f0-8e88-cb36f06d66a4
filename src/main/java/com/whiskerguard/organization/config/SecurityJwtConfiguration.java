package com.whiskerguard.organization.config;

import com.nimbusds.jose.jwk.source.ImmutableSecret;
import com.whiskerguard.organization.management.SecurityMetersService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.oauth2.jwt.JwtDecoder;
import org.springframework.security.oauth2.jwt.JwtEncoder;
import org.springframework.security.oauth2.jwt.NimbusJwtDecoder;
import org.springframework.security.oauth2.jwt.NimbusJwtEncoder;

import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;

import static com.whiskerguard.organization.security.SecurityUtils.JWT_ALGORITHM;

@Configuration
public class SecurityJwtConfiguration {

    /**
     * 日志记录器
     */
    private static final Logger LOG = LoggerFactory.getLogger(SecurityJwtConfiguration.class);

    /**
     * JWT密钥
     * 从配置文件中获取Base64编码的密钥
     */
    @Value("${jhipster.security.authentication.jwt.base64-secret}")
    private String jwtKey;

    /**
     * JWT解码器
     * 用于验证和解析JWT令牌
     *
     * @param metersService 安全指标服务，用于跟踪令牌错误
     * @return JWT解码器
     */
    @Bean
    public JwtDecoder jwtDecoder(SecurityMetersService metersService) {
        NimbusJwtDecoder jwtDecoder = NimbusJwtDecoder.withSecretKey(getSecretKey()).macAlgorithm(JWT_ALGORITHM).build();
        return token -> {
            try {
                return jwtDecoder.decode(token);
            } catch (Exception e) {
                if (e.getMessage().contains("Invalid signature")) {
                    // 无效签名错误
                    metersService.trackTokenInvalidSignature();
                } else if (e.getMessage().contains("Jwt expired at")) {
                    // 令牌过期错误
                    metersService.trackTokenExpired();
                } else if (
                    e.getMessage().contains("Invalid JWT serialization") ||
                        e.getMessage().contains("Malformed token") ||
                        e.getMessage().contains("Invalid unsecured/JWS/JWE")
                ) {
                    // 格式错误的令牌
                    metersService.trackTokenMalformed();
                } else {
                    // 未知JWT错误
                    LOG.error("Unknown JWT error {}", e.getMessage());
                }
                throw e;
            }
        };
    }

    /**
     * JWT编码器
     * 用于创建JWT令牌
     *
     * @return JWT编码器
     */
    @Bean
    public JwtEncoder jwtEncoder() {
        return new NimbusJwtEncoder(new ImmutableSecret<>(getSecretKey()));
    }

    /**
     * 获取密钥
     * 将配置的密钥转换为SecretKey对象
     *
     * @return 密钥对象
     */
    private SecretKey getSecretKey() {
        byte[] keyBytes = jwtKey.getBytes();
        return new SecretKeySpec(keyBytes, 0, keyBytes.length, JWT_ALGORITHM.getName());
    }
}

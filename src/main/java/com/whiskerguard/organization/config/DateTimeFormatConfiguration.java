package com.whiskerguard.organization.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.format.FormatterRegistry;
import org.springframework.format.datetime.standard.DateTimeFormatterRegistrar;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * Configure the converters to use the ISO format for dates by default.
 * 配置日期时间格式转换器，默认使用ISO格式
 * 确保应用中所有日期时间的格式一致性
 */
@Configuration
public class DateTimeFormatConfiguration implements WebMvcConfigurer {

    /**
     * 添加格式化器到注册表
     * 设置日期时间格式为ISO标准格式
     * @param registry 格式化器注册表
     */
    @Override
    public void addFormatters(FormatterRegistry registry) {
        DateTimeFormatterRegistrar registrar = new DateTimeFormatterRegistrar();
        registrar.setUseIsoFormat(true); // 使用ISO格式
        registrar.registerFormatters(registry); // 注册格式化器
    }
}

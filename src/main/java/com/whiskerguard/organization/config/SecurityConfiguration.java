package com.whiskerguard.organization.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityCustomizer;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.servlet.util.matcher.MvcRequestMatcher;
import org.springframework.web.servlet.handler.HandlerMappingIntrospector;

@Configuration
@EnableMethodSecurity(securedEnabled = true, jsr250Enabled = true)
@EnableWebSecurity
public class SecurityConfiguration {
    
    /**
     * 配置安全过滤链
     *
     * @param http HTTP安全配置
     * @param mvc  MVC请求匹配器构建器
     * @return 安全过滤链
     * @throws Exception 配置异常
     */
    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http, MvcRequestMatcher.Builder mvc) throws Exception {
        //        http
        //            .csrf(csrf -> csrf.disable())
        //            .addFilterBefore(new TokenValidationFilter(tokenStoreService), BearerTokenAuthenticationFilter.class)
        //            .authorizeHttpRequests(authz ->
        //                // prettier-ignore
        //                authz
        //                    // 公开访问的接口
        //                    .requestMatchers(mvc.pattern(HttpMethod.POST, "/api/authenticate")).permitAll()
        //                    .requestMatchers(mvc.pattern(HttpMethod.GET, "/api/authenticate")).permitAll()
        //                    .requestMatchers(mvc.pattern("/management/health")).permitAll()
        //                    .requestMatchers(mvc.pattern("/management/health/**")).permitAll()
        //                    .requestMatchers(mvc.pattern("/management/info")).permitAll()
        //                    .requestMatchers(mvc.pattern("/management/prometheus")).permitAll()
        //
        //                    // 需要管理员权限的接口
        //                    .requestMatchers(mvc.pattern("/api/admin/**")).hasAuthority(AuthoritiesConstants.ADMIN)
        //                    .requestMatchers(mvc.pattern("/v3/api-docs/**")).hasAuthority(AuthoritiesConstants.ADMIN)
        //                    .requestMatchers(mvc.pattern("/management/**")).hasAuthority(AuthoritiesConstants.ADMIN)
        //
        //                    // 员工管理相关接口
        //                    .requestMatchers(mvc.pattern(HttpMethod.GET, "/api/employees")).hasAuthority("PERM_EMPLOYEE_VIEW")
        //                    .requestMatchers(mvc.pattern(HttpMethod.GET, "/api/employees/{id}")).hasAuthority("PERM_EMPLOYEE_VIEW")
        //                    .requestMatchers(mvc.pattern(HttpMethod.POST, "/api/employees")).hasAuthority("PERM_EMPLOYEE_MANAGE")
        //                    .requestMatchers(mvc.pattern(HttpMethod.PUT, "/api/employees/{id}")).hasAuthority("PERM_EMPLOYEE_MANAGE")
        //                    .requestMatchers(mvc.pattern(HttpMethod.PATCH, "/api/employees/{id}")).hasAuthority("PERM_EMPLOYEE_MANAGE")
        //                    .requestMatchers(mvc.pattern(HttpMethod.DELETE, "/api/employees/{id}")).hasAuthority("PERM_EMPLOYEE_MANAGE")
        //
        //                    // 角色管理相关接口
        //                    .requestMatchers(mvc.pattern(HttpMethod.GET, "/api/roles")).hasAuthority("PERM_ROLE_VIEW")
        //                    .requestMatchers(mvc.pattern(HttpMethod.GET, "/api/roles/{id}")).hasAuthority("PERM_ROLE_VIEW")
        //                    .requestMatchers(mvc.pattern(HttpMethod.POST, "/api/roles")).hasAuthority("PERM_ROLE_MANAGE")
        //                    .requestMatchers(mvc.pattern(HttpMethod.PUT, "/api/roles/{id}")).hasAuthority("PERM_ROLE_MANAGE")
        //                    .requestMatchers(mvc.pattern(HttpMethod.PATCH, "/api/roles/{id}")).hasAuthority("PERM_ROLE_MANAGE")
        //                    .requestMatchers(mvc.pattern(HttpMethod.DELETE, "/api/roles/{id}")).hasAuthority("PERM_ROLE_MANAGE")
        //
        //                    // 权限管理相关接口
        //                    .requestMatchers(mvc.pattern(HttpMethod.GET, "/api/permissions")).hasAuthority("PERM_PERMISSION_VIEW")
        //                    .requestMatchers(mvc.pattern(HttpMethod.GET, "/api/permissions/{id}")).hasAuthority("PERM_PERMISSION_VIEW")
        //                    .requestMatchers(mvc.pattern(HttpMethod.POST, "/api/permissions")).hasAuthority("PERM_PERMISSION_MANAGE")
        //                    .requestMatchers(mvc.pattern(HttpMethod.PUT, "/api/permissions/{id}")).hasAuthority("PERM_PERMISSION_MANAGE")
        //                    .requestMatchers(mvc.pattern(HttpMethod.PATCH, "/api/permissions/{id}")).hasAuthority("PERM_PERMISSION_MANAGE")
        //                    .requestMatchers(mvc.pattern(HttpMethod.DELETE, "/api/permissions/{id}")).hasAuthority("PERM_PERMISSION_MANAGE")
        //
        //                    // 组织单元管理相关接口
        //                    .requestMatchers(mvc.pattern(HttpMethod.GET, "/api/org-units")).hasAuthority("PERM_ORG_UNIT_VIEW")
        //                    .requestMatchers(mvc.pattern(HttpMethod.GET, "/api/org-units/{id}")).hasAuthority("PERM_ORG_UNIT_VIEW")
        //                    .requestMatchers(mvc.pattern(HttpMethod.POST, "/api/org-units")).hasAuthority("PERM_ORG_UNIT_MANAGE")
        //                    .requestMatchers(mvc.pattern(HttpMethod.PUT, "/api/org-units/{id}")).hasAuthority("PERM_ORG_UNIT_MANAGE")
        //                    .requestMatchers(mvc.pattern(HttpMethod.PATCH, "/api/org-units/{id}")).hasAuthority("PERM_ORG_UNIT_MANAGE")
        //                    .requestMatchers(mvc.pattern(HttpMethod.DELETE, "/api/org-units/{id}")).hasAuthority("PERM_ORG_UNIT_MANAGE")
        //
        //                    // 其他所有 API 接口需要认证
        //                    .requestMatchers(mvc.pattern("/api/**")).authenticated()
        //
        //                    // 默认拒绝所有其他请求
        //                    .anyRequest().denyAll()
        //            )
        //            .sessionManagement(session -> session.sessionCreationPolicy(SessionCreationPolicy.STATELESS))
        //            .exceptionHandling(exceptions ->
        //                exceptions
        //                    .authenticationEntryPoint(new BearerTokenAuthenticationEntryPoint())
        //                    .accessDeniedHandler(new BearerTokenAccessDeniedHandler())
        //            )
        //            .oauth2ResourceServer(oauth2 -> oauth2.jwt(withDefaults()));
        http
            .csrf(AbstractHttpConfigurer::disable)
            .authorizeHttpRequests(authz -> authz.anyRequest().permitAll());
        return http.build();
    }

    /**
     * 创建MVC请求匹配器构建器
     *
     * @param introspector 处理器映射内省器
     * @return MVC请求匹配器构建器
     */
    @Bean
    MvcRequestMatcher.Builder mvc(HandlerMappingIntrospector introspector) {
        return new MvcRequestMatcher.Builder(introspector);
    }

    /**
     * 自定义Web安全配置
     * 配置忽略特定路径的安全检查
     *
     * @return Web安全自定义器
     */
    @Bean
    public WebSecurityCustomizer webSecurityCustomizer() {
        return web -> web.ignoring().requestMatchers("/h2-console/**");
    }

    /**
     * 密码编码器
     * 使用BCrypt算法进行密码加密
     *
     * @return 密码编码器
     */
    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }
}

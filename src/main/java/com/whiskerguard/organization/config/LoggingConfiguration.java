package com.whiskerguard.organization.config;

import static tech.jhipster.config.logging.LoggingUtils.*;

import ch.qos.logback.classic.LoggerContext;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.util.HashMap;
import java.util.Map;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.info.BuildProperties;
import org.springframework.cloud.consul.serviceregistry.ConsulRegistration;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;
import tech.jhipster.config.JHipsterProperties;

/*
 * Configures the console and Logstash log appenders from the app properties
 */
/**
 * 日志配置类。
 * 该类根据应用配置动态设置控制台和Logstash日志输出格式（如JSON格式），
 * 并将应用名、端口、版本、实例ID等自定义字段注入日志，便于日志聚合和追踪。
 */
@Configuration
@RefreshScope
public class LoggingConfiguration {

    /**
     * 构造方法，初始化日志配置。
     * @param appName 应用名称
     * @param serverPort 服务端口
     * @param jHipsterProperties JHipster相关配置
     * @param consulRegistration Consul注册信息
     * @param buildProperties 构建属性（如版本号）
     * @param mapper Jackson对象映射器
     * @throws JsonProcessingException JSON处理异常
     */
    public LoggingConfiguration(
        @Value("${spring.application.name}") String appName,
        @Value("${server.port}") String serverPort,
        JHipsterProperties jHipsterProperties,
        ObjectProvider<ConsulRegistration> consulRegistration,
        ObjectProvider<BuildProperties> buildProperties,
        ObjectMapper mapper
    ) throws JsonProcessingException {
        // 获取Logback日志上下文
        LoggerContext context = (LoggerContext) LoggerFactory.getILoggerFactory();

        // 构建自定义日志字段
        Map<String, String> map = new HashMap<>();
        map.put("app_name", appName);
        map.put("app_port", serverPort);
        buildProperties.ifAvailable(it -> map.put("version", it.getVersion()));
        consulRegistration.ifAvailable(it -> map.put("instance_id", it.getInstanceId()));
        String customFields = mapper.writeValueAsString(map);

        // 获取日志相关配置
        JHipsterProperties.Logging loggingProperties = jHipsterProperties.getLogging();
        JHipsterProperties.Logging.Logstash logstashProperties = loggingProperties.getLogstash();

        // 如果启用JSON格式日志，添加JSON控制台输出
        if (loggingProperties.isUseJsonFormat()) {
            addJsonConsoleAppender(context, customFields);
        }
        // 如果启用Logstash日志，添加Logstash TCP输出
        if (logstashProperties.isEnabled()) {
            addLogstashTcpSocketAppender(context, customFields, logstashProperties);
        }
        // 如果启用JSON或Logstash日志，添加上下文监听器
        if (loggingProperties.isUseJsonFormat() || logstashProperties.isEnabled()) {
            addContextListener(context, customFields, loggingProperties);
        }
    }
}

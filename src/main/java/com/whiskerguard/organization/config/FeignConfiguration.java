package com.whiskerguard.organization.config;

import feign.Request;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.cloud.openfeign.FeignClientsConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

import java.time.Duration;

/**
 * Feign 客户端配置类。
 * 该类用于启用 Feign 客户端，并设置 Feign 的日志级别，方便调试 REST 客户端请求。
 */
@Configuration
@EnableFeignClients(basePackages = "com.whiskerguard.organization")
@Import(FeignClientsConfiguration.class)
public class FeignConfiguration {

    /**
     * 设置 Feign 的日志级别为 BASIC，记录基本的客户端 REST 请求日志。
     *
     * @return Feign 日志级别
     */
    @Bean
    feign.Logger.Level feignLoggerLevel() {
        return feign.Logger.Level.BASIC;
    }

    /**
     * 配置Feign请求选项
     * 法规查询可能涉及大量数据，需要较长的超时时间
     */
    @Bean
    public Request.Options feignRequestOptions() {
        //2分钟
        return new Request.Options(Duration.ofMinutes(2L), Duration.ofMinutes(2L), true);
    }
}

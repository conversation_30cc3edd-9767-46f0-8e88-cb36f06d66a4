package com.whiskerguard.organization.config;

import com.whiskerguard.organization.domain.enumeration.OrgUnitType;
import com.whiskerguard.organization.domain.enumeration.PositionCategory;
import com.whiskerguard.organization.domain.enumeration.ResourceType;

/**
 * 租户基础数据常量定义
 * 
 * 定义新租户初始化时需要创建的基础角色、部门和岗位常量。
 * 这些常量符合中国法律法规和企业合规要求，确保组织架构的规范性和完整性。
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public final class TenantBaseDataConstants {

    /**
     * 基础角色常量定义
     * 符合企业治理和合规要求的基本角色体系
     */
    public static final class BaseRoles {
        
        /** 租户管理员 - 租户最高权限管理者 */
        public static final String TENANT_ADMIN_CODE = "TENANT_ADMIN";
        public static final String TENANT_ADMIN_NAME = "租户管理员";
        public static final String TENANT_ADMIN_DESC = "租户最高权限管理者，负责租户内所有资源的管理和配置";
        
        /** 系统管理员 - 技术系统管理 */
        public static final String SYSTEM_ADMIN_CODE = "SYSTEM_ADMIN";
        public static final String SYSTEM_ADMIN_NAME = "系统管理员";
        public static final String SYSTEM_ADMIN_DESC = "负责系统技术管理、用户权限分配和系统配置";
        
        /** 人事管理员 - 人力资源管理 */
        public static final String HR_ADMIN_CODE = "HR_ADMIN";
        public static final String HR_ADMIN_NAME = "人事管理员";
        public static final String HR_ADMIN_DESC = "负责员工信息管理、组织架构维护和人事相关业务";
        
        /** 财务管理员 - 财务合规管理 */
        public static final String FINANCE_ADMIN_CODE = "FINANCE_ADMIN";
        public static final String FINANCE_ADMIN_NAME = "财务管理员";
        public static final String FINANCE_ADMIN_DESC = "负责财务数据管理、成本控制和财务合规监督";
        
        /** 审计员 - 内部审计监督 */
        public static final String AUDITOR_CODE = "AUDITOR";
        public static final String AUDITOR_NAME = "审计员";
        public static final String AUDITOR_DESC = "负责内部审计、合规检查和风险监控";
        
        /** 部门经理 - 部门管理 */
        public static final String DEPT_MANAGER_CODE = "DEPT_MANAGER";
        public static final String DEPT_MANAGER_NAME = "部门经理";
        public static final String DEPT_MANAGER_DESC = "负责部门日常管理、团队协调和业务执行";
        
        /** 普通员工 - 基础用户 */
        public static final String EMPLOYEE_CODE = "EMPLOYEE";
        public static final String EMPLOYEE_NAME = "普通员工";
        public static final String EMPLOYEE_DESC = "企业普通员工，具有基本的系统访问和操作权限";
        
        /** 访客用户 - 临时访问 */
        public static final String GUEST_CODE = "GUEST";
        public static final String GUEST_NAME = "访客用户";
        public static final String GUEST_DESC = "临时访问用户，仅具有有限的查看权限";
    }
    
    /**
     * 基础部门常量定义
     * 符合现代企业组织架构和法律合规要求的基础部门设置
     */
    public static final class BaseDepartments {
        
        /** 总经理办公室 - 最高管理层 */
        public static final String CEO_OFFICE_CODE = "CEO_OFFICE";
        public static final String CEO_OFFICE_NAME = "总经理办公室";
        public static final String CEO_OFFICE_DESC = "企业最高管理层，负责战略决策和整体运营管理";
        public static final OrgUnitType CEO_OFFICE_TYPE = OrgUnitType.DEPARTMENT;
        
        /** 人力资源部 - 人事管理 */
        public static final String HR_DEPT_CODE = "HR_DEPT";
        public static final String HR_DEPT_NAME = "人力资源部";
        public static final String HR_DEPT_DESC = "负责人员招聘、培训、绩效管理和员工关系维护";
        public static final OrgUnitType HR_DEPT_TYPE = OrgUnitType.DEPARTMENT;
        
        /** 财务部 - 财务管理 */
        public static final String FINANCE_DEPT_CODE = "FINANCE_DEPT";
        public static final String FINANCE_DEPT_NAME = "财务部";
        public static final String FINANCE_DEPT_DESC = "负责财务核算、资金管理、成本控制和财务合规";
        public static final OrgUnitType FINANCE_DEPT_TYPE = OrgUnitType.DEPARTMENT;
        
        /** 法务部 - 法律合规 */
        public static final String LEGAL_DEPT_CODE = "LEGAL_DEPT";
        public static final String LEGAL_DEPT_NAME = "法务部";
        public static final String LEGAL_DEPT_DESC = "负责法律事务处理、合同管理和合规风险控制";
        public static final OrgUnitType LEGAL_DEPT_TYPE = OrgUnitType.DEPARTMENT;
        
        /** 行政部 - 行政管理 */
        public static final String ADMIN_DEPT_CODE = "ADMIN_DEPT";
        public static final String ADMIN_DEPT_NAME = "行政部";
        public static final String ADMIN_DEPT_DESC = "负责行政事务管理、办公环境维护和后勤保障";
        public static final OrgUnitType ADMIN_DEPT_TYPE = OrgUnitType.DEPARTMENT;
        
        /** 信息技术部 - 技术支持 */
        public static final String IT_DEPT_CODE = "IT_DEPT";
        public static final String IT_DEPT_NAME = "信息技术部";
        public static final String IT_DEPT_DESC = "负责信息系统建设、技术支持和网络安全管理";
        public static final OrgUnitType IT_DEPT_TYPE = OrgUnitType.DEPARTMENT;
        
        /** 市场部 - 市场营销 */
        public static final String MARKETING_DEPT_CODE = "MARKETING_DEPT";
        public static final String MARKETING_DEPT_NAME = "市场部";
        public static final String MARKETING_DEPT_DESC = "负责市场调研、品牌推广和营销策略制定";
        public static final OrgUnitType MARKETING_DEPT_TYPE = OrgUnitType.DEPARTMENT;
        
        /** 销售部 - 销售管理 */
        public static final String SALES_DEPT_CODE = "SALES_DEPT";
        public static final String SALES_DEPT_NAME = "销售部";
        public static final String SALES_DEPT_DESC = "负责产品销售、客户关系维护和销售目标达成";
        public static final OrgUnitType SALES_DEPT_TYPE = OrgUnitType.DEPARTMENT;
        
        /** 客服部 - 客户服务 */
        public static final String CUSTOMER_SERVICE_DEPT_CODE = "CUSTOMER_SERVICE_DEPT";
        public static final String CUSTOMER_SERVICE_DEPT_NAME = "客服部";
        public static final String CUSTOMER_SERVICE_DEPT_DESC = "负责客户服务、售后支持和客户满意度管理";
        public static final OrgUnitType CUSTOMER_SERVICE_DEPT_TYPE = OrgUnitType.DEPARTMENT;
    }
    
    /**
     * 基础岗位常量定义
     * 符合现代企业管理和职业发展要求的基础岗位体系
     */
    public static final class BasePositions {
        
        // 管理类岗位
        /** 总经理 */
        public static final String CEO_CODE = "CEO";
        public static final String CEO_NAME = "总经理";
        public static final String CEO_DESC = "企业最高管理者，负责企业战略规划和整体运营";
        public static final PositionCategory CEO_CATEGORY = PositionCategory.MANAGEMENT;
        public static final Integer CEO_LEVEL = 10;
        
        /** 副总经理 */
        public static final String VICE_CEO_CODE = "VICE_CEO";
        public static final String VICE_CEO_NAME = "副总经理";
        public static final String VICE_CEO_DESC = "协助总经理管理企业，负责特定业务领域";
        public static final PositionCategory VICE_CEO_CATEGORY = PositionCategory.MANAGEMENT;
        public static final Integer VICE_CEO_LEVEL = 9;
        
        /** 部门总监 */
        public static final String DIRECTOR_CODE = "DIRECTOR";
        public static final String DIRECTOR_NAME = "部门总监";
        public static final String DIRECTOR_DESC = "部门最高管理者，负责部门战略规划和管理";
        public static final PositionCategory DIRECTOR_CATEGORY = PositionCategory.MANAGEMENT;
        public static final Integer DIRECTOR_LEVEL = 8;
        
        /** 部门经理 */
        public static final String MANAGER_CODE = "MANAGER";
        public static final String MANAGER_NAME = "部门经理";
        public static final String MANAGER_DESC = "部门日常管理者，负责团队管理和业务执行";
        public static final PositionCategory MANAGER_CATEGORY = PositionCategory.MANAGEMENT;
        public static final Integer MANAGER_LEVEL = 7;
        
        /** 主管 */
        public static final String SUPERVISOR_CODE = "SUPERVISOR";
        public static final String SUPERVISOR_NAME = "主管";
        public static final String SUPERVISOR_DESC = "基层管理者，负责小组管理和任务协调";
        public static final PositionCategory SUPERVISOR_CATEGORY = PositionCategory.MANAGEMENT;
        public static final Integer SUPERVISOR_LEVEL = 6;
        
        // 技术类岗位
        /** 技术总监 */
        public static final String CTO_CODE = "CTO";
        public static final String CTO_NAME = "技术总监";
        public static final String CTO_DESC = "技术部门最高管理者，负责技术战略和架构规划";
        public static final PositionCategory CTO_CATEGORY = PositionCategory.TECHNICAL;
        public static final Integer CTO_LEVEL = 8;
        
        /** 高级工程师 */
        public static final String SENIOR_ENGINEER_CODE = "SENIOR_ENGINEER";
        public static final String SENIOR_ENGINEER_NAME = "高级工程师";
        public static final String SENIOR_ENGINEER_DESC = "资深技术专家，负责核心技术开发和技术指导";
        public static final PositionCategory SENIOR_ENGINEER_CATEGORY = PositionCategory.TECHNICAL;
        public static final Integer SENIOR_ENGINEER_LEVEL = 6;
        
        /** 工程师 */
        public static final String ENGINEER_CODE = "ENGINEER";
        public static final String ENGINEER_NAME = "工程师";
        public static final String ENGINEER_DESC = "技术开发人员，负责具体技术实现和维护";
        public static final PositionCategory ENGINEER_CATEGORY = PositionCategory.TECHNICAL;
        public static final Integer ENGINEER_LEVEL = 5;
        
        /** 初级工程师 */
        public static final String JUNIOR_ENGINEER_CODE = "JUNIOR_ENGINEER";
        public static final String JUNIOR_ENGINEER_NAME = "初级工程师";
        public static final String JUNIOR_ENGINEER_DESC = "技术新人，负责基础技术工作和学习成长";
        public static final PositionCategory JUNIOR_ENGINEER_CATEGORY = PositionCategory.TECHNICAL;
        public static final Integer JUNIOR_ENGINEER_LEVEL = 3;
        
        // 业务类岗位
        /** 业务总监 */
        public static final String BUSINESS_DIRECTOR_CODE = "BUSINESS_DIRECTOR";
        public static final String BUSINESS_DIRECTOR_NAME = "业务总监";
        public static final String BUSINESS_DIRECTOR_DESC = "业务部门最高管理者，负责业务战略和市场拓展";
        public static final PositionCategory BUSINESS_DIRECTOR_CATEGORY = PositionCategory.BUSINESS;
        public static final Integer BUSINESS_DIRECTOR_LEVEL = 8;
        
        /** 高级专员 */
        public static final String SENIOR_SPECIALIST_CODE = "SENIOR_SPECIALIST";
        public static final String SENIOR_SPECIALIST_NAME = "高级专员";
        public static final String SENIOR_SPECIALIST_DESC = "资深业务专家，负责复杂业务处理和专业指导";
        public static final PositionCategory SENIOR_SPECIALIST_CATEGORY = PositionCategory.BUSINESS;
        public static final Integer SENIOR_SPECIALIST_LEVEL = 6;
        
        /** 专员 */
        public static final String SPECIALIST_CODE = "SPECIALIST";
        public static final String SPECIALIST_NAME = "专员";
        public static final String SPECIALIST_DESC = "业务执行人员，负责具体业务操作和客户服务";
        public static final PositionCategory SPECIALIST_CATEGORY = PositionCategory.BUSINESS;
        public static final Integer SPECIALIST_LEVEL = 4;
        
        /** 助理 */
        public static final String ASSISTANT_CODE = "ASSISTANT";
        public static final String ASSISTANT_NAME = "助理";
        public static final String ASSISTANT_DESC = "业务辅助人员，负责基础业务支持和协助工作";
        public static final PositionCategory ASSISTANT_CATEGORY = PositionCategory.BUSINESS;
        public static final Integer ASSISTANT_LEVEL = 2;
        
        // 支持类岗位
        /** 行政专员 */
        public static final String ADMIN_SPECIALIST_CODE = "ADMIN_SPECIALIST";
        public static final String ADMIN_SPECIALIST_NAME = "行政专员";
        public static final String ADMIN_SPECIALIST_DESC = "行政事务处理人员，负责日常行政管理和后勤保障";
        public static final PositionCategory ADMIN_SPECIALIST_CATEGORY = PositionCategory.SUPPORT;
        public static final Integer ADMIN_SPECIALIST_LEVEL = 4;
        
        /** 人事专员 */
        public static final String HR_SPECIALIST_CODE = "HR_SPECIALIST";
        public static final String HR_SPECIALIST_NAME = "人事专员";
        public static final String HR_SPECIALIST_DESC = "人力资源管理人员，负责招聘、培训和员工关系";
        public static final PositionCategory HR_SPECIALIST_CATEGORY = PositionCategory.SUPPORT;
        public static final Integer HR_SPECIALIST_LEVEL = 4;
        
        /** 财务专员 */
        public static final String FINANCE_SPECIALIST_CODE = "FINANCE_SPECIALIST";
        public static final String FINANCE_SPECIALIST_NAME = "财务专员";
        public static final String FINANCE_SPECIALIST_DESC = "财务管理人员，负责财务核算和资金管理";
        public static final PositionCategory FINANCE_SPECIALIST_CATEGORY = PositionCategory.SUPPORT;
        public static final Integer FINANCE_SPECIALIST_LEVEL = 4;
        
        /** 法务专员 */
        public static final String LEGAL_SPECIALIST_CODE = "LEGAL_SPECIALIST";
        public static final String LEGAL_SPECIALIST_NAME = "法务专员";
        public static final String LEGAL_SPECIALIST_DESC = "法律事务处理人员，负责合同管理和法律风险控制";
        public static final PositionCategory LEGAL_SPECIALIST_CATEGORY = PositionCategory.SUPPORT;
        public static final Integer LEGAL_SPECIALIST_LEVEL = 5;
    }
    
    /**
     * 基础权限常量定义
     * 符合企业管理和合规要求的基础权限体系
     */
    public static final class BasePermissions {
        
        // 系统管理权限
        /** 用户管理 */
        public static final String USER_MANAGEMENT_CODE = "USER_MANAGEMENT";
        public static final String USER_MANAGEMENT_NAME = "用户管理";
        public static final ResourceType USER_MANAGEMENT_TYPE = ResourceType.MENU;
        
        /** 角色管理 */
        public static final String ROLE_MANAGEMENT_CODE = "ROLE_MANAGEMENT";
        public static final String ROLE_MANAGEMENT_NAME = "角色管理";
        public static final ResourceType ROLE_MANAGEMENT_TYPE = ResourceType.MENU;
        
        /** 权限管理 */
        public static final String PERMISSION_MANAGEMENT_CODE = "PERMISSION_MANAGEMENT";
        public static final String PERMISSION_MANAGEMENT_NAME = "权限管理";
        public static final ResourceType PERMISSION_MANAGEMENT_TYPE = ResourceType.MENU;
        
        /** 组织管理 */
        public static final String ORG_MANAGEMENT_CODE = "ORG_MANAGEMENT";
        public static final String ORG_MANAGEMENT_NAME = "组织管理";
        public static final ResourceType ORG_MANAGEMENT_TYPE = ResourceType.MENU;
        
        /** 岗位管理 */
        public static final String POSITION_MANAGEMENT_CODE = "POSITION_MANAGEMENT";
        public static final String POSITION_MANAGEMENT_NAME = "岗位管理";
        public static final ResourceType POSITION_MANAGEMENT_TYPE = ResourceType.MENU;
        
        // 业务操作权限
        /** 数据查看 */
        public static final String DATA_VIEW_CODE = "DATA_VIEW";
        public static final String DATA_VIEW_NAME = "数据查看";
        public static final ResourceType DATA_VIEW_TYPE = ResourceType.DATA_SCOPE;
        
        /** 数据编辑 */
        public static final String DATA_EDIT_CODE = "DATA_EDIT";
        public static final String DATA_EDIT_NAME = "数据编辑";
        public static final ResourceType DATA_EDIT_TYPE = ResourceType.DATA_SCOPE;
        
        /** 数据删除 */
        public static final String DATA_DELETE_CODE = "DATA_DELETE";
        public static final String DATA_DELETE_NAME = "数据删除";
        public static final ResourceType DATA_DELETE_TYPE = ResourceType.DATA_SCOPE;
        
        /** 数据导出 */
        public static final String DATA_EXPORT_CODE = "DATA_EXPORT";
        public static final String DATA_EXPORT_NAME = "数据导出";
        public static final ResourceType DATA_EXPORT_TYPE = ResourceType.BUTTON;
        
        /** 数据导入 */
        public static final String DATA_IMPORT_CODE = "DATA_IMPORT";
        public static final String DATA_IMPORT_NAME = "数据导入";
        public static final ResourceType DATA_IMPORT_TYPE = ResourceType.BUTTON;
        
        // 审计和合规权限
        /** 审计日志查看 */
        public static final String AUDIT_LOG_VIEW_CODE = "AUDIT_LOG_VIEW";
        public static final String AUDIT_LOG_VIEW_NAME = "审计日志查看";
        public static final ResourceType AUDIT_LOG_VIEW_TYPE = ResourceType.MENU;
        
        /** 系统监控 */
        public static final String SYSTEM_MONITOR_CODE = "SYSTEM_MONITOR";
        public static final String SYSTEM_MONITOR_NAME = "系统监控";
        public static final ResourceType SYSTEM_MONITOR_TYPE = ResourceType.MENU;
    }
    
    /**
     * 状态常量定义
     */
    public static final class Status {
        /** 启用状态 */
        public static final Integer ENABLED = 1;
        
        /** 禁用状态 */
        public static final Integer DISABLED = 0;
    }
    
    /**
     * 默认排序常量
     */
    public static final class DefaultSort {
        /** 默认排序起始值 */
        public static final Integer DEFAULT_SORT_ORDER = 100;
        
        /** 排序间隔 */
        public static final Integer SORT_INTERVAL = 10;
    }
    
    /**
     * 私有构造函数
     * 防止实例化常量类
     */
    private TenantBaseDataConstants() {}
}
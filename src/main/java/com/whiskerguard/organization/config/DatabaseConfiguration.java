package com.whiskerguard.organization.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * 数据库配置类
 * 配置JPA仓库、审计和事务管理
 */
@Configuration
@EnableJpaRepositories({ "com.whiskerguard.organization.repository" }) // 启用JPA仓库，指定仓库包路径
@EnableJpaAuditing(auditorAwareRef = "springSecurityAuditorAware") // 启用JPA审计，使用Spring Security审计器
@EnableTransactionManagement // 启用事务管理
public class DatabaseConfiguration {}

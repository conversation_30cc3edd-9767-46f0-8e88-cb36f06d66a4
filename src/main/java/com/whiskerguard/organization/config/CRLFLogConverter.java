package com.whiskerguard.organization.config;

import ch.qos.logback.classic.spi.ILoggingEvent;
import ch.qos.logback.core.pattern.CompositeConverter;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.slf4j.Marker;
import org.slf4j.MarkerFactory;
import org.springframework.boot.ansi.AnsiColor;
import org.springframework.boot.ansi.AnsiElement;
import org.springframework.boot.ansi.AnsiOutput;
import org.springframework.boot.ansi.AnsiStyle;

/**
 * Log filter to prevent attackers from forging log entries by submitting input containing CRLF characters.
 * CRLF characters are replaced with a red colored _ character.
 * 日志过滤器，防止攻击者通过提交包含CRLF字符的输入来伪造日志条目。
 * CRLF字符会被替换为红色的_字符。
 *
 * @see <a href="https://owasp.org/www-community/attacks/Log_Injection">Log Forging Description</a>
 * @see <a href="https://github.com/jhipster/generator-jhipster/issues/14949">JHipster issue</a>
 */
public class CRLFLogConverter extends CompositeConverter<ILoggingEvent> {

    /**
     * 安全标记，用于标记不需要进行CRLF过滤的日志
     */
    public static final Marker CRLF_SAFE_MARKER = MarkerFactory.getMarker("CRLF_SAFE");

    /**
     * 安全日志包名列表，这些包下的日志不需要进行CRLF过滤
     */
    private static final String[] SAFE_LOGS = {
        "org.hibernate",
        "org.springframework.boot.autoconfigure",
        "org.springframework.boot.diagnostics",
    };

    /**
     * ANSI颜色元素映射表
     */
    private static final Map<String, AnsiElement> ELEMENTS;

    /**
     * 静态初始化块，初始化ANSI颜色元素映射表
     */
    static {
        Map<String, AnsiElement> ansiElements = new HashMap<>();
        ansiElements.put("faint", AnsiStyle.FAINT); // 暗淡样式
        ansiElements.put("red", AnsiColor.RED); // 红色
        ansiElements.put("green", AnsiColor.GREEN); // 绿色
        ansiElements.put("yellow", AnsiColor.YELLOW); // 黄色
        ansiElements.put("blue", AnsiColor.BLUE); // 蓝色
        ansiElements.put("magenta", AnsiColor.MAGENTA); // 洋红色
        ansiElements.put("cyan", AnsiColor.CYAN); // 青色
        ELEMENTS = Collections.unmodifiableMap(ansiElements); // 创建不可变映射
    }

    @Override
    protected String transform(ILoggingEvent event, String in) {
        // 获取ANSI颜色元素
        AnsiElement element = ELEMENTS.get(getFirstOption());
        // 获取日志标记列表
        List<Marker> markers = event.getMarkerList();
        // 如果日志包含CRLF_SAFE标记或属于安全日志包，则不进行CRLF过滤
        if ((markers != null && !markers.isEmpty() && markers.get(0).contains(CRLF_SAFE_MARKER)) || isLoggerSafe(event)) {
            return in;
        }
        // 替换CRLF字符为带颜色的下划线
        String replacement = element == null ? "_" : toAnsiString("_", element);
        return in.replaceAll("[\n\r\t]", replacement);
    }

    /**
     * 判断日志记录器是否属于安全日志包
     * @param event 日志事件
     * @return 如果是安全日志包则返回true，否则返回false
     */
    protected boolean isLoggerSafe(ILoggingEvent event) {
        for (String safeLogger : SAFE_LOGS) {
            // 判断日志记录器名称是否以安全包名开头
            if (event.getLoggerName().startsWith(safeLogger)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 将字符串使用指定的ANSI颜色元素进行着色
     * @param in 需要着色的字符串
     * @param element ANSI颜色元素
     * @return 着色后的字符串
     */
    protected String toAnsiString(String in, AnsiElement element) {
        return AnsiOutput.toString(element, in);
    }
}

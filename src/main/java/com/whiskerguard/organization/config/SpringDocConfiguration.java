package com.whiskerguard.organization.config;

import java.util.List;
import org.springdoc.core.customizers.ServerBaseUrlCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * SpringDoc配置类
 * 配置OpenAPI文档生成相关设置
 */
@Configuration
public class SpringDocConfiguration {

    /**
     * 服务器基础URL自定义器
     * 处理反向代理场景下的API文档URL
     * 支持通过X-Forwarded-Prefix头部调整基础URL
     * @return 服务器基础URL自定义器
     */
    @Bean
    public ServerBaseUrlCustomizer serverBaseUrlRequestCustomizer() {
        return (serverBaseUrl, request) -> {
            List<String> forwardedPrefix = request.getHeaders().get("X-Forwarded-Prefix");
            if (forwardedPrefix != null && forwardedPrefix.size() > 0) {
                return forwardedPrefix.get(0);
            }
            return serverBaseUrl;
        };
    }
}

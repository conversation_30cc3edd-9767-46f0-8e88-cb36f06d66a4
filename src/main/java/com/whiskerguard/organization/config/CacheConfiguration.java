package com.whiskerguard.organization.config;

import com.fasterxml.jackson.annotation.JsonTypeInfo;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.jsontype.BasicPolymorphicTypeValidator;
import org.hibernate.cache.jcache.ConfigSettings;
import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.codec.JsonJacksonCodec;
import org.redisson.config.ClusterServersConfig;
import org.redisson.config.Config;
import org.redisson.config.SingleServerConfig;
import org.redisson.jcache.configuration.RedissonConfiguration;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.cache.JCacheManagerCustomizer;
import org.springframework.boot.autoconfigure.orm.jpa.HibernatePropertiesCustomizer;
import org.springframework.boot.info.BuildProperties;
import org.springframework.boot.info.GitProperties;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.interceptor.KeyGenerator;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import tech.jhipster.config.JHipsterProperties;
import tech.jhipster.config.cache.PrefixedKeyGenerator;

import javax.cache.configuration.MutableConfiguration;
import javax.cache.expiry.CreatedExpiryPolicy;
import javax.cache.expiry.Duration;
import java.net.URI;
import java.util.concurrent.TimeUnit;

/**
 * 缓存配置类。
 * 该类负责配置JCache（JSR-107）和Redisson作为缓存实现，支持单机和集群模式，
 * 并根据JHipster配置动态设置缓存参数。
 */
@Configuration
@EnableCaching
public class CacheConfiguration {

    // Git版本信息
    private GitProperties gitProperties;
    // 构建版本信息
    private BuildProperties buildProperties;

    /**
     * 配置JCache缓存参数和Redisson客户端。
     *
     * @param jHipsterProperties JHipster相关配置
     * @return JCache配置对象
     */
    @Bean
    public javax.cache.configuration.Configuration<Object, Object> jcacheConfiguration(JHipsterProperties jHipsterProperties) {
        MutableConfiguration<Object, Object> jcacheConfig = new MutableConfiguration<>();

        // 获取Redis连接地址
        URI redisUri = URI.create(jHipsterProperties.getCache().getRedis().getServer()[0]);

        Config config = new Config();
        ObjectMapper mapper = new ObjectMapper();
        mapper.activateDefaultTyping(
            BasicPolymorphicTypeValidator.builder().allowIfSubType(Object.class).build(),
            ObjectMapper.DefaultTyping.NON_FINAL,
            JsonTypeInfo.As.PROPERTY
        );
        config.setCodec(new JsonJacksonCodec(mapper));

        if (jHipsterProperties.getCache().getRedis().isCluster()) {
            // 配置Redis集群模式
            ClusterServersConfig clusterServersConfig = config
                .useClusterServers()
                .setMasterConnectionPoolSize(jHipsterProperties.getCache().getRedis().getConnectionPoolSize())
                .setMasterConnectionMinimumIdleSize(jHipsterProperties.getCache().getRedis().getConnectionMinimumIdleSize())
                .setSubscriptionConnectionPoolSize(jHipsterProperties.getCache().getRedis().getSubscriptionConnectionPoolSize())
                .addNodeAddress(jHipsterProperties.getCache().getRedis().getServer());

            if (redisUri.getUserInfo() != null) {
                clusterServersConfig.setPassword(redisUri.getUserInfo().substring(redisUri.getUserInfo().indexOf(':') + 1));
            }
        } else {
            // 配置Redis单机模式
            SingleServerConfig singleServerConfig = config
                .useSingleServer()
                .setConnectionPoolSize(jHipsterProperties.getCache().getRedis().getConnectionPoolSize())
                .setConnectionMinimumIdleSize(jHipsterProperties.getCache().getRedis().getConnectionMinimumIdleSize())
                .setSubscriptionConnectionPoolSize(jHipsterProperties.getCache().getRedis().getSubscriptionConnectionPoolSize())
                .setAddress(jHipsterProperties.getCache().getRedis().getServer()[0]);

            if (redisUri.getUserInfo() != null) {
                singleServerConfig.setPassword(redisUri.getUserInfo().substring(redisUri.getUserInfo().indexOf(':') + 1));
            }
        }
        jcacheConfig.setStatisticsEnabled(true);
        jcacheConfig.setExpiryPolicyFactory(
            CreatedExpiryPolicy.factoryOf(new Duration(TimeUnit.SECONDS, jHipsterProperties.getCache().getRedis().getExpiration()))
        );
        return RedissonConfiguration.fromInstance(Redisson.create(config), jcacheConfig);
    }

    /**
     * 配置Hibernate属性自定义器。
     *
     * @param cm JCache缓存管理器
     * @return Hibernate属性自定义器
     */
    @Bean
    public HibernatePropertiesCustomizer hibernatePropertiesCustomizer(javax.cache.CacheManager cm) {
        return hibernateProperties -> hibernateProperties.put(ConfigSettings.CACHE_MANAGER, cm);
    }

    /**
     * 配置JCache缓存管理器自定义器。
     *
     * @param jcacheConfiguration JCache配置对象
     * @return JCache缓存管理器自定义器
     */
    @Bean
    public JCacheManagerCustomizer cacheManagerCustomizer(javax.cache.configuration.Configuration<Object, Object> jcacheConfiguration) {
        return cm -> {
            createCache(cm, com.whiskerguard.organization.domain.OrgUnit.class.getName(), jcacheConfiguration);
            createCache(cm, com.whiskerguard.organization.domain.Employee.class.getName(), jcacheConfiguration);
            createCache(cm, com.whiskerguard.organization.domain.Position.class.getName(), jcacheConfiguration);
            createCache(cm, com.whiskerguard.organization.domain.EmployeeOrg.class.getName(), jcacheConfiguration);
            createCache(cm, com.whiskerguard.organization.domain.Role.class.getName(), jcacheConfiguration);
            createCache(cm, com.whiskerguard.organization.domain.Permission.class.getName(), jcacheConfiguration);
            createCache(cm, com.whiskerguard.organization.domain.RolePermission.class.getName(), jcacheConfiguration);
            createCache(cm, com.whiskerguard.organization.domain.EmployeeRole.class.getName(), jcacheConfiguration);
            createCache(cm, com.whiskerguard.organization.domain.AuditLog.class.getName(), jcacheConfiguration);
            createCache(cm, com.whiskerguard.organization.domain.Tenant.class.getName(), jcacheConfiguration);
            createCache(cm, com.whiskerguard.organization.domain.TenantProfile.class.getName(), jcacheConfiguration);
            createCache(cm, com.whiskerguard.organization.domain.TenantAttachment.class.getName(), jcacheConfiguration);
            createCache(cm, com.whiskerguard.organization.domain.NewsCategory.class.getName(), jcacheConfiguration);
            createCache(cm, com.whiskerguard.organization.domain.TagCategory.class.getName(), jcacheConfiguration);
            createCache(cm, com.whiskerguard.organization.domain.Tag.class.getName(), jcacheConfiguration);
            createCache(cm, com.whiskerguard.organization.domain.Tag.class.getName() + ".news", jcacheConfiguration);
            createCache(cm, com.whiskerguard.organization.domain.News.class.getName(), jcacheConfiguration);
            createCache(cm, com.whiskerguard.organization.domain.News.class.getName() + ".tags", jcacheConfiguration);
            createCache(cm, com.whiskerguard.organization.domain.NewsComment.class.getName(), jcacheConfiguration);
            createCache(cm, com.whiskerguard.organization.domain.NewsLike.class.getName(), jcacheConfiguration);
            createCache(cm, com.whiskerguard.organization.domain.NewsAttachment.class.getName(), jcacheConfiguration);
            createCache(cm, com.whiskerguard.organization.domain.NewsReport.class.getName(), jcacheConfiguration);
            createCache(cm, com.whiskerguard.organization.domain.NewsReadRecord.class.getName(), jcacheConfiguration);
            // jhipster-needle-redis-add-entry
        };
    }

    /**
     * 创建缓存。
     *
     * @param cm                  JCache缓存管理器
     * @param cacheName           缓存名称
     * @param jcacheConfiguration JCache配置对象
     */
    private void createCache(
        javax.cache.CacheManager cm,
        String cacheName,
        javax.cache.configuration.Configuration<Object, Object> jcacheConfiguration
    ) {
        javax.cache.Cache<Object, Object> cache = cm.getCache(cacheName);
        if (cache != null) {
            cache.clear();
        } else {
            cm.createCache(cacheName, jcacheConfiguration);
        }
    }

    /**
     * 设置Git版本信息。
     *
     * @param gitProperties Git版本信息
     */
    @Autowired(required = false)
    public void setGitProperties(GitProperties gitProperties) {
        this.gitProperties = gitProperties;
    }

    /**
     * 设置构建版本信息。
     *
     * @param buildProperties 构建版本信息
     */
    @Autowired(required = false)
    public void setBuildProperties(BuildProperties buildProperties) {
        this.buildProperties = buildProperties;
    }

    /**
     * 配置缓存键生成器。
     *
     * @return 缓存键生成器
     */
    @Bean
    public KeyGenerator keyGenerator() {
        return new PrefixedKeyGenerator(this.gitProperties, this.buildProperties);
    }

    @Bean(destroyMethod = "shutdown")
    public RedissonClient getRedissonClient(JHipsterProperties jHipsterProperties) {
        RedissonConfiguration redissonConfiguration = (RedissonConfiguration) jcacheConfiguration(jHipsterProperties);
        return redissonConfiguration.getRedisson();
    }
}

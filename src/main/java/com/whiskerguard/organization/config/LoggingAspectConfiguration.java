package com.whiskerguard.organization.config;

import com.whiskerguard.organization.aop.logging.LoggingAspect;
import org.springframework.context.annotation.*;
import org.springframework.core.env.Environment;
import tech.jhipster.config.JHipsterConstants;

/**
 * 日志切面配置类。
 * 该类用于在开发环境下启用AOP日志切面，便于方法调用的日志记录和调试。
 */
@Configuration
@EnableAspectJAutoProxy
public class LoggingAspectConfiguration {

    /**
     * 仅在开发环境下启用日志切面Bean。
     * @param env Spring环境变量
     * @return LoggingAspect实例
     */
    @Bean
//    @Profile(JHipsterConstants.SPRING_PROFILE_DEVELOPMENT)
    public LoggingAspect loggingAspect(Environment env) {
        return new LoggingAspect(env);
    }
}

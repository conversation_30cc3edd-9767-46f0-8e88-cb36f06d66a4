package com.whiskerguard.organization.client.ai;


import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

/**
 * AI服务Feign客户端的回退工厂
 * 当用户服务不可用时，提供默认的回退实现
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-19
 */
@Component
public class AiServiceFallbackFactory implements FallbackFactory<AiServiceFeignClient> {

    private static final Logger log = LoggerFactory.getLogger(AiServiceFallbackFactory.class);

    @Override
    public AiServiceFeignClient create(Throwable cause) {
        return request -> {
            log.error("调用AI服务失败: {}", cause.getMessage());
            return ResponseEntity.ok().body(null);
        };
    }
}

/**
 * =============================================================================
 * 公司名称：中合数联（苏州）科技有限公司
 * 项目名称：猫伯伯合规管家项目 - whiskerguard-org-service
 * 文件名称：null.java
 * 包    名：com.whiskerguard.organization.client
 * 描    述：猫伯伯合规管家公共微服务：提供通用工具类和帮助模块、云对象存储（COS）服务、分布式配置与安全组件、消息和事件驱动服务、缓存与分布式协同、国际化、多语言支持、公共 API 响应与文档功能
 * 作    者：[yanhaishui]
 * 邮    箱：<EMAIL>
 * 创建日期：2025/5/21
 * 版本信息：1.0
 * =============================================================================
 * 修订记录：
 * 1. [修改日期] [修改人] - [修改描述]
 * =============================================================================
 */

package com.whiskerguard.organization.client.ai;

import com.whiskerguard.common.dto.ai.AiInvocationRequestDTO;
import com.whiskerguard.common.dto.ai.AiRequestDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * Feign 客户端，用于调用 whiskerguard-ai-service 的 API
 */
@FeignClient(name = "whiskerguardaiservice", path = "/api", fallbackFactory = AiServiceFallbackFactory.class)
public interface AiServiceFeignClient {

    /**
     * 通用AI调用接口
     * 对应 ai-service 的 POST /api/ai/invoke
     *
     * @param request AI调用请求
     * @return AI响应
     */
    @PostMapping("/ai/invoke")
    ResponseEntity<AiRequestDTO> invokeAi(@RequestBody AiInvocationRequestDTO request);

}

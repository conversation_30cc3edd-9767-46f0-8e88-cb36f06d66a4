package com.whiskerguard.organization.client.contract;


import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * 合同服务Feign客户端的回退工厂
 * 当用户服务不可用时，提供默认的回退实现
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-19
 */
@Component
public class ContractServiceFallbackFactory implements FallbackFactory<ContractServiceClient> {

    private static final Logger log = LoggerFactory.getLogger(ContractServiceFallbackFactory.class);

    @Override
    public ContractServiceClient create(Throwable cause) {
        return (Long tenantId, String username) -> {
            log.error("初始化合同服务失败", cause);
            return null;
        };
    }
}

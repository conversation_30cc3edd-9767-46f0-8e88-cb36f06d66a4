package com.whiskerguard.organization.client.contract;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 合同服务客户端 - 用于调用whiskerguard-contract-service的接口
 * 基于实际的contract-service API端点设计
 *
 * <AUTHOR>
 * @since 2025-07-19
 */
@FeignClient(name = "whiskerguardcontractservice", fallbackFactory = ContractServiceFallbackFactory.class, path = "/api")
public interface ContractServiceClient {

    @GetMapping("/contract/reviews/init")
    ResponseEntity<Void> init(@RequestParam("tenantId") Long tenantId, @RequestParam("username") String username);

}

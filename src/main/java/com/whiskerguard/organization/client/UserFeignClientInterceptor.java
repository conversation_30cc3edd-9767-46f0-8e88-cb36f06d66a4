package com.whiskerguard.organization.client;

import com.whiskerguard.common.config.RequestConstants;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

/**
 * Feign客户端拦截器，用于在转发请求时自动携带当前请求的Authorization头（JWT令牌）。
 * 这样可以保证微服务之间调用时，用户身份信息能够被正确传递和认证。
 */
@Component
public class UserFeignClientInterceptor implements RequestInterceptor {

    /**
     * 在Feign请求模板中添加Authorization头。
     * 如果当前请求存在Authorization头，则将其传递到下游服务。
     *
     * @param template Feign请求模板
     */
    @Override
    public void apply(RequestTemplate template) {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes != null) {
            HttpServletRequest request = attributes.getRequest();
            String token = request.getHeader("Authorization");
            template.header("Authorization", token);
            template.header(RequestConstants.X_TENANT_ID, request.getHeader(RequestConstants.X_TENANT_ID));
            template.header(RequestConstants.X_USER_ID, request.getHeader(RequestConstants.X_USER_ID));
            template.header(RequestConstants.X_USER_NAME, request.getHeader(RequestConstants.X_USER_NAME));
            template.header(RequestConstants.X_SOURCE, request.getHeader(RequestConstants.X_SOURCE));
            template.header(RequestConstants.X_VERSION, request.getHeader(RequestConstants.X_VERSION));
            template.header(RequestConstants.X_FORWARDED_FOR, request.getHeader(RequestConstants.X_FORWARDED_FOR));
        }
    }
}

package com.whiskerguard.organization.client.regulatory;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 法规服务客户端 - 用于调用whiskerguard-regulatory-service的接口
 * 基于实际的regulatory-service API端点设计
 *
 * <AUTHOR>
 * @since 2025-07-19
 */
@FeignClient(name = "whiskerguardregulatoryservice", fallbackFactory = RegulatoryServiceFallbackFactory.class, path = "/api")
public interface RegulatoryServiceClient {

    @GetMapping("/compliance/cases/init")
    ResponseEntity<Void> init(@RequestParam("tenantId") Long tenantId, @RequestParam("username") String username);

}

package com.whiskerguard.organization.client.licensse;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * Feign 客户端，用于调用 whiskerguard-license-service 的 API
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/21
 */
@FeignClient(name = "whiskerguardlicenseservice", fallbackFactory = LicenseServiceFallbackFactory.class)
public interface LicenseServiceFeignClient {

    /**
     * 初始化租户的许可证
     *
     * @param tenantId 租户 ID
     * @return 初始化结果
     */
    @PostMapping("/api/licenses/init")
    ResponseEntity<String> initLicense(@RequestParam("tenantId") Long tenantId);

}

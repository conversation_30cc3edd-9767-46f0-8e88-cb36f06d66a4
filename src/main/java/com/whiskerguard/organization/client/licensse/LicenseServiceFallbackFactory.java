package com.whiskerguard.organization.client.licensse;


import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

/**
 * 许可证服务Feign客户端的回退工厂
 * 当用户服务不可用时，提供默认的回退实现
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-19
 */
@Component
public class LicenseServiceFallbackFactory implements FallbackFactory<LicenseServiceFeignClient> {

    private static final Logger log = LoggerFactory.getLogger(LicenseServiceFallbackFactory.class);

    @Override
    public LicenseServiceFeignClient create(Throwable cause) {
        return (Long tenantId) -> {
            log.error("初始化租户增值服务失败", cause);
            return ResponseEntity.ok().body(null);
        };
    }
}

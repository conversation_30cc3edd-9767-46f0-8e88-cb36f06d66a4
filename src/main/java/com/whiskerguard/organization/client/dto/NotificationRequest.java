package com.whiskerguard.organization.client.dto;

import java.util.Map;

/**
 * 通知请求基类
 */
public abstract class NotificationRequest {

    /**
     * 接收者
     */
    private String recipient;

    /**
     * 通知类型
     */
    private NotificationType type;

    /**
     * 模板ID
     */
    private String templateId = "2435890";

    /**
     * 模板参数
     */
    private Map<String, Object> templateParams;

    /**
     * 租户ID
     */
    private Long tenantId;

    public String getRecipient() {
        return recipient;
    }

    public void setRecipient(String recipient) {
        this.recipient = recipient;
    }

    public NotificationType getType() {
        return type;
    }

    public void setType(NotificationType type) {
        this.type = type;
    }

    public String getTemplateId() {
        return templateId;
    }

    public void setTemplateId(String templateId) {
        this.templateId = templateId;
    }

    public Map<String, Object> getTemplateParams() {
        return templateParams;
    }

    public void setTemplateParams(Map<String, Object> templateParams) {
        this.templateParams = templateParams;
    }

    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }
}

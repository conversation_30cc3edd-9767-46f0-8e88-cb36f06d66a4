package com.whiskerguard.organization.client.dto;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.time.Instant;

/**
 * 验证码响应模型
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/21
 */
public class VerificationCodeResponse {

    /**
     * 操作是否成功
     */
    private boolean success;

    /**
     * 响应消息
     */
    private String message;

    /**
     * 手机号（脱敏显示）
     */
    private String phoneNumber;

    /**
     * 验证码类型
     */
    private VerificationCodeType codeType;

    /**
     * 验证码发送时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Instant sentAt;

    /**
     * 下次可发送时间（防止频繁发送）
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Instant nextSendTime;

    /**
     * 验证码（仅开发环境返回，生产环境不返回）
     */
    private String code;

    public VerificationCodeResponse() {
    }

    public VerificationCodeResponse(boolean success, String message) {
        this.success = success;
        this.message = message;
    }

    public static VerificationCodeResponse success(String message) {
        return new VerificationCodeResponse(true, message);
    }

    public static VerificationCodeResponse failure(String message) {
        return new VerificationCodeResponse(false, message);
    }

    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getPhoneNumber() {
        return phoneNumber;
    }

    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    public VerificationCodeType getCodeType() {
        return codeType;
    }

    public void setCodeType(VerificationCodeType codeType) {
        this.codeType = codeType;
    }

    public Instant getSentAt() {
        return sentAt;
    }

    public void setSentAt(Instant sentAt) {
        this.sentAt = sentAt;
    }

    public Instant getNextSendTime() {
        return nextSendTime;
    }

    public void setNextSendTime(Instant nextSendTime) {
        this.nextSendTime = nextSendTime;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    /**
     * 脱敏显示手机号
     */
    public static String maskPhoneNumber(String phoneNumber) {
        if (phoneNumber == null || phoneNumber.length() < 7) {
            return phoneNumber;
        }
        return phoneNumber.substring(0, 3) + "****" + phoneNumber.substring(phoneNumber.length() - 4);
    }
}

package com.whiskerguard.organization.client.dto;

/**
 * 短信通知请求
 */
public class SmsRequest extends NotificationRequest {

    /**
     * 短信提供商类型
     */
    private SmsProviderType providerType = SmsProviderType.ALIYUN;

    /**
     * 国际区号，默认中国大陆 +86
     */
    private String regionCode = "86";

    public SmsRequest() {
        setType(NotificationType.SMS);
    }

    public SmsProviderType getProviderType() {
        return providerType;
    }

    public void setProviderType(SmsProviderType providerType) {
        this.providerType = providerType;
    }

    public String getRegionCode() {
        return regionCode;
    }

    public void setRegionCode(String regionCode) {
        this.regionCode = regionCode;
    }
}

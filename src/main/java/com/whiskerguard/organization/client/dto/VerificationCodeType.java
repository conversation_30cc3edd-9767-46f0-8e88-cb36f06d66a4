package com.whiskerguard.organization.client.dto;

/**
 * 验证码类型枚举
 */
public enum VerificationCodeType {
    /**
     * 登录验证
     */
    LOGIN("登录验证"),
    REGISTER("注册验证"),
    RESET_PASSWORD("重置密码"),
    BIND_PHONE("绑定手机"),
    UNBIND_PHONE("解绑手机"),
    PAYMENT("支付验证"),
    SECURITY("安全验证");

    private final String description;

    VerificationCodeType(String description) {
        this.description = description;
    }

    public String getDescription() {
        return description;
    }
}

package com.whiskerguard.organization.client.retrieval;

import com.whiskerguard.organization.client.dto.DocumentRecordDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 检索服务客户端 - 用于调用whiskerguard-retrieval-service的接口
 * 基于实际的retrieval-service API端点设计
 *
 * <AUTHOR>
 * @since 2025-01-14
 */
@FeignClient(name = "whiskerguardretrievalservice", fallbackFactory = RetrievalServiceFallbackFactory.class, path = "/api")
public interface RetrievalServiceClient {

    /**
     * 创建文档记录
     * 对应 retrieval-service 的 POST /api/document-records
     *
     * @param request 文档记录请求
     * @return 创建结果
     */
    @PostMapping("/document-records")
    ResponseEntity<DocumentRecordDTO> createDocumentRecord(@RequestBody DocumentRecordDTO request);

}

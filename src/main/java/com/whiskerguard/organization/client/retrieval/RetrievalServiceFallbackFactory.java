package com.whiskerguard.organization.client.retrieval;


import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

/**
 * 检索服务Feign客户端的回退工厂
 * 当用户服务不可用时，提供默认的回退实现
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/5
 */
@Component
public class RetrievalServiceFallbackFactory implements FallbackFactory<RetrievalServiceClient> {

    private static final Logger log = LoggerFactory.getLogger(RetrievalServiceFallbackFactory.class);

    @Override
    public RetrievalServiceClient create(Throwable cause) {
        return request -> {
            log.error("调用创建文档记录接口失败: {}", cause.getMessage());
            return ResponseEntity.ok().body(request);
        };
    }
}

package com.whiskerguard.organization.client.general;


import com.whiskerguard.organization.client.dto.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 通用服务Feign客户端的回退工厂
 * 当用户服务不可用时，提供默认的回退实现
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-19
 */
@Component
public class GeneralServiceFallbackFactory implements FallbackFactory<GeneralServiceFeignClient> {

    private static final Logger log = LoggerFactory.getLogger(GeneralServiceFallbackFactory.class);

    @Override
    public GeneralServiceFeignClient create(Throwable cause) {
        return new GeneralServiceFeignClient() {
            @Override
            public ResponseEntity<NotificationResponse> sendSms(SmsRequest request) {
                log.error("发送短信失败", cause);
                return ResponseEntity.ok().body(NotificationResponse.failure("发送短信失败"));
            }

            @Override
            public ResponseEntity<VerificationCodeResponse> validateVerificationCode(VerificationCodeValidateRequest request) {
                log.error("校验验证码失败", cause);
                return ResponseEntity.ok().body(VerificationCodeResponse.failure("校验验证码失败"));
            }

            @Override
            public ResponseEntity<Map<String, Object>> bindUser(String openId, String unionId, Long userId, Long tenantId, String username) {
                log.error("绑定微信失败", cause);
                return ResponseEntity.ok().body(Map.of("success", false, "message", "绑定微信失败"));
            }

            @Override
            public ResponseEntity<Map<String, Object>> unbindUser(String openId) {
                log.error("解绑微信失败", cause);
                return ResponseEntity.ok().body(Map.of("success", false, "message", "解绑微信失败"));
            }

            @Override
            public ResponseEntity<NotificationResponse> sendEmailAsync(EmailRequest emailRequest) {
                log.error("发送邮件失败", cause);
                return ResponseEntity.ok().body(NotificationResponse.failure("发送邮件失败"));
            }
        };
    }
}

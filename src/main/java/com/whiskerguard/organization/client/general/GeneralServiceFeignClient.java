package com.whiskerguard.organization.client.general;

import com.whiskerguard.organization.client.dto.*;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Map;

/**
 * 描述：通用服务Feign客户端，用于调用其他微服务的通用API
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/5
 */
@FeignClient(name = "whiskerguardgeneralservice", fallbackFactory = GeneralServiceFallbackFactory.class)
public interface GeneralServiceFeignClient {

    /**
     * 发送短信通知
     *
     * @param request 短信请求
     * @return 通知响应
     */
    @PostMapping("/api/notifications/sms")
    ResponseEntity<NotificationResponse> sendSms(@RequestBody SmsRequest request);

    /**
     * 校验验证码
     *
     * @param request 验证码校验请求
     * @return 验证码响应
     */
    @PostMapping("/api/verification/code/validate")
    ResponseEntity<VerificationCodeResponse> validateVerificationCode(@Valid @RequestBody VerificationCodeValidateRequest request);

    /**
     * 方法名称：bindUser
     * 描述：绑定用户微信。
     *
     * @param openId  微信OpenID
     * @param unionId 微信UnionID
     * @return 绑定结果
     * @since 1.0
     */
    @GetMapping("/api/wechat/binding/bind")
    ResponseEntity<Map<String, Object>> bindUser(@NotBlank(message = "微信OpenID不能为空") @RequestParam("openId") String openId,
                                                 @RequestParam(name = "unionId", required = false) String unionId,
                                                 @RequestParam(name = "employeeId") Long employeeId, @RequestParam(name = "tenantId") Long tenantId,
                                                 @RequestParam(name = "createdBy") String createdBy);


    /**
     * 方法名称：unbindUserWechat
     * 描述：解绑用户微信。
     *
     * @param openId 微信OpenID
     * @return 解绑结果
     * @since 1.0
     */
    @GetMapping("/api/wechat/binding/unbind")
    ResponseEntity<Map<String, Object>> unbindUser(@NotBlank(message = "微信OpenID不能为空") @RequestParam String openId);

    @PostMapping("/api/email/send/async")
    ResponseEntity<NotificationResponse> sendEmailAsync(@Valid @RequestBody EmailRequest emailRequest);
}

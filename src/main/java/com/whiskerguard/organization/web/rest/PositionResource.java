package com.whiskerguard.organization.web.rest;

import com.whiskerguard.organization.domain.enumeration.PositionCategory;
import com.whiskerguard.organization.repository.PositionRepository;
import com.whiskerguard.organization.service.PositionService;
import com.whiskerguard.organization.service.dto.PositionDTO;
import com.whiskerguard.organization.web.rest.errors.BadRequestAlertException;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;
import tech.jhipster.web.util.HeaderUtil;
import tech.jhipster.web.util.PaginationUtil;
import tech.jhipster.web.util.ResponseUtil;

import java.net.URI;
import java.net.URISyntaxException;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * REST controller for managing {@link com.whiskerguard.organization.domain.Position}.
 * 职位管理的REST控制器
 * 提供职位的CRUD操作API接口
 */
@RestController
@RequestMapping("/api/positions")
public class PositionResource {

    /**
     * 日志记录器
     */
    private static final Logger LOG = LoggerFactory.getLogger(PositionResource.class);

    /**
     * 实体名称，用于错误消息
     */
    private static final String ENTITY_NAME = "whiskerguardOrgServicePosition";

    /**
     * 应用名称，从配置中获取
     */
    @Value("${jhipster.clientApp.name}")
    private String applicationName;

    /**
     * 职位服务
     */
    private final PositionService positionService;

    /**
     * 职位仓库
     */
    private final PositionRepository positionRepository;

    /**
     * 构造函数
     *
     * @param positionService    职位服务
     * @param positionRepository 职位仓库
     */
    public PositionResource(PositionService positionService, PositionRepository positionRepository) {
        this.positionService = positionService;
        this.positionRepository = positionRepository;
    }

    /**
     * {@code POST  /positions} : Create a new position.
     * 创建新的职位
     *
     * @param positionDTO the positionDTO to create. 要创建的职位DTO
     * @return the {@link ResponseEntity} with status {@code 201 (Created)} and with body the new positionDTO, or with status {@code 400 (Bad Request)} if the position has already an ID.
     * 返回状态码为201的响应和新创建的职位DTO，如果职位已有ID则返回400错误
     * @throws URISyntaxException if the Location URI syntax is incorrect. 如果位置URI语法不正确
     */
    @PostMapping("")
    public ResponseEntity<PositionDTO> createPosition(@Valid @RequestBody PositionDTO positionDTO) throws URISyntaxException {
        LOG.debug("REST request to save Position : {}", positionDTO);
        if (positionDTO.getId() != null) {
            throw new BadRequestAlertException("新的岗位不能有ID", ENTITY_NAME, "idexists");
        }
        positionDTO = positionService.save(positionDTO);
        return ResponseEntity.created(new URI("/api/positions/" + positionDTO.getId()))
            .headers(HeaderUtil.createEntityCreationAlert(applicationName, true, ENTITY_NAME, positionDTO.getId().toString()))
            .body(positionDTO);
    }

    /**
     * {@code PUT  /positions/:id} : Updates an existing position.
     * 更新现有的职位
     *
     * @param id          the id of the positionDTO to save. 要保存的职位DTO的ID
     * @param positionDTO the positionDTO to update. 要更新的职位DTO
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated positionDTO,
     * or with status {@code 400 (Bad Request)} if the positionDTO is not valid,
     * or with status {@code 500 (Internal Server Error)} if the positionDTO couldn't be updated.
     * 返回状态码为200的响应和更新后的职位DTO，如果职位DTO无效则返回400错误，如果无法更新则返回500错误
     */
    @PutMapping("/{id}")
    public ResponseEntity<PositionDTO> updatePosition(
        @PathVariable(value = "id", required = false) final Long id,
        @Valid @RequestBody PositionDTO positionDTO
    ) {
        LOG.debug("REST request to update Position : {}, {}", id, positionDTO);
        if (positionDTO.getId() == null) {
            throw new BadRequestAlertException("无效的ID", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, positionDTO.getId())) {
            throw new BadRequestAlertException("无效的ID", ENTITY_NAME, "idinvalid");
        }

        if (!positionRepository.existsById(id)) {
            throw new BadRequestAlertException("实体未找到", ENTITY_NAME, "idnotfound");
        }

        positionDTO = positionService.update(positionDTO);
        return ResponseEntity.ok()
            .headers(HeaderUtil.createEntityUpdateAlert(applicationName, true, ENTITY_NAME, positionDTO.getId().toString()))
            .body(positionDTO);
    }

    /**
     * {@code PATCH  /positions/:id} : Partial updates given fields of an existing position, field will ignore if it is null
     * 部分更新现有职位的指定字段，如果字段为null则忽略
     *
     * @param id          the id of the positionDTO to save. 要保存的职位DTO的ID
     * @param positionDTO the positionDTO to update. 要更新的职位DTO
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated positionDTO,
     * or with status {@code 400 (Bad Request)} if the positionDTO is not valid,
     * or with status {@code 404 (Not Found)} if the positionDTO is not found,
     * or with status {@code 500 (Internal Server Error)} if the positionDTO couldn't be updated.
     * 返回状态码为200的响应和更新后的职位DTO，如果职位DTO无效则返回400错误，如果找不到职位则返回404错误，如果无法更新则返回500错误
     */
    @PatchMapping(value = "/{id}", consumes = {"application/json", "application/merge-patch+json"})
    public ResponseEntity<PositionDTO> partialUpdatePosition(
        @PathVariable(value = "id", required = false) final Long id,
        @NotNull @Valid @RequestBody PositionDTO positionDTO
    ) {
        LOG.debug("REST request to partial update Position partially : {}, {}", id, positionDTO);
        if (positionDTO.getId() == null) {
            throw new BadRequestAlertException("无效的ID", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, positionDTO.getId())) {
            throw new BadRequestAlertException("无效的ID", ENTITY_NAME, "idinvalid");
        }

        if (!positionRepository.existsById(id)) {
            throw new BadRequestAlertException("实体未找到", ENTITY_NAME, "idnotfound");
        }

        Optional<PositionDTO> result = positionService.partialUpdate(positionDTO);

        return ResponseUtil.wrapOrNotFound(
            result,
            HeaderUtil.createEntityUpdateAlert(applicationName, true, ENTITY_NAME, positionDTO.getId().toString())
        );
    }

    /**
     * {@code GET  /positions} : get all the positions.
     * 获取所有职位，支持按岗位分类、所属部门、岗位名称、岗位编码进行综合搜索
     *
     * @param pageable  the pagination information. 分页信息
     * @param category  the position category to filter by (optional). 岗位分类筛选条件（可选）
     * @param orgUnitId the organization unit id to filter by (optional). 所属部门ID筛选条件（可选）
     * @param keyword   the keyword to search in name or code (optional). 关键词搜索条件（可选，搜索名称或编码）
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and the page of positions in body.
     * 返回状态码为200的响应和职位分页数据
     */
    @GetMapping("")
    public ResponseEntity<Page<PositionDTO>> getAllPositions(
        @org.springdoc.core.annotations.ParameterObject Pageable pageable,
        @RequestParam(required = false) PositionCategory category,
        @RequestParam(required = false) Long orgUnitId,
        @RequestParam(required = false) String keyword
    ) {
        LOG.debug("REST request to get a page of Positions with filters - category: {}, orgUnitId: {}, keyword: {}",
            category, orgUnitId, keyword);

        Page<PositionDTO> page;

        // 如果没有提供任何搜索条件，使用原有的查询所有方法
        if (category == null && orgUnitId == null && (keyword == null || keyword.trim().isEmpty())) {
            page = positionService.findAll(pageable);
        } else {
            // 使用综合搜索方法
            String trimmedKeyword = (keyword != null && !keyword.trim().isEmpty()) ? keyword.trim() : null;
            page = positionService.findPositionsWithFilters(category, orgUnitId, trimmedKeyword, pageable);
        }

        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(ServletUriComponentsBuilder.fromCurrentRequest(), page);
        return ResponseEntity.ok().headers(headers).body(page);
    }

    /**
     * {@code GET  /positions/:id} : get the "id" position.
     * 根据ID获取职位
     *
     * @param id the id of the positionDTO to retrieve. 要检索的职位DTO的ID
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the positionDTO, or with status {@code 404 (Not Found)}.
     * 返回状态码为200的响应和职位DTO，如果找不到则返回404错误
     */
    @GetMapping("/{id}")
    public ResponseEntity<PositionDTO> getPosition(@PathVariable("id") Long id) {
        LOG.debug("REST request to get Position : {}", id);
        Optional<PositionDTO> positionDTO = positionService.findOne(id);
        return ResponseUtil.wrapOrNotFound(positionDTO);
    }

    /**
     * {@code DELETE  /positions/:id} : delete the "id" position.
     * 根据ID删除职位
     *
     * @param id the id of the positionDTO to delete. 要删除的职位DTO的ID
     * @return the {@link ResponseEntity} with status {@code 204 (NO_CONTENT)}.
     * 返回状态码为204的响应（无内容）
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deletePosition(@PathVariable("id") Long id) {
        LOG.debug("REST request to delete Position : {}", id);
        positionService.delete(id);
        return ResponseEntity.noContent()
            .headers(HeaderUtil.createEntityDeletionAlert(applicationName, true, ENTITY_NAME, id.toString()))
            .build();
    }

    /**
     * {@code GET  /positions/dept/:id} : get the "id" position by department.
     * 根据部门ID获取职位
     *
     * @param id the id of the department to retrieve the position for. 要检索的部门ID
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the positionDTO, or with status {@code 404 (Not Found)}.
     * 返回状态码为200的响应和职位DTO，如果找不到则返回404错误
     */
    @GetMapping("/dept/positions/{id}")
    public ResponseEntity<List<PositionDTO>> getPositionByDept(@PathVariable("id") Long id) {
        LOG.debug("REST request to get Position by department : {}", id);
        List<PositionDTO> positionDTO = positionService.findByOrgUnitId(id);
        return ResponseEntity.ok().body(positionDTO);
    }

    /**
     * {@code GET  /positions/search} : search positions by name (fuzzy search) within current tenant.
     * 根据岗位名称模糊查询本租户下的岗位列表
     *
     * @param name 岗位名称关键词 position name keyword
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and the list of positions.
     * 返回状态码为200的响应和岗位列表
     */
    @GetMapping("/search")
    public ResponseEntity<List<PositionDTO>> searchPositionsByName(@RequestParam("name") String name) {
        LOG.debug("REST request to search Positions by name : {}", name);

        if (name == null || name.trim().isEmpty()) {
            throw new BadRequestAlertException("搜索名称不能为空", ENTITY_NAME, "name.empty");
        }

        List<PositionDTO> positions = positionService.findByNameContainingIgnoreCase(name.trim());
        return ResponseEntity.ok().body(positions);
    }

}

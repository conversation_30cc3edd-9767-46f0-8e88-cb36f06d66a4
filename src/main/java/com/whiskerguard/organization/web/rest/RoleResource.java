package com.whiskerguard.organization.web.rest;

import com.whiskerguard.common.constant.FileTypeConstants;
import com.whiskerguard.common.dto.ImportResultDTO;
import com.whiskerguard.organization.repository.RoleRepository;
import com.whiskerguard.organization.request.RoleReq;
import com.whiskerguard.organization.service.RoleService;
import com.whiskerguard.organization.service.dto.RoleDTO;
import com.whiskerguard.organization.service.dto.RoleImportDTO;
import com.whiskerguard.organization.util.ExcelTemplateUtil;
import com.whiskerguard.organization.web.rest.errors.BadRequestAlertException;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;
import tech.jhipster.web.util.HeaderUtil;
import tech.jhipster.web.util.PaginationUtil;
import tech.jhipster.web.util.ResponseUtil;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.net.URI;
import java.net.URISyntaxException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * REST controller for managing {@link com.whiskerguard.organization.domain.Role}.
 * 角色的REST控制器
 * 提供角色的CRUD操作API接口
 */
@RestController
@RequestMapping("/api/roles")
public class RoleResource {

    /**
     * 日志记录器
     */
    private static final Logger LOG = LoggerFactory.getLogger(RoleResource.class);

    /**
     * 实体名称，用于错误消息
     */
    private static final String ENTITY_NAME = "whiskerguardOrgServiceRole";

    /**
     * 应用名称，从配置中获取
     */
    @Value("${jhipster.clientApp.name}")
    private String applicationName;

    /**
     * 角色服务
     */
    private final RoleService roleService;

    /**
     * 角色仓库
     */
    private final RoleRepository roleRepository;

    /**
     * 构造函数
     *
     * @param roleService    角色服务
     * @param roleRepository 角色仓库
     */
    public RoleResource(RoleService roleService, RoleRepository roleRepository) {
        this.roleService = roleService;
        this.roleRepository = roleRepository;
    }

    /**
     * {@code POST  /roles} : Create a new role.
     * 创建新的角色
     *
     * @param roleDTO the roleDTO to create. 要创建的角色DTO
     * @return the {@link ResponseEntity} with status {@code 201 (Created)} and with body the new roleDTO, or with status {@code 400 (Bad Request)} if the role has already an ID.
     * 返回状态码为201的响应和新创建的角色DTO，如果角色已有ID则返回400错误
     * @throws URISyntaxException if the Location URI syntax is incorrect. 如果位置URI语法不正确
     */
    @PostMapping("")
    public ResponseEntity<RoleDTO> createRole(@Valid @RequestBody RoleDTO roleDTO) throws URISyntaxException {
        LOG.debug("REST request to save Role : {}", roleDTO);
        if (roleDTO.getId() != null) {
            throw new BadRequestAlertException("新的角色不能有ID", ENTITY_NAME, "idexists");
        }
        roleDTO = roleService.save(roleDTO);
        return ResponseEntity.created(new URI("/api/roles/" + roleDTO.getId()))
            .headers(HeaderUtil.createEntityCreationAlert(applicationName, true, ENTITY_NAME, roleDTO.getId().toString()))
            .body(roleDTO);
    }

    /**
     * {@code PATCH  /roles/:id} : Partial updates given fields of an existing role, field will ignore if it is null
     * 部分更新现有角色的指定字段，如果字段为null则忽略
     *
     * @param id      the id of the roleDTO to save. 要保存的角色DTO的ID
     * @param roleDTO the roleDTO to update. 要更新的角色DTO
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated roleDTO,
     * or with status {@code 400 (Bad Request)} if the roleDTO is not valid,
     * or with status {@code 404 (Not Found)} if the roleDTO is not found,
     * or with status {@code 500 (Internal Server Error)} if the roleDTO couldn't be updated.
     * 返回状态码为200的响应和更新后的角色DTO，如果角色DTO无效则返回400错误，如果找不到角色则返回404错误，如果无法更新则返回500错误
     */
    @PatchMapping(value = "/{id}", consumes = {"application/json", "application/merge-patch+json"})
    public ResponseEntity<RoleDTO> partialUpdateRole(
        @PathVariable(value = "id", required = false) final Long id,
        @NotNull @Valid @RequestBody RoleDTO roleDTO
    ) {
        LOG.debug("REST request to partial update Role partially : {}, {}", id, roleDTO);
        if (roleDTO.getId() == null) {
            throw new BadRequestAlertException("无效的ID", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, roleDTO.getId())) {
            throw new BadRequestAlertException("无效的ID", ENTITY_NAME, "idinvalid");
        }

        if (!roleRepository.existsById(id)) {
            throw new BadRequestAlertException("实体未找到", ENTITY_NAME, "idnotfound");
        }

        Optional<RoleDTO> result = roleService.partialUpdate(roleDTO);

        return ResponseUtil.wrapOrNotFound(
            result,
            HeaderUtil.createEntityUpdateAlert(applicationName, true, ENTITY_NAME, roleDTO.getId().toString())
        );
    }

    /**
     * {@code GET  /roles} : get all the roles.
     * 获取所有角色
     *
     * @param roleReq the role request. 角色查询请求对象
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and the page of roles in body.
     * 返回状态码为200的响应和角色分页数据
     */
    @PostMapping("/page")
    public ResponseEntity<Page<RoleDTO>> getAllRoles(@RequestBody RoleReq roleReq) {
        LOG.debug("REST request to get a page of Roles");
        Page<RoleDTO> page = roleService.findByCondition(roleReq);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(ServletUriComponentsBuilder.fromCurrentRequest(), page);
        return ResponseEntity.ok().headers(headers).body(page);
    }

    /**
     * {@code GET  /roles/:id} : get the "id" role.
     * 获取指定ID的角色
     *
     * @param id the id of the roleDTO to retrieve. 要检索的角色DTO的ID
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the roleDTO, or with status {@code 404 (Not Found)}.
     * 返回状态码为200的响应和角色DTO，如果找不到则返回404错误
     */
    @GetMapping("/{id}")
    public ResponseEntity<RoleDTO> getRole(@PathVariable("id") Long id) {
        LOG.debug("REST request to get Role : {}", id);
        Optional<RoleDTO> roleDTO = roleService.findOne(id);
        return ResponseUtil.wrapOrNotFound(roleDTO);
    }

    /**
     * {@code DELETE  /roles/:id} : delete the "id" role.
     * 删除指定ID的角色
     *
     * @param id the id of the roleDTO to delete. 要删除的角色DTO的ID
     * @return the {@link ResponseEntity} with status {@code 204 (NO_CONTENT)}.
     * 返回状态码为204的响应（无内容）
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteRole(@PathVariable("id") Long id) {
        LOG.debug("REST request to delete Role : {}", id);
        roleService.delete(id);
        return ResponseEntity.noContent()
            .headers(HeaderUtil.createEntityDeletionAlert(applicationName, true, ENTITY_NAME, id.toString()))
            .build();
    }

    /**
     * {@code POST  /roles/batch} : Batch create new roles.
     * 批量创建新角色
     *
     * @param roleDTOs the list of roleDTOs to create. 要创建的角色DTO列表
     * @return the {@link ResponseEntity} with status {@code 201 (Created)} and with body the list of new roleDTOs.
     * 返回状态码为201的响应和新角色DTO列表
     * @throws URISyntaxException if the Location URI syntax is incorrect. 如果位置URI语法不正确
     */
    @PostMapping("/batch")
    public ResponseEntity<List<RoleDTO>> batchCreateRoles(@Valid @RequestBody List<RoleDTO> roleDTOs) throws URISyntaxException {
        LOG.debug("REST request to batch save Roles : {}", roleDTOs.size());

        if (roleDTOs.isEmpty()) {
            throw new BadRequestAlertException("角色列表不能为空", ENTITY_NAME, "emptylist");
        }

        List<RoleDTO> result = roleService.saveAll(roleDTOs);
        return ResponseEntity.created(new URI("/api/roles/batch"))
            .headers(HeaderUtil.createAlert(applicationName, "roleManagement.batchCreated",
                String.valueOf(result.size())))
            .body(result);
    }

    /**
     * {@code POST  /roles/import} : Import roles from Excel file.
     * 从Excel文件导入角色
     *
     * @param file the Excel file to import. 要导入的Excel文件
     * @return the {@link ResponseEntity} with status {@code 201 (Created)} and with body the import result.
     * 返回状态码为201的响应和导入结果
     * @throws URISyntaxException if the Location URI syntax is incorrect. 如果位置URI语法不正确
     */
    @PostMapping(value = "/import", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<ImportResultDTO<RoleImportDTO>> importRoles(@RequestParam("file") MultipartFile file)
        throws URISyntaxException {
        LOG.debug("REST request to import Roles from Excel file : {}", file.getOriginalFilename());

        if (file.isEmpty()) {
            throw new BadRequestAlertException("上传文件为空", ENTITY_NAME, "file.empty");
        }

        // 验证文件类型
        if (!Objects.requireNonNull(file.getOriginalFilename()).endsWith(FileTypeConstants.EXT_XLS) &&
            !Objects.requireNonNull(file.getOriginalFilename()).endsWith(FileTypeConstants.EXT_XLSX)) {
            throw new BadRequestAlertException("只支持.xlsx或.xls格式文件", ENTITY_NAME, "file.invalid.type");
        }

        ImportResultDTO<RoleImportDTO> result = roleService.importFromExcel(file);

        return ResponseEntity.created(new URI("/api/roles/import"))
            .headers(HeaderUtil.createAlert(applicationName, "roleManagement.imported",
                String.format("成功导入%d个角色", result.getSuccessCount())))
            .body(result);
    }

    /**
     * {@code GET  /roles/search} : search roles by name (fuzzy search) within current tenant.
     * 根据角色名称模糊查询本租户下的角色列表
     *
     * @param name 角色名称关键词 role name keyword
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and the list of roles.
     * 返回状态码为200的响应和角色列表
     */
    @GetMapping("/search")
    public ResponseEntity<List<RoleDTO>> searchRolesByName(@RequestParam("name") String name) {
        LOG.debug("REST request to search Roles by name : {}", name);

        if (name == null || name.trim().isEmpty()) {
            throw new BadRequestAlertException("搜索名称不能为空", ENTITY_NAME, "name.empty");
        }

        List<RoleDTO> roles = roleService.findByNameContainingIgnoreCase(name.trim());
        return ResponseEntity.ok().body(roles);
    }

    /**
     * {@code GET  /roles/template} : Download role import template.
     * 下载企业角色导入模板
     *
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and Excel template file.
     * 返回状态码为200的响应和Excel模板文件
     */
    @GetMapping("/template")
    public ResponseEntity<byte[]> downloadRoleTemplate() {
        LOG.debug("REST request to download Role import template");

        try {
            // 创建模板数据
            List<RoleImportDTO> templateData = createRoleTemplateData();

            // 创建导入说明
            List<String> instructions = createRoleImportInstructions();

            // 生成Excel文件
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            ExcelTemplateUtil.exportToExcel(templateData, RoleImportDTO.class, "企业角色导入模板", instructions, outputStream);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.parseMediaType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"));
            String fileName = "企业角色导入模板.xlsx";
            String encodedFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8);
            headers.set("Content-Disposition", "attachment; filename*=UTF-8''" + encodedFileName);

            return ResponseEntity.ok()
                .headers(headers)
                .body(outputStream.toByteArray());

        } catch (IOException e) {
            LOG.error("生成企业角色导入模板失败", e);
            throw new BadRequestAlertException("生成模板失败: " + e.getMessage(), ENTITY_NAME, "templatefailed");
        }
    }

    /**
     * 创建角色导入模板数据
     */
    private List<RoleImportDTO> createRoleTemplateData() {
        List<RoleImportDTO> templateData = new ArrayList<>();

        // 添加企业常用角色示例数据
        RoleImportDTO admin = new RoleImportDTO();
        admin.setCode("TENANT_ADMIN");
        admin.setName("租户管理员");
        admin.setDescription("系统超级管理员，拥有所有系统权限，负责系统配置和用户管理");
        admin.setPermissionCodes("USER_READ,USER_WRITE,USER_DELETE,ROLE_READ,ROLE_WRITE,ROLE_DELETE,ORG_READ,ORG_WRITE,ORG_DELETE,SYSTEM_CONFIG");

        RoleImportDTO manager = new RoleImportDTO();
        manager.setCode("STAFF");
        manager.setName("部门经理");
        manager.setDescription("部门管理者角色，负责部门人员管理和业务审批");
        manager.setPermissionCodes("USER_READ,USER_WRITE,ORG_READ,PROJECT_READ,PROJECT_WRITE,APPROVAL_PROCESS");

        RoleImportDTO employee = new RoleImportDTO();
        employee.setCode("STAFF");
        employee.setName("普通员工");
        employee.setDescription("普通员工角色，拥有基础的系统访问权限");
        employee.setPermissionCodes("USER_READ,PROFILE_READ,PROFILE_WRITE,BASIC_ACCESS");

        templateData.add(admin);
        templateData.add(manager);
        templateData.add(employee);

        return templateData;
    }

    /**
     * 创建角色导入说明
     */
    private List<String> createRoleImportInstructions() {
        List<String> instructions = new ArrayList<>();

        instructions.add("1. 带*号的列为必填项");
        instructions.add("2. 角色编码：管理员（TENANT_ADMIN），普通员工（STAFF）");
        instructions.add("3. 角色名称必须唯一，不能重复");
        instructions.add("4. 权限编码列表：多个权限编码用英文逗号分隔，如：USER_MANAGE,ROLE_MANAGE");
        instructions.add("5. 权限编码必须在系统中已存在，不存在的权限编码将被忽略");
        instructions.add("6. 角色描述建议详细说明角色职责");
        instructions.add("7. 请删除示例数据行后再导入实际数据");

        return instructions;
    }
}

package com.whiskerguard.organization.web.rest;

import com.whiskerguard.organization.repository.TenantAttachmentRepository;
import com.whiskerguard.organization.service.TenantAttachmentService;
import com.whiskerguard.organization.service.dto.TenantAttachmentDTO;
import com.whiskerguard.organization.web.rest.errors.BadRequestAlertException;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.Objects;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;
import tech.jhipster.web.util.HeaderUtil;
import tech.jhipster.web.util.PaginationUtil;
import tech.jhipster.web.util.ResponseUtil;

/**
 * REST controller for managing {@link com.whiskerguard.organization.domain.TenantAttachment}.
 * 租户附件的REST控制器
 * 提供租户附件的CRUD操作API接口
 */
@RestController
@RequestMapping("/api/tenant/attachments")
public class TenantAttachmentResource {

    /**
     * 日志记录器
     */
    private static final Logger LOG = LoggerFactory.getLogger(TenantAttachmentResource.class);

    /**
     * 实体名称，用于错误消息
     */
    private static final String ENTITY_NAME = "whiskerguardOrgServiceTenantAttachment";

    /**
     * 应用名称，从配置中获取
     */
    @Value("${jhipster.clientApp.name}")
    private String applicationName;

    /**
     * 租户附件服务
     */
    private final TenantAttachmentService tenantAttachmentService;

    /**
     * 租户附件仓库
     */
    private final TenantAttachmentRepository tenantAttachmentRepository;

    /**
     * 构造函数
     * @param tenantAttachmentService 租户附件服务
     * @param tenantAttachmentRepository 租户附件仓库
     */
    public TenantAttachmentResource(
        TenantAttachmentService tenantAttachmentService,
        TenantAttachmentRepository tenantAttachmentRepository
    ) {
        this.tenantAttachmentService = tenantAttachmentService;
        this.tenantAttachmentRepository = tenantAttachmentRepository;
    }

    /**
     * {@code POST  /tenant-attachments} : Create a new tenantAttachment.
     * 创建新的租户附件
     *
     * @param tenantAttachmentDTO the tenantAttachmentDTO to create. 要创建的租户附件DTO
     * @return the {@link ResponseEntity} with status {@code 201 (Created)} and with body the new tenantAttachmentDTO, or with status {@code 400 (Bad Request)} if the tenantAttachment has already an ID.
     *         返回状态码为201的响应和新创建的租户附件DTO，如果租户附件已有ID则返回400错误
     * @throws URISyntaxException if the Location URI syntax is incorrect. 如果位置URI语法不正确
     */
    @PostMapping("")
    public ResponseEntity<TenantAttachmentDTO> createTenantAttachment(@Valid @RequestBody TenantAttachmentDTO tenantAttachmentDTO)
        throws URISyntaxException {
        LOG.debug("REST request to save TenantAttachment : {}", tenantAttachmentDTO);
        if (tenantAttachmentDTO.getId() != null) {
            throw new BadRequestAlertException("新的租户附件不能有ID", ENTITY_NAME, "idexists");
        }
        tenantAttachmentDTO = tenantAttachmentService.save(tenantAttachmentDTO);
        return ResponseEntity.created(new URI("/api/tenant/attachments/" + tenantAttachmentDTO.getId()))
            .headers(HeaderUtil.createEntityCreationAlert(applicationName, true, ENTITY_NAME, tenantAttachmentDTO.getId().toString()))
            .body(tenantAttachmentDTO);
    }

    /**
     * {@code PUT  /tenant-attachments/:id} : Updates an existing tenantAttachment.
     * 更新现有的租户附件
     *
     * @param id the id of the tenantAttachmentDTO to save. 要保存的租户附件DTO的ID
     * @param tenantAttachmentDTO the tenantAttachmentDTO to update. 要更新的租户附件DTO
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated tenantAttachmentDTO,
     * or with status {@code 400 (Bad Request)} if the tenantAttachmentDTO is not valid,
     * or with status {@code 500 (Internal Server Error)} if the tenantAttachmentDTO couldn't be updated.
     * 返回状态码为200的响应和更新后的租户附件DTO，如果租户附件DTO无效则返回400错误，如果无法更新则返回500错误
     * @throws URISyntaxException if the Location URI syntax is incorrect. 如果位置URI语法不正确
     */
    @PutMapping("/{id}")
    public ResponseEntity<TenantAttachmentDTO> updateTenantAttachment(
        @PathVariable(value = "id", required = false) final Long id,
        @Valid @RequestBody TenantAttachmentDTO tenantAttachmentDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to update TenantAttachment : {}, {}", id, tenantAttachmentDTO);
        if (tenantAttachmentDTO.getId() == null) {
            throw new BadRequestAlertException("无效的ID", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, tenantAttachmentDTO.getId())) {
            throw new BadRequestAlertException("无效的ID", ENTITY_NAME, "idinvalid");
        }

        if (!tenantAttachmentRepository.existsById(id)) {
            throw new BadRequestAlertException("实体未找到", ENTITY_NAME, "idnotfound");
        }

        tenantAttachmentDTO = tenantAttachmentService.update(tenantAttachmentDTO);
        return ResponseEntity.ok()
            .headers(HeaderUtil.createEntityUpdateAlert(applicationName, true, ENTITY_NAME, tenantAttachmentDTO.getId().toString()))
            .body(tenantAttachmentDTO);
    }

    /**
     * {@code PATCH  /tenant-attachments/:id} : Partial updates given fields of an existing tenantAttachment, field will ignore if it is null
     * 部分更新现有租户附件的指定字段，如果字段为null则忽略
     *
     * @param id the id of the tenantAttachmentDTO to save. 要保存的租户附件DTO的ID
     * @param tenantAttachmentDTO the tenantAttachmentDTO to update. 要更新的租户附件DTO
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated tenantAttachmentDTO,
     * or with status {@code 400 (Bad Request)} if the tenantAttachmentDTO is not valid,
     * or with status {@code 404 (Not Found)} if the tenantAttachmentDTO is not found,
     * or with status {@code 500 (Internal Server Error)} if the tenantAttachmentDTO couldn't be updated.
     * 返回状态码为200的响应和更新后的租户附件DTO，如果租户附件DTO无效则返回400错误，如果找不到租户附件则返回404错误，如果无法更新则返回500错误
     * @throws URISyntaxException if the Location URI syntax is incorrect. 如果位置URI语法不正确
     */
    @PatchMapping(value = "/{id}", consumes = { "application/json", "application/merge-patch+json" })
    public ResponseEntity<TenantAttachmentDTO> partialUpdateTenantAttachment(
        @PathVariable(value = "id", required = false) final Long id,
        @NotNull @Valid @RequestBody TenantAttachmentDTO tenantAttachmentDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to partial update TenantAttachment partially : {}, {}", id, tenantAttachmentDTO);
        if (tenantAttachmentDTO.getId() == null) {
            throw new BadRequestAlertException("无效的ID", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, tenantAttachmentDTO.getId())) {
            throw new BadRequestAlertException("无效的ID", ENTITY_NAME, "idinvalid");
        }

        if (!tenantAttachmentRepository.existsById(id)) {
            throw new BadRequestAlertException("实体未找到", ENTITY_NAME, "idnotfound");
        }

        Optional<TenantAttachmentDTO> result = tenantAttachmentService.partialUpdate(tenantAttachmentDTO);

        return ResponseUtil.wrapOrNotFound(
            result,
            HeaderUtil.createEntityUpdateAlert(applicationName, true, ENTITY_NAME, tenantAttachmentDTO.getId().toString())
        );
    }

    /**
     * {@code GET  /tenant-attachments} : get all the tenantAttachments.
     * 获取所有租户附件
     *
     * @param pageable the pagination information. 分页信息
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and the page of tenantAttachments in body.
     *         返回状态码为200的响应和租户附件分页数据
     */
    @GetMapping("")
    public ResponseEntity<Page<TenantAttachmentDTO>> getAllTenantAttachments(
        @org.springdoc.core.annotations.ParameterObject Pageable pageable
    ) {
        LOG.debug("REST request to get a page of TenantAttachments");
        Page<TenantAttachmentDTO> page = tenantAttachmentService.findAll(pageable);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(ServletUriComponentsBuilder.fromCurrentRequest(), page);
        return ResponseEntity.ok().headers(headers).body(page);
    }

    /**
     * {@code GET  /tenant-attachments/:id} : get the "id" tenantAttachment.
     * 获取指定ID的租户附件
     *
     * @param id the id of the tenantAttachmentDTO to retrieve. 要检索的租户附件DTO的ID
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the tenantAttachmentDTO, or with status {@code 404 (Not Found)}.
     *         返回状态码为200的响应和租户附件DTO，如果找不到则返回404错误
     */
    @GetMapping("/{id}")
    public ResponseEntity<TenantAttachmentDTO> getTenantAttachment(@PathVariable("id") Long id) {
        LOG.debug("REST request to get TenantAttachment : {}", id);
        Optional<TenantAttachmentDTO> tenantAttachmentDTO = tenantAttachmentService.findOne(id);
        return ResponseUtil.wrapOrNotFound(tenantAttachmentDTO);
    }

    /**
     * {@code DELETE  /tenant-attachments/:id} : delete the "id" tenantAttachment.
     * 删除指定ID的租户附件
     *
     * @param id the id of the tenantAttachmentDTO to delete. 要删除的租户附件DTO的ID
     * @return the {@link ResponseEntity} with status {@code 204 (NO_CONTENT)}.
     *         返回状态码为204的响应（无内容）
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteTenantAttachment(@PathVariable("id") Long id) {
        LOG.debug("REST request to delete TenantAttachment : {}", id);
        tenantAttachmentService.delete(id);
        return ResponseEntity.noContent()
            .headers(HeaderUtil.createEntityDeletionAlert(applicationName, true, ENTITY_NAME, id.toString()))
            .build();
    }
}

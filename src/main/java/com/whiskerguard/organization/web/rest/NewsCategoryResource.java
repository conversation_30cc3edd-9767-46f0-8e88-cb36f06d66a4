package com.whiskerguard.organization.web.rest;

import com.whiskerguard.organization.repository.NewsCategoryRepository;
import com.whiskerguard.organization.service.NewsCategoryService;
import com.whiskerguard.organization.service.dto.NewsCategoryDTO;
import com.whiskerguard.organization.web.rest.errors.BadRequestAlertException;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.Objects;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;
import tech.jhipster.web.util.HeaderUtil;
import tech.jhipster.web.util.PaginationUtil;
import tech.jhipster.web.util.ResponseUtil;

/**
 * 新闻分类管理的REST控制器
 */
@RestController
@RequestMapping("/api/news/categories")
public class NewsCategoryResource {

    private static final Logger LOG = LoggerFactory.getLogger(NewsCategoryResource.class);

    private static final String ENTITY_NAME = "whiskerguardOrgServiceNewsCategory";

    @Value("${jhipster.clientApp.name}")
    private String applicationName;

    private final NewsCategoryService newsCategoryService;

    private final NewsCategoryRepository newsCategoryRepository;

    public NewsCategoryResource(NewsCategoryService newsCategoryService, NewsCategoryRepository newsCategoryRepository) {
        this.newsCategoryService = newsCategoryService;
        this.newsCategoryRepository = newsCategoryRepository;
    }

    /**
     * {@code POST  /news-categories} : 创建新闻分类
     *
     * @param newsCategoryDTO 要创建的新闻分类DTO
     * @return {@link ResponseEntity}，状态为 {@code 201 (Created)}，响应体为新创建的新闻分类DTO；
     *         如果新闻分类已经有ID，则状态为 {@code 400 (Bad Request)}
     * @throws URISyntaxException 如果Location URI语法不正确
     */
    @PostMapping("/create")
    public ResponseEntity<NewsCategoryDTO> createNewsCategory(@Valid @RequestBody NewsCategoryDTO newsCategoryDTO)
        throws URISyntaxException {
        LOG.debug("REST request to save NewsCategory : {}", newsCategoryDTO);
        if (newsCategoryDTO.getId() != null) {
            throw new BadRequestAlertException("新的新闻分类不能有ID", ENTITY_NAME, "idexists");
        }
        newsCategoryDTO = newsCategoryService.save(newsCategoryDTO);
        return ResponseEntity.created(new URI("/api/news-categories/" + newsCategoryDTO.getId()))
            .headers(HeaderUtil.createEntityCreationAlert(applicationName, true, ENTITY_NAME, newsCategoryDTO.getId().toString()))
            .body(newsCategoryDTO);
    }

    /**
     * {@code PUT  /news-categories/:id} : 更新已存在的新闻分类
     *
     * @param id 要保存的新闻分类DTO的ID
     * @param newsCategoryDTO 要更新的新闻分类DTO
     * @return {@link ResponseEntity}，状态为 {@code 200 (OK)}，响应体为更新后的新闻分类DTO；
     *         如果新闻分类DTO无效，则状态为 {@code 400 (Bad Request)}；
     *         如果新闻分类DTO无法更新，则状态为 {@code 500 (Internal Server Error)}
     * @throws URISyntaxException 如果Location URI语法不正确
     */
    @PutMapping("/update/{id}")
    public ResponseEntity<NewsCategoryDTO> updateNewsCategory(
        @PathVariable(value = "id", required = false) final Long id,
        @Valid @RequestBody NewsCategoryDTO newsCategoryDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to update NewsCategory : {}, {}", id, newsCategoryDTO);
        if (newsCategoryDTO.getId() == null) {
            throw new BadRequestAlertException("无效的ID", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, newsCategoryDTO.getId())) {
            throw new BadRequestAlertException("无效的ID", ENTITY_NAME, "idinvalid");
        }

        if (!newsCategoryRepository.existsById(id)) {
            throw new BadRequestAlertException("实体未找到", ENTITY_NAME, "idnotfound");
        }

        newsCategoryDTO = newsCategoryService.update(newsCategoryDTO);
        return ResponseEntity.ok()
            .headers(HeaderUtil.createEntityUpdateAlert(applicationName, true, ENTITY_NAME, newsCategoryDTO.getId().toString()))
            .body(newsCategoryDTO);
    }

    /**
     * {@code PATCH  /news-categories/:id} : 部分更新已存在的新闻分类，null字段将被忽略
     *
     * @param id 要保存的新闻分类DTO的ID
     * @param newsCategoryDTO 要更新的新闻分类DTO
     * @return {@link ResponseEntity}，状态为 {@code 200 (OK)}，响应体为更新后的新闻分类DTO；
     *         如果新闻分类DTO无效，则状态为 {@code 400 (Bad Request)}；
     *         如果找不到新闻分类DTO，则状态为 {@code 404 (Not Found)}；
     *         如果新闻分类DTO无法更新，则状态为 {@code 500 (Internal Server Error)}
     * @throws URISyntaxException 如果Location URI语法不正确
     */
    @PatchMapping(value = "/partial/{id}", consumes = { "application/json", "application/merge-patch+json" })
    public ResponseEntity<NewsCategoryDTO> partialUpdateNewsCategory(
        @PathVariable(value = "id", required = false) final Long id,
        @NotNull @Valid @RequestBody NewsCategoryDTO newsCategoryDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to partial update NewsCategory partially : {}, {}", id, newsCategoryDTO);
        if (newsCategoryDTO.getId() == null) {
            throw new BadRequestAlertException("无效的ID", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, newsCategoryDTO.getId())) {
            throw new BadRequestAlertException("无效的ID", ENTITY_NAME, "idinvalid");
        }

        if (!newsCategoryRepository.existsById(id)) {
            throw new BadRequestAlertException("实体未找到", ENTITY_NAME, "idnotfound");
        }

        Optional<NewsCategoryDTO> result = newsCategoryService.partialUpdate(newsCategoryDTO);

        return ResponseUtil.wrapOrNotFound(
            result,
            HeaderUtil.createEntityUpdateAlert(applicationName, true, ENTITY_NAME, newsCategoryDTO.getId().toString())
        );
    }

    /**
     * {@code GET  /news-categories} : 获取所有新闻分类
     *
     * @param pageable 分页信息
     * @param eagerload 是否急切加载关联实体的标志（适用于多对多关系）
     * @return {@link ResponseEntity}，状态为 {@code 200 (OK)}，响应体为新闻分类分页数据
     */
    @GetMapping("/list")
    public ResponseEntity<Page<NewsCategoryDTO>> getAllNewsCategories(
        @org.springdoc.core.annotations.ParameterObject Pageable pageable,
        @RequestParam(name = "eagerload", required = false, defaultValue = "true") boolean eagerload
    ) {
        LOG.debug("REST request to get a page of NewsCategories");
        Page<NewsCategoryDTO> page;
        if (eagerload) {
            page = newsCategoryService.findAllWithEagerRelationships(pageable);
        } else {
            page = newsCategoryService.findAll(pageable);
        }
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(ServletUriComponentsBuilder.fromCurrentRequest(), page);
        return ResponseEntity.ok().headers(headers).body(page);
    }

    /**
     * {@code GET  /news-categories/:id} : 获取指定ID的新闻分类
     *
     * @param id 要获取的新闻分类DTO的ID
     * @return {@link ResponseEntity}，状态为 {@code 200 (OK)}，响应体为新闻分类DTO；
     *         如果找不到新闻分类，则状态为 {@code 404 (Not Found)}
     */
    @GetMapping("/detail/{id}")
    public ResponseEntity<NewsCategoryDTO> getNewsCategory(@PathVariable("id") Long id) {
        LOG.debug("REST request to get NewsCategory : {}", id);
        Optional<NewsCategoryDTO> newsCategoryDTO = newsCategoryService.findOne(id);
        return ResponseUtil.wrapOrNotFound(newsCategoryDTO);
    }

    /**
     * {@code DELETE  /news-categories/:id} : 删除指定ID的新闻分类
     *
     * @param id 要删除的新闻分类DTO的ID
     * @return {@link ResponseEntity}，状态为 {@code 204 (NO_CONTENT)}
     */
    @DeleteMapping("/delete/{id}")
    public ResponseEntity<Void> deleteNewsCategory(@PathVariable("id") Long id) {
        LOG.debug("REST request to delete NewsCategory : {}", id);
        newsCategoryService.delete(id);
        return ResponseEntity.noContent()
            .headers(HeaderUtil.createEntityDeletionAlert(applicationName, true, ENTITY_NAME, id.toString()))
            .build();
    }
}

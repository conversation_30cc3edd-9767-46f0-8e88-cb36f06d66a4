package com.whiskerguard.organization.web.rest;

import com.whiskerguard.organization.repository.NewsReportRepository;
import com.whiskerguard.organization.service.NewsReportService;
import com.whiskerguard.organization.service.dto.NewsReportDTO;
import com.whiskerguard.organization.web.rest.errors.BadRequestAlertException;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.Objects;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;
import tech.jhipster.web.util.HeaderUtil;
import tech.jhipster.web.util.PaginationUtil;
import tech.jhipster.web.util.ResponseUtil;

/**
 * 新闻举报管理的REST控制器
 */
@RestController
@RequestMapping("/api/news/reports")
public class NewsReportResource {

    private static final Logger LOG = LoggerFactory.getLogger(NewsReportResource.class);

    private static final String ENTITY_NAME = "whiskerguardOrgServiceNewsReport";

    @Value("${jhipster.clientApp.name}")
    private String applicationName;

    private final NewsReportService newsReportService;

    private final NewsReportRepository newsReportRepository;

    public NewsReportResource(NewsReportService newsReportService, NewsReportRepository newsReportRepository) {
        this.newsReportService = newsReportService;
        this.newsReportRepository = newsReportRepository;
    }

    /**
     * {@code POST  /news-reports} : 创建新闻举报
     *
     * @param newsReportDTO 要创建的新闻举报DTO
     * @return {@link ResponseEntity}，状态为 {@code 201 (Created)}，响应体为新创建的新闻举报DTO；
     *         如果新闻举报已经有ID，则状态为 {@code 400 (Bad Request)}
     * @throws URISyntaxException 如果Location URI语法不正确
     */
    @PostMapping("/create")
    public ResponseEntity<NewsReportDTO> createNewsReport(@Valid @RequestBody NewsReportDTO newsReportDTO) throws URISyntaxException {
        LOG.debug("REST request to save NewsReport : {}", newsReportDTO);
        if (newsReportDTO.getId() != null) {
            throw new BadRequestAlertException("新的新闻举报不能有ID", ENTITY_NAME, "idexists");
        }
        newsReportDTO = newsReportService.save(newsReportDTO);
        return ResponseEntity.created(new URI("/api/news/reports/" + newsReportDTO.getId()))
            .headers(HeaderUtil.createEntityCreationAlert(applicationName, true, ENTITY_NAME, newsReportDTO.getId().toString()))
            .body(newsReportDTO);
    }

    /**
     * {@code PUT  /news-reports/:id} : 更新已存在的新闻举报
     *
     * @param id 要保存的新闻举报DTO的ID
     * @param newsReportDTO 要更新的新闻举报DTO
     * @return {@link ResponseEntity}，状态为 {@code 200 (OK)}，响应体为更新后的新闻举报DTO；
     *         如果新闻举报DTO无效，则状态为 {@code 400 (Bad Request)}；
     *         如果新闻举报DTO无法更新，则状态为 {@code 500 (Internal Server Error)}
     * @throws URISyntaxException 如果Location URI语法不正确
     */
    @PutMapping("/update/{id}")
    public ResponseEntity<NewsReportDTO> updateNewsReport(
        @PathVariable(value = "id", required = false) final Long id,
        @Valid @RequestBody NewsReportDTO newsReportDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to update NewsReport : {}, {}", id, newsReportDTO);
        if (newsReportDTO.getId() == null) {
            throw new BadRequestAlertException("无效的ID", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, newsReportDTO.getId())) {
            throw new BadRequestAlertException("无效的ID", ENTITY_NAME, "idinvalid");
        }

        if (!newsReportRepository.existsById(id)) {
            throw new BadRequestAlertException("实体未找到", ENTITY_NAME, "idnotfound");
        }

        newsReportDTO = newsReportService.update(newsReportDTO);
        return ResponseEntity.ok()
            .headers(HeaderUtil.createEntityUpdateAlert(applicationName, true, ENTITY_NAME, newsReportDTO.getId().toString()))
            .body(newsReportDTO);
    }

    /**
     * {@code PATCH  /news-reports/:id} : 部分更新已存在的新闻举报，null字段将被忽略
     *
     * @param id 要保存的新闻举报DTO的ID
     * @param newsReportDTO 要更新的新闻举报DTO
     * @return {@link ResponseEntity}，状态为 {@code 200 (OK)}，响应体为更新后的新闻举报DTO；
     *         如果新闻举报DTO无效，则状态为 {@code 400 (Bad Request)}；
     *         如果找不到新闻举报DTO，则状态为 {@code 404 (Not Found)}；
     *         如果新闻举报DTO无法更新，则状态为 {@code 500 (Internal Server Error)}
     * @throws URISyntaxException 如果Location URI语法不正确
     */
    @PatchMapping(value = "/partial/{id}", consumes = { "application/json", "application/merge-patch+json" })
    public ResponseEntity<NewsReportDTO> partialUpdateNewsReport(
        @PathVariable(value = "id", required = false) final Long id,
        @NotNull @Valid @RequestBody NewsReportDTO newsReportDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to partial update NewsReport partially : {}, {}", id, newsReportDTO);
        if (newsReportDTO.getId() == null) {
            throw new BadRequestAlertException("无效的ID", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, newsReportDTO.getId())) {
            throw new BadRequestAlertException("无效的ID", ENTITY_NAME, "idinvalid");
        }

        if (!newsReportRepository.existsById(id)) {
            throw new BadRequestAlertException("实体未找到", ENTITY_NAME, "idnotfound");
        }

        Optional<NewsReportDTO> result = newsReportService.partialUpdate(newsReportDTO);

        return ResponseUtil.wrapOrNotFound(
            result,
            HeaderUtil.createEntityUpdateAlert(applicationName, true, ENTITY_NAME, newsReportDTO.getId().toString())
        );
    }

    /**
     * {@code GET  /news-reports} : 获取所有新闻举报
     *
     * @param pageable 分页信息
     * @param eagerload 是否急切加载关联实体的标志（适用于多对多关系）
     * @return {@link ResponseEntity}，状态为 {@code 200 (OK)}，响应体为新闻举报分页数据
     */
    @GetMapping("/list")
    public ResponseEntity<Page<NewsReportDTO>> getAllNewsReports(
        @org.springdoc.core.annotations.ParameterObject Pageable pageable,
        @RequestParam(name = "eagerload", required = false, defaultValue = "true") boolean eagerload
    ) {
        LOG.debug("REST request to get a page of NewsReports");
        Page<NewsReportDTO> page;
        if (eagerload) {
            page = newsReportService.findAllWithEagerRelationships(pageable);
        } else {
            page = newsReportService.findAll(pageable);
        }
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(ServletUriComponentsBuilder.fromCurrentRequest(), page);
        return ResponseEntity.ok().headers(headers).body(page);
    }

    /**
     * {@code GET  /news-reports/:id} : 获取指定ID的新闻举报
     *
     * @param id 要获取的新闻举报DTO的ID
     * @return {@link ResponseEntity}，状态为 {@code 200 (OK)}，响应体为新闻举报DTO；
     *         如果找不到新闻举报，则状态为 {@code 404 (Not Found)}
     */
    @GetMapping("/detail/{id}")
    public ResponseEntity<NewsReportDTO> getNewsReport(@PathVariable("id") Long id) {
        LOG.debug("REST request to get NewsReport : {}", id);
        Optional<NewsReportDTO> newsReportDTO = newsReportService.findOne(id);
        return ResponseUtil.wrapOrNotFound(newsReportDTO);
    }

    /**
     * {@code DELETE  /news-reports/:id} : 删除指定ID的新闻举报
     *
     * @param id 要删除的新闻举报DTO的ID
     * @return {@link ResponseEntity}，状态为 {@code 204 (NO_CONTENT)}
     */
    @DeleteMapping("/delete/{id}")
    public ResponseEntity<Void> deleteNewsReport(@PathVariable("id") Long id) {
        LOG.debug("REST request to delete NewsReport : {}", id);
        newsReportService.delete(id);
        return ResponseEntity.noContent()
            .headers(HeaderUtil.createEntityDeletionAlert(applicationName, true, ENTITY_NAME, id.toString()))
            .build();
    }
}

package com.whiskerguard.organization.web.rest;

import com.whiskerguard.organization.repository.PermissionRepository;
import com.whiskerguard.organization.service.PermissionService;
import com.whiskerguard.organization.service.dto.MenuDTO;
import com.whiskerguard.organization.service.dto.PermissionDTO;
import com.whiskerguard.organization.web.rest.errors.BadRequestAlertException;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;
import tech.jhipster.web.util.HeaderUtil;
import tech.jhipster.web.util.PaginationUtil;
import tech.jhipster.web.util.ResponseUtil;

import java.net.URI;
import java.net.URISyntaxException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * REST controller for managing {@link com.whiskerguard.organization.domain.Permission}.
 * 权限的REST控制器
 * 提供权限的CRUD操作API接口
 */
@RestController
@RequestMapping("/api/permissions")
public class PermissionResource {

    /**
     * 日志记录器
     */
    private static final Logger LOG = LoggerFactory.getLogger(PermissionResource.class);

    /**
     * 实体名称，用于错误消息
     */
    private static final String ENTITY_NAME = "whiskerguardOrgServicePermission";

    /**
     * 应用名称，从配置中获取
     */
    @Value("${jhipster.clientApp.name}")
    private String applicationName;

    /**
     * 权限服务
     */
    private final PermissionService permissionService;

    /**
     * 权限仓库
     */
    private final PermissionRepository permissionRepository;

    private final UserDetailsService userDetailsService;

    /**
     * 构造函数
     *
     * @param permissionService    权限服务
     * @param permissionRepository 权限仓库
     */
    public PermissionResource(PermissionService permissionService, PermissionRepository permissionRepository,
                              UserDetailsService userDetailsService) {
        this.permissionService = permissionService;
        this.permissionRepository = permissionRepository;
        this.userDetailsService = userDetailsService;
    }

    /**
     * {@code POST  /permissions} : Create a new permission.
     * 创建新的权限
     *
     * @param menuDTO the menuDTO to create. 要创建的菜单DTO
     * @return the {@link ResponseEntity} with status {@code 201 (Created)} and with body the new menuDTO, or with status {@code 400 (Bad Request)} if the permission has already an ID.
     * 返回状态码为201的响应和新创建的权限DTO，如果权限已有ID则返回400错误
     * @throws URISyntaxException if the Location URI syntax is incorrect. 如果位置URI语法不正确
     */
    @PostMapping("")
    public ResponseEntity<MenuDTO> createPermission(@Valid @RequestBody MenuDTO menuDTO) throws URISyntaxException {
        LOG.debug("REST request to save Permission : {}", menuDTO);
        if (menuDTO.getId() != null) {
            throw new BadRequestAlertException("新的权限不能有ID", ENTITY_NAME, "idexists");
        }
        menuDTO = permissionService.save(menuDTO);
        return ResponseEntity.created(new URI("/api/permissions/" + menuDTO.getId()))
            .headers(HeaderUtil.createEntityCreationAlert(applicationName, true, ENTITY_NAME, menuDTO.getId().toString()))
            .body(menuDTO);
    }

    /**
     * {@code PATCH  /permissions/:id} : Partial updates given fields of an existing permission, field will ignore if it is null
     * 部分更新现有权限的指定字段，如果字段为null则忽略
     *
     * @param id      the id of the permissionDTO to save. 要保存的权限DTO的ID
     * @param menuDTO the menuDTO to update. 要更新的菜单
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated permissionDTO,
     * or with status {@code 400 (Bad Request)} if the permissionDTO is not valid,
     * or with status {@code 404 (Not Found)} if the permissionDTO is not found,
     * or with status {@code 500 (Internal Server Error)} if the permissionDTO couldn't be updated.
     * 返回状态码为200的响应和更新后的权限DTO，如果权限DTO无效则返回400错误，如果找不到权限则返回404错误，如果无法更新则返回500错误
     */
    @PatchMapping(value = "/{id}", consumes = {"application/json", "application/merge-patch+json"})
    public ResponseEntity<MenuDTO> partialUpdatePermission(
        @PathVariable(value = "id", required = false) final Long id, @Valid @RequestBody MenuDTO menuDTO
    ) {
        LOG.debug("REST request to partial update Permission partially : {}, {}", id, menuDTO);
        if (menuDTO.getId() == null) {
            throw new BadRequestAlertException("无效的ID", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, menuDTO.getId())) {
            throw new BadRequestAlertException("无效的ID", ENTITY_NAME, "idinvalid");
        }

        if (!permissionRepository.existsById(id)) {
            throw new BadRequestAlertException("实体未找到", ENTITY_NAME, "idnotfound");
        }

        Optional<MenuDTO> result = permissionService.partialUpdate(menuDTO);
        return ResponseUtil.wrapOrNotFound(
            result,
            HeaderUtil.createEntityUpdateAlert(applicationName, true, ENTITY_NAME, menuDTO.getId().toString())
        );
    }

    /**
     * {@code GET  /permissions} : get all the permissions.
     * 获取所有权限
     *
     * @param pageable the pagination information. 分页信息
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and the page of permissions in body.
     * 返回状态码为200的响应和权限分页数据
     */
    @GetMapping("")
    public ResponseEntity<Page<PermissionDTO>> getAllPermissions(@org.springdoc.core.annotations.ParameterObject Pageable pageable) {
        LOG.debug("REST request to get a page of Permissions");
        Page<PermissionDTO> page = permissionService.findAll(pageable);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(ServletUriComponentsBuilder.fromCurrentRequest(), page);
        return ResponseEntity.ok().headers(headers).body(page);
    }

    /**
     * {@code GET  /permissions/:id} : get the "id" permission.
     * 获取指定ID的权限
     *
     * @param id the id of the permissionDTO to retrieve. 要检索的权限DTO的ID
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the permissionDTO, or with status {@code 404 (Not Found)}.
     * 返回状态码为200的响应和权限DTO，如果找不到则返回404错误
     */
    @GetMapping("/{id}")
    public ResponseEntity<PermissionDTO> getPermission(@PathVariable("id") Long id) {
        LOG.debug("REST request to get Permission : {}", id);
        Optional<PermissionDTO> permissionDTO = permissionService.findOne(id);
        return ResponseUtil.wrapOrNotFound(permissionDTO);
    }

    /**
     * {@code DELETE  /permissions/:id} : delete the "id" permission.
     * 删除指定ID的权限
     *
     * @param id the id of the permissionDTO to delete. 要删除的权限DTO的ID
     * @return the {@link ResponseEntity} with status {@code 204 (NO_CONTENT)}.
     * 返回状态码为204的响应（无内容）
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deletePermission(@PathVariable("id") Long id) {
        LOG.debug("REST request to delete Permission : {}", id);
        permissionService.delete(id);
        return ResponseEntity.noContent()
            .headers(HeaderUtil.createEntityDeletionAlert(applicationName, true, ENTITY_NAME, id.toString()))
            .build();
    }

    /**
     * {@code GET  /permissions/tree/{tenantId}} : Get tree structure permissions by tenant ID with pagination.
     * 获取树形结构的权限
     *
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and the list of permissions in tree structure in body.
     * 返回状态码为200的响应和树形结构的权限列表
     */
    @GetMapping("/tree")
    public ResponseEntity<List<PermissionDTO>> getPermissionTreeByTenantId() {
        return ResponseEntity.ok().body(permissionService.findTreeByTenantId());
    }

    /**
     * {@code GET  /permissions/menu/tree} : Get tree structure menu by tenant ID with pagination.
     * 获取树形结构的菜单
     *
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and the list of menu meta data in body.
     * 返回状态码为200的响应和菜单元数据列表
     */
    @GetMapping("/menu/tree")
    public ResponseEntity<List<MenuDTO>> getAllMenuTree() {
        return ResponseEntity.ok().body(permissionService.findAllMenu());
    }

    /**
     * {@code GET  /permissions/children/{parentId}} : Get child permissions by parent ID.
     * 根据父权限ID获取子权限
     *
     * @param parentId the ID of the parent permission. 父权限ID
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and the list of child permissions in body.
     * 返回状态码为200的响应和子权限列表
     */
    @GetMapping("/children/{parentId}")
    public ResponseEntity<List<PermissionDTO>> getChildPermissionsByParentId(@PathVariable Long parentId) {
        LOG.debug("REST request to get child permissions by parent ID : {}", parentId);
        List<PermissionDTO> children = permissionService.findByParentId(parentId);
        return ResponseEntity.ok().body(children);
    }

    /**
     * {@code GET  /permissions/tree/employee/{employeeId}} : Get tree structure permissions by employee ID.
     * 根据员工ID获取树形结构的权限
     *
     * @param employeeId the ID of the employee. 员工ID
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and the list of menu meta data in body.
     * 返回状态码为200的响应和菜单元数据列表
     */
    @GetMapping("/tree/employee/{employeeId}")
    public ResponseEntity<List<MenuDTO>> getPermissionTreeByEmployeeId(@PathVariable Long employeeId) {
        LOG.debug("REST request to get tree structure permissions by employee ID : {}", employeeId);
        List<MenuDTO> menuList = permissionService.findMenuTreeByEmployeeId(employeeId);
        return ResponseEntity.ok().body(menuList);
    }

    /**
     * {@code GET  /permissions/employee/username} : Get permissions by username.
     * 根据用户名获取权限
     *
     * @param username the username. 用户名
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and the set of permissions in body.
     * 返回状态码为200的响应和权限集合
     */
    @GetMapping("/employee/username")
    public ResponseEntity<Set<String>> getPermissionByUsername(@RequestParam("username") String username) {
        LOG.debug("REST request to get permissions by username : {}", username);
        UserDetails userDetails = userDetailsService.loadUserByUsername(username);
        Set<GrantedAuthority> authorities = new HashSet<>(userDetails.getAuthorities());
        Set<String> permissions = authorities.stream().map(GrantedAuthority::getAuthority).collect(Collectors.toSet());
        return ResponseEntity.ok().body(permissions);
    }

    /**
     * {@code GET  /permissions/tree/role/{roleId}} : Get tree structure permissions by role ID.
     * 根据角色ID获取树形结构的权限
     *
     * @param roleId the ID of the roleId. 角色ID
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and the list of menu meta data in body.
     * 返回状态码为200的响应和菜单元数据列表
     */
    @GetMapping("/tree/role/{roleId}")
    public ResponseEntity<List<MenuDTO>> getPermissionTreeByRoleId(@PathVariable Long roleId) {
        LOG.debug("REST request to get tree structure permissions by roleId ID : {}", roleId);
        List<MenuDTO> menuList = permissionService.findMenuTreeByByRoleId(roleId);
        return ResponseEntity.ok().body(menuList);
    }
    
    /**
     * {@code POST  /permissions/batch} : Create multiple new permissions.
     * 批量创建新的权限
     *
     * @param list the list of menuDTO to create. 要创建的菜单DTO列表
     * @return the {@link ResponseEntity} with status {@code 201 (Created)} and with body the new list, or with status {@code 400 (Bad Request)} if any permission has already an ID.
     * 返回状态码为201的响应和新创建的菜单DTO列表，如果任何权限已有ID则返回400错误
     * @throws URISyntaxException if the Location URI syntax is incorrect. 如果位置URI语法不正确
     */
    @PostMapping("/batch")
    public ResponseEntity<List<MenuDTO>> createMenuBatch(@Valid @RequestBody List<MenuDTO> list)
        throws URISyntaxException {
        LOG.debug("REST request to batch save Menu : {}", list.size());
        List<MenuDTO> result = permissionService.saveAll(list);
        return ResponseEntity.created(new URI("/api/permissions/batch"))
            .headers(HeaderUtil.createAlert(applicationName, "permissionManagement.created.batch", String.valueOf(result.size())))
            .body(result);
    }
}

package com.whiskerguard.organization.web.rest;

import com.whiskerguard.organization.repository.AuditLogRepository;
import com.whiskerguard.organization.service.AuditLogService;
import com.whiskerguard.organization.service.dto.AuditLogDTO;
import com.whiskerguard.organization.web.rest.errors.BadRequestAlertException;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.Objects;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;
import tech.jhipster.web.util.HeaderUtil;
import tech.jhipster.web.util.PaginationUtil;
import tech.jhipster.web.util.ResponseUtil;

/**
 * REST controller for managing {@link com.whiskerguard.organization.domain.AuditLog}.
 * 审计日志管理的REST控制器
 * 提供审计日志的CRUD操作API接口，用于记录和查询系统操作日志
 */
@RestController
@RequestMapping("/api/audit/logs")
public class AuditLogResource {

    /**
     * 日志记录器
     */
    private static final Logger LOG = LoggerFactory.getLogger(AuditLogResource.class);

    /**
     * 实体名称，用于错误消息
     */
    private static final String ENTITY_NAME = "whiskerguardOrgServiceAuditLog";

    /**
     * 应用名称，从配置中获取
     */
    @Value("${jhipster.clientApp.name}")
    private String applicationName;

    /**
     * 审计日志服务
     */
    private final AuditLogService auditLogService;

    /**
     * 审计日志仓库
     */
    private final AuditLogRepository auditLogRepository;

    /**
     * 构造函数
     *
     * @param auditLogService    审计日志服务
     * @param auditLogRepository 审计日志仓库
     */
    public AuditLogResource(AuditLogService auditLogService, AuditLogRepository auditLogRepository) {
        this.auditLogService = auditLogService;
        this.auditLogRepository = auditLogRepository;
    }

    /**
     * {@code POST  /audit-logs} : Create a new auditLog.
     * 创建新的审计日志
     *
     * @param auditLogDTO the auditLogDTO to create. 要创建的审计日志DTO
     * @return the {@link ResponseEntity} with status {@code 201 (Created)} and with body the new auditLogDTO, or with status {@code 400 (Bad Request)} if the auditLog has already an ID.
     * 返回状态码为201的响应和新创建的审计日志DTO，如果审计日志已有ID则返回400错误
     * @throws URISyntaxException if the Location URI syntax is incorrect. 如果位置URI语法不正确
     */
    @PostMapping("")
    public ResponseEntity<AuditLogDTO> createAuditLog(@Valid @RequestBody AuditLogDTO auditLogDTO) throws URISyntaxException {
        LOG.debug("REST request to save AuditLog : {}", auditLogDTO);
        if (auditLogDTO.getId() != null) {
            throw new BadRequestAlertException("新的审计日志不能有ID", ENTITY_NAME, "idexists");
        }
        auditLogDTO = auditLogService.save(auditLogDTO);
        return ResponseEntity.created(new URI("/api/audit/logs/" + auditLogDTO.getId()))
            .headers(HeaderUtil.createEntityCreationAlert(applicationName, true, ENTITY_NAME, auditLogDTO.getId().toString()))
            .body(auditLogDTO);
    }

    /**
     * {@code PUT  /audit-logs/:id} : Updates an existing auditLog.
     * 更新现有的审计日志
     *
     * @param id the id of the auditLogDTO to save. 要保存的审计日志DTO的ID
     * @param auditLogDTO the auditLogDTO to update. 要更新的审计日志DTO
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated auditLogDTO,
     * or with status {@code 400 (Bad Request)} if the auditLogDTO is not valid,
     * or with status {@code 500 (Internal Server Error)} if the auditLogDTO couldn't be updated.
     * 返回状态码为200的响应和更新后的审计日志DTO，如果审计日志DTO无效则返回400错误，如果无法更新则返回500错误
     * @throws URISyntaxException if the Location URI syntax is incorrect. 如果位置URI语法不正确
     */
    @PutMapping("/{id}")
    public ResponseEntity<AuditLogDTO> updateAuditLog(
        @PathVariable(value = "id", required = false) final Long id,
        @Valid @RequestBody AuditLogDTO auditLogDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to update AuditLog : {}, {}", id, auditLogDTO);
        if (auditLogDTO.getId() == null) {
            throw new BadRequestAlertException("无效的ID", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, auditLogDTO.getId())) {
            throw new BadRequestAlertException("无效的ID", ENTITY_NAME, "idinvalid");
        }

        if (!auditLogRepository.existsById(id)) {
            throw new BadRequestAlertException("实体未找到", ENTITY_NAME, "idnotfound");
        }

        auditLogDTO = auditLogService.update(auditLogDTO);
        return ResponseEntity.ok()
            .headers(HeaderUtil.createEntityUpdateAlert(applicationName, true, ENTITY_NAME, auditLogDTO.getId().toString()))
            .body(auditLogDTO);
    }

    /**
     * {@code PATCH  /audit-logs/:id} : Partial updates given fields of an existing auditLog, field will ignore if it is null
     * 部分更新现有审计日志的指定字段，如果字段为null则忽略
     *
     * @param id the id of the auditLogDTO to save. 要保存的审计日志DTO的ID
     * @param auditLogDTO the auditLogDTO to update. 要更新的审计日志DTO
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated auditLogDTO,
     * or with status {@code 400 (Bad Request)} if the auditLogDTO is not valid,
     * or with status {@code 404 (Not Found)} if the auditLogDTO is not found,
     * or with status {@code 500 (Internal Server Error)} if the auditLogDTO couldn't be updated.
     * 返回状态码为200的响应和更新后的审计日志DTO，如果审计日志DTO无效则返回400错误，如果找不到审计日志则返回404错误，如果无法更新则返回500错误
     * @throws URISyntaxException if the Location URI syntax is incorrect. 如果位置URI语法不正确
     */
    @PatchMapping(value = "/{id}", consumes = { "application/json", "application/merge-patch+json" })
    public ResponseEntity<AuditLogDTO> partialUpdateAuditLog(
        @PathVariable(value = "id", required = false) final Long id,
        @NotNull @Valid @RequestBody AuditLogDTO auditLogDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to partial update AuditLog partially : {}, {}", id, auditLogDTO);
        if (auditLogDTO.getId() == null) {
            throw new BadRequestAlertException("无效的ID", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, auditLogDTO.getId())) {
            throw new BadRequestAlertException("无效的ID", ENTITY_NAME, "idinvalid");
        }

        if (!auditLogRepository.existsById(id)) {
            throw new BadRequestAlertException("实体未找到", ENTITY_NAME, "idnotfound");
        }

        Optional<AuditLogDTO> result = auditLogService.partialUpdate(auditLogDTO);

        return ResponseUtil.wrapOrNotFound(
            result,
            HeaderUtil.createEntityUpdateAlert(applicationName, true, ENTITY_NAME, auditLogDTO.getId().toString())
        );
    }

    /**
     * {@code GET  /audit-logs} : get all the auditLogs.
     * 获取所有审计日志
     *
     * @param pageable the pagination information. 分页信息
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and the page of auditLogs in body.
     * 返回状态码为200的响应和审计日志分页数据
     */
    @GetMapping("")
    public ResponseEntity<Page<AuditLogDTO>> getAllAuditLogs(@org.springdoc.core.annotations.ParameterObject Pageable pageable) {
        LOG.debug("REST request to get a page of AuditLogs");
        Page<AuditLogDTO> page = auditLogService.findAll(pageable);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(ServletUriComponentsBuilder.fromCurrentRequest(), page);
        return ResponseEntity.ok().headers(headers).body(page);
    }

    /**
     * {@code GET  /audit-logs/:id} : get the "id" auditLog.
     * 根据ID获取审计日志
     *
     * @param id the id of the auditLogDTO to retrieve. 要检索的审计日志DTO的ID
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the auditLogDTO, or with status {@code 404 (Not Found)}.
     * 返回状态码为200的响应和审计日志DTO，如果找不到则返回404错误
     */
    @GetMapping("/{id}")
    public ResponseEntity<AuditLogDTO> getAuditLog(@PathVariable("id") Long id) {
        LOG.debug("REST request to get AuditLog : {}", id);
        Optional<AuditLogDTO> auditLogDTO = auditLogService.findOne(id);
        return ResponseUtil.wrapOrNotFound(auditLogDTO);
    }

    /**
     * {@code DELETE  /audit-logs/:id} : delete the "id" auditLog.
     * 根据ID删除审计日志
     *
     * @param id the id of the auditLogDTO to delete. 要删除的审计日志DTO的ID
     * @return the {@link ResponseEntity} with status {@code 204 (NO_CONTENT)}.
     * 返回状态码为204的响应（无内容）
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteAuditLog(@PathVariable("id") Long id) {
        LOG.debug("REST request to delete AuditLog : {}", id);
        auditLogService.delete(id);
        return ResponseEntity.noContent()
            .headers(HeaderUtil.createEntityDeletionAlert(applicationName, true, ENTITY_NAME, id.toString()))
            .build();
    }
}

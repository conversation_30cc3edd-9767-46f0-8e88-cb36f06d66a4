package com.whiskerguard.organization.web.rest;

import com.whiskerguard.organization.repository.RiskModelRepository;
import com.whiskerguard.organization.service.RiskModelService;
import com.whiskerguard.organization.service.dto.RiskModelDTO;
import com.whiskerguard.organization.web.rest.errors.BadRequestAlertException;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;
import tech.jhipster.web.util.HeaderUtil;
import tech.jhipster.web.util.PaginationUtil;
import tech.jhipster.web.util.ResponseUtil;

import java.net.URI;
import java.net.URISyntaxException;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * 描述：风险模型管理的REST控制器
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/20
 */
@RestController
@RequestMapping("/api/risk/models")
public class RiskModelResource {

    private static final Logger LOG = LoggerFactory.getLogger(RiskModelResource.class);

    private static final String ENTITY_NAME = "whiskerguardOrgServiceRiskModel";

    @Value("${jhipster.clientApp.name}")
    private String applicationName;

    private final RiskModelService riskModelService;

    private final RiskModelRepository riskModelRepository;

    public RiskModelResource(RiskModelService riskModelService, RiskModelRepository riskModelRepository) {
        this.riskModelService = riskModelService;
        this.riskModelRepository = riskModelRepository;
    }

    /**
     * 方法名称：createRiskModel
     * 描述：创建新的风险模型
     *
     * @param riskModelDTO 要创建的风险模型DTO
     * @return {@link ResponseEntity}，状态为 {@code 201 (Created)}，响应体为新创建的风险模型DTO；
     * 如果风险模型已经有ID，则状态为 {@code 400 (Bad Request)}
     * @throws URISyntaxException 如果 URI 语法不正确。
     * @since 1.0
     */
    @PostMapping("")
    public ResponseEntity<RiskModelDTO> createRiskModel(@Valid @RequestBody RiskModelDTO riskModelDTO) throws URISyntaxException {
        LOG.debug("REST request to save RiskModel : {}", riskModelDTO);
        if (riskModelDTO.getId() != null) {
            throw new BadRequestAlertException("新的风险模型不能有ID", ENTITY_NAME, "idexists");
        }
        riskModelDTO = riskModelService.save(riskModelDTO);
        return ResponseEntity.created(new URI("/api/risk/models/" + riskModelDTO.getId()))
            .headers(HeaderUtil.createEntityCreationAlert(applicationName, true, ENTITY_NAME, riskModelDTO.getId().toString()))
            .body(riskModelDTO);
    }

    /**
     * 方法名称：partialUpdateRiskModel
     * 描述：部分更新已存在的风险模型记录
     *
     * @param riskModelDTO 要更新的风险模型DTO
     * @return {@link ResponseEntity}，状态为 {@code 200 (OK)}，响应体为更新后的风险模型DTO；
     * 如果风险模型DTO无效，则状态为 {@code 400 (Bad Request)}；
     * 如果风险模型DTO无法更新，则状态为 {@code 500 (Internal Server Error)}
     * @since 1.0
     */
    @PatchMapping(value = "/{id}", consumes = {"application/json", "application/merge-patch+json"})
    public ResponseEntity<RiskModelDTO> partialUpdateRiskModel(
        @PathVariable(value = "id", required = false) final Long id,
        @NotNull @RequestBody RiskModelDTO riskModelDTO
    ) {
        LOG.debug("REST request to partial update RiskModel partially : {}, {}", id, riskModelDTO);
        if (riskModelDTO.getId() == null) {
            throw new BadRequestAlertException("无效的ID", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, riskModelDTO.getId())) {
            throw new BadRequestAlertException("无效的ID", ENTITY_NAME, "idinvalid");
        }

        if (!riskModelRepository.existsById(id)) {
            throw new BadRequestAlertException("实体未找到", ENTITY_NAME, "idnotfound");
        }

        Optional<RiskModelDTO> result = riskModelService.partialUpdate(riskModelDTO);

        return ResponseUtil.wrapOrNotFound(
            result,
            HeaderUtil.createEntityUpdateAlert(applicationName, true, ENTITY_NAME, riskModelDTO.getId().toString())
        );
    }

    /**
     * 方法名称：getAllRiskModels
     * 描述：获取所有风险模型记录
     *
     * @param pageable 分页信息
     * @return {@link ResponseEntity}，状态为 {@code 200 (OK)}，响应体为风险模型DTO的分页列表
     * @since 1.0
     */
    @GetMapping("")
    public ResponseEntity<Page<RiskModelDTO>> getAllRiskModels(@org.springdoc.core.annotations.ParameterObject Pageable pageable) {
        LOG.debug("REST request to get a page of RiskModels");
        Page<RiskModelDTO> page = riskModelService.findAll(pageable);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(ServletUriComponentsBuilder.fromCurrentRequest(), page);
        return ResponseEntity.ok().headers(headers).body(page);
    }

    /**
     * 方法名称：getRiskModel
     * 描述：根据ID获取风险模型记录
     *
     * @param id 风险模型的ID
     * @return {@link ResponseEntity}，状态为 {@code 200 (OK)}，响应体为风险模型DTO；
     * 如果风险模型不存在，则状态为 {@code 404 (Not Found)}
     * @since 1.0
     */
    @GetMapping("/{id}")
    public ResponseEntity<RiskModelDTO> getRiskModel(@PathVariable("id") Long id) {
        LOG.debug("REST request to get RiskModel : {}", id);
        Optional<RiskModelDTO> riskModelDTO = riskModelService.findOne(id);
        return ResponseUtil.wrapOrNotFound(riskModelDTO);
    }

    /**
     * 方法名称：deleteRiskModel
     * 描述：根据ID删除风险模型记录
     *
     * @param id 风险模型的ID
     * @return {@link ResponseEntity}，状态为 {@code 204 (NO_CONTENT)}
     * @since 1.0
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteRiskModel(@PathVariable("id") Long id) {
        LOG.debug("REST request to delete RiskModel : {}", id);
        riskModelService.delete(id);
        return ResponseEntity.noContent()
            .headers(HeaderUtil.createEntityDeletionAlert(applicationName, true, ENTITY_NAME, id.toString()))
            .build();
    }

    /**
     * 方法名称：analyzeRiskModel
     * 描述：根据指定的内容结合风险模型，进行AI分析后，得到多种风险级别
     *
     * @param param 要分析的内容
     * @return {@link ResponseEntity}，状态为 {@code 200 (OK)}，响应体为分析结果
     * @since 1.0
     */
    @PostMapping("/analyze")
    public ResponseEntity<String> analyzeRiskModel(@RequestBody Map<String, Object> param) {
        String content = param.get("content").toString();
        if (content == null || content.trim().isEmpty()) {
            throw new BadRequestAlertException("要分析的内容不能为空", ENTITY_NAME, "content.empty");
        }
        LOG.debug("REST request to analyze RiskModel : {}", content);
        String result = riskModelService.analyzeRiskModel(content);
        return ResponseEntity.ok(result);
    }

}

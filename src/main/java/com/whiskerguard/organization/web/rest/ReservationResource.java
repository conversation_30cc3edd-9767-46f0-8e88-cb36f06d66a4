package com.whiskerguard.organization.web.rest;

import com.whiskerguard.organization.service.ReservationService;
import com.whiskerguard.organization.service.dto.ReservationDTO;
import com.whiskerguard.organization.web.rest.errors.BadRequestAlertException;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;
import tech.jhipster.web.util.HeaderUtil;
import tech.jhipster.web.util.PaginationUtil;

import java.net.URI;
import java.net.URISyntaxException;
import java.util.List;

/**
 * 描述：预约体验表的REST控制器
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/17
 */
@RestController
@RequestMapping("/api/reservations")
public class ReservationResource {

    private static final Logger LOG = LoggerFactory.getLogger(ReservationResource.class);

    private static final String ENTITY_NAME = "whiskerguardOrgServiceReservation";

    @Value("${jhipster.clientApp.name}")
    private String applicationName;

    private final ReservationService reservationService;

    public ReservationResource(ReservationService reservationService) {
        this.reservationService = reservationService;
    }

    /**
     * 方法名称：createReservation
     * 描述：创建新的预约体验记录
     *
     * @param reservationDTO 要创建的预约体验记录DTO
     * @return {@link ResponseEntity}，状态为 {@code 201 (Created)}，响应体为新创建的预约体验记录DTO；
     * 如果预约体验记录已经有ID，则状态为 {@code 400 (Bad Request)}
     * @throws URISyntaxException 如果 URI 语法不正确。
     * @since 1.0
     */
    @PostMapping("")
    public ResponseEntity<ReservationDTO> createReservation(@Valid @RequestBody ReservationDTO reservationDTO) throws URISyntaxException {
        LOG.debug("REST request to save Reservation : {}", reservationDTO);
        if (reservationDTO.getId() != null) {
            throw new BadRequestAlertException("新的预约体验不能有ID", ENTITY_NAME, "idexists");
        }
        reservationDTO = reservationService.save(reservationDTO);
        return ResponseEntity.created(new URI("/api/reservations/" + reservationDTO.getId()))
            .headers(HeaderUtil.createEntityCreationAlert(applicationName, true, ENTITY_NAME, reservationDTO.getId().toString()))
            .body(reservationDTO);
    }

    /**
     * 方法名称：getAllReservations
     * 描述：获取所有预约体验记录
     *
     * @param pageable 分页信息
     * @return {@link ResponseEntity}，状态为 {@code 200 (OK)}，响应体为预约体验记录DTO的分页列表
     * @since 1.0
     */
    @GetMapping("")
    public ResponseEntity<List<ReservationDTO>> getAllReservations(@org.springdoc.core.annotations.ParameterObject Pageable pageable) {
        LOG.debug("REST request to get a page of Reservations");
        Page<ReservationDTO> page = reservationService.findAll(pageable);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(ServletUriComponentsBuilder.fromCurrentRequest(), page);
        return ResponseEntity.ok().headers(headers).body(page.getContent());
    }

}

package com.whiskerguard.organization.web.rest;

import com.whiskerguard.organization.service.LetterCommitmentService;
import com.whiskerguard.organization.service.dto.LetterCommitmentDTO;
import com.whiskerguard.organization.web.rest.errors.BadRequestAlertException;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import tech.jhipster.web.util.HeaderUtil;

import java.net.URI;
import java.net.URISyntaxException;
import java.util.Optional;

/**
 * 描述：承诺书管理的REST控制器
 * 提供承诺书的增删改查等操作接口，包括合规承诺书等各类承诺书的管理
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/14
 */
@RestController
@RequestMapping("/api/letter/commitments")
public class LetterCommitmentResource {

    private static final Logger LOG = LoggerFactory.getLogger(LetterCommitmentResource.class);

    private static final String ENTITY_NAME = "whiskerguardOrgServiceLetterCommitment";

    @Value("${jhipster.clientApp.name}")
    private String applicationName;

    private final LetterCommitmentService letterCommitmentService;

    /**
     * 构造函数：初始化承诺书资源控制器
     *
     * @param letterCommitmentService 承诺书服务
     */
    public LetterCommitmentResource(LetterCommitmentService letterCommitmentService) {
        this.letterCommitmentService = letterCommitmentService;
    }

    /**
     * 方法名称：createLetterCommitment
     * 描述：创建新的承诺书记录
     *
     * @param letterCommitmentDTO 要创建的承诺书DTO
     * @return {@link ResponseEntity}，状态为 {@code 201 (Created)}，响应体为新创建的承诺书DTO；
     * 如果承诺书已经有ID，则状态为 {@code 400 (Bad Request)}
     * @throws URISyntaxException 如果 URI 语法不正确
     * @since 1.0
     */
    @PostMapping("")
    public ResponseEntity<LetterCommitmentDTO> createLetterCommitment(@Valid @RequestBody LetterCommitmentDTO letterCommitmentDTO)
        throws URISyntaxException {
        LOG.debug("REST request to save LetterCommitment : {}", letterCommitmentDTO);
        if (letterCommitmentDTO.getId() != null) {
            throw new BadRequestAlertException("新的承诺书不能已经有ID", ENTITY_NAME, "id exists");
        }
        letterCommitmentDTO = letterCommitmentService.save(letterCommitmentDTO);
        return ResponseEntity.created(new URI("/api/letter/commitments/" + letterCommitmentDTO.getId()))
            .headers(HeaderUtil.createEntityCreationAlert(applicationName, true, ENTITY_NAME, letterCommitmentDTO.getId().toString()))
            .body(letterCommitmentDTO);
    }

    /**
     * 方法名称：getLetterCommitmentByEmployee
     * 描述：根据员工获取承诺书
     *
     * @return {@link ResponseEntity}，状态为 {@code 200 (OK)}，响应体为承诺书DTO；
     * 如果承诺书不存在，则状态为 {@code 404 (Not Found)}
     * @since 1.0
     */
    @GetMapping("/employee")
    public ResponseEntity<Boolean> getLetterCommitmentByEmployee() {
        LOG.debug("REST request to get LetterCommitment");
        Optional<LetterCommitmentDTO> letterCommitmentDTO = letterCommitmentService.findLetterCommitmentByEmployee();
        return ResponseEntity.ok(letterCommitmentDTO.isPresent());
    }

}

package com.whiskerguard.organization.web.rest;

import com.whiskerguard.organization.repository.TagRepository;
import com.whiskerguard.organization.service.TagService;
import com.whiskerguard.organization.service.dto.TagDTO;
import com.whiskerguard.organization.web.rest.errors.BadRequestAlertException;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.Objects;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;
import tech.jhipster.web.util.HeaderUtil;
import tech.jhipster.web.util.PaginationUtil;
import tech.jhipster.web.util.ResponseUtil;

/**
 * 标签管理的REST控制器
 */
@RestController
@RequestMapping("/api/tags")
public class TagResource {

    private static final Logger LOG = LoggerFactory.getLogger(TagResource.class);

    private static final String ENTITY_NAME = "whiskerguardOrgServiceTag";

    @Value("${jhipster.clientApp.name}")
    private String applicationName;

    private final TagService tagService;

    private final TagRepository tagRepository;

    public TagResource(TagService tagService, TagRepository tagRepository) {
        this.tagService = tagService;
        this.tagRepository = tagRepository;
    }

    /**
     * {@code POST  /tags} : 创建标签
     *
     * @param tagDTO 要创建的标签DTO
     * @return {@link ResponseEntity}，状态为 {@code 201 (Created)}，响应体为新创建的标签DTO；
     *         如果标签已经有ID，则状态为 {@code 400 (Bad Request)}
     * @throws URISyntaxException 如果Location URI语法不正确
     */
    @PostMapping("/create")
    public ResponseEntity<TagDTO> createTag(@Valid @RequestBody TagDTO tagDTO) throws URISyntaxException {
        LOG.debug("REST request to save Tag : {}", tagDTO);
        if (tagDTO.getId() != null) {
            throw new BadRequestAlertException("新的标签不能有ID", ENTITY_NAME, "idexists");
        }
        tagDTO = tagService.save(tagDTO);
        return ResponseEntity.created(new URI("/api/tags/" + tagDTO.getId()))
            .headers(HeaderUtil.createEntityCreationAlert(applicationName, true, ENTITY_NAME, tagDTO.getId().toString()))
            .body(tagDTO);
    }

    /**
     * {@code PUT  /tags/:id} : 更新已存在的标签
     *
     * @param id 要保存的标签DTO的ID
     * @param tagDTO 要更新的标签DTO
     * @return {@link ResponseEntity}，状态为 {@code 200 (OK)}，响应体为更新后的标签DTO；
     *         如果标签DTO无效，则状态为 {@code 400 (Bad Request)}；
     *         如果标签DTO无法更新，则状态为 {@code 500 (Internal Server Error)}
     * @throws URISyntaxException 如果Location URI语法不正确
     */
    @PutMapping("/update/{id}")
    public ResponseEntity<TagDTO> updateTag(@PathVariable(value = "id", required = false) final Long id, @Valid @RequestBody TagDTO tagDTO)
        throws URISyntaxException {
        LOG.debug("REST request to update Tag : {}, {}", id, tagDTO);
        if (tagDTO.getId() == null) {
            throw new BadRequestAlertException("无效的ID", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, tagDTO.getId())) {
            throw new BadRequestAlertException("无效的ID", ENTITY_NAME, "idinvalid");
        }

        if (!tagRepository.existsById(id)) {
            throw new BadRequestAlertException("实体未找到", ENTITY_NAME, "idnotfound");
        }

        tagDTO = tagService.update(tagDTO);
        return ResponseEntity.ok()
            .headers(HeaderUtil.createEntityUpdateAlert(applicationName, true, ENTITY_NAME, tagDTO.getId().toString()))
            .body(tagDTO);
    }

    /**
     * {@code PATCH  /tags/:id} : 部分更新已存在的标签，null字段将被忽略
     *
     * @param id 要保存的标签DTO的ID
     * @param tagDTO 要更新的标签DTO
     * @return {@link ResponseEntity}，状态为 {@code 200 (OK)}，响应体为更新后的标签DTO；
     *         如果标签DTO无效，则状态为 {@code 400 (Bad Request)}；
     *         如果找不到标签DTO，则状态为 {@code 404 (Not Found)}；
     *         如果标签DTO无法更新，则状态为 {@code 500 (Internal Server Error)}
     * @throws URISyntaxException 如果Location URI语法不正确
     */
    @PatchMapping(value = "/partial/{id}", consumes = { "application/json", "application/merge-patch+json" })
    public ResponseEntity<TagDTO> partialUpdateTag(
        @PathVariable(value = "id", required = false) final Long id,
        @NotNull @Valid @RequestBody TagDTO tagDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to partial update Tag partially : {}, {}", id, tagDTO);
        if (tagDTO.getId() == null) {
            throw new BadRequestAlertException("无效的ID", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, tagDTO.getId())) {
            throw new BadRequestAlertException("无效的ID", ENTITY_NAME, "idinvalid");
        }

        if (!tagRepository.existsById(id)) {
            throw new BadRequestAlertException("实体未找到", ENTITY_NAME, "idnotfound");
        }

        Optional<TagDTO> result = tagService.partialUpdate(tagDTO);

        return ResponseUtil.wrapOrNotFound(
            result,
            HeaderUtil.createEntityUpdateAlert(applicationName, true, ENTITY_NAME, tagDTO.getId().toString())
        );
    }

    /**
     * {@code GET  /tags} : 获取所有标签
     *
     * @param pageable 分页信息
     * @param eagerload 是否急切加载关联实体的标志（适用于多对多关系）
     * @return {@link ResponseEntity}，状态为 {@code 200 (OK)}，响应体为标签分页数据
     */
    @GetMapping("/list")
    public ResponseEntity<Page<TagDTO>> getAllTags(
        @org.springdoc.core.annotations.ParameterObject Pageable pageable,
        @RequestParam(name = "eagerload", required = false, defaultValue = "true") boolean eagerload
    ) {
        LOG.debug("REST request to get a page of Tags");
        Page<TagDTO> page;
        if (eagerload) {
            page = tagService.findAllWithEagerRelationships(pageable);
        } else {
            page = tagService.findAll(pageable);
        }
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(ServletUriComponentsBuilder.fromCurrentRequest(), page);
        return ResponseEntity.ok().headers(headers).body(page);
    }

    /**
     * {@code GET  /tags/:id} : 获取指定ID的标签
     *
     * @param id 要获取的标签DTO的ID
     * @return {@link ResponseEntity}，状态为 {@code 200 (OK)}，响应体为标签DTO；
     *         如果找不到标签，则状态为 {@code 404 (Not Found)}
     */
    @GetMapping("/detail/{id}")
    public ResponseEntity<TagDTO> getTag(@PathVariable("id") Long id) {
        LOG.debug("REST request to get Tag : {}", id);
        Optional<TagDTO> tagDTO = tagService.findOne(id);
        return ResponseUtil.wrapOrNotFound(tagDTO);
    }

    /**
     * {@code DELETE  /tags/:id} : 删除指定ID的标签
     *
     * @param id 要删除的标签DTO的ID
     * @return {@link ResponseEntity}，状态为 {@code 204 (NO_CONTENT)}
     */
    @DeleteMapping("/delete/{id}")
    public ResponseEntity<Void> deleteTag(@PathVariable("id") Long id) {
        LOG.debug("REST request to delete Tag : {}", id);
        tagService.delete(id);
        return ResponseEntity.noContent()
            .headers(HeaderUtil.createEntityDeletionAlert(applicationName, true, ENTITY_NAME, id.toString()))
            .build();
    }
}

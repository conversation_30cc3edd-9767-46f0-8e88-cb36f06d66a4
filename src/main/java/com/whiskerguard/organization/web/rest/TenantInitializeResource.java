package com.whiskerguard.organization.web.rest;

import com.whiskerguard.organization.repository.TenantInitializeRepository;
import com.whiskerguard.organization.service.TenantInitializeService;
import com.whiskerguard.organization.service.dto.TenantInitializeDTO;
import com.whiskerguard.organization.web.rest.errors.BadRequestAlertException;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;
import tech.jhipster.web.util.HeaderUtil;
import tech.jhipster.web.util.PaginationUtil;
import tech.jhipster.web.util.ResponseUtil;

import java.net.URI;
import java.net.URISyntaxException;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * 描述：租户基础信息初始化表的REST控制器
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/20
 */
@RestController
@RequestMapping("/api/tenant/initializes")
public class TenantInitializeResource {

    private static final Logger LOG = LoggerFactory.getLogger(TenantInitializeResource.class);

    private static final String ENTITY_NAME = "whiskerguardOrgServiceTenantInitialize";

    @Value("${jhipster.clientApp.name}")
    private String applicationName;

    private final TenantInitializeService tenantInitializeService;

    private final TenantInitializeRepository tenantInitializeRepository;

    public TenantInitializeResource(
        TenantInitializeService tenantInitializeService,
        TenantInitializeRepository tenantInitializeRepository
    ) {
        this.tenantInitializeService = tenantInitializeService;
        this.tenantInitializeRepository = tenantInitializeRepository;
    }

    /**
     * 方法名称：createTenantInitialize
     * 描述：创建新的租户基础信息初始化记录
     *
     * @param tenantInitializeDTO 要创建的租户基础信息初始化记录DTO
     * @return {@link ResponseEntity}，状态为 {@code 201 (Created)}，响应体为新创建的租户基础信息初始化记录DTO；
     * 如果租户基础信息初始化记录已经有ID，则状态为 {@code 400 (Bad Request)}
     * @throws URISyntaxException 如果 URI 语法不正确。
     * @since 1.0
     */
    @PostMapping("")
    public ResponseEntity<TenantInitializeDTO> createTenantInitialize(@Valid @RequestBody TenantInitializeDTO tenantInitializeDTO)
        throws URISyntaxException {
        LOG.debug("REST request to save TenantInitialize : {}", tenantInitializeDTO);
        if (tenantInitializeDTO.getId() != null) {
            throw new BadRequestAlertException("新的租户初始化不能有ID", ENTITY_NAME, "idexists");
        }
        tenantInitializeDTO = tenantInitializeService.save(tenantInitializeDTO);
        return ResponseEntity.created(new URI("/api/tenant-initializes/" + tenantInitializeDTO.getId()))
            .headers(HeaderUtil.createEntityCreationAlert(applicationName, true, ENTITY_NAME, tenantInitializeDTO.getId().toString()))
            .body(tenantInitializeDTO);
    }

    /**
     * 方法名称：partialUpdateTenantInitialize
     * 描述：部分更新已存在的租户基础信息初始化记录
     *
     * @param tenantInitializeDTO 要更新的租户基础信息初始化记录DTO
     * @return {@link ResponseEntity}，状态为 {@code 200 (OK)}，响应体为更新后的租户基础信息初始化记录DTO；
     * 如果租户基础信息初始化记录DTO无效，则状态为 {@code 400 (Bad Request)}；
     * 如果租户基础信息初始化记录DTO无法更新，则状态为 {@code 500 (Internal Server Error)}
     * @since 1.0
     */
    @PatchMapping(value = "/{id}", consumes = {"application/json", "application/merge-patch+json"})
    public ResponseEntity<TenantInitializeDTO> partialUpdateTenantInitialize(
        @PathVariable(value = "id", required = false) final Long id,
        @NotNull @RequestBody TenantInitializeDTO tenantInitializeDTO
    ) {
        LOG.debug("REST request to partial update TenantInitialize partially : {}, {}", id, tenantInitializeDTO);
        if (tenantInitializeDTO.getId() == null) {
            throw new BadRequestAlertException("无效的ID", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, tenantInitializeDTO.getId())) {
            throw new BadRequestAlertException("无效的ID", ENTITY_NAME, "idinvalid");
        }

        if (!tenantInitializeRepository.existsById(id)) {
            throw new BadRequestAlertException("实体未找到", ENTITY_NAME, "idnotfound");
        }

        Optional<TenantInitializeDTO> result = tenantInitializeService.partialUpdate(tenantInitializeDTO);

        return ResponseUtil.wrapOrNotFound(
            result,
            HeaderUtil.createEntityUpdateAlert(applicationName, true, ENTITY_NAME, tenantInitializeDTO.getId().toString())
        );
    }

    /**
     * 方法名称：getAllTenantInitializes
     * 描述：获取所有租户基础信息初始化记录
     *
     * @param pageable 分页信息
     * @return {@link ResponseEntity}，状态为 {@code 200 (OK)}，响应体为租户基础信息初始化记录DTO的分页列表
     * @since 1.0
     */
    @GetMapping("")
    public ResponseEntity<List<TenantInitializeDTO>> getAllTenantInitializes(
        @org.springdoc.core.annotations.ParameterObject Pageable pageable
    ) {
        LOG.debug("REST request to get a page of TenantInitializes");
        Page<TenantInitializeDTO> page = tenantInitializeService.findAll(pageable);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(ServletUriComponentsBuilder.fromCurrentRequest(), page);
        return ResponseEntity.ok().headers(headers).body(page.getContent());
    }

    /**
     * 方法名称：deleteTenantInitialize
     * 描述：根据ID删除租户基础信息初始化记录
     *
     * @param id 租户基础信息初始化记录的ID
     * @return {@link ResponseEntity}，状态为 {@code 204 (NO_CONTENT)}
     * @since 1.0
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteTenantInitialize(@PathVariable("id") Long id) {
        LOG.debug("REST request to delete TenantInitialize : {}", id);
        tenantInitializeService.delete(id);
        return ResponseEntity.noContent()
            .headers(HeaderUtil.createEntityDeletionAlert(applicationName, true, ENTITY_NAME, id.toString()))
            .build();
    }
}

package com.whiskerguard.organization.web.rest;

import com.whiskerguard.organization.repository.EmployeeOrgRepository;
import com.whiskerguard.organization.service.EmployeeOrgService;
import com.whiskerguard.organization.service.dto.EmployeeOrgDTO;
import com.whiskerguard.organization.web.rest.errors.BadRequestAlertException;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;
import tech.jhipster.web.util.HeaderUtil;
import tech.jhipster.web.util.PaginationUtil;
import tech.jhipster.web.util.ResponseUtil;

import java.net.URI;
import java.net.URISyntaxException;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * REST controller for managing {@link com.whiskerguard.organization.domain.EmployeeOrg}.
 * 员工组织关系的REST控制器
 * 提供员工与组织单元关联关系的CRUD操作API接口
 */
@RestController
@RequestMapping("/api/employee/orgs")
public class EmployeeOrgResource {

    private static final Logger LOG = LoggerFactory.getLogger(EmployeeOrgResource.class);

    private static final String ENTITY_NAME = "whiskerguardOrgServiceEmployeeOrg";

    @Value("${jhipster.clientApp.name}")
    private String applicationName;

    private final EmployeeOrgService employeeOrgService;

    private final EmployeeOrgRepository employeeOrgRepository;

    public EmployeeOrgResource(EmployeeOrgService employeeOrgService, EmployeeOrgRepository employeeOrgRepository) {
        this.employeeOrgService = employeeOrgService;
        this.employeeOrgRepository = employeeOrgRepository;
    }

    /**
     * {@code POST  /employee-orgs} : Create a new employeeOrg.
     * 创建新的员工组织关系
     *
     * @param employeeOrgDTO the employeeOrgDTO to create. 要创建的员工组织关系DTO
     * @return the {@link ResponseEntity} with status {@code 201 (Created)} and with body the new employeeOrgDTO, or with status {@code 400 (Bad Request)} if the employeeOrg has already an ID.
     * 返回状态码为201的响应和新创建的员工组织关系DTO，如果员工组织关系已有ID则返回400错误
     * @throws URISyntaxException if the Location URI syntax is incorrect. 如果位置URI语法不正确
     */
    @PostMapping("")
    public ResponseEntity<EmployeeOrgDTO> createEmployeeOrg(@Valid @RequestBody EmployeeOrgDTO employeeOrgDTO) throws URISyntaxException {
        LOG.debug("REST request to save EmployeeOrg : {}", employeeOrgDTO);
        if (employeeOrgDTO.getId() != null) {
            throw new BadRequestAlertException("新的员工组织关系不能有ID", ENTITY_NAME, "idexists");
        }
        employeeOrgDTO = employeeOrgService.save(employeeOrgDTO);
        return ResponseEntity.created(new URI("/api/employee/orgs/" + employeeOrgDTO.getId()))
            .headers(HeaderUtil.createEntityCreationAlert(applicationName, true, ENTITY_NAME, employeeOrgDTO.getId().toString()))
            .body(employeeOrgDTO);
    }

    /**
     * {@code PUT  /employee-orgs/:id} : Updates an existing employeeOrg.
     * 更新现有的员工组织关系
     *
     * @param id             the id of the employeeOrgDTO to save. 要保存的员工组织关系DTO的ID
     * @param employeeOrgDTO the employeeOrgDTO to update. 要更新的员工组织关系DTO
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated employeeOrgDTO,
     * or with status {@code 400 (Bad Request)} if the employeeOrgDTO is not valid,
     * or with status {@code 500 (Internal Server Error)} if the employeeOrgDTO couldn't be updated.
     * 返回状态码为200的响应和更新后的员工组织关系DTO，如果员工组织关系DTO无效则返回400错误，如果无法更新则返回500错误
     * @throws URISyntaxException if the Location URI syntax is incorrect. 如果位置URI语法不正确
     */
    @PutMapping("/{id}")
    public ResponseEntity<EmployeeOrgDTO> updateEmployeeOrg(
        @PathVariable(value = "id", required = false) final Long id,
        @Valid @RequestBody EmployeeOrgDTO employeeOrgDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to update EmployeeOrg : {}, {}", id, employeeOrgDTO);
        if (employeeOrgDTO.getId() == null) {
            throw new BadRequestAlertException("无效的ID", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, employeeOrgDTO.getId())) {
            throw new BadRequestAlertException("无效的ID", ENTITY_NAME, "idinvalid");
        }

        if (!employeeOrgRepository.existsById(id)) {
            throw new BadRequestAlertException("实体未找到", ENTITY_NAME, "idnotfound");
        }

        employeeOrgDTO = employeeOrgService.update(employeeOrgDTO);
        return ResponseEntity.ok()
            .headers(HeaderUtil.createEntityUpdateAlert(applicationName, true, ENTITY_NAME, employeeOrgDTO.getId().toString()))
            .body(employeeOrgDTO);
    }

    /**
     * {@code PATCH  /employee-orgs/:id} : Partial updates given fields of an existing employeeOrg, field will ignore if it is null
     * 部分更新现有员工组织关系的指定字段，如果字段为null则忽略
     *
     * @param id             the id of the employeeOrgDTO to save. 要保存的员工组织关系DTO的ID
     * @param employeeOrgDTO the employeeOrgDTO to update. 要更新的员工组织关系DTO
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated employeeOrgDTO,
     * or with status {@code 400 (Bad Request)} if the employeeOrgDTO is not valid,
     * or with status {@code 404 (Not Found)} if the employeeOrgDTO is not found,
     * or with status {@code 500 (Internal Server Error)} if the employeeOrgDTO couldn't be updated.
     * 返回状态码为200的响应和更新后的员工组织关系DTO，如果员工组织关系DTO无效则返回400错误，如果找不到员工组织关系则返回404错误，如果无法更新则返回500错误
     * @throws URISyntaxException if the Location URI syntax is incorrect. 如果位置URI语法不正确
     */
    @PatchMapping(value = "/{id}", consumes = {"application/json", "application/merge-patch+json"})
    public ResponseEntity<EmployeeOrgDTO> partialUpdateEmployeeOrg(
        @PathVariable(value = "id", required = false) final Long id,
        @NotNull @Valid @RequestBody EmployeeOrgDTO employeeOrgDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to partial update EmployeeOrg partially : {}, {}", id, employeeOrgDTO);
        if (employeeOrgDTO.getId() == null) {
            throw new BadRequestAlertException("无效的ID", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, employeeOrgDTO.getId())) {
            throw new BadRequestAlertException("无效的ID", ENTITY_NAME, "idinvalid");
        }

        if (!employeeOrgRepository.existsById(id)) {
            throw new BadRequestAlertException("实体未找到", ENTITY_NAME, "idnotfound");
        }

        Optional<EmployeeOrgDTO> result = employeeOrgService.partialUpdate(employeeOrgDTO);

        return ResponseUtil.wrapOrNotFound(
            result,
            HeaderUtil.createEntityUpdateAlert(applicationName, true, ENTITY_NAME, employeeOrgDTO.getId().toString())
        );
    }

    /**
     * {@code GET  /employee-orgs} : get all the employeeOrgs.
     * 获取所有员工组织关系
     *
     * @param pageable the pagination information. 分页信息
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and the page of employeeOrgs in body.
     * 返回状态码为200的响应和员工组织关系分页数据
     */
    @GetMapping("")
    public ResponseEntity<Page<EmployeeOrgDTO>> getAllEmployeeOrgs(@org.springdoc.core.annotations.ParameterObject Pageable pageable) {
        LOG.debug("REST request to get a page of EmployeeOrgs");
        Page<EmployeeOrgDTO> page = employeeOrgService.findAll(pageable);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(ServletUriComponentsBuilder.fromCurrentRequest(), page);
        return ResponseEntity.ok().headers(headers).body(page);
    }

    /**
     * {@code GET  /employee-orgs/:id} : get the "id" employeeOrg.
     * 获取指定ID的员工组织关系
     *
     * @param id the id of the employeeOrgDTO to retrieve. 要检索的员工组织关系DTO的ID
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the employeeOrgDTO, or with status {@code 404 (Not Found)}.
     * 返回状态码为200的响应和员工组织关系DTO，如果找不到则返回404错误
     */
    @GetMapping("/{id}")
    public ResponseEntity<EmployeeOrgDTO> getEmployeeOrg(@PathVariable("id") Long id) {
        LOG.debug("REST request to get EmployeeOrg : {}", id);
        Optional<EmployeeOrgDTO> employeeOrgDTO = employeeOrgService.findOne(id);
        return ResponseUtil.wrapOrNotFound(employeeOrgDTO);
    }

    /**
     * {@code DELETE  /employee-orgs/:id} : delete the "id" employeeOrg.
     * 删除指定ID的员工组织关系
     *
     * @param id the id of the employeeOrgDTO to delete. 要删除的员工组织关系DTO的ID
     * @return the {@link ResponseEntity} with status {@code 204 (NO_CONTENT)}.
     * 返回状态码为204的响应（无内容）
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteEmployeeOrg(@PathVariable("id") Long id) {
        LOG.debug("REST request to delete EmployeeOrg : {}", id);
        employeeOrgService.delete(id);
        return ResponseEntity.noContent()
            .headers(HeaderUtil.createEntityDeletionAlert(applicationName, true, ENTITY_NAME, id.toString()))
            .build();
    }

    /**
     * 方法名称：getEmployeeOrgByEmployee
     * 描述：获取指定员工的所有组织关系。
     *
     * @param employeeIds the employee id. 员工ID
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and the list of employeeOrgs in body.
     * 返回状态码为200的响应和员工组织关系列表
     * @since 1.0
     */
    @GetMapping("/employees")
    public ResponseEntity<List<EmployeeOrgDTO>> getEmployeeOrgByEmployee(@RequestParam List<Long> employeeIds) {
        LOG.debug("REST request to get EmployeeOrgs by Employee : {}", employeeIds);
        return ResponseEntity.ok(employeeOrgService.findByEmployees(employeeIds));
    }
}

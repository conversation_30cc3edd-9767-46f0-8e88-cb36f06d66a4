package com.whiskerguard.organization.web.rest.errors;

import com.fasterxml.jackson.databind.ObjectMapper;
import feign.FeignException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.ConstraintViolationException;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.dao.ConcurrencyFailureException;
import org.springframework.dao.DataAccessException;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.ResponseEntity;
import org.springframework.lang.Nullable;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.validation.FieldError;
import org.springframework.web.ErrorResponse;
import org.springframework.web.ErrorResponseException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.context.request.WebRequest;
import org.springframework.web.servlet.mvc.method.annotation.ResponseEntityExceptionHandler;
import tech.jhipster.web.rest.errors.ProblemDetailWithCause;
import tech.jhipster.web.rest.errors.ProblemDetailWithCause.ProblemDetailWithCauseBuilder;
import tech.jhipster.web.util.HeaderUtil;

import java.net.URI;
import java.util.*;
import java.util.stream.Collectors;

import static org.springframework.core.annotation.AnnotatedElementUtils.findMergedAnnotation;

/**
 * 控制器通知，用于将服务器端异常转换为客户端友好的JSON结构
 * 错误响应遵循RFC7807 - HTTP API的问题详情规范
 */
@ControllerAdvice
public class ExceptionTranslator extends ResponseEntityExceptionHandler {

    private static final String FIELD_ERRORS_KEY = "fieldErrors";
    private static final String MESSAGE_KEY = "message";
    private static final String PATH_KEY = "path";
    private static final boolean CASUAL_CHAIN_ENABLED = false;

    private static final Logger LOG = LoggerFactory.getLogger(ExceptionTranslator.class);

    @Value("${jhipster.clientApp.name}")
    private String applicationName;

    /**
     * 处理FeignException异常
     * 直接返回远程服务的错误响应，不进行包装
     */
    @ExceptionHandler(FeignException.class)
    public ResponseEntity<Object> handleFeignException(FeignException ex, NativeWebRequest request) {
        LOG.debug("Handling FeignException:", ex);

        try {
            // 尝试解析远程服务返回的错误响应
            String responseBody = ex.contentUTF8();
            if (StringUtils.isNotBlank(responseBody)) {
                ObjectMapper objectMapper = new ObjectMapper();
                // 尝试将响应体解析为Map，然后手动构建ProblemDetailWithCause
                @SuppressWarnings("unchecked")
                Map<String, Object> responseMap = objectMapper.readValue(responseBody, Map.class);

                // 手动构建ProblemDetailWithCause对象
                ProblemDetailWithCause remoteProblem = ProblemDetailWithCauseBuilder.instance()
                    .withStatus(ex.status())
                    .build();

                // 设置基本属性
                if (responseMap.containsKey("type")) {
                    String typeStr = responseMap.get("type").toString();
                    try {
                        remoteProblem.setType(URI.create(typeStr));
                    } catch (Exception e) {
                        LOG.warn("远程响应类型中的URI无效: {}", typeStr);
                    }
                }

                if (responseMap.containsKey("title")) {
                    String originalTitle = responseMap.get("title").toString();
                    // 针对400、404和500状态码，修改title为通用中文
                    if (ex.status() == 400) {
                        remoteProblem.setTitle("请求参数错误");
                    } else if (ex.status() == 404) {
                        remoteProblem.setTitle("系统正在初始化，请稍后再试");
                    } else if (ex.status() == 500) {
                        remoteProblem.setTitle("系统繁忙，请稍后再试");
                    } else {
                        remoteProblem.setTitle(originalTitle);
                    }
                } else {
                    // 如果远程响应没有title，根据状态码设置通用中文title
                    if (ex.status() == 400) {
                        remoteProblem.setTitle("请求参数错误");
                    } else if (ex.status() == 404) {
                        remoteProblem.setTitle("系统正在初始化，请稍后再试");
                    } else if (ex.status() == 500) {
                        remoteProblem.setTitle("系统繁忙，请稍后再试");
                    }
                }

                if (responseMap.containsKey("detail")) {
                    remoteProblem.setDetail(responseMap.get("detail").toString());
                }

                if (responseMap.containsKey("instance")) {
                    String instanceStr = responseMap.get("instance").toString();
                    try {
                        remoteProblem.setInstance(URI.create(instanceStr));
                    } catch (Exception e) {
                        LOG.warn("Invalid URI in remote response instance: {}", instanceStr);
                    }
                }

                if (responseMap.containsKey("status")) {
                    Object statusObj = responseMap.get("status");
                    if (statusObj instanceof Number) {
                        remoteProblem.setStatus(((Number) statusObj).intValue());
                    }
                }

                // 设置properties
                Map<String, Object> properties = new HashMap<>();
                for (Map.Entry<String, Object> entry : responseMap.entrySet()) {
                    String key = entry.getKey();
                    // 跳过标准的Problem Details字段
                    if (!"type".equals(key) && !"title".equals(key) && !"detail".equals(key)
                        && !"instance".equals(key) && !"status".equals(key)) {
                        properties.put(key, entry.getValue());
                    }
                }
                if (!properties.isEmpty()) {
                    remoteProblem.setProperties(properties);
                }

                // 直接返回远程服务的错误响应，保持原始状态码
                HttpStatus status = HttpStatus.valueOf(ex.status());
                return new ResponseEntity<>(remoteProblem, status);
            }
        } catch (Exception parseException) {
            LOG.warn("Failed to parse FeignException response body, falling back to default handling", parseException);
        }

        // 如果解析失败，针对特定状态码提供特殊处理
        if (ex.status() == 400) {
            ProblemDetailWithCause badRequestProblem = ProblemDetailWithCauseBuilder.instance()
                .withStatus(HttpStatus.BAD_REQUEST.value())
                .withTitle("请求参数错误")
                .withDetail(getOriginalExceptionDetail(ex))
                .withProperty("message", "error.http.400")
                .withProperty("path", getPathValue(request))
                .build();
            return new ResponseEntity<>(badRequestProblem, HttpStatus.BAD_REQUEST);
        } else if (ex.status() == 404) {
            ProblemDetailWithCause notFoundProblem = ProblemDetailWithCauseBuilder.instance()
                .withStatus(HttpStatus.NOT_FOUND.value())
                .withTitle("系统正在初始化，请稍后再试")
                .withDetail(getOriginalExceptionDetail(ex))
                .withProperty("message", "error.http.404")
                .withProperty("path", getPathValue(request))
                .build();
            return new ResponseEntity<>(notFoundProblem, HttpStatus.NOT_FOUND);
        } else if (ex.status() == 500) {
            ProblemDetailWithCause internalServerErrorProblem = ProblemDetailWithCauseBuilder.instance()
                .withStatus(HttpStatus.INTERNAL_SERVER_ERROR.value())
                .withTitle("系统繁忙，请稍后再试")
                .withDetail(getOriginalExceptionDetail(ex))
                .withProperty("message", "error.http.500")
                .withProperty("path", getPathValue(request))
                .build();
            return new ResponseEntity<>(internalServerErrorProblem, HttpStatus.INTERNAL_SERVER_ERROR);
        } else if (ex.status() == 502) {
            ProblemDetailWithCause badGatewayProblem = ProblemDetailWithCauseBuilder.instance()
                .withStatus(HttpStatus.BAD_GATEWAY.value())
                .withTitle("服务繁忙，请稍后再试")
                .withDetail(getOriginalExceptionDetail(ex))
                .withProperty("message", "error.http.502")
                .withProperty("path", getPathValue(request))
                .build();
            return new ResponseEntity<>(badGatewayProblem, HttpStatus.BAD_GATEWAY);
        }

        // 其他情况使用默认的异常处理逻辑
        ProblemDetailWithCause pdCause = wrapAndCustomizeProblem(ex, request);
        return handleExceptionInternal(ex, pdCause, buildHeaders(ex), HttpStatusCode.valueOf(pdCause.getStatus()), request);
    }

    @ExceptionHandler(Exception.class)
    public ResponseEntity<Object> handleAnyException(Throwable ex, NativeWebRequest request) {
        LOG.debug("Converting Exception to Problem Details:", ex);
        ProblemDetailWithCause pdCause = wrapAndCustomizeProblem(ex, request);
        return handleExceptionInternal((Exception) ex, pdCause, buildHeaders(ex), HttpStatusCode.valueOf(pdCause.getStatus()), request);
    }

    /**
     * 专门处理Bean Validation约束违反异常
     *
     * @param ex      ConstraintViolationException异常
     * @param request Web请求
     * @return 错误响应
     */
    @ExceptionHandler(ConstraintViolationException.class)
    public ResponseEntity<Object> handleConstraintViolationException(ConstraintViolationException ex, NativeWebRequest request) {
        LOG.debug("Converting ConstraintViolationException to Problem Details:", ex);
        ProblemDetailWithCause pdCause = wrapAndCustomizeProblem(ex, request);

        // 添加约束违反的详细信息
        Map<String, Object> properties = pdCause.getProperties();
        if (properties == null) {
            properties = new HashMap<>();
        }

        // 收集所有约束违反信息
        List<ConstraintViolationVM> constraintViolations = ex.getConstraintViolations()
            .stream()
            .map(violation -> new ConstraintViolationVM(
                violation.getPropertyPath().toString(),
                violation.getMessage(),
                violation.getInvalidValue() != null ? violation.getInvalidValue().toString() : null
            ))
            .collect(Collectors.toList());

        properties.put("constraintViolations", constraintViolations);
        pdCause.setProperties(properties);

        return handleExceptionInternal(ex, pdCause, buildHeaders(ex), HttpStatusCode.valueOf(pdCause.getStatus()), request);
    }

    @Nullable
    @Override
    protected ResponseEntity<Object> handleExceptionInternal(
        Exception ex,
        @Nullable Object body,
        HttpHeaders headers,
        HttpStatusCode statusCode,
        WebRequest request
    ) {
        body = body == null ? wrapAndCustomizeProblem(ex, (NativeWebRequest) request) : body;
        return super.handleExceptionInternal(ex, body, headers, statusCode, request);
    }

    protected ProblemDetailWithCause wrapAndCustomizeProblem(Throwable ex, NativeWebRequest request) {
        return customizeProblem(getProblemDetailWithCause(ex), ex, request);
    }

    private ProblemDetailWithCause getProblemDetailWithCause(Throwable ex) {
        if (
            ex instanceof ErrorResponseException exp && exp.getBody() instanceof ProblemDetailWithCause problemDetailWithCause
        ) return problemDetailWithCause;
        return ProblemDetailWithCauseBuilder.instance().withStatus(toStatus(ex).value()).build();
    }

    protected ProblemDetailWithCause customizeProblem(ProblemDetailWithCause problem, Throwable err, NativeWebRequest request) {
        if (problem.getStatus() <= 0) problem.setStatus(toStatus(err));

        if (problem.getType().equals(URI.create("about:blank")))
            problem.setType(getMappedType(err));

        // 统一处理所有异常的title，优先使用异常消息
        String customTitle = getExceptionMessageAsTitle(err);
        if (customTitle != null) {
            problem.setTitle(customTitle);
        } else {
            // 如果没有合适的异常消息，使用默认的title提取逻辑
            String title = extractTitle(err, problem.getStatus());
            String problemTitle = problem.getTitle();
            if (problemTitle == null || !problemTitle.equals(title)) {
                problem.setTitle(title);
            }
        }

        if (problem.getDetail() == null) {
            // 为detail字段提供详细信息，区别于title的简洁描述
            String detailedInfo = getDetailedErrorInfo(err, problem.getStatus());
            problem.setDetail(detailedInfo);
        }

        Map<String, Object> problemProperties = problem.getProperties();
        if (problemProperties == null || !problemProperties.containsKey(MESSAGE_KEY)) problem.setProperty(
            MESSAGE_KEY,
            getMappedMessageKey(err) != null ? getMappedMessageKey(err) : "error.http." + problem.getStatus()
        );

        if (problemProperties == null || !problemProperties.containsKey(PATH_KEY))
            problem.setProperty(PATH_KEY, getPathValue(request));

        if (
            (err instanceof MethodArgumentNotValidException fieldException) &&
                (problemProperties == null || !problemProperties.containsKey(FIELD_ERRORS_KEY))
        ) problem.setProperty(FIELD_ERRORS_KEY, getFieldErrors(fieldException));

        problem.setCause(buildCause(err.getCause(), request).orElse(null));

        return problem;
    }

    private String extractTitle(Throwable err, int statusCode) {
        String customizedTitle = getCustomizedTitle(err);
        if (customizedTitle != null) {
            return customizedTitle;
        }

        // 如果没有自定义title，则使用ResponseStatus或HTTP状态码的默认描述
        return extractTitleForResponseStatus(err, statusCode);
    }

    private List<FieldErrorVM> getFieldErrors(MethodArgumentNotValidException ex) {
        return ex
            .getBindingResult()
            .getFieldErrors()
            .stream()
            .map(f ->
                new FieldErrorVM(
                    f.getObjectName().replaceFirst("DTO$", ""),
                    f.getField(),
                    StringUtils.isNotBlank(f.getDefaultMessage()) ? f.getDefaultMessage() : f.getCode()
                )
            )
            .toList();
    }

    private String extractTitleForResponseStatus(Throwable err, int statusCode) {
        ResponseStatus specialStatus = extractResponseStatus(err);
        return specialStatus == null ? HttpStatus.valueOf(statusCode).getReasonPhrase() : specialStatus.reason();
    }

    private String extractURI(NativeWebRequest request) {
        HttpServletRequest nativeRequest = request.getNativeRequest(HttpServletRequest.class);
        return nativeRequest != null ? nativeRequest.getRequestURI() : StringUtils.EMPTY;
    }

    private HttpStatus toStatus(final Throwable throwable) {
        // Let the ErrorResponse take this responsibility
        if (throwable instanceof ErrorResponse err) return HttpStatus.valueOf(err.getBody().getStatus());

        return Optional.ofNullable(getMappedStatus(throwable)).orElse(
            Optional.ofNullable(resolveResponseStatus(throwable)).map(ResponseStatus::value).orElse(HttpStatus.INTERNAL_SERVER_ERROR)
        );
    }

    private ResponseStatus extractResponseStatus(final Throwable throwable) {
        return resolveResponseStatus(throwable);
    }

    private ResponseStatus resolveResponseStatus(final Throwable type) {
        final ResponseStatus candidate = findMergedAnnotation(type.getClass(), ResponseStatus.class);
        return candidate == null && type.getCause() != null ? resolveResponseStatus(type.getCause()) : candidate;
    }

    private URI getMappedType(Throwable err) {
        if (err instanceof MethodArgumentNotValidException) return ErrorConstants.CONSTRAINT_VIOLATION_TYPE;
        return ErrorConstants.DEFAULT_TYPE;
    }

    private String getMappedMessageKey(Throwable err) {
        if (err instanceof MethodArgumentNotValidException) {
            return ErrorConstants.ERR_VALIDATION;
        } else if (err instanceof ConcurrencyFailureException || err.getCause() instanceof ConcurrencyFailureException) {
            return ErrorConstants.ERR_CONCURRENCY_FAILURE;
        }
        return null;
    }

    private String getCustomizedTitle(Throwable err) {
        // 对于特定异常类型，如果没有消息，则返回默认title
        if (err instanceof MethodArgumentNotValidException) {
            return "方法参数无效";
        }
        if (err instanceof BadCredentialsException) {
            return "验证失败";
        }
        if (err instanceof AccessDeniedException) {
            return "访问被拒绝";
        }
        if (err instanceof ConcurrencyFailureException) {
            return "并发失败";
        }
        if (err instanceof DataAccessException) {
            return "数据访问错误";
        }

        return null;
    }

    /**
     * 获取异常消息作为title
     * 统一处理所有异常，优先使用异常的message，如果为空则使用cause的message
     * 特别处理Jakarta Bean Validation异常，提取验证注解中的message
     */
    private String getExceptionMessageAsTitle(Throwable err) {
        if (err instanceof BadRequestAlertException badRequestException) {
            ProblemDetailWithCause innerProblem = badRequestException.getProblemDetailWithCause();
            if (innerProblem != null && innerProblem.getTitle() != null) {
                return replaceTile(innerProblem.getTitle());
            }
        }

        // 特殊处理Jakarta Bean Validation异常，提取验证注解中的message
        String validationMessage = extractValidationMessage(err);
        if (validationMessage != null) {
            return replaceTile(validationMessage);
        }

        // 对于所有其他异常，提取异常消息作为title
        String message = err.getMessage();
        if (StringUtils.isNotBlank(message) && !containsPackageName(message)) {
            return replaceTile(message);
        }

        // 如果异常本身的消息为空或包含包名，尝试获取cause的消息
        Throwable cause = err.getCause();
        if (cause != null) {
            String causeMessage = cause.getMessage();
            if (StringUtils.isNotBlank(causeMessage) && !containsPackageName(causeMessage)) {
                return replaceTile(causeMessage);
            }
        }

        // 如果都没有合适的消息，返回null
        return null;
    }

    /**
     * 提取校验异常中的message信息
     * 解析Bean Validation注解中的自定义message，如@NotNull(message = "字段不能为空")
     *
     * @param err 异常对象
     * @return 校验注解中的message，如果没有则返回null
     */
    private String extractValidationMessage(Throwable err) {
        // 处理MethodArgumentNotValidException（@Valid注解触发的校验异常）
        if (err instanceof MethodArgumentNotValidException methodArgumentException) {
            List<FieldError> fieldErrors = methodArgumentException.getBindingResult().getFieldErrors();
            if (!fieldErrors.isEmpty()) {
                // 获取第一个字段错误的默认消息（通常是注解中的message属性）
                FieldError firstError = fieldErrors.get(0);
                String defaultMessage = firstError.getDefaultMessage();

                // 如果defaultMessage不是以{开头的国际化key，则直接使用
                if (StringUtils.isNotBlank(defaultMessage) && !defaultMessage.startsWith("{")) {
                    // 对于单个字段错误，直接返回消息
                    if (fieldErrors.size() == 1) {
                        return defaultMessage;
                    } else {
                        // 多个字段错误时，显示第一个并提示还有其他错误
                        String fieldName = firstError.getField();
                        return String.format("字段 '%s' %s等%d个字段校验失败",
                            fieldName, defaultMessage, fieldErrors.size());
                    }
                } else {
                    // 对于国际化key，显示字段名和描述
                    String fieldName = firstError.getField();
                    if (fieldErrors.size() == 1) {
                        return String.format("字段 '%s' %s", fieldName,
                            StringUtils.isNotBlank(defaultMessage) ? defaultMessage : "校验失败");
                    } else {
                        return String.format("字段 '%s' %s等%d个字段校验失败",
                            fieldName,
                            StringUtils.isNotBlank(defaultMessage) ? defaultMessage : "校验失败",
                            fieldErrors.size());
                    }
                }
            }
        }

        // 处理ConstraintViolationException（@Validated注解触发的校验异常）
        if (err instanceof ConstraintViolationException constraintException) {
            Set<ConstraintViolation<?>> violations = constraintException.getConstraintViolations();
            if (!violations.isEmpty()) {
                // 获取第一个约束违反的消息
                ConstraintViolation<?> firstViolation = violations.iterator().next();
                String message = firstViolation.getMessage();

                // 如果message不是以{开头的国际化key，则直接使用
                if (StringUtils.isNotBlank(message) && !message.startsWith("{")) {
                    // 对于单个约束违反，直接返回消息
                    if (violations.size() == 1) {
                        return message;
                    } else {
                        // 多个约束违反时，显示第一个并提示还有其他错误
                        String propertyPath = firstViolation.getPropertyPath().toString();
                        return String.format("字段 '%s' %s等%d个字段校验失败",
                            propertyPath, message, violations.size());
                    }
                } else {
                    // 对于国际化key，显示字段名和描述
                    String propertyPath = firstViolation.getPropertyPath().toString();
                    if (violations.size() == 1) {
                        return String.format("字段 '%s' %s", propertyPath,
                            StringUtils.isNotBlank(message) ? message : "校验失败");
                    } else {
                        return String.format("字段 '%s' %s等%d个字段校验失败",
                            propertyPath,
                            StringUtils.isNotBlank(message) ? message : "校验失败",
                            violations.size());
                    }
                }
            }
        }

        return null;
    }

    /**
     * 获取详细的错误信息，用于detail字段
     * 使用原生异常信息，提供比title更详细的错误描述
     */
    private String getDetailedErrorInfo(Throwable err, int statusCode) {
        // 特殊异常类型的详细信息
        if (err instanceof BadRequestAlertException badRequestException) {
            ProblemDetailWithCause innerProblem = badRequestException.getProblemDetailWithCause();
            if (innerProblem != null && innerProblem.getDetail() != null) {
                return innerProblem.getDetail();
            }
            // 使用原生异常信息
            return getOriginalExceptionDetail(err);
        }

        if (err instanceof MethodArgumentNotValidException methodException) {
            // 获取字段验证错误的详细信息
            StringBuilder details = new StringBuilder();
            methodException.getBindingResult().getFieldErrors().forEach(fieldError -> {
                if (!details.isEmpty()) {
                    details.append("; ");
                }
                details.append("Field '").append(fieldError.getField()).append("': ").append(fieldError.getDefaultMessage());
            });
            return !details.isEmpty() ? details.toString() : getOriginalExceptionDetail(err);
        }

        if (err instanceof ConstraintViolationException constraintException) {
            // 获取约束违反的详细信息
            StringBuilder details = new StringBuilder();
            constraintException.getConstraintViolations().forEach(violation -> {
                if (!details.isEmpty()) {
                    details.append("; ");
                }
                details.append("Property '").append(violation.getPropertyPath()).append("': ").append(violation.getMessage());
            });
            return !details.isEmpty() ? details.toString() : getOriginalExceptionDetail(err);
        }

        // 对于其他异常类型，直接使用原生异常信息
        return getOriginalExceptionDetail(err);
    }

    /**
     * 获取原生异常的详细信息
     */
    private String getOriginalExceptionDetail(Throwable err) {
        StringBuilder detail = new StringBuilder();

        // 添加异常类型信息
        detail.append("Exception: ").append(err.getClass().getSimpleName());

        // 添加异常消息
        if (err.getMessage() != null && !err.getMessage().trim().isEmpty()) {
            detail.append(", Message: ").append(err.getMessage());
        }

        // 添加cause信息
        if (err.getCause() != null) {
            detail.append(", Cause: ").append(err.getCause().getClass().getSimpleName());
            if (err.getCause().getMessage() != null && !err.getCause().getMessage().trim().isEmpty()) {
                detail.append(" - ").append(err.getCause().getMessage());
            }
        }

        return detail.toString();
    }

    private HttpStatus getMappedStatus(Throwable err) {
        if (err instanceof AccessDeniedException) return HttpStatus.FORBIDDEN;
        if (err instanceof ConcurrencyFailureException) return HttpStatus.CONFLICT;
        if (err instanceof BadCredentialsException) return HttpStatus.UNAUTHORIZED;
        if (err instanceof ConstraintViolationException) return HttpStatus.BAD_REQUEST;
        return null;
    }

    private URI getPathValue(NativeWebRequest request) {
        if (request == null) return URI.create("about:blank");
        return URI.create(extractURI(request));
    }

    private HttpHeaders buildHeaders(Throwable err) {
        return err instanceof BadRequestAlertException badRequestAlertException
            ? HeaderUtil.createFailureAlert(
            applicationName,
            true,
            badRequestAlertException.getEntityName(),
            badRequestAlertException.getErrorKey(),
            badRequestAlertException.getMessage()
        )
            : null;
    }

    public Optional<ProblemDetailWithCause> buildCause(final Throwable throwable, NativeWebRequest request) {
        if (throwable != null && isCasualChainEnabled()) {
            return Optional.of(customizeProblem(getProblemDetailWithCause(throwable), throwable, request));
        }
        return Optional.empty();
    }

    private boolean isCasualChainEnabled() {
        return CASUAL_CHAIN_ENABLED;
    }

    private boolean containsPackageName(String message) {
        return StringUtils.containsAny(
            message,
            "org.",
            "java.",
            "net.",
            "jakarta.",
            "javax.",
            "com.",
            "io.",
            "de.",
            "com.whiskerguard.auth"
        );
    }

    private String replaceTile(String title) {
        if (title.contains("Bad Request")) {
            return "请求参数错误";
        } else if (title.contains("Internal Server Error")) {
            return "系统繁忙，请稍后再试";
        } else if (title.contains("500")) {
            return "系统繁忙，请稍后再试";
        } else if (title.contains("Not Found")) {
            return "系统正在初始化，请稍后再试";
        } else if (title.contains("404")) {
            return "系统正在初始化，请稍后再试";
        } else {
            if (title.length() > 20) {
                return "系统繁忙，请稍后再试";
            }
            return title;
        }
    }

}

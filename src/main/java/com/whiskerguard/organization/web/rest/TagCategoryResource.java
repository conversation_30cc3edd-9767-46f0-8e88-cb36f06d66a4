package com.whiskerguard.organization.web.rest;

import com.whiskerguard.organization.repository.TagCategoryRepository;
import com.whiskerguard.organization.service.TagCategoryService;
import com.whiskerguard.organization.service.dto.TagCategoryDTO;
import com.whiskerguard.organization.web.rest.errors.BadRequestAlertException;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.Objects;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;
import tech.jhipster.web.util.HeaderUtil;
import tech.jhipster.web.util.PaginationUtil;
import tech.jhipster.web.util.ResponseUtil;

/**
 * 标签分类管理的REST控制器
 */
@RestController
@RequestMapping("/api/tag/categories")
public class TagCategoryResource {

    private static final Logger LOG = LoggerFactory.getLogger(TagCategoryResource.class);

    private static final String ENTITY_NAME = "whiskerguardOrgServiceTagCategory";

    @Value("${jhipster.clientApp.name}")
    private String applicationName;

    private final TagCategoryService tagCategoryService;

    private final TagCategoryRepository tagCategoryRepository;

    public TagCategoryResource(TagCategoryService tagCategoryService, TagCategoryRepository tagCategoryRepository) {
        this.tagCategoryService = tagCategoryService;
        this.tagCategoryRepository = tagCategoryRepository;
    }

    /**
     * {@code POST  /tag-categories} : 创建标签分类
     *
     * @param tagCategoryDTO 要创建的标签分类DTO
     * @return {@link ResponseEntity}，状态为 {@code 201 (Created)}，响应体为新创建的标签分类DTO；
     *         如果标签分类已经有ID，则状态为 {@code 400 (Bad Request)}
     * @throws URISyntaxException 如果Location URI语法不正确
     */
    @PostMapping("/create")
    public ResponseEntity<TagCategoryDTO> createTagCategory(@Valid @RequestBody TagCategoryDTO tagCategoryDTO) throws URISyntaxException {
        LOG.debug("REST request to save TagCategory : {}", tagCategoryDTO);
        if (tagCategoryDTO.getId() != null) {
            throw new BadRequestAlertException("新的标签分类不能有ID", ENTITY_NAME, "idexists");
        }
        tagCategoryDTO = tagCategoryService.save(tagCategoryDTO);
        return ResponseEntity.created(new URI("/api/tag/categories/" + tagCategoryDTO.getId()))
            .headers(HeaderUtil.createEntityCreationAlert(applicationName, true, ENTITY_NAME, tagCategoryDTO.getId().toString()))
            .body(tagCategoryDTO);
    }

    /**
     * {@code PUT  /tag-categories/:id} : 更新已存在的标签分类
     *
     * @param id 要保存的标签分类DTO的ID
     * @param tagCategoryDTO 要更新的标签分类DTO
     * @return {@link ResponseEntity}，状态为 {@code 200 (OK)}，响应体为更新后的标签分类DTO；
     *         如果标签分类DTO无效，则状态为 {@code 400 (Bad Request)}；
     *         如果标签分类DTO无法更新，则状态为 {@code 500 (Internal Server Error)}
     * @throws URISyntaxException 如果Location URI语法不正确
     */
    @PutMapping("/update/{id}")
    public ResponseEntity<TagCategoryDTO> updateTagCategory(
        @PathVariable(value = "id", required = false) final Long id,
        @Valid @RequestBody TagCategoryDTO tagCategoryDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to update TagCategory : {}, {}", id, tagCategoryDTO);
        if (tagCategoryDTO.getId() == null) {
            throw new BadRequestAlertException("无效的ID", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, tagCategoryDTO.getId())) {
            throw new BadRequestAlertException("无效的ID", ENTITY_NAME, "idinvalid");
        }

        if (!tagCategoryRepository.existsById(id)) {
            throw new BadRequestAlertException("实体未找到", ENTITY_NAME, "idnotfound");
        }

        tagCategoryDTO = tagCategoryService.update(tagCategoryDTO);
        return ResponseEntity.ok()
            .headers(HeaderUtil.createEntityUpdateAlert(applicationName, true, ENTITY_NAME, tagCategoryDTO.getId().toString()))
            .body(tagCategoryDTO);
    }

    /**
     * {@code PATCH  /tag-categories/:id} : 部分更新已存在的标签分类，null字段将被忽略
     *
     * @param id 要保存的标签分类DTO的ID
     * @param tagCategoryDTO 要更新的标签分类DTO
     * @return {@link ResponseEntity}，状态为 {@code 200 (OK)}，响应体为更新后的标签分类DTO；
     *         如果标签分类DTO无效，则状态为 {@code 400 (Bad Request)}；
     *         如果找不到标签分类DTO，则状态为 {@code 404 (Not Found)}；
     *         如果标签分类DTO无法更新，则状态为 {@code 500 (Internal Server Error)}
     * @throws URISyntaxException 如果Location URI语法不正确
     */
    @PatchMapping(value = "/partial/{id}", consumes = { "application/json", "application/merge-patch+json" })
    public ResponseEntity<TagCategoryDTO> partialUpdateTagCategory(
        @PathVariable(value = "id", required = false) final Long id,
        @NotNull @Valid @RequestBody TagCategoryDTO tagCategoryDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to partial update TagCategory partially : {}, {}", id, tagCategoryDTO);
        if (tagCategoryDTO.getId() == null) {
            throw new BadRequestAlertException("无效的ID", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, tagCategoryDTO.getId())) {
            throw new BadRequestAlertException("无效的ID", ENTITY_NAME, "idinvalid");
        }

        if (!tagCategoryRepository.existsById(id)) {
            throw new BadRequestAlertException("实体未找到", ENTITY_NAME, "idnotfound");
        }

        Optional<TagCategoryDTO> result = tagCategoryService.partialUpdate(tagCategoryDTO);

        return ResponseUtil.wrapOrNotFound(
            result,
            HeaderUtil.createEntityUpdateAlert(applicationName, true, ENTITY_NAME, tagCategoryDTO.getId().toString())
        );
    }

    /**
     * {@code GET  /tag-categories} : 获取所有标签分类
     *
     * @param pageable 分页信息
     * @return {@link ResponseEntity}，状态为 {@code 200 (OK)}，响应体为标签分类分页数据
     */
    @GetMapping("/list")
    public ResponseEntity<Page<TagCategoryDTO>> getAllTagCategories(@org.springdoc.core.annotations.ParameterObject Pageable pageable) {
        LOG.debug("REST request to get a page of TagCategories");
        Page<TagCategoryDTO> page = tagCategoryService.findAll(pageable);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(ServletUriComponentsBuilder.fromCurrentRequest(), page);
        return ResponseEntity.ok().headers(headers).body(page);
    }

    /**
     * {@code GET  /tag-categories/:id} : 获取指定ID的标签分类
     *
     * @param id 要获取的标签分类DTO的ID
     * @return {@link ResponseEntity}，状态为 {@code 200 (OK)}，响应体为标签分类DTO；
     *         如果找不到标签分类，则状态为 {@code 404 (Not Found)}
     */
    @GetMapping("/detail/{id}")
    public ResponseEntity<TagCategoryDTO> getTagCategory(@PathVariable("id") Long id) {
        LOG.debug("REST request to get TagCategory : {}", id);
        Optional<TagCategoryDTO> tagCategoryDTO = tagCategoryService.findOne(id);
        return ResponseUtil.wrapOrNotFound(tagCategoryDTO);
    }

    /**
     * {@code DELETE  /tag-categories/:id} : 删除指定ID的标签分类
     *
     * @param id 要删除的标签分类DTO的ID
     * @return {@link ResponseEntity}，状态为 {@code 204 (NO_CONTENT)}
     */
    @DeleteMapping("/delete/{id}")
    public ResponseEntity<Void> deleteTagCategory(@PathVariable("id") Long id) {
        LOG.debug("REST request to delete TagCategory : {}", id);
        tagCategoryService.delete(id);
        return ResponseEntity.noContent()
            .headers(HeaderUtil.createEntityDeletionAlert(applicationName, true, ENTITY_NAME, id.toString()))
            .build();
    }
}

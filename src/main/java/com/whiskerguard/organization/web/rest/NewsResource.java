package com.whiskerguard.organization.web.rest;

import com.whiskerguard.organization.repository.NewsRepository;
import com.whiskerguard.organization.service.NewsService;
import com.whiskerguard.organization.service.dto.NewsDTO;
import com.whiskerguard.organization.web.rest.errors.BadRequestAlertException;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;
import tech.jhipster.web.util.HeaderUtil;
import tech.jhipster.web.util.PaginationUtil;
import tech.jhipster.web.util.ResponseUtil;

import java.net.URI;
import java.net.URISyntaxException;
import java.util.Objects;
import java.util.Optional;

/**
 * 新闻管理的REST控制器
 */
@RestController
@RequestMapping("/api/news")
public class NewsResource {

    private static final Logger LOG = LoggerFactory.getLogger(NewsResource.class);

    private static final String ENTITY_NAME = "whiskerguardOrgServiceNews";

    @Value("${jhipster.clientApp.name}")
    private String applicationName;

    private final NewsService newsService;

    private final NewsRepository newsRepository;

    public NewsResource(NewsService newsService, NewsRepository newsRepository) {
        this.newsService = newsService;
        this.newsRepository = newsRepository;
    }

    /**
     * {@code POST  /news} : 创建新闻
     *
     * @param newsDTO 要创建的新闻DTO
     * @return {@link ResponseEntity}，状态为 {@code 201 (Created)}，响应体为新创建的新闻DTO；
     * 如果新闻已经有ID，则状态为 {@code 400 (Bad Request)}
     * @throws URISyntaxException 如果Location URI语法不正确
     */
    @PostMapping("/create")
    public ResponseEntity<NewsDTO> createNews(@Valid @RequestBody NewsDTO newsDTO) throws URISyntaxException {
        LOG.debug("REST request to save News : {}", newsDTO);
        if (newsDTO.getId() != null) {
            throw new BadRequestAlertException("新的新闻不能有ID", ENTITY_NAME, "idexists");
        }
        newsDTO = newsService.save(newsDTO);
        return ResponseEntity.created(new URI("/api/news/" + newsDTO.getId()))
            .headers(HeaderUtil.createEntityCreationAlert(applicationName, true, ENTITY_NAME, newsDTO.getId().toString()))
            .body(newsDTO);
    }

    /**
     * {@code PATCH  /news/:id} : 部分更新已存在的新闻，null字段将被忽略
     *
     * @param id      要保存的新闻DTO的ID
     * @param newsDTO 要更新的新闻DTO
     * @return {@link ResponseEntity}，状态为 {@code 200 (OK)}，响应体为更新后的新闻DTO；
     * 如果新闻DTO无效，则状态为 {@code 400 (Bad Request)}；
     * 如果找不到新闻DTO，则状态为 {@code 404 (Not Found)}；
     * 如果新闻DTO无法更新，则状态为 {@code 500 (Internal Server Error)}
     */
    @PatchMapping(value = "/partial/{id}", consumes = {"application/json", "application/merge-patch+json"})
    public ResponseEntity<NewsDTO> partialUpdateNews(
        @PathVariable(value = "id", required = false) final Long id,
        @NotNull @Valid @RequestBody NewsDTO newsDTO
    ) {
        LOG.debug("REST request to partial update News partially : {}, {}", id, newsDTO);
        if (newsDTO.getId() == null) {
            throw new BadRequestAlertException("无效的ID", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, newsDTO.getId())) {
            throw new BadRequestAlertException("无效的ID", ENTITY_NAME, "idinvalid");
        }

        if (!newsRepository.existsById(id)) {
            throw new BadRequestAlertException("实体未找到", ENTITY_NAME, "idnotfound");
        }

        Optional<NewsDTO> result = newsService.partialUpdate(newsDTO);

        return ResponseUtil.wrapOrNotFound(
            result,
            HeaderUtil.createEntityUpdateAlert(applicationName, true, ENTITY_NAME, newsDTO.getId().toString())
        );
    }

    /**
     * {@code GET  /news} : 获取所有新闻
     *
     * @param pageable   分页信息
     * @param categoryId 新闻分类ID
     * @param eagerload  是否急切加载关联实体的标志（适用于多对多关系）
     * @return {@link ResponseEntity}，状态为 {@code 200 (OK)}，响应体为新闻分页数据
     */
    @GetMapping("/list")
    public ResponseEntity<Page<NewsDTO>> getAllNews(
        @org.springdoc.core.annotations.ParameterObject Pageable pageable,
        @RequestParam(name = "categoryId", required = false) Long categoryId,
        @RequestParam(name = "eagerload", required = false, defaultValue = "true") boolean eagerload
    ) {
        LOG.debug("REST request to get a page of News");
        Page<NewsDTO> page;
        if (eagerload) {
            page = newsService.findAllWithEagerRelationships(pageable, categoryId);
        } else {
            page = newsService.findAll(pageable, categoryId);
        }
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(ServletUriComponentsBuilder.fromCurrentRequest(), page);
        return ResponseEntity.ok().headers(headers).body(page);
    }

    /**
     * {@code GET  /news/:id} : 获取指定ID的新闻
     *
     * @param id 要获取的新闻DTO的ID
     * @return {@link ResponseEntity}，状态为 {@code 200 (OK)}，响应体为新闻DTO；
     * 如果找不到新闻，则状态为 {@code 404 (Not Found)}
     */
    @GetMapping("/detail/{id}")
    public ResponseEntity<NewsDTO> getNews(@PathVariable("id") Long id) {
        LOG.debug("REST request to get News : {}", id);
        Optional<NewsDTO> newsDTO = newsService.findOne(id);
        return ResponseUtil.wrapOrNotFound(newsDTO);
    }

    /**
     * {@code DELETE  /news/:id} : 删除指定ID的新闻
     *
     * @param id 要删除的新闻DTO的ID
     * @return {@link ResponseEntity}，状态为 {@code 204 (NO_CONTENT)}
     */
    @DeleteMapping("/delete/{id}")
    public ResponseEntity<Void> deleteNews(@PathVariable("id") Long id) {
        LOG.debug("REST request to delete News : {}", id);
        newsService.delete(id);
        return ResponseEntity.noContent()
            .headers(HeaderUtil.createEntityDeletionAlert(applicationName, true, ENTITY_NAME, id.toString()))
            .build();
    }
}

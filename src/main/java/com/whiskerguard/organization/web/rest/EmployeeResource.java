package com.whiskerguard.organization.web.rest;

import com.whiskerguard.common.constant.FileTypeConstants;
import com.whiskerguard.common.dto.ImportResultDTO;
import com.whiskerguard.organization.domain.enumeration.EmployeeStatus;
import com.whiskerguard.organization.repository.EmployeeRepository;
import com.whiskerguard.organization.request.EmployeeReq;
import com.whiskerguard.organization.service.EmployeeService;
import com.whiskerguard.organization.service.dto.*;
import com.whiskerguard.organization.service.exception.EmployeeNotFoundException;
import com.whiskerguard.organization.service.exception.InvalidPasswordException;
import com.whiskerguard.organization.util.ExcelTemplateUtil;
import com.whiskerguard.organization.web.rest.errors.BadRequestAlertException;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;
import tech.jhipster.web.util.HeaderUtil;
import tech.jhipster.web.util.PaginationUtil;
import tech.jhipster.web.util.ResponseUtil;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.net.URI;
import java.net.URISyntaxException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * 员工（用户）
 */
@RestController
@RequestMapping("/api/employees")
public class EmployeeResource {

    private static final Logger LOG = LoggerFactory.getLogger(EmployeeResource.class);

    private static final String ENTITY_NAME = "whiskerguardOrgServiceEmployee";

    @Value("${jhipster.clientApp.name}")
    private String applicationName;

    private final EmployeeService employeeService;

    private final EmployeeRepository employeeRepository;

    public EmployeeResource(EmployeeService employeeService, EmployeeRepository employeeRepository) {
        this.employeeService = employeeService;
        this.employeeRepository = employeeRepository;
    }

    /**
     * {@code POST  /employees} : Create a new employee.
     * 创建新员工
     *
     * @param employeeDTO the employeeDTO to create. 要创建的员工DTO
     * @return the {@link ResponseEntity} with status {@code 201 (Created)} and with body the new employeeDTO, or with status {@code 400 (Bad Request)} if the employee has already an ID.
     * 返回状态码为201的响应和新创建的员工DTO，如果员工已有ID则返回400错误
     * @throws URISyntaxException if the Location URI syntax is incorrect. 如果位置URI语法不正确
     */
    @PostMapping("")
    public ResponseEntity<EmployeeDTO> createEmployee(@Valid @RequestBody EmployeeDTO employeeDTO) throws URISyntaxException {
        LOG.debug("REST request to save Employee : {}", employeeDTO);
        if (employeeDTO.getId() != null) {
            throw new BadRequestAlertException("新的员工不能有ID", ENTITY_NAME, "id exists");
        }
        if (CollectionUtils.isEmpty(employeeDTO.getEmployeeOrgList())) {
            throw new BadRequestAlertException("员工必须分配到组织单元", ENTITY_NAME, "org unit required");
        }
        employeeDTO = employeeService.save(employeeDTO);
        return ResponseEntity.created(new URI("/api/employees/" + employeeDTO.getId()))
            .headers(HeaderUtil.createEntityCreationAlert(applicationName, true, ENTITY_NAME, employeeDTO.getId().toString()))
            .body(employeeDTO);
    }

    /**
     * {@code PUT  /employees/:id} : Updates an existing employee.
     * 更新现有员工
     *
     * @param id          the id of the employeeDTO to save. 要保存的员工DTO的ID
     * @param employeeDTO the employeeDTO to update. 要更新的员工DTO
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated employeeDTO,
     * or with status {@code 400 (Bad Request)} if the employeeDTO is not valid,
     * or with status {@code 500 (Internal Server Error)} if the employeeDTO couldn't be updated.
     * 返回状态码为200的响应和更新后的员工DTO，如果员工DTO无效则返回400错误，如果无法更新则返回500错误
     */
    @PutMapping("/{id}")
    public ResponseEntity<EmployeeDTO> updateEmployee(
        @PathVariable(value = "id", required = false) final Long id,
        @Valid @RequestBody EmployeeDTO employeeDTO
    ) {
        LOG.debug("REST request to update Employee : {}, {}", id, employeeDTO);
        if (employeeDTO.getId() == null) {
            throw new BadRequestAlertException("无效的ID", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, employeeDTO.getId())) {
            throw new BadRequestAlertException("无效的ID", ENTITY_NAME, "idinvalid");
        }

        if (!employeeRepository.existsById(id)) {
            throw new BadRequestAlertException("实体未找到", ENTITY_NAME, "idnotfound");
        }

        employeeDTO = employeeService.update(employeeDTO);
        return ResponseEntity.ok()
            .headers(HeaderUtil.createEntityUpdateAlert(applicationName, true, ENTITY_NAME, employeeDTO.getId().toString()))
            .body(employeeDTO);
    }

    /**
     * {@code PATCH  /employees/:id} : Partial updates given fields of an existing employee, field will ignore if it is null
     * 部分更新现有员工的指定字段，如果字段为null则忽略
     *
     * @param id          the id of the employeeDTO to save. 要保存的员工DTO的ID
     * @param employeeDTO the employeeDTO to update. 要更新的员工DTO
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated employeeDTO,
     * or with status {@code 400 (Bad Request)} if the employeeDTO is not valid,
     * or with status {@code 404 (Not Found)} if the employeeDTO is not found,
     * or with status {@code 500 (Internal Server Error)} if the employeeDTO couldn't be updated.
     * 返回状态码为200的响应和更新后的员工DTO，如果员工DTO无效则返回400错误，如果找不到员工则返回404错误，如果无法更新则返回500错误
     */
    @PatchMapping(value = "/{id}", consumes = {"application/json", "application/merge-patch+json"})
    public ResponseEntity<EmployeeDTO> partialUpdateEmployee(
        @PathVariable(value = "id", required = false) final Long id,
        @NotNull @Valid @RequestBody EmployeeDTO employeeDTO
    ) {
        LOG.debug("REST request to partial update Employee partially : {}, {}", id, employeeDTO);
        if (employeeDTO.getId() == null) {
            throw new BadRequestAlertException("无效的ID", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, employeeDTO.getId())) {
            throw new BadRequestAlertException("无效的ID", ENTITY_NAME, "idinvalid");
        }

        if (!employeeRepository.existsById(id)) {
            throw new BadRequestAlertException("实体未找到", ENTITY_NAME, "idnotfound");
        }

        Optional<EmployeeDTO> result = employeeService.partialUpdate(employeeDTO);

        return ResponseUtil.wrapOrNotFound(
            result,
            HeaderUtil.createEntityUpdateAlert(applicationName, true, ENTITY_NAME, employeeDTO.getId().toString())
        );
    }

    /**
     * {@code GET  /employees} : get all the employees.
     * 获取所有员工
     *
     * @param employeeReq 员工查询请求对象
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and the page of employees in body.
     * 返回状态码为200的响应和员工分页数据
     */
    @PostMapping("/page")
    public ResponseEntity<Page<EmployeeDTO>> getAllEmployees(@RequestBody EmployeeReq employeeReq) {
        LOG.debug("REST request to get a page of Employees");
        Page<EmployeeDTO> page = employeeService.findByCondition(employeeReq);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(ServletUriComponentsBuilder.fromCurrentRequest(), page);
        return ResponseEntity.ok().headers(headers).body(page);
    }

    /**
     * {@code GET  /employees/:id} : get the "id" employee.
     * 获取指定ID的员工
     *
     * @param id the id of the employeeDTO to retrieve. 要检索的员工DTO的ID
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the employeeDTO, or with status {@code 404 (Not Found)}.
     * 返回状态码为200的响应和员工DTO，如果找不到则返回404错误
     */
    @GetMapping("/{id}")
    public ResponseEntity<EmployeeDTO> getEmployee(@PathVariable("id") Long id) {
        LOG.debug("REST request to get Employee : {}", id);
        Optional<EmployeeDTO> employeeDTO = employeeService.findOne(id);
        return ResponseUtil.wrapOrNotFound(employeeDTO);
    }

    /**
     * {@code DELETE  /employees/:id} : delete the "id" employee.
     * 删除指定ID的员工
     *
     * @param id the id of the employeeDTO to delete. 要删除的员工DTO的ID
     * @return the {@link ResponseEntity} with status {@code 204 (NO_CONTENT)}.
     * 返回状态码为204的响应（无内容）
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteEmployee(@PathVariable("id") Long id) {
        LOG.debug("REST request to delete Employee : {}", id);
        employeeService.delete(id);
        return ResponseEntity.noContent()
            .headers(HeaderUtil.createEntityDeletionAlert(applicationName, true, ENTITY_NAME, id.toString()))
            .build();
    }

    /**
     * {@code PUT  /employees/:id/status} : 修改员工状态
     *
     * @param id     员工ID
     * @param status 新状态
     * @return {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated employeeDTO,
     * or with status {@code 404 (Not Found)} if the employeeDTO is not found.
     */
    @PutMapping("/{id}/status")
    public ResponseEntity<EmployeeDTO> changeStatus(
        @PathVariable(value = "id", required = false) final Long id,
        @RequestParam(value = "status") final EmployeeStatus status
    ) {
        LOG.debug("REST request to change Employee status : {} to {}", id, status);
        if (!employeeRepository.existsById(id)) {
            throw new BadRequestAlertException("实体未找到", ENTITY_NAME, "idnotfound");
        }
        EmployeeDTO result = employeeService.changeStatus(id, status);
        return ResponseEntity.ok()
            .headers(HeaderUtil.createEntityUpdateAlert(applicationName, true, ENTITY_NAME, id.toString()))
            .body(result);
    }

    /**
     * {@code GET  /employees/org-unit/:orgUnitId} : get all the employees by org unit.
     * 根据组织单元获取所有员工
     *
     * @param orgUnitId the org unit id. 组织单元ID
     * @param pageable  the pagination information. 分页信息
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and the page of employees in body.
     * 返回状态码为200的响应和员工分页数据
     */
    @GetMapping("/org/unit/{orgUnitId}")
    public ResponseEntity<Page<EmployeeDTO>> getEmployeesByOrgUnit(
        @PathVariable Long orgUnitId,
        @org.springdoc.core.annotations.ParameterObject Pageable pageable
    ) {
        LOG.debug("REST request to get Employees by OrgUnit : {}", orgUnitId);
        Page<EmployeeDTO> page = employeeService.findByOrgUnit(orgUnitId, pageable);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(ServletUriComponentsBuilder.fromCurrentRequest(), page);
        return ResponseEntity.ok().headers(headers).body(page);
    }

    /**
     * {@code GET  /employees/status/:status} : get all the employees by status.
     * 根据状态获取所有员工
     *
     * @param status   the employee status. 员工状态
     * @param pageable the pagination information. 分页信息
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and the page of employees in body.
     * 返回状态码为200的响应和员工分页数据
     */
    @GetMapping("/status/{status}")
    public ResponseEntity<Page<EmployeeDTO>> getEmployeesByStatus(
        @PathVariable EmployeeStatus status,
        @org.springdoc.core.annotations.ParameterObject Pageable pageable
    ) {
        LOG.debug("REST request to get Employees by status : {}", status);
        Page<EmployeeDTO> page = employeeService.findByStatus(status, pageable);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(ServletUriComponentsBuilder.fromCurrentRequest(), page);
        return ResponseEntity.ok().headers(headers).body(page);
    }

    /**
     * {@code POST  /employees/:employeeId/org-unit/:orgUnitId} : Assign an employee to an org unit.
     * 将员工分配到组织单元
     *
     * @param employeeId the id of the employee. 员工ID
     * @param orgUnitId  the id of the org unit. 组织单元ID
     * @param positionId the id of the position. 职位ID
     * @param isPrimary  whether this is the primary org unit. 是否为主要组织单元
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated employeeDTO,
     * or with status {@code 404 (Not Found)} if the employee or org unit is not found.
     * 返回状态码为200的响应和更新后的员工DTO，如果找不到员工或组织单元则返回404错误
     */
    @PostMapping("/{employeeId}/org/unit/{orgUnitId}")
    public ResponseEntity<EmployeeDTO> assignEmployeeToOrgUnit(
        @PathVariable Long employeeId,
        @PathVariable Long orgUnitId,
        @RequestParam(required = false) Long positionId,
        @RequestParam(defaultValue = "false") Boolean isPrimary
    ) {
        LOG.debug("REST request to assign Employee : {} to OrgUnit : {} with Position : {}", employeeId, orgUnitId, positionId);
        EmployeeDTO result = employeeService.assignToOrgUnit(employeeId, orgUnitId, positionId, isPrimary);
        return ResponseEntity.ok()
            .headers(HeaderUtil.createEntityUpdateAlert(applicationName, true, ENTITY_NAME, employeeId.toString()))
            .body(result);
    }

    /**
     * {@code DELETE  /employees/:employeeId/org-unit/:orgUnitId} : Remove an employee from an org unit.
     * 从组织单元中移除员工
     *
     * @param employeeId the id of the employee. 员工ID
     * @param orgUnitId  the id of the org unit. 组织单元ID
     * @return the {@link ResponseEntity} with status {@code 204 (NO_CONTENT)}.
     * 返回状态码为204的响应（无内容）
     */
    @DeleteMapping("/{employeeId}/org/unit/{orgUnitId}")
    public ResponseEntity<Void> removeEmployeeFromOrgUnit(@PathVariable Long employeeId, @PathVariable Long orgUnitId) {
        LOG.debug("REST request to remove Employee : {} from OrgUnit : {}", employeeId, orgUnitId);
        employeeService.removeFromOrgUnit(employeeId, orgUnitId);
        return ResponseEntity.noContent()
            .headers(HeaderUtil.createEntityDeletionAlert(applicationName, true, ENTITY_NAME, employeeId.toString()))
            .build();
    }

    /**
     * {@code POST  /employees/login} : 员工登录
     *
     * @param loginDTO 登录信息
     * @return {@link ResponseEntity} with status {@code 200 (OK)} and with body the login response.
     */
    @PostMapping("/login")
    public ResponseEntity<LoginResponseDTO> login(
        @Valid @RequestBody LoginDTO loginDTO,
        @RequestHeader(value = "X-Forwarded-For", required = false) String clientIp
    ) {
        LOG.debug("REST request to login Employee : {}", loginDTO.getUsername());
        LoginResponseDTO response = employeeService.login(loginDTO, clientIp);
        return ResponseEntity.ok(response);
    }

    /**
     * {@code POST  /employees/wechat/phone-login} : 微信手机号登录
     *
     * @param loginDTO 微信登录信息
     * @param clientIp 客户端IP
     * @return {@link ResponseEntity} with status {@code 200 (OK)} and with body the login response.
     */
    @PostMapping("/wechat/phone/login")
    public EmployeeDTO wechatPhoneLogin(@Valid @RequestBody WechatPhoneLoginDTO loginDTO,
                                        @RequestHeader(value = "X-Forwarded-For", required = false) String clientIp
    ) {
        LOG.debug("REST request to login Employee by WeChat phone : {}", loginDTO.getCode());
        return employeeService.wechatPhoneLogin(loginDTO, clientIp);
    }

    /**
     * {@code GET  /employees/phone/{phone}} : 根据手机号查找员工
     *
     * @param phone 手机号
     * @return {@link ResponseEntity} with status {@code 200 (OK)} and with body the employee,
     * or with status {@code 404 (Not Found)} if the employee is not found.
     */
    @GetMapping("/phone/{phone}")
    public ResponseEntity<EmployeeDTO> getEmployeeByPhone(@PathVariable String phone) {
        LOG.debug("REST request to get Employee by phone: {}", phone);
        Optional<EmployeeDTO> employeeDTO = employeeService.findByPhone(phone);
        return ResponseEntity.ok().body(employeeDTO.orElse(null));
    }

    /**
     * {@code PUT  /employees/:id/password} : 修改密码
     *
     * @param id                员工ID
     * @param passwordChangeDTO 密码修改信息
     * @return {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated employeeDTO,
     * or with status {@code 400 (Bad Request)} if the passwordChangeDTO is not valid.
     */
    @PutMapping("/{id}/password")
    public ResponseEntity<EmployeeDTO> changePassword(@PathVariable Long id, @RequestBody PasswordChangeDTO passwordChangeDTO) {
        LOG.debug("REST request to change password for Employee : {}", id);
        if (!employeeRepository.existsById(id)) {
            throw new BadRequestAlertException("实体未找到", ENTITY_NAME, "id not found");
        }
        if (StringUtils.isEmpty(passwordChangeDTO.getCurrentPassword())) {
            throw new BadRequestAlertException("当前密码不能为空", ENTITY_NAME, "current password required");
        }
        if (StringUtils.isEmpty(passwordChangeDTO.getNewPassword())) {
            throw new BadRequestAlertException("新密码不能为空", ENTITY_NAME, "new password required");
        }
        EmployeeDTO result = employeeService.changePassword(id, passwordChangeDTO);
        return ResponseEntity.ok()
            .headers(HeaderUtil.createEntityUpdateAlert(applicationName, true, ENTITY_NAME, id.toString()))
            .body(result);
    }

    /**
     * {@code PUT  /employees/:id/profile} : 更新个人信息
     *
     * @param id          员工ID
     * @param employeeDTO 员工信息
     * @return {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated employeeDTO,
     * or with status {@code 400 (Bad Request)} if the employeeDTO is not valid.
     */
    @PutMapping("/{id}/profile")
    public ResponseEntity<EmployeeDTO> updateProfile(@PathVariable Long id, @Valid @RequestBody EmployeeDTO employeeDTO) {
        LOG.debug("REST request to update profile for Employee : {}", id);
        EmployeeDTO result = employeeService.updateProfile(id, employeeDTO);
        return ResponseEntity.ok()
            .headers(HeaderUtil.createEntityUpdateAlert(applicationName, true, ENTITY_NAME, id.toString()))
            .body(result);
    }

    /**
     * {@code POST  /employees/employee-login} : 员工登录验证
     *
     * @param loginDTO 登录信息
     * @return {@link ResponseEntity} with status {@code 200 (OK)} and with body the employee details,
     * or with status {@code 400 (Bad Request)} if the credentials are invalid.
     */
    @PostMapping("/employee/login")
    public ResponseEntity<EmployeeDTO> employeeLogin(@Valid @RequestBody LoginDTO loginDTO) {
        LOG.debug("REST request to verify employee login : {}", loginDTO.getUsername());

        try {
            Optional<EmployeeDTO> employeeDTO = employeeService.findByUsernameAndPassword(
                loginDTO.getUsername(),
                loginDTO.getPassword(),
                EmployeeStatus.ACTIVE
            );

            return employeeDTO
                .map(ResponseEntity::ok)
                .orElseThrow(() -> new BadRequestAlertException("用户名或密码无效", ENTITY_NAME, "invalidcredentials"));
        } catch (EmployeeNotFoundException e) {
            throw new BadRequestAlertException("用户名未找到", ENTITY_NAME, "usernotfound");
        } catch (InvalidPasswordException e) {
            throw new BadRequestAlertException("密码无效", ENTITY_NAME, "invalidpassword");
        }
    }

    /**
     * {@code GET  /employees/:id/auth-info} : 获取员工的认证信息
     *
     * @param id 员工ID
     * @return {@link ResponseEntity} with status {@code 200 (OK)} and with body the employee auth info,
     * or with status {@code 404 (Not Found)} if the employee is not found.
     */
    @GetMapping("/{id}/auth/info")
    public ResponseEntity<EmployeeAuthDTO> getEmployeeAuthInfo(@PathVariable Long id) {
        LOG.debug("REST request to get Employee auth info : {}", id);
        EmployeeAuthDTO authDTO = employeeService.getEmployeeAuthInfo(id);
        return ResponseEntity.ok(authDTO);
    }

    /**
     * {@code GET  /employees/info} : 获取员工信息
     *
     * @param username 员工用户名
     * @return {@link ResponseEntity} with status {@code 200 (OK)} and with body the employee auth info,
     * or with status {@code 404 (Not Found)} if the employee is not found.
     */
    @GetMapping("/info")
    public ResponseEntity<EmployeeDTO> getUserByUsername(@RequestParam("username") String username) {
        LOG.debug("REST request to get Employee info : {}", username);
        EmployeeDTO employeeDTO = employeeService.getEmployeeAuthInfoByUsername(username);
        return ResponseEntity.ok(employeeDTO);
    }

    /**
     * {@code POST  /employees/import} : Import employees from Excel file.
     * 从Excel文件导入员工
     *
     * @param file the Excel file to import. 要导入的Excel文件
     * @return the {@link ResponseEntity} with status {@code 201 (Created)} and with body the imported employeeDTOs.
     * 返回状态码为201的响应和导入的员工DTO列表
     * @throws URISyntaxException if the Location URI syntax is incorrect. 如果位置URI语法不正确
     */
    @PostMapping(value = "/import", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<ImportResultDTO<EmployeeImportDTO>> importEmployees(@RequestParam("file") MultipartFile file)
        throws URISyntaxException {
        LOG.debug("REST request to import Employees from Excel file : {}", file.getOriginalFilename());

        if (file.isEmpty()) {
            throw new BadRequestAlertException("上传文件为空", ENTITY_NAME, "file.empty");
        }

        // 验证文件类型
        if (!Objects.requireNonNull(file.getOriginalFilename()).endsWith(FileTypeConstants.EXT_XLS) &&
            !Objects.requireNonNull(file.getOriginalFilename()).endsWith(FileTypeConstants.EXT_XLSX)) {
            throw new BadRequestAlertException("只支持.xlsx或.xls格式文件", ENTITY_NAME, "file.invalid.type");
        }

        ImportResultDTO<EmployeeImportDTO> result = employeeService.importFromExcel(file);

        return ResponseEntity.created(new URI("/api/employees/import"))
            .headers(HeaderUtil.createAlert(applicationName, "employeeManagement.imported",
                String.format("成功导入%d个员工", result.getSuccessCount())))
            .body(result);
    }

    /**
     * {@code PUT  /employees/:id/password/reset} : 重置密码
     *
     * @param passwordChangeDTO 密码修改信息
     * @return {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated employeeDTO,
     * or with status {@code 400 (Bad Request)} if the passwordChangeDTO is not valid.
     */
    @PutMapping("/password/reset")
    public ResponseEntity<EmployeeDTO> resetPassword(@Valid @RequestBody PasswordChangeDTO passwordChangeDTO) {
        LOG.debug("REST request to reset password for Employee : {}", passwordChangeDTO.getPhone());
        if (StringUtils.isEmpty(passwordChangeDTO.getPhone())) {
            throw new BadRequestAlertException("手机号不能为空", ENTITY_NAME, "phone required");
        }
        if (StringUtils.isEmpty(passwordChangeDTO.getVerifyCode())) {
            throw new BadRequestAlertException("验证码不能为空", ENTITY_NAME, "verify code required");
        }
        if (StringUtils.isEmpty(passwordChangeDTO.getNewPassword())) {
            throw new BadRequestAlertException("新密码不能为空", ENTITY_NAME, "new password required");
        }
        if (!passwordChangeDTO.getNewPassword().equals(passwordChangeDTO.getConfirmPassword())) {
            throw new BadRequestAlertException("新密码和确认密码不匹配", ENTITY_NAME, "password not match");
        }
        EmployeeDTO result = employeeService.resetPassword(passwordChangeDTO);
        return ResponseEntity.ok()
            .headers(HeaderUtil.createEntityUpdateAlert(applicationName, true, ENTITY_NAME, passwordChangeDTO.toString()))
            .body(result);
    }

    /**
     * {@code PUT  /employees/:id/phone/change} : 更换绑定手机号
     *
     * @param passwordChangeDTO 密码修改信息
     * @return {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated employeeDTO,
     * or with status {@code 400 (Bad Request)} if the passwordChangeDTO is not valid.
     */
    @PutMapping("/phone/change")
    public ResponseEntity<Void> changePhone(@Valid @RequestBody PasswordChangeDTO passwordChangeDTO) {
        LOG.debug("REST request to change phone for Employee : {}", passwordChangeDTO.getPhone());
        if (StringUtils.isEmpty(passwordChangeDTO.getPhone())) {
            throw new BadRequestAlertException("手机号不能为空", ENTITY_NAME, "phone required");
        }
        if (StringUtils.isEmpty(passwordChangeDTO.getVerifyCode())) {
            throw new BadRequestAlertException("验证码不能为空", ENTITY_NAME, "verify code required");
        }
        employeeService.changePhone(passwordChangeDTO);
        return ResponseEntity.noContent()
            .headers(HeaderUtil.createEntityUpdateAlert(applicationName, true, ENTITY_NAME, passwordChangeDTO.toString()))
            .build();
    }

    /**
     * {@code GET  /employees/list} : 根据employeeId集合返回员工信息
     *
     * @param ids 员工ID集合
     * @return 员工信息列表
     */
    @GetMapping("/list")
    public List<EmployeeDTO> getEmployeesByIds(@RequestParam List<Long> ids) {
        LOG.debug("REST request to get Employees by ids : {}", ids);
        return employeeService.findByIds(ids);
    }

    /**
     * {@code PUT  /employees/:id/avatar} : 更新头像
     *
     * @param id     员工ID
     * @param avatar 头像
     * @return {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated employeeDTO,
     * or with status {@code 400 (Bad Request)} if the employeeDTO is not valid.
     */
    @PutMapping("/avatar")
    public ResponseEntity<EmployeeDTO> updateAvatar(@RequestParam("id") Long id, @RequestParam("avatar") String avatar) {
        LOG.debug("REST request to update avatar for Employee : {}", id);
        EmployeeDTO result = employeeService.updateAvatar(id, avatar);
        return ResponseEntity.ok()
            .headers(HeaderUtil.createEntityUpdateAlert(applicationName, true, ENTITY_NAME, result.getId().toString()))
            .body(result);
    }

    /**
     * {@code GET  /employees/tenant/:tenantId/ids} : 根据租户ID获取所有员工ID
     *
     * @return {@link ResponseEntity} with status {@code 200 (OK)} and with body the list of employee IDs.
     * 返回状态码为200的响应和员工ID列表
     */
    @GetMapping("/tenant/ids")
    public ResponseEntity<List<Long>> getEmployeeIdsByTenantId() {
        List<Long> employeeIds = employeeService.findEmployeeIdsByTenantId();
        return ResponseEntity.ok(employeeIds);
    }

    /**
     * {@code PUT  /employees/bind/openid} : 绑定openId
     *
     * @param openId  微信openId
     * @param unionId 微信unionId
     * @return {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated employeeDTO,
     * or with status {@code 400 (Bad Request)} if the employeeDTO is not valid.
     */
    @PutMapping("/bind/openid")
    public ResponseEntity<EmployeeDTO> bindOpenId(@RequestParam("openId") String openId, @RequestParam(name = "unionId", required = false) String unionId) {
        LOG.debug("REST request to bind openId for Employee : {}", openId);
        EmployeeDTO result = employeeService.bindOpenId(openId, unionId);
        return ResponseEntity.ok()
            .headers(HeaderUtil.createEntityUpdateAlert(applicationName, true, ENTITY_NAME, result.getId().toString()))
            .body(result);
    }

    /**
     * {@code PUT  /employees/unbind/openid} : 解绑openId
     *
     * @param openId 微信openId
     * @return {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated employeeDTO,
     * or with status {@code 400 (Bad Request)} if the employeeDTO is not valid.
     */
    @PutMapping("/unbind/openid")
    public ResponseEntity<EmployeeDTO> unbindOpenId(@RequestParam("openId") String openId) {
        LOG.debug("REST request to unbind openId for Employee : {}", openId);
        EmployeeDTO result = employeeService.unbindOpenId(openId);
        return ResponseEntity.ok()
            .headers(HeaderUtil.createEntityUpdateAlert(applicationName, true, ENTITY_NAME, result.getId().toString()))
            .body(result);
    }

    /**
     * {@code GET  /employees/template} : Download employee import template.
     * 下载企业员工信息模板
     *
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and Excel template file.
     * 返回状态码为200的响应和Excel模板文件
     */
    @GetMapping("/template")
    public ResponseEntity<byte[]> downloadEmployeeTemplate() {
        LOG.debug("REST request to download Employee import template");

        try {
            // 创建模板数据
            List<EmployeeImportDTO> templateData = createEmployeeTemplateData();

            // 创建导入说明
            List<String> instructions = createEmployeeImportInstructions();

            // 生成Excel文件
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            ExcelTemplateUtil.exportToExcel(templateData, EmployeeImportDTO.class, "企业员工信息模板", instructions, outputStream);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.parseMediaType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"));
            String fileName = "企业员工信息模板.xlsx";
            String encodedFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8);
            headers.set("Content-Disposition", "attachment; filename*=UTF-8''" + encodedFileName);

            return ResponseEntity.ok()
                .headers(headers)
                .body(outputStream.toByteArray());

        } catch (IOException e) {
            LOG.error("生成企业员工信息模板失败", e);
            throw new BadRequestAlertException("生成模板失败: " + e.getMessage(), ENTITY_NAME, "templatefailed");
        }
    }

    /**
     * 创建员工导入模板数据
     */
    private List<EmployeeImportDTO> createEmployeeTemplateData() {
        List<EmployeeImportDTO> templateData = new ArrayList<>();

        EmployeeImportDTO employee1 = new EmployeeImportDTO();
        employee1.setUsername("zhangsan");
        employee1.setRealName("张三");
        employee1.setEmail("<EMAIL>");
        employee1.setPhone("13800138001");
        employee1.setGender("FEMALE");
        employee1.setBirthDate("1990-01-15");
        employee1.setIdCard("110101199001151234");
        employee1.setEmployeeNo("EMP001");
        employee1.setHireDate("2024-01-15");
        employee1.setStartDate("2024-01-15");
        employee1.setOrgUnitName("技术部");
        employee1.setPositionName("高级软件工程师");
        employee1.setRoleNames("开发人员,技术专家");

        templateData.add(employee1);
        return templateData;
    }

    /**
     * 创建员工导入说明
     */
    private List<String> createEmployeeImportInstructions() {
        List<String> instructions = new ArrayList<>();

        instructions.add("1. 带*号的列为必填项");
        instructions.add("2. 登录用户名必须唯一，不能重复");
        instructions.add("3. 邮箱地址必须唯一，不能重复");
        instructions.add("4. 性别：MALE/FEMALE/UNKNOWN");
        instructions.add("5. 日期格式：YYYY-MM-DD（如：1990-01-01）");
        instructions.add("6. 角色列表：多个角色用逗号分隔（如：管理员,普通员工）");
        instructions.add("7. 手机号格式必须正确");
        instructions.add("8. 身份证号格式必须正确（18位）");
        instructions.add("9. 组织单元名称和岗位名称必须在系统中存在");
        instructions.add("10. 角色必须在系统中存在");
        instructions.add("11. 请删除示例数据行后再导入实际数据");

        return instructions;
    }

    /**
     * {@code GET  /employees/ids/org/unit/:orgUnitId} : 根据组织单元获取所有员工ID
     *
     * @param orgUnitId the org unit id. 组织单元ID
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and the page of employees in body.
     * 返回状态码为200的响应和员工分页数据
     */
    @GetMapping("/ids/org/unit/{orgUnitId}")
    public ResponseEntity<List<Long>> getEmployeeIdsByOrgUnit(@PathVariable Long orgUnitId) {
        LOG.debug("REST request to get EmployeeIds by OrgUnit : {}", orgUnitId);
        return ResponseEntity.ok().body(employeeService.findEmployeeIdsByOrgUnit(orgUnitId));
    }

    /**
     * {@code GET  /employees/statistics} : 获取用户统计信息
     *
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the user statistics.
     * 返回状态码为200的响应和用户统计信息
     */
    @GetMapping("/statistics")
    public ResponseEntity<UserStatisticsDTO> getUserStatistics() {
        LOG.debug("REST request to get user statistics");
        UserStatisticsDTO statistics = employeeService.getUserStatistics();
        return ResponseEntity.ok().body(statistics);
    }

    /**
     * {@code GET  /employees/search} : 根据姓名模糊搜索员工
     * 搜索用户名(username)和真实姓名(realName)字段
     *
     * @param name 搜索关键词
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the employee list.
     * 返回状态码为200的响应和员工列表
     */
    @GetMapping("/search")
    public ResponseEntity<List<EmployeeDTO>> searchEmployeesByName(@RequestParam("name") String name) {
        LOG.debug("REST request to search employees by name: {}", name);

        try {
            List<EmployeeDTO> employees = employeeService.searchByName(name);
            return ResponseEntity.ok().body(employees);
        } catch (Exception e) {
            LOG.error("Error searching employees by name: {}", name, e);
            throw new BadRequestAlertException("搜索员工失败: " + e.getMessage(), ENTITY_NAME, "search.failed");
        }
    }


}

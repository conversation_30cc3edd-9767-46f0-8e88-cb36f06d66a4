package com.whiskerguard.organization.web.rest;

import com.whiskerguard.common.constant.FileTypeConstants;
import com.whiskerguard.common.dto.ImportResultDTO;
import com.whiskerguard.organization.domain.enumeration.TenantStatus;
import com.whiskerguard.organization.repository.TenantRepository;
import com.whiskerguard.organization.service.TenantService;
import com.whiskerguard.organization.service.dto.TenantDTO;
import com.whiskerguard.organization.service.dto.TenantImportDTO;
import com.whiskerguard.organization.service.dto.TenantTreeDTO;
import com.whiskerguard.organization.util.ExcelTemplateUtil;
import com.whiskerguard.organization.web.rest.errors.BadRequestAlertException;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;
import reactor.core.publisher.Mono;
import tech.jhipster.web.util.HeaderUtil;
import tech.jhipster.web.util.PaginationUtil;
import tech.jhipster.web.util.ResponseUtil;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.URI;
import java.net.URISyntaxException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * REST controller for managing {@link com.whiskerguard.organization.domain.Tenant}.
 * 租户管理的REST控制器
 * 提供租户的CRUD操作和状态管理的API接口
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api")
public class TenantResource {

    /**
     * 日志记录器
     */
    private final Logger log = LoggerFactory.getLogger(TenantResource.class);

    /**
     * 实体名称，用于错误消息
     */
    private static final String ENTITY_NAME = "tenant";

    /**
     * 应用名称，从配置中获取
     */
    @Value("${jhipster.clientApp.name}")
    private String applicationName;

    /**
     * 租户服务
     */
    private final TenantService tenantService;

    /**
     * 租户仓库
     */
    private final TenantRepository tenantRepository;

    /**
     * 构造函数
     *
     * @param tenantService    租户服务
     * @param tenantRepository 租户仓库
     */
    public TenantResource(TenantService tenantService, TenantRepository tenantRepository) {
        this.tenantService = tenantService;
        this.tenantRepository = tenantRepository;
    }

    /**
     * {@code POST  /tenants} : Create a new tenant.
     * 创建新租户
     *
     * @param tenantDTO the tenantDTO to create. 要创建的租户DTO
     * @return the {@link ResponseEntity} with status {@code 201 (Created)} and with body the new tenantDTO, or with status {@code 400 (Bad Request)} if the tenant has already an ID.
     * 返回状态码为201的响应和新创建的租户DTO，如果租户已有ID则返回400错误
     * @throws URISyntaxException if the Location URI syntax is incorrect. 如果位置URI语法不正确
     */
    @PostMapping("/tenants")
    public ResponseEntity<TenantDTO> createTenant(@Valid @RequestBody TenantDTO tenantDTO) throws URISyntaxException {
        log.debug("REST request to save Tenant : {}", tenantDTO);
        if (tenantDTO.getId() != null) {
            throw new BadRequestAlertException("新的租户不能有ID", ENTITY_NAME, "id exists");
        }

        //验证租户名称是否唯一
        tenantService.validateName(tenantDTO.getName());

        TenantDTO result = tenantService.save(tenantDTO);
        return ResponseEntity.created(new URI("/api/tenants/" + result.getId()))
            .headers(HeaderUtil.createEntityCreationAlert(applicationName, true, ENTITY_NAME, result.getId().toString()))
            .body(result);
    }

    /**
     * {@code PUT  /tenants/:id} : Updates an existing tenant.
     * 更新现有租户
     *
     * @param id        the id of the tenantDTO to save. 要保存的租户DTO的ID
     * @param tenantDTO the tenantDTO to update. 要更新的租户DTO
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated tenantDTO,
     * or with status {@code 400 (Bad Request)} if the tenantDTO is not valid,
     * or with status {@code 500 (Internal Server Error)} if the tenantDTO couldn't be updated.
     * 返回状态码为200的响应和更新后的租户DTO，如果租户DTO无效则返回400错误，如果无法更新则返回500错误
     */
    @PutMapping("/tenants/{id}")
    public ResponseEntity<TenantDTO> updateTenant(
        @PathVariable(value = "id", required = false) final Long id,
        @Valid @RequestBody TenantDTO tenantDTO
    ) {
        log.debug("REST request to update Tenant : {}, {}", id, tenantDTO);
        if (tenantDTO.getId() == null) {
            throw new BadRequestAlertException("无效的ID", ENTITY_NAME, "id null");
        }
        if (!Objects.equals(id, tenantDTO.getId())) {
            throw new BadRequestAlertException("无效的ID", ENTITY_NAME, "id invalid");
        }

        if (!tenantRepository.existsById(id)) {
            throw new BadRequestAlertException("实体未找到", ENTITY_NAME, "id not found");
        }

        TenantDTO result = tenantService.update(tenantDTO);
        return ResponseEntity.ok()
            .headers(HeaderUtil.createEntityUpdateAlert(applicationName, true, ENTITY_NAME, tenantDTO.getId().toString()))
            .body(result);
    }

    /**
     * {@code PATCH  /tenants/:id} : Partial updates given fields of an existing tenant, field will ignore if it is null
     * 部分更新现有租户的指定字段，如果字段为null则忽略
     *
     * @param id        the id of the tenantDTO to save. 要保存的租户DTO的ID
     * @param tenantDTO the tenantDTO to update. 要更新的租户DTO
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated tenantDTO,
     * or with status {@code 400 (Bad Request)} if the tenantDTO is not valid,
     * or with status {@code 404 (Not Found)} if the tenantDTO is not found,
     * or with status {@code 500 (Internal Server Error)} if the tenantDTO couldn't be updated.
     * 返回状态码为200的响应和更新后的租户DTO，如果租户DTO无效则返回400错误，如果找不到租户则返回404错误，如果无法更新则返回500错误
     */
    @PatchMapping(value = "/tenants/{id}", consumes = {"application/json", "application/merge-patch+json"})
    public ResponseEntity<TenantDTO> partialUpdateTenant(
        @PathVariable(value = "id", required = false) final Long id,
        @Valid @RequestBody TenantDTO tenantDTO
    ) {
        log.debug("REST request to partial update Tenant partially : {}, {}", id, tenantDTO);
        if (tenantDTO.getId() == null) {
            throw new BadRequestAlertException("无效的ID", ENTITY_NAME, "id null");
        }
        if (!Objects.equals(id, tenantDTO.getId())) {
            throw new BadRequestAlertException("无效的ID", ENTITY_NAME, "id invalid");
        }

        if (!tenantRepository.existsById(id)) {
            throw new BadRequestAlertException("实体未找到", ENTITY_NAME, "id not found");
        }

        Optional<TenantDTO> result = tenantService.partialUpdate(tenantDTO);

        return ResponseUtil.wrapOrNotFound(
            result,
            HeaderUtil.createEntityUpdateAlert(applicationName, true, ENTITY_NAME, tenantDTO.getId().toString())
        );
    }

    /**
     * {@code GET  /tenants} : get all the tenants.
     * 获取所有租户
     *
     * @param pageable the pagination information. 分页信息
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and the page of tenants in body.
     * 返回状态码为200的响应和租户分页数据
     */
    @GetMapping("/tenants")
    public ResponseEntity<Page<TenantDTO>> getAllTenants(@ParameterObject Pageable pageable) {
        log.debug("REST request to get a page of Tenants");
        Page<TenantDTO> page = tenantService.findAll(pageable);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(ServletUriComponentsBuilder.fromCurrentRequest(), page);
        return ResponseEntity.ok().headers(headers).body(page);
    }

    /**
     * {@code GET  /tenants/:id} : get the "id" tenant.
     * 获取指定ID的租户
     *
     * @param id the id of the tenantDTO to retrieve. 要检索的租户DTO的ID
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the tenantDTO, or with status {@code 404 (Not Found)}.
     * 返回状态码为200的响应和租户DTO，如果找不到则返回404错误
     */
    @GetMapping("/tenants/{id}")
    public ResponseEntity<TenantDTO> getTenant(@PathVariable Long id) {
        log.debug("REST request to get Tenant : {}", id);
        Optional<TenantDTO> tenantDTO = tenantService.findOne(id);
        return ResponseUtil.wrapOrNotFound(tenantDTO);
    }

    /**
     * {@code DELETE  /tenants/:id} : delete the "id" tenant.
     * 删除指定ID的租户
     *
     * @param id the id of the tenantDTO to delete. 要删除的租户DTO的ID
     * @return the {@link ResponseEntity} with status {@code 204 (NO_CONTENT)}.
     * 返回状态码为204的响应（无内容）
     */
    @DeleteMapping("/tenants/{id}")
    public ResponseEntity<Void> deleteTenant(@PathVariable Long id) {
        log.debug("REST request to delete Tenant : {}", id);
        tenantService.delete(id);
        return ResponseEntity.noContent()
            .headers(HeaderUtil.createEntityDeletionAlert(applicationName, true, ENTITY_NAME, id.toString()))
            .build();
    }

    /**
     * {@code PATCH  /tenants/:id/status} : Change the status of an existing tenant.
     * 变更现有租户的状态
     *
     * @param id           the id of the tenant to change status. 要变更状态的租户ID
     * @param targetStatus the target status to change to. 要变更的目标状态
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated tenantDTO,
     * or with status {@code 400 (Bad Request)} if the tenant is not valid,
     * or with status {@code 404 (Not Found)} if the tenant is not found.
     * 返回状态码为200的响应和更新后的租户DTO，如果租户无效则返回400错误，如果找不到租户则返回404错误
     */
    @PatchMapping("/tenants/{id}/status")
    public ResponseEntity<TenantDTO> changeTenantStatus(@PathVariable("id") Long id, @RequestParam("status") TenantStatus targetStatus) {
        log.debug("REST request to change Tenant status : {} to {}", id, targetStatus);
        if (!tenantRepository.existsById(id)) {
            throw new BadRequestAlertException("实体未找到", ENTITY_NAME, "idnotfound");
        }
        TenantDTO result = tenantService.changeStatus(id, targetStatus);
        return ResponseEntity.ok()
            .headers(HeaderUtil.createEntityUpdateAlert(applicationName, true, ENTITY_NAME, id.toString()))
            .body(result);
    }

    /**
     * {@code GET  /tenants/status/:status} : get all the tenants by status.
     * 根据状态获取所有租户
     *
     * @param status   the status of the tenants to retrieve. 要检索的租户状态
     * @param pageable the pagination information. 分页信息
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and the page of tenants in body.
     * 返回状态码为200的响应和租户分页数据
     */
    @GetMapping("/tenants/status/{status}")
    public ResponseEntity<Page<TenantDTO>> getAllTenantsByStatus(
        @PathVariable("status") TenantStatus status,
        @ParameterObject Pageable pageable
    ) {
        log.debug("REST request to get a page of Tenants by status : {}", status);
        Page<TenantDTO> page = tenantService.findAllByStatus(status, pageable);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(ServletUriComponentsBuilder.fromCurrentRequest(), page);
        return ResponseEntity.ok().headers(headers).body(page);
    }

    /**
     * {@code POST  /tenants/import} : Import tenants from Excel file.
     * 从Excel文件导入租户
     *
     * @param file the Excel file to import. 要导入的Excel文件
     * @return the {@link ResponseEntity} with status {@code 201 (Created)} and with body the imported tenantDTOs.
     * 返回状态码为201的响应和导入的租户DTO列表
     * @throws URISyntaxException if the Location URI syntax is incorrect. 如果位置URI语法不正确
     */
    @PostMapping(value = "/tenants/import", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<ImportResultDTO<TenantImportDTO>> importTenants(@RequestParam("file") MultipartFile file)
        throws URISyntaxException {
        log.debug("REST request to import Tenants from Excel file : {}", file.getOriginalFilename());

        if (file.isEmpty()) {
            throw new BadRequestAlertException("上传文件为空", ENTITY_NAME, "file.empty");
        }
        //判断文件是不是excel
        if (!Objects.requireNonNull(file.getOriginalFilename()).endsWith(FileTypeConstants.EXT_XLS) &&
            !Objects.requireNonNull(file.getOriginalFilename()).endsWith(FileTypeConstants.EXT_XLSX)) {
            throw new BadRequestAlertException("只支持.xlsx或.xls格式文件", ENTITY_NAME, "file.invalid.type");
        }

        ImportResultDTO<TenantImportDTO> result = tenantService.importFromExcel(file);

        return ResponseEntity.created(new URI("/api/tenants/import"))
            .headers(HeaderUtil.createAlert(applicationName, "tenantManagement.imported",
                String.format("成功导入%d个租户", result.getSuccessCount())))
            .body(result);
    }

    /**
     * {@code GET  /tenants/check} : check if the current tenant can access the system.
     * 检查当前租户是否可以访问系统
     *
     * @param tenantId the id of the tenant to check. 要检查的租户ID
     * @return the {@link Mono} with status {@code 200 (OK)} and with body the boolean value.
     * 返回状态码为200的响应和布尔值
     */
    @GetMapping("/tenants/check")
    public Mono<Boolean> checkTenantAccess(@RequestParam("tenantId") Long tenantId) {
        log.debug("REST request to check tenant access : {}", tenantId);
        Boolean bool = tenantService.checkTenantAccess(tenantId);
        return Mono.just(bool);
    }

    /**
     * {@code GET  /tenants/check/v2} : check if the current tenant can access the system.
     * 检查当前租户是否可以访问系统
     *
     * @param tenantId the id of the tenant to check. 要检查的租户ID
     * @return the {@link Boolean} with status {@code 200 (OK)} and with body the boolean value.
     * 返回状态码为200的响应和布尔值
     */
    @GetMapping("/tenants/check/v2")
    public Boolean checkTenantAccessNoReactive(@RequestParam("tenantId") Long tenantId) {
        log.debug("REST request to check tenant access no reactive : {}", tenantId);
        return tenantService.checkTenantAccess(tenantId);
    }

    /**
     * {@code GET  /tenants/template} : Download tenant import template.
     * 下载租户企业信息模板
     *
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and Excel template file.
     * 返回状态码为200的响应和Excel模板文件
     */
    @GetMapping("/tenants/template")
    public ResponseEntity<byte[]> downloadTenantTemplate() {
        log.debug("REST request to download Tenant import template");

        try {
            // 创建模板数据
            List<TenantImportDTO> templateData = createTenantTemplateData();

            // 创建导入说明
            List<String> instructions = createTenantImportInstructions();

            // 生成Excel文件
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            ExcelTemplateUtil.exportToExcel(templateData, TenantImportDTO.class, "租户企业信息模板", instructions, outputStream);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.parseMediaType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"));
            String fileName = "租户企业信息模板.xlsx";
            String encodedFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8);
            headers.set("Content-Disposition", "attachment; filename*=UTF-8''" + encodedFileName);

            return ResponseEntity.ok()
                .headers(headers)
                .body(outputStream.toByteArray());

        } catch (IOException e) {
            log.error("生成租户企业信息模板失败", e);
            throw new BadRequestAlertException("生成模板失败: " + e.getMessage(), ENTITY_NAME, "templatefailed");
        }
    }

    /**
     * 创建租户企业信息模板数据
     * 根据TenantImportDTO的完整字段结构生成标准的企业信息导入模板
     */
    private List<TenantImportDTO> createTenantTemplateData() {
        List<TenantImportDTO> templateData = new ArrayList<>();

        // 示例：科技公司 - 展示完整的企业信息字段
        TenantImportDTO techCompany = new TenantImportDTO();
        // 基本信息
        techCompany.setName("北京创新科技有限公司");
        techCompany.setContactEmail("<EMAIL>");
        techCompany.setContactPhone("010-88888888");

        // 工商注册信息
        techCompany.setRegistrationNumber("91110108********9A");
        techCompany.setRegistrationDate("2020-03-15");
        techCompany.setRegisteredCapital(new BigDecimal("10000000"));
        techCompany.setCompanyType("有限责任公司");
        techCompany.setBusinessScope("软件开发；人工智能技术研发；大数据分析服务；云计算服务；技术咨询；技术转让；技术推广服务");
        techCompany.setIndustry("软件和信息技术服务业");

        // 详细信息
        techCompany.setTaxRegistrationNumber("91110108********9A");
        techCompany.setOrganizationCode("********-9");
        techCompany.setRegisteredAddress("北京市海淀区中关村大街1号科技大厦10层1001室");
        techCompany.setPostalCode("100080");
        techCompany.setWebsite("https://www.innovate-tech.com");
        techCompany.setFax("010-********");

        // 联系人信息
        techCompany.setContactPerson("张总");
        techCompany.setContactMobile("***********");
        techCompany.setProfileContactEmail("<EMAIL>");

        // 银行信息
        techCompany.setBankName("中国工商银行北京中关村支行");
        techCompany.setBankAccount("020000********90123");

        // 法人信息
        techCompany.setLegalPerson("张三");
        techCompany.setLegalPersonId("110101198001011234");

        // 证照信息
        techCompany.setBusinessLicensePath("/documents/business_license_001.pdf");

        templateData.add(techCompany);
        return templateData;
    }

    /**
     * 创建租户导入说明
     */
    private List<String> createTenantImportInstructions() {
        List<String> instructions = new ArrayList<>();

        instructions.add("1. 带*号的列为必填项");
        instructions.add("2. 租户编码必须唯一，不能重复");
        instructions.add("3. 日期格式：YYYY-MM-DD（如：2025-01-01）");
        instructions.add("4. 注册资本为数字，单位为元");
        instructions.add("5. 邮箱格式必须正确");
        instructions.add("6. 手机号格式必须正确");
        instructions.add("7. 请删除示例数据行后再导入实际数据");

        return instructions;
    }

    /**
     * {@code GET  /tenants/tree} : Get sub tenant tree for current tenant.
     * 获取当前租户的所有下级租户树结构
     *
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the tenant tree.
     * 返回状态码为200的响应和租户树结构
     */
    @GetMapping("/tenants/tree")
    public ResponseEntity<List<TenantTreeDTO>> getSubTenantTree() {
        log.debug("REST request to get sub tenant tree");

        try {
            List<TenantTreeDTO> tenantTree = tenantService.getSubTenantTree();
            return ResponseEntity.ok().body(tenantTree);

        } catch (Exception e) {
            log.error("Error getting sub tenant tree", e);
            throw new BadRequestAlertException("获取下级租户树失败", ENTITY_NAME, "tree.failed");
        }
    }

}

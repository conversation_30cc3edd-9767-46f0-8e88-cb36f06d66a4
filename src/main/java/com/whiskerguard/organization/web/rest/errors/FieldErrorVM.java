package com.whiskerguard.organization.web.rest.errors;

import java.io.Serializable;

/**
 * 字段错误视图模型
 * 用于表示表单验证中的字段错误信息
 */
public class FieldErrorVM implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 对象名称
     * 通常是DTO或实体的名称
     */
    private final String objectName;

    /**
     * 字段名称
     * 发生错误的字段名
     */
    private final String field;

    /**
     * 错误消息
     * 描述错误的详细信息
     */
    private final String message;

    /**
     * 构造函数
     *
     * @param dto 对象名称（通常是DTO名称）
     * @param field 字段名称
     * @param message 错误消息
     */
    public FieldErrorVM(String dto, String field, String message) {
        this.objectName = dto;
        this.field = field;
        this.message = message;
    }

    /**
     * 获取对象名称
     * @return 对象名称
     */
    public String getObjectName() {
        return objectName;
    }

    /**
     * 获取字段名称
     * @return 字段名称
     */
    public String getField() {
        return field;
    }

    /**
     * 获取错误消息
     * @return 错误消息
     */
    public String getMessage() {
        return message;
    }
}

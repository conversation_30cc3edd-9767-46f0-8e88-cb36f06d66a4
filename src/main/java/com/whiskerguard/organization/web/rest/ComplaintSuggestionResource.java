package com.whiskerguard.organization.web.rest;

import com.whiskerguard.organization.repository.ComplaintSuggestionRepository;
import com.whiskerguard.organization.service.ComplaintSuggestionService;
import com.whiskerguard.organization.service.dto.ComplaintSuggestionDTO;
import com.whiskerguard.organization.web.rest.errors.BadRequestAlertException;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;
import tech.jhipster.web.util.HeaderUtil;
import tech.jhipster.web.util.PaginationUtil;
import tech.jhipster.web.util.ResponseUtil;

import java.net.URI;
import java.net.URISyntaxException;
import java.util.Objects;
import java.util.Optional;

/**
 * 描述：投诉与建议管理的REST控制器
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/20
 */
@RestController
@RequestMapping("/api/complaint/suggestions")
public class ComplaintSuggestionResource {

    private static final Logger LOG = LoggerFactory.getLogger(ComplaintSuggestionResource.class);

    private static final String ENTITY_NAME = "whiskerguardOrgServiceComplaintSuggestion";

    @Value("${jhipster.clientApp.name}")
    private String applicationName;

    private final ComplaintSuggestionService complaintSuggestionService;

    private final ComplaintSuggestionRepository complaintSuggestionRepository;

    public ComplaintSuggestionResource(
        ComplaintSuggestionService complaintSuggestionService,
        ComplaintSuggestionRepository complaintSuggestionRepository
    ) {
        this.complaintSuggestionService = complaintSuggestionService;
        this.complaintSuggestionRepository = complaintSuggestionRepository;
    }

    /**
     * 方法名称：createComplaintSuggestion
     * 描述：创建新的投诉与建议记录
     *
     * @param complaintSuggestionDTO 要创建的投诉与建议记录DTO
     * @return {@link ResponseEntity}，状态为 {@code 201 (Created)}，响应体为新创建的投诉与建议记录DTO；
     * 如果投诉与建议记录已经有ID，则状态为 {@code 400 (Bad Request)}
     * @throws URISyntaxException 如果 URI 语法不正确。
     * @since 1.0
     */
    @PostMapping("")
    public ResponseEntity<ComplaintSuggestionDTO> createComplaintSuggestion(
        @Valid @RequestBody ComplaintSuggestionDTO complaintSuggestionDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to save ComplaintSuggestion : {}", complaintSuggestionDTO);
        if (complaintSuggestionDTO.getId() != null) {
            throw new BadRequestAlertException("参数错误，存在非法ID", ENTITY_NAME, "idexists");
        }
        complaintSuggestionDTO = complaintSuggestionService.save(complaintSuggestionDTO);
        return ResponseEntity.created(new URI("/api/complaint/suggestions/" + complaintSuggestionDTO.getId()))
            .headers(HeaderUtil.createEntityCreationAlert(applicationName, true, ENTITY_NAME, complaintSuggestionDTO.getId().toString()))
            .body(complaintSuggestionDTO);
    }

    /**
     * 方法名称：partialUpdateComplaintSuggestion
     * 描述：部分更新已存在的投诉与建议记录
     *
     * @param id                     投诉与建议记录的ID
     * @param complaintSuggestionDTO 要更新的投诉与建议记录DTO
     * @return {@link ResponseEntity}，状态为 {@code 200 (OK)}，响应体为更新后的投诉与建议记录DTO；
     * 如果投诉与建议记录DTO无效，则状态为 {@code 400 (Bad Request)}；
     * 如果投诉与建议记录DTO无法更新，则状态为 {@code 500 (Internal Server Error)}
     * @since 1.0
     */
    @PatchMapping(value = "/{id}", consumes = {"application/json", "application/merge-patch+json"})
    public ResponseEntity<ComplaintSuggestionDTO> partialUpdateComplaintSuggestion(
        @PathVariable(value = "id", required = false) final Long id,
        @NotNull @RequestBody ComplaintSuggestionDTO complaintSuggestionDTO
    ) {
        LOG.debug("REST request to partial update ComplaintSuggestion partially : {}, {}", id, complaintSuggestionDTO);
        if (complaintSuggestionDTO.getId() == null) {
            throw new BadRequestAlertException("参数错误，不存在ID", ENTITY_NAME, "id null");
        }
        if (!Objects.equals(id, complaintSuggestionDTO.getId())) {
            throw new BadRequestAlertException("参数错误，ID不匹配", ENTITY_NAME, "id invalid");
        }

        if (!complaintSuggestionRepository.existsById(id)) {
            throw new BadRequestAlertException("记录不存在", ENTITY_NAME, "id not found");
        }

        Optional<ComplaintSuggestionDTO> result = complaintSuggestionService.partialUpdate(complaintSuggestionDTO);

        return ResponseUtil.wrapOrNotFound(
            result,
            HeaderUtil.createEntityUpdateAlert(applicationName, true, ENTITY_NAME, complaintSuggestionDTO.getId().toString())
        );
    }

    /**
     * 方法名称：getAllComplaintSuggestions
     * 描述：获取所有投诉与建议记录
     *
     * @param pageable 分页信息
     * @return {@link ResponseEntity}，状态为 {@code 200 (OK)}，响应体为投诉与建议记录DTO的分页列表
     * @since 1.0
     */
    @GetMapping("")
    public ResponseEntity<Page<ComplaintSuggestionDTO>> getAllComplaintSuggestions(@org.springdoc.core.annotations.ParameterObject Pageable pageable) {
        LOG.debug("REST request to get a page of ComplaintSuggestions");
        Page<ComplaintSuggestionDTO> page = complaintSuggestionService.findAll(pageable);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(ServletUriComponentsBuilder.fromCurrentRequest(), page);
        return ResponseEntity.ok().headers(headers).body(page);
    }

    /**
     * 方法名称：getComplaintSuggestion
     * 描述：根据ID获取投诉与建议记录
     *
     * @param id 投诉与建议记录的ID
     * @return {@link ResponseEntity}，状态为 {@code 200 (OK)}，响应体为投诉与建议记录DTO；
     * 如果记录不存在，则状态为 {@code 404 (Not Found)}
     * @since 1.0
     */
    @GetMapping("/{id}")
    public ResponseEntity<ComplaintSuggestionDTO> getComplaintSuggestion(@PathVariable("id") Long id) {
        LOG.debug("REST request to get ComplaintSuggestion : {}", id);
        Optional<ComplaintSuggestionDTO> complaintSuggestionDTO = complaintSuggestionService.findOne(id);
        return ResponseUtil.wrapOrNotFound(complaintSuggestionDTO);
    }

    /**
     * 方法名称：deleteComplaintSuggestion
     * 描述：根据ID删除投诉与建议记录
     *
     * @param id 投诉与建议记录的ID
     * @return {@link ResponseEntity}，状态为 {@code 204 (NO_CONTENT)}
     * @since 1.0
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteComplaintSuggestion(@PathVariable("id") Long id) {
        LOG.debug("REST request to delete ComplaintSuggestion : {}", id);
        complaintSuggestionService.delete(id);
        return ResponseEntity.noContent()
            .headers(HeaderUtil.createEntityDeletionAlert(applicationName, true, ENTITY_NAME, id.toString()))
            .build();
    }
}

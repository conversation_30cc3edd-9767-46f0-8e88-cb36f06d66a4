package com.whiskerguard.organization.web.rest;

import com.whiskerguard.organization.repository.NewsReadRecordRepository;
import com.whiskerguard.organization.service.NewsReadRecordService;
import com.whiskerguard.organization.service.dto.NewsReadRecordDTO;
import com.whiskerguard.organization.web.rest.errors.BadRequestAlertException;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.Objects;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;
import tech.jhipster.web.util.HeaderUtil;
import tech.jhipster.web.util.PaginationUtil;
import tech.jhipster.web.util.ResponseUtil;

/**
 * 新闻阅读记录管理的REST控制器
 */
@RestController
@RequestMapping("/api/news/read/records")
public class NewsReadRecordResource {

    private static final Logger LOG = LoggerFactory.getLogger(NewsReadRecordResource.class);

    private static final String ENTITY_NAME = "whiskerguardOrgServiceNewsReadRecord";

    @Value("${jhipster.clientApp.name}")
    private String applicationName;

    private final NewsReadRecordService newsReadRecordService;

    private final NewsReadRecordRepository newsReadRecordRepository;

    public NewsReadRecordResource(NewsReadRecordService newsReadRecordService, NewsReadRecordRepository newsReadRecordRepository) {
        this.newsReadRecordService = newsReadRecordService;
        this.newsReadRecordRepository = newsReadRecordRepository;
    }

    /**
     * {@code POST  /news-read-records} : 创建新闻阅读记录
     *
     * @param newsReadRecordDTO 要创建的新闻阅读记录DTO
     * @return {@link ResponseEntity}，状态为 {@code 201 (Created)}，响应体为新创建的新闻阅读记录DTO；
     *         如果新闻阅读记录已经有ID，则状态为 {@code 400 (Bad Request)}
     * @throws URISyntaxException 如果Location URI语法不正确
     */
    @PostMapping("/create")
    public ResponseEntity<NewsReadRecordDTO> createNewsReadRecord(@Valid @RequestBody NewsReadRecordDTO newsReadRecordDTO)
        throws URISyntaxException {
        LOG.debug("REST request to save NewsReadRecord : {}", newsReadRecordDTO);
        if (newsReadRecordDTO.getId() != null) {
            throw new BadRequestAlertException("新的新闻阅读记录不能有ID", ENTITY_NAME, "idexists");
        }
        newsReadRecordDTO = newsReadRecordService.save(newsReadRecordDTO);
        return ResponseEntity.created(new URI("/api/news/read/records/" + newsReadRecordDTO.getId()))
            .headers(HeaderUtil.createEntityCreationAlert(applicationName, true, ENTITY_NAME, newsReadRecordDTO.getId().toString()))
            .body(newsReadRecordDTO);
    }

    /**
     * {@code PUT  /news-read-records/:id} : 更新已存在的新闻阅读记录
     *
     * @param id 要保存的新闻阅读记录DTO的ID
     * @param newsReadRecordDTO 要更新的新闻阅读记录DTO
     * @return {@link ResponseEntity}，状态为 {@code 200 (OK)}，响应体为更新后的新闻阅读记录DTO；
     *         如果新闻阅读记录DTO无效，则状态为 {@code 400 (Bad Request)}；
     *         如果新闻阅读记录DTO无法更新，则状态为 {@code 500 (Internal Server Error)}
     * @throws URISyntaxException 如果Location URI语法不正确
     */
    @PutMapping("/update/{id}")
    public ResponseEntity<NewsReadRecordDTO> updateNewsReadRecord(
        @PathVariable(value = "id", required = false) final Long id,
        @Valid @RequestBody NewsReadRecordDTO newsReadRecordDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to update NewsReadRecord : {}, {}", id, newsReadRecordDTO);
        if (newsReadRecordDTO.getId() == null) {
            throw new BadRequestAlertException("无效的ID", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, newsReadRecordDTO.getId())) {
            throw new BadRequestAlertException("无效的ID", ENTITY_NAME, "idinvalid");
        }

        if (!newsReadRecordRepository.existsById(id)) {
            throw new BadRequestAlertException("实体未找到", ENTITY_NAME, "idnotfound");
        }

        newsReadRecordDTO = newsReadRecordService.update(newsReadRecordDTO);
        return ResponseEntity.ok()
            .headers(HeaderUtil.createEntityUpdateAlert(applicationName, true, ENTITY_NAME, newsReadRecordDTO.getId().toString()))
            .body(newsReadRecordDTO);
    }

    /**
     * {@code PATCH  /news-read-records/:id} : 部分更新已存在的新闻阅读记录，null字段将被忽略
     *
     * @param id 要保存的新闻阅读记录DTO的ID
     * @param newsReadRecordDTO 要更新的新闻阅读记录DTO
     * @return {@link ResponseEntity}，状态为 {@code 200 (OK)}，响应体为更新后的新闻阅读记录DTO；
     *         如果新闻阅读记录DTO无效，则状态为 {@code 400 (Bad Request)}；
     *         如果找不到新闻阅读记录DTO，则状态为 {@code 404 (Not Found)}；
     *         如果新闻阅读记录DTO无法更新，则状态为 {@code 500 (Internal Server Error)}
     * @throws URISyntaxException 如果Location URI语法不正确
     */
    @PatchMapping(value = "/partial/{id}", consumes = { "application/json", "application/merge-patch+json" })
    public ResponseEntity<NewsReadRecordDTO> partialUpdateNewsReadRecord(
        @PathVariable(value = "id", required = false) final Long id,
        @NotNull @Valid @RequestBody NewsReadRecordDTO newsReadRecordDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to partial update NewsReadRecord partially : {}, {}", id, newsReadRecordDTO);
        if (newsReadRecordDTO.getId() == null) {
            throw new BadRequestAlertException("无效的ID", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, newsReadRecordDTO.getId())) {
            throw new BadRequestAlertException("无效的ID", ENTITY_NAME, "idinvalid");
        }

        if (!newsReadRecordRepository.existsById(id)) {
            throw new BadRequestAlertException("实体未找到", ENTITY_NAME, "idnotfound");
        }

        Optional<NewsReadRecordDTO> result = newsReadRecordService.partialUpdate(newsReadRecordDTO);

        return ResponseUtil.wrapOrNotFound(
            result,
            HeaderUtil.createEntityUpdateAlert(applicationName, true, ENTITY_NAME, newsReadRecordDTO.getId().toString())
        );
    }

    /**
     * {@code GET  /news-read-records} : 获取所有新闻阅读记录
     *
     * @param pageable 分页信息
     * @param eagerload 是否急切加载关联实体的标志（适用于多对多关系）
     * @return {@link ResponseEntity}，状态为 {@code 200 (OK)}，响应体为新闻阅读记录分页数据
     */
    @GetMapping("/list")
    public ResponseEntity<Page<NewsReadRecordDTO>> getAllNewsReadRecords(
        @org.springdoc.core.annotations.ParameterObject Pageable pageable,
        @RequestParam(name = "eagerload", required = false, defaultValue = "true") boolean eagerload
    ) {
        LOG.debug("REST request to get a page of NewsReadRecords");
        Page<NewsReadRecordDTO> page;
        if (eagerload) {
            page = newsReadRecordService.findAllWithEagerRelationships(pageable);
        } else {
            page = newsReadRecordService.findAll(pageable);
        }
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(ServletUriComponentsBuilder.fromCurrentRequest(), page);
        return ResponseEntity.ok().headers(headers).body(page);
    }

    /**
     * {@code GET  /news-read-records/:id} : 获取指定ID的新闻阅读记录
     *
     * @param id 要获取的新闻阅读记录DTO的ID
     * @return {@link ResponseEntity}，状态为 {@code 200 (OK)}，响应体为新闻阅读记录DTO；
     *         如果找不到新闻阅读记录，则状态为 {@code 404 (Not Found)}
     */
    @GetMapping("/detail/{id}")
    public ResponseEntity<NewsReadRecordDTO> getNewsReadRecord(@PathVariable("id") Long id) {
        LOG.debug("REST request to get NewsReadRecord : {}", id);
        Optional<NewsReadRecordDTO> newsReadRecordDTO = newsReadRecordService.findOne(id);
        return ResponseUtil.wrapOrNotFound(newsReadRecordDTO);
    }

    /**
     * {@code DELETE  /news-read-records/:id} : 删除指定ID的新闻阅读记录
     *
     * @param id 要删除的新闻阅读记录DTO的ID
     * @return {@link ResponseEntity}，状态为 {@code 204 (NO_CONTENT)}
     */
    @DeleteMapping("/delete/{id}")
    public ResponseEntity<Void> deleteNewsReadRecord(@PathVariable("id") Long id) {
        LOG.debug("REST request to delete NewsReadRecord : {}", id);
        newsReadRecordService.delete(id);
        return ResponseEntity.noContent()
            .headers(HeaderUtil.createEntityDeletionAlert(applicationName, true, ENTITY_NAME, id.toString()))
            .build();
    }
}

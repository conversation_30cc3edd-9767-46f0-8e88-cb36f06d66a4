package com.whiskerguard.organization.web.rest.errors;

import java.net.URI;

/**
 * 错误常量类
 * 定义系统中使用的错误常量和错误类型URI
 */
public final class ErrorConstants {

    /**
     * 并发失败错误键
     * 用于标识并发操作导致的错误
     */
    public static final String ERR_CONCURRENCY_FAILURE = "error.concurrencyFailure";

    /**
     * 验证错误键
     * 用于标识数据验证失败的错误
     */
    public static final String ERR_VALIDATION = "error.validation";

    /**
     * 问题基础URL
     * 用于构建错误类型URI
     */
    public static final String PROBLEM_BASE_URL = "https://www.jhipster.tech/problem";

    /**
     * 默认错误类型URI
     * 用于一般性错误消息
     */
    public static final URI DEFAULT_TYPE = URI.create(PROBLEM_BASE_URL + "/problem-with-message");

    /**
     * 约束违反错误类型URI
     * 用于数据验证约束违反错误
     */
    public static final URI CONSTRAINT_VIOLATION_TYPE = URI.create(PROBLEM_BASE_URL + "/constraint-violation");

    /**
     * 私有构造函数
     * 防止实例化常量类
     */
    private ErrorConstants() {}
}

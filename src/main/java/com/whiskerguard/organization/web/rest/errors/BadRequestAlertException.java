package com.whiskerguard.organization.web.rest.errors;

import org.springframework.http.HttpStatus;
import org.springframework.web.ErrorResponseException;
import tech.jhipster.web.rest.errors.ProblemDetailWithCause;
import tech.jhipster.web.rest.errors.ProblemDetailWithCause.ProblemDetailWithCauseBuilder;

import java.net.URI;

/**
 * 错误请求警告异常
 * 用于处理客户端发送的错误请求，并提供友好的错误信息
 */
public class BadRequestAlertException extends ErrorResponseException {

    private static final long serialVersionUID = 1L;

    /**
     * 实体名称，用于标识错误发生的实体
     */
    private final String entityName;

    /**
     * 错误键，用于国际化错误消息
     */
    private final String errorKey;

    /**
     * 构造函数，使用默认错误类型
     *
     * @param defaultMessage 默认错误消息
     * @param entityName     实体名称
     * @param errorKey       错误键
     */
    public BadRequestAlertException(String defaultMessage, String entityName, String errorKey) {
        this(ErrorConstants.DEFAULT_TYPE, defaultMessage, entityName, errorKey);
    }

    /**
     * 构造函数，使用指定错误类型
     *
     * @param type           错误类型URI
     * @param defaultMessage 默认错误消息
     * @param entityName     实体名称
     * @param errorKey       错误键
     */
    public BadRequestAlertException(URI type, String defaultMessage, String entityName, String errorKey) {
        super(
            HttpStatus.BAD_REQUEST,
            ProblemDetailWithCauseBuilder.instance()
                .withStatus(HttpStatus.BAD_REQUEST.value())
                .withType(type)
                .withTitle(defaultMessage)
                .withProperty("message", "error." + errorKey)
                .withProperty("params", entityName)
                .build(),
            null
        );
        this.entityName = entityName;
        this.errorKey = errorKey;
    }

    /**
     * 获取实体名称
     *
     * @return 实体名称
     */
    public String getEntityName() {
        return entityName;
    }

    /**
     * 获取错误键
     *
     * @return 错误键
     */
    public String getErrorKey() {
        return errorKey;
    }

    /**
     * 获取带原因的问题详情
     *
     * @return 带原因的问题详情对象
     */
    public ProblemDetailWithCause getProblemDetailWithCause() {
        return (ProblemDetailWithCause) this.getBody();
    }
}

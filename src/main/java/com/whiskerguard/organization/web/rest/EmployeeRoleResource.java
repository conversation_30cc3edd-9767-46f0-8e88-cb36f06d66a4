package com.whiskerguard.organization.web.rest;

import com.whiskerguard.organization.repository.EmployeeRoleRepository;
import com.whiskerguard.organization.service.EmployeeRoleService;
import com.whiskerguard.organization.service.dto.EmployeeRoleDTO;
import com.whiskerguard.organization.web.rest.errors.BadRequestAlertException;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.Objects;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;
import tech.jhipster.web.util.HeaderUtil;
import tech.jhipster.web.util.PaginationUtil;
import tech.jhipster.web.util.ResponseUtil;

/**
 * REST controller for managing {@link com.whiskerguard.organization.domain.EmployeeRole}.
 * 员工角色关系的REST控制器
 * 提供员工与角色关联关系的CRUD操作API接口
 */
@RestController
@RequestMapping("/api/employee/roles")
public class EmployeeRoleResource {

    /**
     * 日志记录器
     */
    private static final Logger LOG = LoggerFactory.getLogger(EmployeeRoleResource.class);

    /**
     * 实体名称，用于错误消息
     */
    private static final String ENTITY_NAME = "whiskerguardOrgServiceEmployeeRole";

    /**
     * 应用名称，从配置中获取
     */
    @Value("${jhipster.clientApp.name}")
    private String applicationName;

    /**
     * 员工角色服务
     */
    private final EmployeeRoleService employeeRoleService;

    /**
     * 员工角色仓库
     */
    private final EmployeeRoleRepository employeeRoleRepository;

    /**
     * 构造函数
     * @param employeeRoleService 员工角色服务
     * @param employeeRoleRepository 员工角色仓库
     */
    public EmployeeRoleResource(EmployeeRoleService employeeRoleService, EmployeeRoleRepository employeeRoleRepository) {
        this.employeeRoleService = employeeRoleService;
        this.employeeRoleRepository = employeeRoleRepository;
    }

    /**
     * {@code POST  /employee-roles} : Create a new employeeRole.
     * 创建新的员工角色关系
     *
     * @param employeeRoleDTO the employeeRoleDTO to create. 要创建的员工角色关系DTO
     * @return the {@link ResponseEntity} with status {@code 201 (Created)} and with body the new employeeRoleDTO, or with status {@code 400 (Bad Request)} if the employeeRole has already an ID.
     *         返回状态码为201的响应和新创建的员工角色关系DTO，如果员工角色关系已有ID则返回400错误
     * @throws URISyntaxException if the Location URI syntax is incorrect. 如果位置URI语法不正确
     */
    @PostMapping("")
    public ResponseEntity<EmployeeRoleDTO> createEmployeeRole(@Valid @RequestBody EmployeeRoleDTO employeeRoleDTO)
        throws URISyntaxException {
        LOG.debug("REST request to save EmployeeRole : {}", employeeRoleDTO);
        if (employeeRoleDTO.getId() != null) {
            throw new BadRequestAlertException("新的员工角色关系不能有ID", ENTITY_NAME, "idexists");
        }
        employeeRoleDTO = employeeRoleService.save(employeeRoleDTO);
        return ResponseEntity.created(new URI("/api/employee/roles/" + employeeRoleDTO.getId()))
            .headers(HeaderUtil.createEntityCreationAlert(applicationName, true, ENTITY_NAME, employeeRoleDTO.getId().toString()))
            .body(employeeRoleDTO);
    }

    /**
     * {@code PUT  /employee-roles/:id} : Updates an existing employeeRole.
     * 更新现有的员工角色关系
     *
     * @param id the id of the employeeRoleDTO to save. 要保存的员工角色关系DTO的ID
     * @param employeeRoleDTO the employeeRoleDTO to update. 要更新的员工角色关系DTO
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated employeeRoleDTO,
     * or with status {@code 400 (Bad Request)} if the employeeRoleDTO is not valid,
     * or with status {@code 500 (Internal Server Error)} if the employeeRoleDTO couldn't be updated.
     * 返回状态码为200的响应和更新后的员工角色关系DTO，如果员工角色关系DTO无效则返回400错误，如果无法更新则返回500错误
     * @throws URISyntaxException if the Location URI syntax is incorrect. 如果位置URI语法不正确
     */
    @PutMapping("/{id}")
    public ResponseEntity<EmployeeRoleDTO> updateEmployeeRole(
        @PathVariable(value = "id", required = false) final Long id,
        @Valid @RequestBody EmployeeRoleDTO employeeRoleDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to update EmployeeRole : {}, {}", id, employeeRoleDTO);
        if (employeeRoleDTO.getId() == null) {
            throw new BadRequestAlertException("无效的ID", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, employeeRoleDTO.getId())) {
            throw new BadRequestAlertException("无效的ID", ENTITY_NAME, "idinvalid");
        }

        if (!employeeRoleRepository.existsById(id)) {
            throw new BadRequestAlertException("实体未找到", ENTITY_NAME, "idnotfound");
        }

        employeeRoleDTO = employeeRoleService.update(employeeRoleDTO);
        return ResponseEntity.ok()
            .headers(HeaderUtil.createEntityUpdateAlert(applicationName, true, ENTITY_NAME, employeeRoleDTO.getId().toString()))
            .body(employeeRoleDTO);
    }

    /**
     * {@code PATCH  /employee-roles/:id} : Partial updates given fields of an existing employeeRole, field will ignore if it is null
     * 部分更新现有员工角色关系的指定字段，如果字段为null则忽略
     *
     * @param id the id of the employeeRoleDTO to save. 要保存的员工角色关系DTO的ID
     * @param employeeRoleDTO the employeeRoleDTO to update. 要更新的员工角色关系DTO
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated employeeRoleDTO,
     * or with status {@code 400 (Bad Request)} if the employeeRoleDTO is not valid,
     * or with status {@code 404 (Not Found)} if the employeeRoleDTO is not found,
     * or with status {@code 500 (Internal Server Error)} if the employeeRoleDTO couldn't be updated.
     * 返回状态码为200的响应和更新后的员工角色关系DTO，如果员工角色关系DTO无效则返回400错误，如果找不到员工角色关系则返回404错误，如果无法更新则返回500错误
     * @throws URISyntaxException if the Location URI syntax is incorrect. 如果位置URI语法不正确
     */
    @PatchMapping(value = "/{id}", consumes = { "application/json", "application/merge-patch+json" })
    public ResponseEntity<EmployeeRoleDTO> partialUpdateEmployeeRole(
        @PathVariable(value = "id", required = false) final Long id,
        @NotNull @Valid @RequestBody EmployeeRoleDTO employeeRoleDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to partial update EmployeeRole partially : {}, {}", id, employeeRoleDTO);
        if (employeeRoleDTO.getId() == null) {
            throw new BadRequestAlertException("无效的ID", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, employeeRoleDTO.getId())) {
            throw new BadRequestAlertException("无效的ID", ENTITY_NAME, "idinvalid");
        }

        if (!employeeRoleRepository.existsById(id)) {
            throw new BadRequestAlertException("实体未找到", ENTITY_NAME, "idnotfound");
        }

        Optional<EmployeeRoleDTO> result = employeeRoleService.partialUpdate(employeeRoleDTO);

        return ResponseUtil.wrapOrNotFound(
            result,
            HeaderUtil.createEntityUpdateAlert(applicationName, true, ENTITY_NAME, employeeRoleDTO.getId().toString())
        );
    }

    /**
     * {@code GET  /employee-roles} : get all the employeeRoles.
     * 获取所有员工角色关系
     *
     * @param pageable the pagination information. 分页信息
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and the page of employeeRoles in body.
     *         返回状态码为200的响应和员工角色关系分页数据
     */
    @GetMapping("")
    public ResponseEntity<Page<EmployeeRoleDTO>> getAllEmployeeRoles(@org.springdoc.core.annotations.ParameterObject Pageable pageable) {
        LOG.debug("REST request to get a page of EmployeeRoles");
        Page<EmployeeRoleDTO> page = employeeRoleService.findAll(pageable);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(ServletUriComponentsBuilder.fromCurrentRequest(), page);
        return ResponseEntity.ok().headers(headers).body(page);
    }

    /**
     * {@code GET  /employee-roles/:id} : get the "id" employeeRole.
     * 获取指定ID的员工角色关系
     *
     * @param id the id of the employeeRoleDTO to retrieve. 要检索的员工角色关系DTO的ID
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the employeeRoleDTO, or with status {@code 404 (Not Found)}.
     *         返回状态码为200的响应和员工角色关系DTO，如果找不到则返回404错误
     */
    @GetMapping("/{id}")
    public ResponseEntity<EmployeeRoleDTO> getEmployeeRole(@PathVariable("id") Long id) {
        LOG.debug("REST request to get EmployeeRole : {}", id);
        Optional<EmployeeRoleDTO> employeeRoleDTO = employeeRoleService.findOne(id);
        return ResponseUtil.wrapOrNotFound(employeeRoleDTO);
    }

    /**
     * {@code DELETE  /employee-roles/:id} : delete the "id" employeeRole.
     * 删除指定ID的员工角色关系
     *
     * @param id the id of the employeeRoleDTO to delete. 要删除的员工角色关系DTO的ID
     * @return the {@link ResponseEntity} with status {@code 204 (NO_CONTENT)}.
     *         返回状态码为204的响应（无内容）
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteEmployeeRole(@PathVariable("id") Long id) {
        LOG.debug("REST request to delete EmployeeRole : {}", id);
        employeeRoleService.delete(id);
        return ResponseEntity.noContent()
            .headers(HeaderUtil.createEntityDeletionAlert(applicationName, true, ENTITY_NAME, id.toString()))
            .build();
    }
}

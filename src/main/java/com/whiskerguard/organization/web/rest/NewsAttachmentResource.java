package com.whiskerguard.organization.web.rest;

import com.whiskerguard.organization.repository.NewsAttachmentRepository;
import com.whiskerguard.organization.service.NewsAttachmentService;
import com.whiskerguard.organization.service.dto.NewsAttachmentDTO;
import com.whiskerguard.organization.web.rest.errors.BadRequestAlertException;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.Objects;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;
import tech.jhipster.web.util.HeaderUtil;
import tech.jhipster.web.util.PaginationUtil;
import tech.jhipster.web.util.ResponseUtil;

/**
 * 新闻附件管理的REST控制器
 */
@RestController
@RequestMapping("/api/news/attachments")
public class NewsAttachmentResource {

    private static final Logger LOG = LoggerFactory.getLogger(NewsAttachmentResource.class);

    private static final String ENTITY_NAME = "whiskerguardOrgServiceNewsAttachment";

    @Value("${jhipster.clientApp.name}")
    private String applicationName;

    private final NewsAttachmentService newsAttachmentService;

    private final NewsAttachmentRepository newsAttachmentRepository;

    public NewsAttachmentResource(NewsAttachmentService newsAttachmentService, NewsAttachmentRepository newsAttachmentRepository) {
        this.newsAttachmentService = newsAttachmentService;
        this.newsAttachmentRepository = newsAttachmentRepository;
    }

    /**
     * {@code POST  /news-attachments} : 创建新闻附件
     *
     * @param newsAttachmentDTO 要创建的新闻附件DTO
     * @return {@link ResponseEntity}，状态为 {@code 201 (Created)}，响应体为新创建的新闻附件DTO；
     *         如果新闻附件已经有ID，则状态为 {@code 400 (Bad Request)}
     * @throws URISyntaxException 如果Location URI语法不正确
     */
    @PostMapping("/create")
    public ResponseEntity<NewsAttachmentDTO> createNewsAttachment(@Valid @RequestBody NewsAttachmentDTO newsAttachmentDTO)
        throws URISyntaxException {
        LOG.debug("REST request to save NewsAttachment : {}", newsAttachmentDTO);
        if (newsAttachmentDTO.getId() != null) {
            throw new BadRequestAlertException("新的新闻附件不能有ID", ENTITY_NAME, "idexists");
        }
        newsAttachmentDTO = newsAttachmentService.save(newsAttachmentDTO);
        return ResponseEntity.created(new URI("/api/news/attachments/" + newsAttachmentDTO.getId()))
            .headers(HeaderUtil.createEntityCreationAlert(applicationName, true, ENTITY_NAME, newsAttachmentDTO.getId().toString()))
            .body(newsAttachmentDTO);
    }

    /**
     * {@code PUT  /news-attachments/:id} : 更新已存在的新闻附件
     *
     * @param id 要保存的新闻附件DTO的ID
     * @param newsAttachmentDTO 要更新的新闻附件DTO
     * @return {@link ResponseEntity}，状态为 {@code 200 (OK)}，响应体为更新后的新闻附件DTO；
     *         如果新闻附件DTO无效，则状态为 {@code 400 (Bad Request)}；
     *         如果新闻附件DTO无法更新，则状态为 {@code 500 (Internal Server Error)}
     * @throws URISyntaxException 如果Location URI语法不正确
     */
    @PutMapping("/update/{id}")
    public ResponseEntity<NewsAttachmentDTO> updateNewsAttachment(
        @PathVariable(value = "id", required = false) final Long id,
        @Valid @RequestBody NewsAttachmentDTO newsAttachmentDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to update NewsAttachment : {}, {}", id, newsAttachmentDTO);
        if (newsAttachmentDTO.getId() == null) {
            throw new BadRequestAlertException("无效的ID", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, newsAttachmentDTO.getId())) {
            throw new BadRequestAlertException("无效的ID", ENTITY_NAME, "idinvalid");
        }

        if (!newsAttachmentRepository.existsById(id)) {
            throw new BadRequestAlertException("实体未找到", ENTITY_NAME, "idnotfound");
        }

        newsAttachmentDTO = newsAttachmentService.update(newsAttachmentDTO);
        return ResponseEntity.ok()
            .headers(HeaderUtil.createEntityUpdateAlert(applicationName, true, ENTITY_NAME, newsAttachmentDTO.getId().toString()))
            .body(newsAttachmentDTO);
    }

    /**
     * {@code PATCH  /news-attachments/:id} : 部分更新已存在的新闻附件，null字段将被忽略
     *
     * @param id 要保存的新闻附件DTO的ID
     * @param newsAttachmentDTO 要更新的新闻附件DTO
     * @return {@link ResponseEntity}，状态为 {@code 200 (OK)}，响应体为更新后的新闻附件DTO；
     *         如果新闻附件DTO无效，则状态为 {@code 400 (Bad Request)}；
     *         如果找不到新闻附件DTO，则状态为 {@code 404 (Not Found)}；
     *         如果新闻附件DTO无法更新，则状态为 {@code 500 (Internal Server Error)}
     * @throws URISyntaxException 如果Location URI语法不正确
     */
    @PatchMapping(value = "/partial/{id}", consumes = { "application/json", "application/merge-patch+json" })
    public ResponseEntity<NewsAttachmentDTO> partialUpdateNewsAttachment(
        @PathVariable(value = "id", required = false) final Long id,
        @NotNull @Valid @RequestBody NewsAttachmentDTO newsAttachmentDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to partial update NewsAttachment partially : {}, {}", id, newsAttachmentDTO);
        if (newsAttachmentDTO.getId() == null) {
            throw new BadRequestAlertException("无效的ID", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, newsAttachmentDTO.getId())) {
            throw new BadRequestAlertException("无效的ID", ENTITY_NAME, "idinvalid");
        }

        if (!newsAttachmentRepository.existsById(id)) {
            throw new BadRequestAlertException("实体未找到", ENTITY_NAME, "idnotfound");
        }

        Optional<NewsAttachmentDTO> result = newsAttachmentService.partialUpdate(newsAttachmentDTO);

        return ResponseUtil.wrapOrNotFound(
            result,
            HeaderUtil.createEntityUpdateAlert(applicationName, true, ENTITY_NAME, newsAttachmentDTO.getId().toString())
        );
    }

    /**
     * {@code GET  /news-attachments} : 获取所有新闻附件
     *
     * @param pageable 分页信息
     * @param eagerload 是否急切加载关联实体的标志（适用于多对多关系）
     * @return {@link ResponseEntity}，状态为 {@code 200 (OK)}，响应体为新闻附件分页数据
     */
    @GetMapping("/list")
    public ResponseEntity<Page<NewsAttachmentDTO>> getAllNewsAttachments(
        @org.springdoc.core.annotations.ParameterObject Pageable pageable,
        @RequestParam(name = "eagerload", required = false, defaultValue = "true") boolean eagerload
    ) {
        LOG.debug("REST request to get a page of NewsAttachments");
        Page<NewsAttachmentDTO> page;
        if (eagerload) {
            page = newsAttachmentService.findAllWithEagerRelationships(pageable);
        } else {
            page = newsAttachmentService.findAll(pageable);
        }
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(ServletUriComponentsBuilder.fromCurrentRequest(), page);
        return ResponseEntity.ok().headers(headers).body(page);
    }

    /**
     * {@code GET  /news-attachments/:id} : 获取指定ID的新闻附件
     *
     * @param id 要获取的新闻附件DTO的ID
     * @return {@link ResponseEntity}，状态为 {@code 200 (OK)}，响应体为新闻附件DTO；
     *         如果找不到新闻附件，则状态为 {@code 404 (Not Found)}
     */
    @GetMapping("/detail/{id}")
    public ResponseEntity<NewsAttachmentDTO> getNewsAttachment(@PathVariable("id") Long id) {
        LOG.debug("REST request to get NewsAttachment : {}", id);
        Optional<NewsAttachmentDTO> newsAttachmentDTO = newsAttachmentService.findOne(id);
        return ResponseUtil.wrapOrNotFound(newsAttachmentDTO);
    }

    /**
     * {@code DELETE  /news-attachments/:id} : 删除指定ID的新闻附件
     *
     * @param id 要删除的新闻附件DTO的ID
     * @return {@link ResponseEntity}，状态为 {@code 204 (NO_CONTENT)}
     */
    @DeleteMapping("/delete/{id}")
    public ResponseEntity<Void> deleteNewsAttachment(@PathVariable("id") Long id) {
        LOG.debug("REST request to delete NewsAttachment : {}", id);
        newsAttachmentService.delete(id);
        return ResponseEntity.noContent()
            .headers(HeaderUtil.createEntityDeletionAlert(applicationName, true, ENTITY_NAME, id.toString()))
            .build();
    }
}

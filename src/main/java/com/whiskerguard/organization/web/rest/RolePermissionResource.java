package com.whiskerguard.organization.web.rest;

import com.whiskerguard.organization.repository.RolePermissionRepository;
import com.whiskerguard.organization.service.RolePermissionService;
import com.whiskerguard.organization.service.dto.RolePermissionDTO;
import com.whiskerguard.organization.web.rest.errors.BadRequestAlertException;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;
import tech.jhipster.web.util.HeaderUtil;
import tech.jhipster.web.util.PaginationUtil;
import tech.jhipster.web.util.ResponseUtil;

import java.net.URI;
import java.net.URISyntaxException;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * REST controller for managing {@link com.whiskerguard.organization.domain.RolePermission}.
 * 角色权限关系的REST控制器
 * 提供角色与权限关联关系的CRUD操作API接口
 */
@RestController
@RequestMapping("/api/role/permissions")
public class RolePermissionResource {

    /**
     * 日志记录器
     */
    private static final Logger LOG = LoggerFactory.getLogger(RolePermissionResource.class);

    /**
     * 实体名称，用于错误消息
     */
    private static final String ENTITY_NAME = "whiskerguardOrgServiceRolePermission";

    /**
     * 应用名称，从配置中获取
     */
    @Value("${jhipster.clientApp.name}")
    private String applicationName;

    /**
     * 角色权限服务
     */
    private final RolePermissionService rolePermissionService;

    /**
     * 角色权限仓库
     */
    private final RolePermissionRepository rolePermissionRepository;

    /**
     * 构造函数
     *
     * @param rolePermissionService    角色权限服务
     * @param rolePermissionRepository 角色权限仓库
     */
    public RolePermissionResource(RolePermissionService rolePermissionService, RolePermissionRepository rolePermissionRepository) {
        this.rolePermissionService = rolePermissionService;
        this.rolePermissionRepository = rolePermissionRepository;
    }

    /**
     * {@code POST  /role-permissions} : Create a new rolePermission.
     * 创建新的角色权限关系
     *
     * @param rolePermissionDTO the rolePermissionDTO to create. 要创建的角色权限关系DTO
     * @return the {@link ResponseEntity} with status {@code 201 (Created)} and with body the new rolePermissionDTO, or with status {@code 400 (Bad Request)} if the rolePermission has already an ID.
     * 返回状态码为201的响应和新创建的角色权限关系DTO，如果角色权限关系已有ID则返回400错误
     * @throws URISyntaxException if the Location URI syntax is incorrect. 如果位置URI语法不正确
     */
    @PostMapping("")
    public ResponseEntity<RolePermissionDTO> createRolePermission(@Valid @RequestBody RolePermissionDTO rolePermissionDTO)
        throws URISyntaxException {
        LOG.debug("REST request to save RolePermission : {}", rolePermissionDTO);
        if (rolePermissionDTO.getId() != null) {
            throw new BadRequestAlertException("新的角色权限关系不能有ID", ENTITY_NAME, "idexists");
        }
        rolePermissionDTO = rolePermissionService.save(rolePermissionDTO);
        return ResponseEntity.created(new URI("/api/role-permissions/" + rolePermissionDTO.getId()))
            .headers(HeaderUtil.createEntityCreationAlert(applicationName, true, ENTITY_NAME, rolePermissionDTO.getId().toString()))
            .body(rolePermissionDTO);
    }

    /**
     * {@code POST  /role-permissions/batch} : Create multiple new rolePermissions.
     * 批量创建新的角色权限关系
     *
     * @param list the list of rolePermissionDTO to create. 要创建的角色权限关系DTO列表
     * @return the {@link ResponseEntity} with status {@code 201 (Created)} and with body the new list, or with status {@code 400 (Bad Request)} if any rolePermission has already an ID.
     * 返回状态码为201的响应和新创建的角色权限关系DTO列表，如果任何角色权限关系已有ID则返回400错误
     * @throws URISyntaxException if the Location URI syntax is incorrect. 如果位置URI语法不正确
     */
    @PostMapping("/batch")
    public ResponseEntity<List<RolePermissionDTO>> createRolePermissionsBatch(@Valid @RequestBody List<RolePermissionDTO> list)
        throws URISyntaxException {
        LOG.debug("REST request to batch save RolePermissions : {}", list.size());

        // 验证所有DTO都没有ID
        for (RolePermissionDTO rolePermissionDTO : list) {
            if (rolePermissionDTO.getId() != null) {
                throw new BadRequestAlertException("新的角色权限关系不能有ID", ENTITY_NAME, "idexists");
            }
        }

        List<RolePermissionDTO> result = rolePermissionService.saveAll(list);
        return ResponseEntity.created(new URI("/api/role-permissions/batch"))
            .headers(HeaderUtil.createAlert(applicationName, "rolePermissionManagement.created.batch", String.valueOf(result.size())))
            .body(result);
    }

    /**
     * {@code PUT  /role-permissions/:id} : Updates an existing rolePermission.
     * 更新现有的角色权限关系
     *
     * @param id                the id of the rolePermissionDTO to save. 要保存的角色权限关系DTO的ID
     * @param rolePermissionDTO the rolePermissionDTO to update. 要更新的角色权限关系DTO
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated rolePermissionDTO,
     * or with status {@code 400 (Bad Request)} if the rolePermissionDTO is not valid,
     * or with status {@code 500 (Internal Server Error)} if the rolePermissionDTO couldn't be updated.
     * 返回状态码为200的响应和更新后的角色权限关系DTO，如果角色权限关系DTO无效则返回400错误，如果无法更新则返回500错误
     */
    @PutMapping("/{id}")
    public ResponseEntity<RolePermissionDTO> updateRolePermission(
        @PathVariable(value = "id", required = false) final Long id,
        @Valid @RequestBody RolePermissionDTO rolePermissionDTO
    ) {
        LOG.debug("REST request to update RolePermission : {}, {}", id, rolePermissionDTO);
        if (rolePermissionDTO.getId() == null) {
            throw new BadRequestAlertException("无效的ID", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, rolePermissionDTO.getId())) {
            throw new BadRequestAlertException("无效的ID", ENTITY_NAME, "idinvalid");
        }

        if (!rolePermissionRepository.existsById(id)) {
            throw new BadRequestAlertException("实体未找到", ENTITY_NAME, "idnotfound");
        }

        rolePermissionDTO = rolePermissionService.update(rolePermissionDTO);
        return ResponseEntity.ok()
            .headers(HeaderUtil.createEntityUpdateAlert(applicationName, true, ENTITY_NAME, rolePermissionDTO.getId().toString()))
            .body(rolePermissionDTO);
    }

    /**
     * {@code PATCH  /role-permissions/:id} : Partial updates given fields of an existing rolePermission, field will ignore if it is null
     * 部分更新现有角色权限关系的指定字段，如果字段为null则忽略
     *
     * @param id                the id of the rolePermissionDTO to save. 要保存的角色权限关系DTO的ID
     * @param rolePermissionDTO the rolePermissionDTO to update. 要更新的角色权限关系DTO
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated rolePermissionDTO,
     * or with status {@code 400 (Bad Request)} if the rolePermissionDTO is not valid,
     * or with status {@code 404 (Not Found)} if the rolePermissionDTO is not found,
     * or with status {@code 500 (Internal Server Error)} if the rolePermissionDTO couldn't be updated.
     * 返回状态码为200的响应和更新后的角色权限关系DTO，如果角色权限关系DTO无效则返回400错误，如果找不到角色权限关系则返回404错误，如果无法更新则返回500错误
     */
    @PatchMapping(value = "/{id}", consumes = {"application/json", "application/merge-patch+json"})
    public ResponseEntity<RolePermissionDTO> partialUpdateRolePermission(
        @PathVariable(value = "id", required = false) final Long id,
        @NotNull @Valid @RequestBody RolePermissionDTO rolePermissionDTO
    ) {
        LOG.debug("REST request to partial update RolePermission partially : {}, {}", id, rolePermissionDTO);
        if (rolePermissionDTO.getId() == null) {
            throw new BadRequestAlertException("无效的ID", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, rolePermissionDTO.getId())) {
            throw new BadRequestAlertException("无效的ID", ENTITY_NAME, "idinvalid");
        }

        if (!rolePermissionRepository.existsById(id)) {
            throw new BadRequestAlertException("实体未找到", ENTITY_NAME, "idnotfound");
        }

        Optional<RolePermissionDTO> result = rolePermissionService.partialUpdate(rolePermissionDTO);

        return ResponseUtil.wrapOrNotFound(
            result,
            HeaderUtil.createEntityUpdateAlert(applicationName, true, ENTITY_NAME, rolePermissionDTO.getId().toString())
        );
    }

    /**
     * {@code GET  /role-permissions} : get all the rolePermissions.
     * 获取所有角色权限关系
     *
     * @param pageable the pagination information. 分页信息
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and the page of rolePermissions in body.
     * 返回状态码为200的响应和角色权限关系分页数据
     */
    @GetMapping("")
    public ResponseEntity<Page<RolePermissionDTO>> getAllRolePermissions(
        @org.springdoc.core.annotations.ParameterObject Pageable pageable
    ) {
        LOG.debug("REST request to get a page of RolePermissions");
        Page<RolePermissionDTO> page = rolePermissionService.findAll(pageable);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(ServletUriComponentsBuilder.fromCurrentRequest(), page);
        return ResponseEntity.ok().headers(headers).body(page);
    }

    /**
     * {@code GET  /role-permissions/:id} : get the "id" rolePermission.
     * 获取指定ID的角色权限关系
     *
     * @param id the id of the rolePermissionDTO to retrieve. 要检索的角色权限关系DTO的ID
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the rolePermissionDTO, or with status {@code 404 (Not Found)}.
     * 返回状态码为200的响应和角色权限关系DTO，如果找不到则返回404错误
     */
    @GetMapping("/{id}")
    public ResponseEntity<RolePermissionDTO> getRolePermission(@PathVariable("id") Long id) {
        LOG.debug("REST request to get RolePermission : {}", id);
        Optional<RolePermissionDTO> rolePermissionDTO = rolePermissionService.findOne(id);
        return ResponseUtil.wrapOrNotFound(rolePermissionDTO);
    }

    /**
     * {@code DELETE  /role-permissions/:id} : delete the "id" rolePermission.
     * 删除指定ID的角色权限关系
     *
     * @param id the id of the rolePermissionDTO to delete. 要删除的角色权限关系DTO的ID
     * @return the {@link ResponseEntity} with status {@code 204 (NO_CONTENT)}.
     * 返回状态码为204的响应（无内容）
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteRolePermission(@PathVariable("id") Long id) {
        LOG.debug("REST request to delete RolePermission : {}", id);
        rolePermissionService.delete(id);
        return ResponseEntity.noContent()
            .headers(HeaderUtil.createEntityDeletionAlert(applicationName, true, ENTITY_NAME, id.toString()))
            .build();
    }

    /**
     * {@code DELETE  /role-permissions/batch} : delete multiple rolePermissions by IDs.
     * 批量删除指定ID列表的角色权限关系
     *
     * @param ids the list of IDs of the rolePermissionDTOs to delete. 要删除的角色权限关系DTO的ID列表
     * @return the {@link ResponseEntity} with status {@code 204 (NO_CONTENT)}.
     * 返回状态码为204的响应（无内容）
     */
    @DeleteMapping("/batch")
    public ResponseEntity<Void> deleteRolePermissionsBatch(@RequestBody List<Long> ids) {
        LOG.debug("REST request to batch delete RolePermissions : {}", ids);
        rolePermissionService.deleteAll(ids);
        return ResponseEntity.noContent()
            .headers(HeaderUtil.createAlert(applicationName, "rolePermissionManagement.deleted.batch", String.valueOf(ids.size())))
            .build();
    }
}

package com.whiskerguard.organization.web.rest;

import com.whiskerguard.organization.repository.NewsLikeRepository;
import com.whiskerguard.organization.service.NewsLikeService;
import com.whiskerguard.organization.service.dto.NewsLikeDTO;
import com.whiskerguard.organization.web.rest.errors.BadRequestAlertException;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.Objects;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;
import tech.jhipster.web.util.HeaderUtil;
import tech.jhipster.web.util.PaginationUtil;
import tech.jhipster.web.util.ResponseUtil;

/**
 * 新闻点赞管理的REST控制器
 */
@RestController
@RequestMapping("/api/news/likes")
public class NewsLikeResource {

    private static final Logger LOG = LoggerFactory.getLogger(NewsLikeResource.class);

    private static final String ENTITY_NAME = "whiskerguardOrgServiceNewsLike";

    @Value("${jhipster.clientApp.name}")
    private String applicationName;

    private final NewsLikeService newsLikeService;

    private final NewsLikeRepository newsLikeRepository;

    public NewsLikeResource(NewsLikeService newsLikeService, NewsLikeRepository newsLikeRepository) {
        this.newsLikeService = newsLikeService;
        this.newsLikeRepository = newsLikeRepository;
    }

    /**
     * {@code POST  /news-likes} : 创建新闻点赞
     *
     * @param newsLikeDTO 要创建的新闻点赞DTO
     * @return {@link ResponseEntity}，状态为 {@code 201 (Created)}，响应体为新创建的新闻点赞DTO；
     *         如果新闻点赞已经有ID，则状态为 {@code 400 (Bad Request)}
     * @throws URISyntaxException 如果Location URI语法不正确
     */
    @PostMapping("/create")
    public ResponseEntity<NewsLikeDTO> createNewsLike(@Valid @RequestBody NewsLikeDTO newsLikeDTO) throws URISyntaxException {
        LOG.debug("REST request to save NewsLike : {}", newsLikeDTO);
        if (newsLikeDTO.getId() != null) {
            throw new BadRequestAlertException("新的新闻点赞不能有ID", ENTITY_NAME, "idexists");
        }
        newsLikeDTO = newsLikeService.save(newsLikeDTO);
        return ResponseEntity.created(new URI("/api/news/likes/" + newsLikeDTO.getId()))
            .headers(HeaderUtil.createEntityCreationAlert(applicationName, true, ENTITY_NAME, newsLikeDTO.getId().toString()))
            .body(newsLikeDTO);
    }

    /**
     * {@code PUT  /news-likes/:id} : 更新已存在的新闻点赞
     *
     * @param id 要保存的新闻点赞DTO的ID
     * @param newsLikeDTO 要更新的新闻点赞DTO
     * @return {@link ResponseEntity}，状态为 {@code 200 (OK)}，响应体为更新后的新闻点赞DTO；
     *         如果新闻点赞DTO无效，则状态为 {@code 400 (Bad Request)}；
     *         如果新闻点赞DTO无法更新，则状态为 {@code 500 (Internal Server Error)}
     * @throws URISyntaxException 如果Location URI语法不正确
     */
    @PutMapping("/update/{id}")
    public ResponseEntity<NewsLikeDTO> updateNewsLike(
        @PathVariable(value = "id", required = false) final Long id,
        @Valid @RequestBody NewsLikeDTO newsLikeDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to update NewsLike : {}, {}", id, newsLikeDTO);
        if (newsLikeDTO.getId() == null) {
            throw new BadRequestAlertException("无效的ID", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, newsLikeDTO.getId())) {
            throw new BadRequestAlertException("无效的ID", ENTITY_NAME, "idinvalid");
        }

        if (!newsLikeRepository.existsById(id)) {
            throw new BadRequestAlertException("实体未找到", ENTITY_NAME, "idnotfound");
        }

        newsLikeDTO = newsLikeService.update(newsLikeDTO);
        return ResponseEntity.ok()
            .headers(HeaderUtil.createEntityUpdateAlert(applicationName, true, ENTITY_NAME, newsLikeDTO.getId().toString()))
            .body(newsLikeDTO);
    }

    /**
     * {@code PATCH  /news-likes/:id} : 部分更新已存在的新闻点赞，null字段将被忽略
     *
     * @param id 要保存的新闻点赞DTO的ID
     * @param newsLikeDTO 要更新的新闻点赞DTO
     * @return {@link ResponseEntity}，状态为 {@code 200 (OK)}，响应体为更新后的新闻点赞DTO；
     *         如果新闻点赞DTO无效，则状态为 {@code 400 (Bad Request)}；
     *         如果找不到新闻点赞DTO，则状态为 {@code 404 (Not Found)}；
     *         如果新闻点赞DTO无法更新，则状态为 {@code 500 (Internal Server Error)}
     * @throws URISyntaxException 如果Location URI语法不正确
     */
    @PatchMapping(value = "/partial/{id}", consumes = { "application/json", "application/merge-patch+json" })
    public ResponseEntity<NewsLikeDTO> partialUpdateNewsLike(
        @PathVariable(value = "id", required = false) final Long id,
        @NotNull @Valid @RequestBody NewsLikeDTO newsLikeDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to partial update NewsLike partially : {}, {}", id, newsLikeDTO);
        if (newsLikeDTO.getId() == null) {
            throw new BadRequestAlertException("无效的ID", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, newsLikeDTO.getId())) {
            throw new BadRequestAlertException("无效的ID", ENTITY_NAME, "idinvalid");
        }

        if (!newsLikeRepository.existsById(id)) {
            throw new BadRequestAlertException("实体未找到", ENTITY_NAME, "idnotfound");
        }

        Optional<NewsLikeDTO> result = newsLikeService.partialUpdate(newsLikeDTO);

        return ResponseUtil.wrapOrNotFound(
            result,
            HeaderUtil.createEntityUpdateAlert(applicationName, true, ENTITY_NAME, newsLikeDTO.getId().toString())
        );
    }

    /**
     * {@code GET  /news-likes} : 获取所有新闻点赞
     *
     * @param pageable 分页信息
     * @param eagerload 是否急切加载关联实体的标志（适用于多对多关系）
     * @return {@link ResponseEntity}，状态为 {@code 200 (OK)}，响应体为新闻点赞分页数据
     */
    @GetMapping("/list")
    public ResponseEntity<Page<NewsLikeDTO>> getAllNewsLikes(
        @org.springdoc.core.annotations.ParameterObject Pageable pageable,
        @RequestParam(name = "eagerload", required = false, defaultValue = "true") boolean eagerload
    ) {
        LOG.debug("REST request to get a page of NewsLikes");
        Page<NewsLikeDTO> page;
        if (eagerload) {
            page = newsLikeService.findAllWithEagerRelationships(pageable);
        } else {
            page = newsLikeService.findAll(pageable);
        }
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(ServletUriComponentsBuilder.fromCurrentRequest(), page);
        return ResponseEntity.ok().headers(headers).body(page);
    }

    /**
     * {@code GET  /news-likes/:id} : 获取指定ID的新闻点赞
     *
     * @param id 要获取的新闻点赞DTO的ID
     * @return {@link ResponseEntity}，状态为 {@code 200 (OK)}，响应体为新闻点赞DTO；
     *         如果找不到新闻点赞，则状态为 {@code 404 (Not Found)}
     */
    @GetMapping("/detail/{id}")
    public ResponseEntity<NewsLikeDTO> getNewsLike(@PathVariable("id") Long id) {
        LOG.debug("REST request to get NewsLike : {}", id);
        Optional<NewsLikeDTO> newsLikeDTO = newsLikeService.findOne(id);
        return ResponseUtil.wrapOrNotFound(newsLikeDTO);
    }

    /**
     * {@code DELETE  /news-likes/:id} : 删除指定ID的新闻点赞
     *
     * @param id 要删除的新闻点赞DTO的ID
     * @return {@link ResponseEntity}，状态为 {@code 204 (NO_CONTENT)}
     */
    @DeleteMapping("/delete/{id}")
    public ResponseEntity<Void> deleteNewsLike(@PathVariable("id") Long id) {
        LOG.debug("REST request to delete NewsLike : {}", id);
        newsLikeService.delete(id);
        return ResponseEntity.noContent()
            .headers(HeaderUtil.createEntityDeletionAlert(applicationName, true, ENTITY_NAME, id.toString()))
            .build();
    }
}

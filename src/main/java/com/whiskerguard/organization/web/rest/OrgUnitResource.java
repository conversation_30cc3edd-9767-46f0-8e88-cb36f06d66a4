package com.whiskerguard.organization.web.rest;

import com.whiskerguard.common.constant.FileTypeConstants;
import com.whiskerguard.common.dto.ImportResultDTO;
import com.whiskerguard.organization.repository.OrgUnitRepository;
import com.whiskerguard.organization.service.OrgUnitService;
import com.whiskerguard.organization.service.dto.OrgStructureImportDTO;
import com.whiskerguard.organization.service.dto.OrgStructureImportResultDTO;
import com.whiskerguard.organization.service.dto.OrgUnitDTO;
import com.whiskerguard.organization.util.ExcelTemplateUtil;
import com.whiskerguard.organization.web.rest.errors.BadRequestAlertException;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;
import tech.jhipster.web.util.HeaderUtil;
import tech.jhipster.web.util.PaginationUtil;
import tech.jhipster.web.util.ResponseUtil;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.net.URI;
import java.net.URISyntaxException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * REST controller for managing {@link com.whiskerguard.organization.domain.OrgUnit}.
 * 组织单元的REST控制器
 * 提供组织单元的CRUD操作和树形结构管理的API接口
 */
@RestController
@RequestMapping("/api")
public class OrgUnitResource {

    /**
     * 日志记录器
     */
    private final Logger log = LoggerFactory.getLogger(OrgUnitResource.class);

    /**
     * 实体名称，用于错误消息
     */
    private static final String ENTITY_NAME = "orgUnit";

    /**
     * 应用名称，从配置中获取
     */
    @Value("${jhipster.clientApp.name}")
    private String applicationName;

    /**
     * 组织单元服务
     */
    private final OrgUnitService orgUnitService;

    /**
     * 组织单元仓库
     */
    private final OrgUnitRepository orgUnitRepository;

    /**
     * 构造函数
     *
     * @param orgUnitService    组织单元服务
     * @param orgUnitRepository 组织单元仓库
     */
    public OrgUnitResource(OrgUnitService orgUnitService, OrgUnitRepository orgUnitRepository) {
        this.orgUnitService = orgUnitService;
        this.orgUnitRepository = orgUnitRepository;
    }

    /**
     * {@code POST  /org-units} : Create a new orgUnit.
     * 创建新的组织单元
     *
     * @param orgUnitDTO the orgUnitDTO to create. 要创建的组织单元DTO
     * @return the {@link ResponseEntity} with status {@code 201 (Created)} and with body the new orgUnitDTO, or with status {@code 400 (Bad Request)} if the orgUnit has already an ID.
     * 返回状态码为201的响应和新创建的组织单元DTO，如果组织单元已有ID则返回400错误
     * @throws URISyntaxException if the Location URI syntax is incorrect. 如果位置URI语法不正确
     */
    @PostMapping("/org/units")
    public ResponseEntity<OrgUnitDTO> createOrgUnit(@Valid @RequestBody OrgUnitDTO orgUnitDTO) throws URISyntaxException {
        log.debug("REST request to save OrgUnit : {}", orgUnitDTO);
        if (orgUnitDTO.getId() != null) {
            throw new BadRequestAlertException("新的组织单元不能有ID", ENTITY_NAME, "idexists");
        }
        OrgUnitDTO result = orgUnitService.save(orgUnitDTO);
        return ResponseEntity.created(new URI("/api/org/units/" + result.getId()))
            .headers(HeaderUtil.createEntityCreationAlert(applicationName, true, ENTITY_NAME, result.getId().toString()))
            .body(result);
    }

    /**
     * {@code PUT  /org-units/:id} : Updates an existing orgUnit.
     * 更新现有的组织单元
     *
     * @param id         the id of the orgUnitDTO to save. 要保存的组织单元DTO的ID
     * @param orgUnitDTO the orgUnitDTO to update. 要更新的组织单元DTO
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated orgUnitDTO,
     * or with status {@code 400 (Bad Request)} if the orgUnitDTO is not valid,
     * or with status {@code 500 (Internal Server Error)} if the orgUnitDTO couldn't be updated.
     * 返回状态码为200的响应和更新后的组织单元DTO，如果组织单元DTO无效则返回400错误，如果无法更新则返回500错误
     */
    @PutMapping("/org/units/{id}")
    public ResponseEntity<OrgUnitDTO> updateOrgUnit(
        @PathVariable(value = "id", required = false) final Long id,
        @Valid @RequestBody OrgUnitDTO orgUnitDTO
    ) {
        log.debug("REST request to update OrgUnit : {}, {}", id, orgUnitDTO);
        if (orgUnitDTO.getId() == null) {
            throw new BadRequestAlertException("无效的ID", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, orgUnitDTO.getId())) {
            throw new BadRequestAlertException("无效的ID", ENTITY_NAME, "idinvalid");
        }

        if (!orgUnitRepository.existsById(id)) {
            throw new BadRequestAlertException("实体未找到", ENTITY_NAME, "idnotfound");
        }

        OrgUnitDTO result = orgUnitService.update(orgUnitDTO);
        return ResponseEntity.ok()
            .headers(HeaderUtil.createEntityUpdateAlert(applicationName, true, ENTITY_NAME, orgUnitDTO.getId().toString()))
            .body(result);
    }

    /**
     * {@code PATCH  /org-units/:id} : Partial updates given fields of an existing orgUnit, field will ignore if it is null
     * 部分更新现有组织单元的指定字段，如果字段为null则忽略
     *
     * @param id         the id of the orgUnitDTO to save. 要保存的组织单元DTO的ID
     * @param orgUnitDTO the orgUnitDTO to update. 要更新的组织单元DTO
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated orgUnitDTO,
     * or with status {@code 400 (Bad Request)} if the orgUnitDTO is not valid,
     * or with status {@code 404 (Not Found)} if the orgUnitDTO is not found,
     * or with status {@code 500 (Internal Server Error)} if the orgUnitDTO couldn't be updated.
     * 返回状态码为200的响应和更新后的组织单元DTO，如果组织单元DTO无效则返回400错误，如果找不到组织单元则返回404错误，如果无法更新则返回500错误
     */
    @PatchMapping(value = "/org/units/{id}", consumes = {"application/json", "application/merge-patch+json"})
    public ResponseEntity<OrgUnitDTO> partialUpdateOrgUnit(
        @PathVariable(value = "id", required = false) final Long id,
        @NotNull @Valid @RequestBody OrgUnitDTO orgUnitDTO
    ) {
        log.debug("REST request to partial update OrgUnit partially : {}, {}", id, orgUnitDTO);
        if (orgUnitDTO.getId() == null) {
            throw new BadRequestAlertException("无效的ID", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, orgUnitDTO.getId())) {
            throw new BadRequestAlertException("无效的ID", ENTITY_NAME, "idinvalid");
        }

        if (!orgUnitRepository.existsById(id)) {
            throw new BadRequestAlertException("实体未找到", ENTITY_NAME, "idnotfound");
        }

        Optional<OrgUnitDTO> result = orgUnitService.partialUpdate(orgUnitDTO);

        return ResponseUtil.wrapOrNotFound(
            result,
            HeaderUtil.createEntityUpdateAlert(applicationName, true, ENTITY_NAME, orgUnitDTO.getId().toString())
        );
    }

    /**
     * {@code GET  /org-units} : get all the orgUnits.
     * 获取所有组织单元，可按parentId和keyword进行综合搜索
     *
     * @param pageable the pagination information. 分页信息
     * @param parentId the parent id to filter by (optional). 父级ID筛选条件（可选）
     * @param keyword  the keyword to search in name or code (optional). 关键词搜索条件（可选，搜索名称或编码）
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and the page of orgUnits in body.
     * 返回状态码为200的响应和组织单元分页数据
     */
    @GetMapping("/org/units")
    public ResponseEntity<Page<OrgUnitDTO>> getAllOrgUnits(
        @ParameterObject Pageable pageable,
        @RequestParam(required = false) Long parentId,
        @RequestParam(required = false) String keyword
    ) {
        log.debug("REST request to get a page of OrgUnits with parentId: {} and keyword: {}", parentId, keyword);
        Page<OrgUnitDTO> page;

        if (parentId == null) {
            // parentId 为空，按原有逻辑查询所有
            page = orgUnitService.findAll(pageable);
        } else if (keyword == null || keyword.trim().isEmpty()) {
            // parentId 不为空，keyword 为空，按 parentId 查询
            page = orgUnitService.findAllByParentId(parentId, pageable);
        } else {
            // parentId 不为空，keyword 不为空，按 parentId 和 keyword 组合查询
            page = orgUnitService.findAllByParentIdAndKeyword(parentId, keyword.trim(), pageable);
        }

        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(ServletUriComponentsBuilder.fromCurrentRequest(), page);
        return ResponseEntity.ok().headers(headers).body(page);
    }

    /**
     * {@code GET  /org-units/:id} : get the "id" orgUnit.
     * 获取指定ID的组织单元
     *
     * @param id the id of the orgUnitDTO to retrieve. 要检索的组织单元DTO的ID
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the orgUnitDTO, or with status {@code 404 (Not Found)}.
     * 返回状态码为200的响应和组织单元DTO，如果找不到则返回404错误
     */
    @GetMapping("/org/units/{id}")
    public ResponseEntity<OrgUnitDTO> getOrgUnit(@PathVariable Long id) {
        log.debug("REST request to get OrgUnit : {}", id);
        Optional<OrgUnitDTO> orgUnitDTO = orgUnitService.findOne(id);
        return ResponseUtil.wrapOrNotFound(orgUnitDTO);
    }

    /**
     * {@code DELETE  /org-units/:id} : delete the "id" orgUnit.
     * 删除指定ID的组织单元
     *
     * @param id the id of the orgUnitDTO to delete. 要删除的组织单元DTO的ID
     * @return the {@link ResponseEntity} with status {@code 204 (NO_CONTENT)}.
     * 返回状态码为204的响应（无内容）
     */
    @DeleteMapping("/org/units/{id}")
    public ResponseEntity<Void> deleteOrgUnit(@PathVariable Long id) {
        log.debug("REST request to delete OrgUnit : {}", id);
        orgUnitService.delete(id);
        return ResponseEntity.noContent()
            .headers(HeaderUtil.createEntityDeletionAlert(applicationName, true, ENTITY_NAME, id.toString()))
            .build();
    }

    /**
     * {@code GET  /org-units/tree} : get the org unit tree.
     * 获取组织单元树形结构
     *
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the org unit tree.
     * 返回状态码为200的响应和组织单元树形结构
     */
    @GetMapping("/org/units/tree")
    public ResponseEntity<List<OrgUnitDTO>> getOrgUnitTree() {
        log.debug("REST request to get org unit tree");
        List<OrgUnitDTO> tree = orgUnitService.getOrgUnitTree();
        return ResponseEntity.ok().body(tree);
    }

    /**
     * {@code PATCH  /org-units/:id/status} : change the status of an existing org unit.
     * 变更现有组织单元的状态
     *
     * @param id     the id of the org unit to change status. 要变更状态的组织单元ID
     * @param status the target status. 目标状态
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated org unit.
     * 返回状态码为200的响应和更新后的组织单元
     */
    @PatchMapping("/org/units/{id}/status")
    public ResponseEntity<OrgUnitDTO> changeOrgUnitStatus(@PathVariable Long id, @RequestParam Integer status) {
        log.debug("REST request to change org unit status : {} to {}", id, status);
        OrgUnitDTO result = orgUnitService.changeStatus(id, status);
        return ResponseEntity.ok()
            .headers(HeaderUtil.createEntityUpdateAlert(applicationName, true, ENTITY_NAME, id.toString()))
            .body(result);
    }

    /**
     * {@code PUT  /org-units/sort} : update the sort order of org units.
     * 更新组织单元的排序顺序
     *
     * @param ids the list of org unit ids in order. 按顺序排列的组织单元ID列表
     * @return the {@link ResponseEntity} with status {@code 200 (OK)}.
     * 返回状态码为200的响应
     */
    @PutMapping("/org/units/sort")
    public ResponseEntity<Void> updateOrgUnitSortOrder(@Valid @RequestBody List<Long> ids) {
        log.debug("REST request to update org unit sort order : {}", ids);
        orgUnitService.updateSortOrder(ids);
        return ResponseEntity.ok().build();
    }

    /**
     * {@code POST  /org-units/import} : Import organization structure from Excel file.
     * 从Excel文件导入组织架构
     *
     * @param file the Excel file to import. 要导入的Excel文件
     * @return the {@link ResponseEntity} with status {@code 201 (Created)} and with body the import result.
     * 返回状态码为201的响应和导入结果
     * @throws URISyntaxException if the Location URI syntax is incorrect. 如果位置URI语法不正确
     */
    @PostMapping(value = "/org/units/import", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<ImportResultDTO<OrgStructureImportDTO>> importOrgStructure(@RequestParam("file") MultipartFile file)
        throws URISyntaxException {
        log.debug("REST request to import Organization Structure from Excel file : {}", file.getOriginalFilename());

        if (file.isEmpty()) {
            throw new BadRequestAlertException("上传文件为空", ENTITY_NAME, "file.empty");
        }

        // 验证文件类型
        if (!Objects.requireNonNull(file.getOriginalFilename()).endsWith(FileTypeConstants.EXT_XLS) &&
            !Objects.requireNonNull(file.getOriginalFilename()).endsWith(FileTypeConstants.EXT_XLSX)) {
            throw new BadRequestAlertException("只支持.xlsx或.xls格式文件", ENTITY_NAME, "file.invalid.type");
        }

        OrgStructureImportResultDTO orgResult;
        try {
            orgResult = orgUnitService.importFromExcel(file);
        } catch (Exception e) {
            log.error("导入组织架构失败", e);
            throw new BadRequestAlertException("导入失败: 数据异常", ENTITY_NAME, "import.failed");
        }

        // 转换为通用的ImportResultDTO
        ImportResultDTO<OrgStructureImportDTO> result = new ImportResultDTO<>();
        result.setSuccessCount(orgResult.getTotalSuccessCount());
        result.setFailedCount(orgResult.getFailureCount());

        // 将错误信息转换为失败记录
        for (String errorMessage : orgResult.getErrorMessages()) {
            result.addFailed(errorMessage);
        }

        return ResponseEntity.created(new URI("/api/org/units/import"))
            .headers(HeaderUtil.createAlert(applicationName, "orgStructureManagement.imported",
                String.format("成功导入%d个组织单元和%d个岗位", result.getSuccessCount(), result.getFailedCount())))
            .body(result);
    }

    /**
     * {@code GET  /org-units/search} : search organization units by name (fuzzy search) within current tenant.
     * 根据组织单元名称模糊查询本租户下的组织单元列表
     *
     * @param name 组织单元名称关键词 organization unit name keyword
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and the list of organization units.
     * 返回状态码为200的响应和组织单元列表
     */
    @GetMapping("/org/units/search")
    public ResponseEntity<List<OrgUnitDTO>> searchOrgUnitsByName(@RequestParam("name") String name) {
        log.debug("REST request to search OrgUnits by name : {}", name);

        if (name == null || name.trim().isEmpty()) {
            throw new BadRequestAlertException("搜索名称不能为空", ENTITY_NAME, "name.empty");
        }

        List<OrgUnitDTO> orgUnits = orgUnitService.findByNameContainingIgnoreCase(name.trim());
        return ResponseEntity.ok().body(orgUnits);
    }

    /**
     * {@code GET  /top/org/unit} : check if the employee is in top org unit.
     * 判断用户所在的部门是否已经是最高的组织单元
     *
     * @param employeeId the id of the employee to check. 要检查的员工ID
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the boolean value.
     * 返回状态码为200的响应和布尔值
     */
    @GetMapping("/top/org/unit")
    public ResponseEntity<Boolean> isTopOrgUnit(@RequestParam("employeeId") Long employeeId) {
        log.debug("REST request to check if employee is in top org unit : {}", employeeId);
        Boolean result = orgUnitService.isTopOrgUnit(employeeId);
        return ResponseEntity.ok(result);
    }

    /**
     * {@code POST  /org-units/ids} : get organization units by ids.
     * 根据组织单元ID列表获取组织单元数据
     *
     * @param ids the list of org unit ids. 组织单元ID列表
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and the list of organization units.
     * 返回状态码为200的响应和组织单元列表
     */
    @PostMapping("/org/units/ids")
    public ResponseEntity<List<OrgUnitDTO>> getOrgUnitsByIds(@RequestBody List<Long> ids) {
        log.debug("REST request to get OrgUnits by ids : {}", ids);
        List<OrgUnitDTO> orgUnits = orgUnitService.findByIds(ids);
        return ResponseEntity.ok().body(orgUnits);
    }

    /**
     * {@code GET  /org-units/template} : Download organization structure import template.
     * 下载企业部门岗位信息模板
     *
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and Excel template file.
     * 返回状态码为200的响应和Excel模板文件
     */
    @GetMapping("/org/units/template")
    public ResponseEntity<byte[]> downloadOrgUnitTemplate() {
        log.debug("REST request to download Organization Unit import template");

        try {
            // 创建模板数据
            List<OrgStructureImportDTO> templateData = createOrgUnitTemplateData();

            // 创建导入说明
            List<String> instructions = createOrgUnitImportInstructions();

            // 生成Excel文件
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            ExcelTemplateUtil.exportToExcel(templateData, OrgStructureImportDTO.class, "企业部门岗位信息模板", instructions, outputStream);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.parseMediaType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"));
            String fileName = "企业部门岗位信息模板.xlsx";
            String encodedFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8);
            headers.set("Content-Disposition", "attachment; filename*=UTF-8''" + encodedFileName);

            return ResponseEntity.ok()
                .headers(headers)
                .body(outputStream.toByteArray());

        } catch (IOException e) {
            log.error("生成企业部门岗位信息模板失败", e);
            throw new BadRequestAlertException("生成模板失败: " + e.getMessage(), ENTITY_NAME, "templatefailed");
        }
    }

    /**
     * 创建组织架构导入模板数据
     */
    private List<OrgStructureImportDTO> createOrgUnitTemplateData() {
        List<OrgStructureImportDTO> templateData = new ArrayList<>();
        // 1. 根节点 - 总公司
        OrgStructureImportDTO root = new OrgStructureImportDTO();
        root.setDataType("ORGUNIT");
        root.setOrgUnitCode("COMPANY");
        root.setOrgUnitName("示例科技有限公司");
        root.setOrgUnitType("公司");
        root.setParentOrgUnitCode("");
        root.setOrgUnitLevel(1);
        root.setOrgUnitSortOrder(1);
        root.setOrgUnitDescription("公司总部，负责整体战略规划和管理");

        // 2. 一级部门
        OrgStructureImportDTO techDept = new OrgStructureImportDTO();
        techDept.setDataType("ORGUNIT");
        techDept.setOrgUnitCode("TECH_DEPT");
        techDept.setOrgUnitName("技术研发部");
        techDept.setOrgUnitType("部门");
        techDept.setParentOrgUnitCode("COMPANY");
        techDept.setOrgUnitLevel(2);
        techDept.setOrgUnitSortOrder(1);
        techDept.setOrgUnitDescription("负责产品技术研发、系统架构设计和技术创新");

        // 3. 二级部门（技术部下的小组）
        OrgStructureImportDTO devTeam = new OrgStructureImportDTO();
        devTeam.setDataType("ORGUNIT");
        devTeam.setOrgUnitCode("DEV_TEAM");
        devTeam.setOrgUnitName("开发小组");
        devTeam.setOrgUnitType("团队");
        devTeam.setParentOrgUnitCode("TECH_DEPT");
        devTeam.setOrgUnitLevel(3);
        devTeam.setOrgUnitSortOrder(1);
        devTeam.setOrgUnitDescription("负责软件产品开发和代码实现");

        // 4. 岗位信息
        OrgStructureImportDTO ctoPos = new OrgStructureImportDTO();
        ctoPos.setDataType("POSITION");
        ctoPos.setPositionCode("CTO");
        ctoPos.setPositionName("技术总监");
        ctoPos.setPositionLevel(5);
        ctoPos.setPositionCategory("管理类");
        ctoPos.setBelongsToOrgUnitCode("TECH_DEPT");
        ctoPos.setPositionDescription("负责技术战略规划、技术团队管理和重大技术决策");

        OrgStructureImportDTO seniorDev = new OrgStructureImportDTO();
        seniorDev.setDataType("POSITION");
        seniorDev.setPositionCode("SENIOR_DEV");
        seniorDev.setPositionName("高级软件工程师");
        seniorDev.setPositionLevel(4);
        seniorDev.setPositionCategory("技术类");
        seniorDev.setBelongsToOrgUnitCode("DEV_TEAM");
        seniorDev.setPositionDescription("负责核心模块开发、技术难点攻关和团队技术指导");


        // 添加所有数据到模板
        templateData.add(root);
        templateData.add(techDept);
        templateData.add(devTeam);
        templateData.add(ctoPos);
        templateData.add(seniorDev);

        return templateData;
    }

    /**
     * 创建组织架构导入说明
     */
    private List<String> createOrgUnitImportInstructions() {
        List<String> instructions = new ArrayList<>();

        instructions.add("1. 带*号的列为必填项");
        instructions.add("2. 数据类型：ORGUNIT（组织单元）或 POSITION（岗位）");
        instructions.add("3. 组织单元编码必须唯一，不能重复");
        instructions.add("4. 岗位编码必须唯一，不能重复");
        instructions.add("5. 组织单元类型：公司/COMPANY、子公司/SUBSIDIARY、事业群/BUSINESS_GROUP、部门/DEPARTMENT、团队/TEAM");
        instructions.add("6. 岗位分类：管理类/MANAGEMENT、技术类/TECHNICAL、业务类/BUSINESS、支持类/SUPPORT");
        instructions.add("7. 层级关系：父级组织单元必须先于子级组织单元导入");
        instructions.add("8. 岗位必须关联到已存在的组织单元");
        instructions.add("9. 组织单元层级：根节点为1，每下一级+1");
        instructions.add("10. 岗位级别：1-10，数字越大级别越高");
        instructions.add("11. 请删除示例数据行后再导入实际数据");
        instructions.add("12. 建议按层级顺序导入：先导入上级组织单元，再导入下级组织单元，最后导入岗位");

        return instructions;
    }

}

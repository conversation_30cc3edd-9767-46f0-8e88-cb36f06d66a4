package com.whiskerguard.organization.web.rest;

import com.whiskerguard.organization.repository.NewsCommentRepository;
import com.whiskerguard.organization.service.NewsCommentService;
import com.whiskerguard.organization.service.dto.NewsCommentDTO;
import com.whiskerguard.organization.web.rest.errors.BadRequestAlertException;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.Objects;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;
import tech.jhipster.web.util.HeaderUtil;
import tech.jhipster.web.util.PaginationUtil;
import tech.jhipster.web.util.ResponseUtil;

/**
 * 新闻评论管理的REST控制器
 */
@RestController
@RequestMapping("/api/news/comments")
public class NewsCommentResource {

    private static final Logger LOG = LoggerFactory.getLogger(NewsCommentResource.class);

    private static final String ENTITY_NAME = "whiskerguardOrgServiceNewsComment";

    @Value("${jhipster.clientApp.name}")
    private String applicationName;

    private final NewsCommentService newsCommentService;

    private final NewsCommentRepository newsCommentRepository;

    public NewsCommentResource(NewsCommentService newsCommentService, NewsCommentRepository newsCommentRepository) {
        this.newsCommentService = newsCommentService;
        this.newsCommentRepository = newsCommentRepository;
    }

    /**
     * {@code POST  /news-comments} : 创建新闻评论
     *
     * @param newsCommentDTO 要创建的新闻评论DTO
     * @return {@link ResponseEntity}，状态为 {@code 201 (Created)}，响应体为新创建的新闻评论DTO；
     *         如果新闻评论已经有ID，则状态为 {@code 400 (Bad Request)}
     * @throws URISyntaxException 如果Location URI语法不正确
     */
    @PostMapping("/create")
    public ResponseEntity<NewsCommentDTO> createNewsComment(@Valid @RequestBody NewsCommentDTO newsCommentDTO) throws URISyntaxException {
        LOG.debug("REST request to save NewsComment : {}", newsCommentDTO);
        if (newsCommentDTO.getId() != null) {
            throw new BadRequestAlertException("新的新闻评论不能有ID", ENTITY_NAME, "idexists");
        }
        newsCommentDTO = newsCommentService.save(newsCommentDTO);
        return ResponseEntity.created(new URI("/api/news/comments/" + newsCommentDTO.getId()))
            .headers(HeaderUtil.createEntityCreationAlert(applicationName, true, ENTITY_NAME, newsCommentDTO.getId().toString()))
            .body(newsCommentDTO);
    }

    /**
     * {@code PUT  /news-comments/:id} : 更新已存在的新闻评论
     *
     * @param id 要保存的新闻评论DTO的ID
     * @param newsCommentDTO 要更新的新闻评论DTO
     * @return {@link ResponseEntity}，状态为 {@code 200 (OK)}，响应体为更新后的新闻评论DTO；
     *         如果新闻评论DTO无效，则状态为 {@code 400 (Bad Request)}；
     *         如果新闻评论DTO无法更新，则状态为 {@code 500 (Internal Server Error)}
     * @throws URISyntaxException 如果Location URI语法不正确
     */
    @PutMapping("/update/{id}")
    public ResponseEntity<NewsCommentDTO> updateNewsComment(
        @PathVariable(value = "id", required = false) final Long id,
        @Valid @RequestBody NewsCommentDTO newsCommentDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to update NewsComment : {}, {}", id, newsCommentDTO);
        if (newsCommentDTO.getId() == null) {
            throw new BadRequestAlertException("无效的ID", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, newsCommentDTO.getId())) {
            throw new BadRequestAlertException("无效的ID", ENTITY_NAME, "idinvalid");
        }

        if (!newsCommentRepository.existsById(id)) {
            throw new BadRequestAlertException("实体未找到", ENTITY_NAME, "idnotfound");
        }

        newsCommentDTO = newsCommentService.update(newsCommentDTO);
        return ResponseEntity.ok()
            .headers(HeaderUtil.createEntityUpdateAlert(applicationName, true, ENTITY_NAME, newsCommentDTO.getId().toString()))
            .body(newsCommentDTO);
    }

    /**
     * {@code PATCH  /news-comments/:id} : 部分更新已存在的新闻评论，null字段将被忽略
     *
     * @param id 要保存的新闻评论DTO的ID
     * @param newsCommentDTO 要更新的新闻评论DTO
     * @return {@link ResponseEntity}，状态为 {@code 200 (OK)}，响应体为更新后的新闻评论DTO；
     *         如果新闻评论DTO无效，则状态为 {@code 400 (Bad Request)}；
     *         如果找不到新闻评论DTO，则状态为 {@code 404 (Not Found)}；
     *         如果新闻评论DTO无法更新，则状态为 {@code 500 (Internal Server Error)}
     * @throws URISyntaxException 如果Location URI语法不正确
     */
    @PatchMapping(value = "/partial/{id}", consumes = { "application/json", "application/merge-patch+json" })
    public ResponseEntity<NewsCommentDTO> partialUpdateNewsComment(
        @PathVariable(value = "id", required = false) final Long id,
        @NotNull @Valid @RequestBody NewsCommentDTO newsCommentDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to partial update NewsComment partially : {}, {}", id, newsCommentDTO);
        if (newsCommentDTO.getId() == null) {
            throw new BadRequestAlertException("无效的ID", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, newsCommentDTO.getId())) {
            throw new BadRequestAlertException("无效的ID", ENTITY_NAME, "idinvalid");
        }

        if (!newsCommentRepository.existsById(id)) {
            throw new BadRequestAlertException("实体未找到", ENTITY_NAME, "idnotfound");
        }

        Optional<NewsCommentDTO> result = newsCommentService.partialUpdate(newsCommentDTO);

        return ResponseUtil.wrapOrNotFound(
            result,
            HeaderUtil.createEntityUpdateAlert(applicationName, true, ENTITY_NAME, newsCommentDTO.getId().toString())
        );
    }

    /**
     * {@code GET  /news-comments} : 获取所有新闻评论
     *
     * @param pageable 分页信息
     * @param eagerload 是否急切加载关联实体的标志（适用于多对多关系）
     * @return {@link ResponseEntity}，状态为 {@code 200 (OK)}，响应体为新闻评论分页数据
     */
    @GetMapping("/list")
    public ResponseEntity<Page<NewsCommentDTO>> getAllNewsComments(
        @org.springdoc.core.annotations.ParameterObject Pageable pageable,
        @RequestParam(name = "eagerload", required = false, defaultValue = "true") boolean eagerload
    ) {
        LOG.debug("REST request to get a page of NewsComments");
        Page<NewsCommentDTO> page;
        if (eagerload) {
            page = newsCommentService.findAllWithEagerRelationships(pageable);
        } else {
            page = newsCommentService.findAll(pageable);
        }
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(ServletUriComponentsBuilder.fromCurrentRequest(), page);
        return ResponseEntity.ok().headers(headers).body(page);
    }

    /**
     * {@code GET  /news-comments/:id} : 获取指定ID的新闻评论
     *
     * @param id 要获取的新闻评论DTO的ID
     * @return {@link ResponseEntity}，状态为 {@code 200 (OK)}，响应体为新闻评论DTO；
     *         如果找不到新闻评论，则状态为 {@code 404 (Not Found)}
     */
    @GetMapping("/detail/{id}")
    public ResponseEntity<NewsCommentDTO> getNewsComment(@PathVariable("id") Long id) {
        LOG.debug("REST request to get NewsComment : {}", id);
        Optional<NewsCommentDTO> newsCommentDTO = newsCommentService.findOne(id);
        return ResponseUtil.wrapOrNotFound(newsCommentDTO);
    }

    /**
     * {@code DELETE  /news-comments/:id} : 删除指定ID的新闻评论
     *
     * @param id 要删除的新闻评论DTO的ID
     * @return {@link ResponseEntity}，状态为 {@code 204 (NO_CONTENT)}
     */
    @DeleteMapping("/delete/{id}")
    public ResponseEntity<Void> deleteNewsComment(@PathVariable("id") Long id) {
        LOG.debug("REST request to delete NewsComment : {}", id);
        newsCommentService.delete(id);
        return ResponseEntity.noContent()
            .headers(HeaderUtil.createEntityDeletionAlert(applicationName, true, ENTITY_NAME, id.toString()))
            .build();
    }
}

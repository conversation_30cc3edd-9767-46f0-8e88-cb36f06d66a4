package com.whiskerguard.organization.web.rest;

import com.whiskerguard.organization.repository.NewsTagsRepository;
import com.whiskerguard.organization.service.NewsTagsService;
import com.whiskerguard.organization.service.dto.NewsTagsDTO;
import com.whiskerguard.organization.web.rest.errors.BadRequestAlertException;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;
import tech.jhipster.web.util.HeaderUtil;
import tech.jhipster.web.util.PaginationUtil;
import tech.jhipster.web.util.ResponseUtil;

/**
 * 描述：新闻标签关联资源控制器
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/18
 */
@RestController
@RequestMapping("/api/news-tags")
public class NewsTagsResource {

    private static final Logger LOG = LoggerFactory.getLogger(NewsTagsResource.class);

    private static final String ENTITY_NAME = "whiskerguardOrgServiceNewsTags";

    @Value("${jhipster.clientApp.name}")
    private String applicationName;

    private final NewsTagsService newsTagsService;

    private final NewsTagsRepository newsTagsRepository;

    /**
     * 构造函数：初始化新闻标签关联资源控制器
     *
     * @param newsTagsService    新闻标签关联服务
     * @param newsTagsRepository 新闻标签关联仓库
     */
    public NewsTagsResource(NewsTagsService newsTagsService, NewsTagsRepository newsTagsRepository) {
        this.newsTagsService = newsTagsService;
        this.newsTagsRepository = newsTagsRepository;
    }

    /**
     * 方法名称：createNewsTags
     * 描述：创建新的新闻标签关联记录
     *
     * @param newsTagsDTO 要创建的新闻标签关联DTO对象
     * @return 响应实体，包含创建的新闻标签关联DTO对象
     * @throws URISyntaxException 如果位置URI语法不正确
     * @since 1.0
     */
    @PostMapping("")
    public ResponseEntity<NewsTagsDTO> createNewsTags(@Valid @RequestBody NewsTagsDTO newsTagsDTO) throws URISyntaxException {
        LOG.debug("REST request to save NewsTags : {}", newsTagsDTO);
        if (newsTagsDTO.getId() != null) {
            throw new BadRequestAlertException("新的新闻标签不能有ID", ENTITY_NAME, "idexists");
        }
        newsTagsDTO = newsTagsService.save(newsTagsDTO);
        return ResponseEntity.created(new URI("/api/news-tags/" + newsTagsDTO.getId()))
            .headers(HeaderUtil.createEntityCreationAlert(applicationName, true, ENTITY_NAME, newsTagsDTO.getId().toString()))
            .body(newsTagsDTO);
    }

    /**
     * 方法名称：updateNewsTags
     * 描述：更新现有的新闻标签关联记录
     *
     * @param id          要更新的新闻标签关联ID
     * @param newsTagsDTO 要更新的新闻标签关联DTO对象
     * @return 响应实体，包含更新后的新闻标签关联DTO对象
     * @throws URISyntaxException 如果位置URI语法不正确
     * @since 1.0
     */
    @PutMapping("/{id}")
    public ResponseEntity<NewsTagsDTO> updateNewsTags(
        @PathVariable(value = "id", required = false) final Long id,
        @Valid @RequestBody NewsTagsDTO newsTagsDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to update NewsTags : {}, {}", id, newsTagsDTO);
        if (newsTagsDTO.getId() == null) {
            throw new BadRequestAlertException("无效的ID", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, newsTagsDTO.getId())) {
            throw new BadRequestAlertException("无效的ID", ENTITY_NAME, "idinvalid");
        }

        if (!newsTagsRepository.existsById(id)) {
            throw new BadRequestAlertException("实体未找到", ENTITY_NAME, "idnotfound");
        }

        newsTagsDTO = newsTagsService.update(newsTagsDTO);
        return ResponseEntity.ok()
            .headers(HeaderUtil.createEntityUpdateAlert(applicationName, true, ENTITY_NAME, newsTagsDTO.getId().toString()))
            .body(newsTagsDTO);
    }

    /**
     * 方法名称：partialUpdateNewsTags
     * 描述：部分更新现有的新闻标签关联记录
     *
     * @param id          要更新的新闻标签关联ID
     * @param newsTagsDTO 要部分更新的新闻标签关联DTO对象
     * @return 响应实体，包含更新后的新闻标签关联DTO对象
     * @throws URISyntaxException 如果位置URI语法不正确
     * @since 1.0
     */
    @PatchMapping(value = "/{id}", consumes = { "application/json", "application/merge-patch+json" })
    public ResponseEntity<NewsTagsDTO> partialUpdateNewsTags(
        @PathVariable(value = "id", required = false) final Long id,
        @NotNull @RequestBody NewsTagsDTO newsTagsDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to partial update NewsTags partially : {}, {}", id, newsTagsDTO);
        if (newsTagsDTO.getId() == null) {
            throw new BadRequestAlertException("无效的ID", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, newsTagsDTO.getId())) {
            throw new BadRequestAlertException("无效的ID", ENTITY_NAME, "idinvalid");
        }

        if (!newsTagsRepository.existsById(id)) {
            throw new BadRequestAlertException("实体未找到", ENTITY_NAME, "idnotfound");
        }

        Optional<NewsTagsDTO> result = newsTagsService.partialUpdate(newsTagsDTO);

        return ResponseUtil.wrapOrNotFound(
            result,
            HeaderUtil.createEntityUpdateAlert(applicationName, true, ENTITY_NAME, newsTagsDTO.getId().toString())
        );
    }

    /**
     * 方法名称：getAllNewsTags
     * 描述：获取所有新闻标签关联记录
     *
     * @param pageable 分页参数
     * @return 响应实体，包含新闻标签关联DTO对象列表
     * @since 1.0
     */
    @GetMapping("")
    public ResponseEntity<List<NewsTagsDTO>> getAllNewsTags(@org.springdoc.core.annotations.ParameterObject Pageable pageable) {
        LOG.debug("REST request to get a page of NewsTags");
        Page<NewsTagsDTO> page = newsTagsService.findAll(pageable);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(ServletUriComponentsBuilder.fromCurrentRequest(), page);
        return ResponseEntity.ok().headers(headers).body(page.getContent());
    }

    /**
     * 方法名称：getNewsTags
     * 描述：根据ID获取新闻标签关联记录
     *
     * @param id 新闻标签关联ID
     * @return 响应实体，包含新闻标签关联DTO对象
     * @since 1.0
     */
    @GetMapping("/{id}")
    public ResponseEntity<NewsTagsDTO> getNewsTags(@PathVariable("id") Long id) {
        LOG.debug("REST request to get NewsTags : {}", id);
        Optional<NewsTagsDTO> newsTagsDTO = newsTagsService.findOne(id);
        return ResponseUtil.wrapOrNotFound(newsTagsDTO);
    }

    /**
     * 方法名称：deleteNewsTags
     * 描述：根据ID删除新闻标签关联记录
     *
     * @param id 要删除的新闻标签关联ID
     * @return 响应实体，状态码为204（无内容）
     * @since 1.0
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteNewsTags(@PathVariable("id") Long id) {
        LOG.debug("REST request to delete NewsTags : {}", id);
        newsTagsService.delete(id);
        return ResponseEntity.noContent()
            .headers(HeaderUtil.createEntityDeletionAlert(applicationName, true, ENTITY_NAME, id.toString()))
            .build();
    }
}

package com.whiskerguard.organization.web.rest;

import com.whiskerguard.organization.repository.RiskCategoryRepository;
import com.whiskerguard.organization.service.RiskCategoryService;
import com.whiskerguard.organization.service.dto.RiskCategoryDTO;
import com.whiskerguard.organization.web.rest.errors.BadRequestAlertException;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;
import tech.jhipster.web.util.HeaderUtil;
import tech.jhipster.web.util.PaginationUtil;
import tech.jhipster.web.util.ResponseUtil;

import java.net.URI;
import java.net.URISyntaxException;
import java.util.Objects;
import java.util.Optional;

/**
 * 描述：风险类别管理的REST控制器
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/20
 */
@RestController
@RequestMapping("/api/risk/categories")
public class RiskCategoryResource {

    private static final Logger LOG = LoggerFactory.getLogger(RiskCategoryResource.class);

    private static final String ENTITY_NAME = "whiskerguardOrgServiceRiskCategory";

    @Value("${jhipster.clientApp.name}")
    private String applicationName;

    private final RiskCategoryService riskCategoryService;

    private final RiskCategoryRepository riskCategoryRepository;

    public RiskCategoryResource(RiskCategoryService riskCategoryService, RiskCategoryRepository riskCategoryRepository) {
        this.riskCategoryService = riskCategoryService;
        this.riskCategoryRepository = riskCategoryRepository;
    }

    /**
     * 方法名称：createRiskCategory
     * 描述：创建新的风险类别记录
     *
     * @param riskCategoryDTO 要创建的风险类别DTO
     * @return {@link ResponseEntity}，状态为 {@code 201 (Created)}，响应体为新创建的风险类别DTO；
     * 如果风险类别已经有ID，则状态为 {@code 400 (Bad Request)}
     * @throws URISyntaxException 如果 URI 语法不正确。
     * @since 1.0
     */
    @PostMapping("")
    public ResponseEntity<RiskCategoryDTO> createRiskCategory(@Valid @RequestBody RiskCategoryDTO riskCategoryDTO)
        throws URISyntaxException {
        LOG.debug("REST request to save RiskCategory : {}", riskCategoryDTO);
        if (riskCategoryDTO.getId() != null) {
            throw new BadRequestAlertException("新的风险类别不能有ID", ENTITY_NAME, "idexists");
        }
        riskCategoryDTO = riskCategoryService.save(riskCategoryDTO);
        return ResponseEntity.created(new URI("/api/risk/categories/" + riskCategoryDTO.getId()))
            .headers(HeaderUtil.createEntityCreationAlert(applicationName, true, ENTITY_NAME, riskCategoryDTO.getId().toString()))
            .body(riskCategoryDTO);
    }

    /**
     * 方法名称：partialUpdateRiskCategory
     * 描述：部分更新已存在的风险类别记录
     *
     * @param riskCategoryDTO 要更新的风险类别DTO
     * @return {@link ResponseEntity}，状态为 {@code 200 (OK)}，响应体为更新后的风险类别DTO；
     * 如果风险类别DTO无效，则状态为 {@code 400 (Bad Request)}；
     * 如果风险类别DTO无法更新，则状态为 {@code 500 (Internal Server Error)}
     * @since 1.0
     */
    @PatchMapping(value = "/{id}", consumes = {"application/json", "application/merge-patch+json"})
    public ResponseEntity<RiskCategoryDTO> partialUpdateRiskCategory(
        @PathVariable(value = "id", required = false) final Long id,
        @NotNull @RequestBody RiskCategoryDTO riskCategoryDTO
    ) {
        LOG.debug("REST request to partial update RiskCategory partially : {}, {}", id, riskCategoryDTO);
        if (riskCategoryDTO.getId() == null) {
            throw new BadRequestAlertException("无效的ID", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, riskCategoryDTO.getId())) {
            throw new BadRequestAlertException("无效的ID", ENTITY_NAME, "idinvalid");
        }

        if (!riskCategoryRepository.existsById(id)) {
            throw new BadRequestAlertException("实体未找到", ENTITY_NAME, "idnotfound");
        }

        Optional<RiskCategoryDTO> result = riskCategoryService.partialUpdate(riskCategoryDTO);

        return ResponseUtil.wrapOrNotFound(
            result,
            HeaderUtil.createEntityUpdateAlert(applicationName, true, ENTITY_NAME, riskCategoryDTO.getId().toString())
        );
    }

    /**
     * 方法名称：getAllRiskCategories
     * 描述：获取所有风险类别记录
     *
     * @param riskModelId 风险模型ID
     * @param pageable    分页信息
     * @return {@link ResponseEntity}，状态为 {@code 200 (OK)}，响应体为风险类别DTO的分页列表
     * @since 1.0
     */
    @GetMapping("")
    public ResponseEntity<Page<RiskCategoryDTO>> getAllRiskCategories(@RequestParam("riskModelId") Long riskModelId,
                                                                      @org.springdoc.core.annotations.ParameterObject Pageable pageable) {
        LOG.debug("REST request to get a page of RiskCategories");
        Page<RiskCategoryDTO> page = riskCategoryService.findAll(riskModelId, pageable);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(ServletUriComponentsBuilder.fromCurrentRequest(), page);
        return ResponseEntity.ok().headers(headers).body(page);
    }

    /**
     * 方法名称：getRiskCategory
     * 描述：根据ID获取风险类别记录
     *
     * @param id 风险类别的ID
     * @return {@link ResponseEntity}，状态为 {@code 200 (OK)}，响应体为风险类别DTO；
     * 如果风险类别不存在，则状态为 {@code 404 (Not Found)}
     * @since 1.0
     */
    @GetMapping("/{id}")
    public ResponseEntity<RiskCategoryDTO> getRiskCategory(@PathVariable("id") Long id) {
        LOG.debug("REST request to get RiskCategory : {}", id);
        Optional<RiskCategoryDTO> riskCategoryDTO = riskCategoryService.findOne(id);
        return ResponseUtil.wrapOrNotFound(riskCategoryDTO);
    }

    /**
     * 方法名称：deleteRiskCategory
     * 描述：根据ID删除风险类别记录
     *
     * @param id 风险类别的ID
     * @return {@link ResponseEntity}，状态为 {@code 204 (NO_CONTENT)}
     * @since 1.0
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteRiskCategory(@PathVariable("id") Long id) {
        LOG.debug("REST request to delete RiskCategory : {}", id);
        riskCategoryService.delete(id);
        return ResponseEntity.noContent()
            .headers(HeaderUtil.createEntityDeletionAlert(applicationName, true, ENTITY_NAME, id.toString()))
            .build();
    }
}

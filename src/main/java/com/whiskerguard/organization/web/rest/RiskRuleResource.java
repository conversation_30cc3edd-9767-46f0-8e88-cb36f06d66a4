package com.whiskerguard.organization.web.rest;

import com.whiskerguard.organization.repository.RiskRuleRepository;
import com.whiskerguard.organization.service.RiskRuleService;
import com.whiskerguard.organization.service.dto.RiskRuleDTO;
import com.whiskerguard.organization.web.rest.errors.BadRequestAlertException;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;
import tech.jhipster.web.util.HeaderUtil;
import tech.jhipster.web.util.PaginationUtil;
import tech.jhipster.web.util.ResponseUtil;

import java.net.URI;
import java.net.URISyntaxException;
import java.util.Objects;
import java.util.Optional;

/**
 * 描述：风险规则管理的REST控制器
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/20
 */
@RestController
@RequestMapping("/api/risk/rules")
public class RiskRuleResource {

    private static final Logger LOG = LoggerFactory.getLogger(RiskRuleResource.class);

    private static final String ENTITY_NAME = "whiskerguardOrgServiceRiskRule";

    @Value("${jhipster.clientApp.name}")
    private String applicationName;

    private final RiskRuleService riskRuleService;

    private final RiskRuleRepository riskRuleRepository;

    public RiskRuleResource(RiskRuleService riskRuleService, RiskRuleRepository riskRuleRepository) {
        this.riskRuleService = riskRuleService;
        this.riskRuleRepository = riskRuleRepository;
    }

    /**
     * 方法名称：createRiskRule
     * 描述：创建新的风险规则记录
     *
     * @param riskRuleDTO 要创建的风险规则DTO
     * @return {@link ResponseEntity}，状态为 {@code 201 (Created)}，响应体为新创建的风险规则DTO；
     * 如果风险规则已经有ID，则状态为 {@code 400 (Bad Request)}
     * @throws URISyntaxException 如果 URI 语法不正确。
     * @since 1.0
     */
    @PostMapping("")
    public ResponseEntity<RiskRuleDTO> createRiskRule(@Valid @RequestBody RiskRuleDTO riskRuleDTO) throws URISyntaxException {
        LOG.debug("REST request to save RiskRule : {}", riskRuleDTO);
        if (riskRuleDTO.getId() != null) {
            throw new BadRequestAlertException("新的风险规则不能有ID", ENTITY_NAME, "idexists");
        }
        riskRuleDTO = riskRuleService.save(riskRuleDTO);
        return ResponseEntity.created(new URI("/api/risk/rules/" + riskRuleDTO.getId()))
            .headers(HeaderUtil.createEntityCreationAlert(applicationName, true, ENTITY_NAME, riskRuleDTO.getId().toString()))
            .body(riskRuleDTO);
    }

    /**
     * 方法名称：partialUpdateRiskRule
     * 描述：部分更新已存在的风险规则记录
     *
     * @param riskRuleDTO 要更新的风险规则DTO
     * @return {@link ResponseEntity}，状态为 {@code 200 (OK)}，响应体为更新后的风险规则DTO；
     * 如果风险规则DTO无效，则状态为 {@code 400 (Bad Request)}；
     * 如果风险规则DTO无法更新，则状态为 {@code 500 (Internal Server Error)}
     * @since 1.0
     */
    @PatchMapping(value = "/{id}", consumes = {"application/json", "application/merge-patch+json"})
    public ResponseEntity<RiskRuleDTO> partialUpdateRiskRule(
        @PathVariable(value = "id", required = false) final Long id,
        @NotNull @RequestBody RiskRuleDTO riskRuleDTO
    ) {
        LOG.debug("REST request to partial update RiskRule partially : {}, {}", id, riskRuleDTO);
        if (riskRuleDTO.getId() == null) {
            throw new BadRequestAlertException("无效的ID", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, riskRuleDTO.getId())) {
            throw new BadRequestAlertException("无效的ID", ENTITY_NAME, "idinvalid");
        }

        if (!riskRuleRepository.existsById(id)) {
            throw new BadRequestAlertException("实体未找到", ENTITY_NAME, "idnotfound");
        }

        Optional<RiskRuleDTO> result = riskRuleService.partialUpdate(riskRuleDTO);

        return ResponseUtil.wrapOrNotFound(
            result,
            HeaderUtil.createEntityUpdateAlert(applicationName, true, ENTITY_NAME, riskRuleDTO.getId().toString())
        );
    }

    /**
     * 方法名称：getAllRiskRules
     * 描述：获取所有风险规则记录
     *
     * @param pageable 分页信息
     * @return {@link ResponseEntity}，状态为 {@code 200 (OK)}，响应体为风险规则DTO的分页列表
     * @since 1.0
     */
    @GetMapping("")
    public ResponseEntity<Page<RiskRuleDTO>> getAllRiskRules(@RequestParam("riskCategoryId") Long riskCategoryId,
                                                             @org.springdoc.core.annotations.ParameterObject Pageable pageable) {
        LOG.debug("REST request to get a page of RiskRules");
        Page<RiskRuleDTO> page = riskRuleService.findAll(riskCategoryId, pageable);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(ServletUriComponentsBuilder.fromCurrentRequest(), page);
        return ResponseEntity.ok().headers(headers).body(page);
    }

    /**
     * 方法名称：getRiskRule
     * 描述：根据ID获取风险规则记录
     *
     * @param id 风险规则的ID
     * @return {@link ResponseEntity}，状态为 {@code 200 (OK)}，响应体为风险规则DTO；
     * 如果风险规则不存在，则状态为 {@code 404 (Not Found)}
     * @since 1.0
     */
    @GetMapping("/{id}")
    public ResponseEntity<RiskRuleDTO> getRiskRule(@PathVariable("id") Long id) {
        LOG.debug("REST request to get RiskRule : {}", id);
        Optional<RiskRuleDTO> riskRuleDTO = riskRuleService.findOne(id);
        return ResponseUtil.wrapOrNotFound(riskRuleDTO);
    }

    /**
     * 方法名称：deleteRiskRule
     * 描述：根据ID删除风险规则记录
     *
     * @param id 风险规则的ID
     * @return {@link ResponseEntity}，状态为 {@code 204 (NO_CONTENT)}
     * @since 1.0
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteRiskRule(@PathVariable("id") Long id) {
        LOG.debug("REST request to delete RiskRule : {}", id);
        riskRuleService.delete(id);
        return ResponseEntity.noContent()
            .headers(HeaderUtil.createEntityDeletionAlert(applicationName, true, ENTITY_NAME, id.toString()))
            .build();
    }
}

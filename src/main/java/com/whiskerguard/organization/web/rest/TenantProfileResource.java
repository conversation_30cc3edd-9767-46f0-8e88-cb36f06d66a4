package com.whiskerguard.organization.web.rest;

import com.whiskerguard.organization.repository.TenantProfileRepository;
import com.whiskerguard.organization.service.TenantProfileService;
import com.whiskerguard.organization.service.dto.TenantProfileDTO;
import com.whiskerguard.organization.web.rest.errors.BadRequestAlertException;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;
import tech.jhipster.web.util.HeaderUtil;
import tech.jhipster.web.util.PaginationUtil;
import tech.jhipster.web.util.ResponseUtil;

import java.net.URI;
import java.net.URISyntaxException;
import java.util.Objects;
import java.util.Optional;

/**
 * REST controller for managing {@link com.whiskerguard.organization.domain.TenantProfile}.
 * 租户详情的REST控制器
 * 提供租户详情的CRUD操作API接口
 */
@RestController
@RequestMapping("/api/tenant/profiles")
public class TenantProfileResource {

    /**
     * 日志记录器
     */
    private static final Logger LOG = LoggerFactory.getLogger(TenantProfileResource.class);

    /**
     * 实体名称，用于错误消息
     */
    private static final String ENTITY_NAME = "tenantProfile";

    /**
     * 应用名称，从配置中获取
     */
    @Value("${jhipster.clientApp.name}")
    private String applicationName;

    /**
     * 租户详情服务
     */
    private final TenantProfileService tenantProfileService;

    /**
     * 租户详情仓库
     */
    private final TenantProfileRepository tenantProfileRepository;

    /**
     * 构造函数
     *
     * @param tenantProfileService    租户详情服务
     * @param tenantProfileRepository 租户详情仓库
     */
    public TenantProfileResource(TenantProfileService tenantProfileService, TenantProfileRepository tenantProfileRepository) {
        this.tenantProfileService = tenantProfileService;
        this.tenantProfileRepository = tenantProfileRepository;
    }

    /**
     * {@code POST  /tenant-profiles} : Create a new tenant profile.
     * 创建新的租户详情
     *
     * @param tenantProfileDTO the tenantProfileDTO to create. 要创建的租户详情DTO
     * @return the {@link ResponseEntity} with status {@code 201 (Created)} and with body the new tenantProfileDTO,
     * or with status {@code 400 (Bad Request)} if the tenantProfile has already an ID,
     * or if the registration number already exists.
     * 返回状态码为201的响应和新创建的租户详情DTO，如果租户详情已有ID则返回400错误，如果注册号已存在也返回400错误
     * @throws URISyntaxException if the Location URI syntax is incorrect. 如果位置URI语法不正确
     */
    @PostMapping("")
    public ResponseEntity<TenantProfileDTO> createTenantProfile(@Valid @RequestBody TenantProfileDTO tenantProfileDTO)
        throws URISyntaxException {
        LOG.debug("REST request to save TenantProfile : {}", tenantProfileDTO);
        if (tenantProfileDTO.getId() != null) {
            throw new BadRequestAlertException("新的租户配置不能有ID", ENTITY_NAME, "idexists");
        }

        // 验证注册号的唯一性
        tenantProfileService.validateRegistrationNumber(tenantProfileDTO.getRegistrationNumber());

        tenantProfileDTO = tenantProfileService.save(tenantProfileDTO);
        return ResponseEntity.created(new URI("/api/tenant-profiles/" + tenantProfileDTO.getId()))
            .headers(HeaderUtil.createEntityCreationAlert(applicationName, true, ENTITY_NAME, tenantProfileDTO.getId().toString()))
            .body(tenantProfileDTO);
    }

    /**
     * {@code PUT  /tenant-profiles/:id} : Updates an existing tenant profile.
     * 更新现有的租户详情
     *
     * @param id               the id of the tenantProfileDTO to save. 要保存的租户详情DTO的ID
     * @param tenantProfileDTO the tenantProfileDTO to update. 要更新的租户详情DTO
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated tenantProfileDTO,
     * or with status {@code 400 (Bad Request)} if the tenantProfileDTO is not valid,
     * or with status {@code 500 (Internal Server Error)} if the tenantProfileDTO couldn't be updated.
     * 返回状态码为200的响应和更新后的租户详情DTO，如果租户详情DTO无效则返回400错误，如果无法更新则返回500错误
     * @throws URISyntaxException if the Location URI syntax is incorrect. 如果位置URI语法不正确
     */
    @PutMapping("/{id}")
    public ResponseEntity<TenantProfileDTO> updateTenantProfile(
        @PathVariable(value = "id", required = false) final Long id,
        @Valid @RequestBody TenantProfileDTO tenantProfileDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to update TenantProfile : {}, {}", id, tenantProfileDTO);
        if (tenantProfileDTO.getId() == null) {
            throw new BadRequestAlertException("无效的ID", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, tenantProfileDTO.getId())) {
            throw new BadRequestAlertException("无效的ID", ENTITY_NAME, "idinvalid");
        }

        if (!tenantProfileRepository.existsById(id)) {
            throw new BadRequestAlertException("实体未找到", ENTITY_NAME, "idnotfound");
        }

        // 验证注册号的唯一性（排除当前记录）
        if (tenantProfileService.existsByRegistrationNumber(tenantProfileDTO.getRegistrationNumber())) {
            Optional<TenantProfileDTO> existingProfile = tenantProfileService.findOne(id);
            if (!existingProfile.orElseThrow().getRegistrationNumber().equals(tenantProfileDTO.getRegistrationNumber())) {
                throw new BadRequestAlertException("租户配置注册号已存在", ENTITY_NAME, "regnumberexists");
            }
        }

        tenantProfileDTO = tenantProfileService.update(tenantProfileDTO);
        return ResponseEntity.ok()
            .headers(HeaderUtil.createEntityUpdateAlert(applicationName, true, ENTITY_NAME, tenantProfileDTO.getId().toString()))
            .body(tenantProfileDTO);
    }

    /**
     * {@code PATCH  /tenant-profiles/:id} : Partial updates given fields of an existing tenant profile, field will ignore if it is null
     * 部分更新现有租户详情的指定字段，如果字段为null则忽略
     *
     * @param id               the id of the tenantProfileDTO to save. 要保存的租户详情DTO的ID
     * @param tenantProfileDTO the tenantProfileDTO to update. 要更新的租户详情DTO
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated tenantProfileDTO,
     * or with status {@code 400 (Bad Request)} if the tenantProfileDTO is not valid,
     * or with status {@code 404 (Not Found)} if the tenantProfileDTO is not found,
     * or with status {@code 500 (Internal Server Error)} if the tenantProfileDTO couldn't be updated.
     * 返回状态码为200的响应和更新后的租户详情DTO，如果租户详情DTO无效则返回400错误，如果找不到租户详情则返回404错误，如果无法更新则返回500错误
     * @throws URISyntaxException if the Location URI syntax is incorrect. 如果位置URI语法不正确
     */
    @PatchMapping(value = "/{id}", consumes = {"application/json", "application/merge-patch+json"})
    public ResponseEntity<TenantProfileDTO> partialUpdateTenantProfile(
        @PathVariable(value = "id", required = false) final Long id,
        @NotNull @Valid @RequestBody TenantProfileDTO tenantProfileDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to partial update TenantProfile partially : {}, {}", id, tenantProfileDTO);
        if (tenantProfileDTO.getId() == null) {
            throw new BadRequestAlertException("无效的ID", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, tenantProfileDTO.getId())) {
            throw new BadRequestAlertException("无效的ID", ENTITY_NAME, "idinvalid");
        }

        if (!tenantProfileRepository.existsById(id)) {
            throw new BadRequestAlertException("实体未找到", ENTITY_NAME, "idnotfound");
        }

        Optional<TenantProfileDTO> result = tenantProfileService.partialUpdate(tenantProfileDTO);

        return ResponseUtil.wrapOrNotFound(
            result,
            HeaderUtil.createEntityUpdateAlert(applicationName, true, ENTITY_NAME, tenantProfileDTO.getId().toString())
        );
    }

    /**
     * {@code GET  /tenant-profiles} : get all the tenant profiles.
     * 获取所有租户详情
     *
     * @param pageable the pagination information. 分页信息
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and the page of tenant profiles in body.
     * 返回状态码为200的响应和租户详情分页数据
     */
    @GetMapping("")
    public ResponseEntity<Page<TenantProfileDTO>> getAllTenantProfiles(@org.springdoc.core.annotations.ParameterObject Pageable pageable) {
        LOG.debug("REST request to get a page of TenantProfiles");
        Page<TenantProfileDTO> page = tenantProfileService.findAll(pageable);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(ServletUriComponentsBuilder.fromCurrentRequest(), page);
        return ResponseEntity.ok().headers(headers).body(page);
    }

    /**
     * {@code GET  /tenant-profiles/:id} : get the "id" tenant profile.
     * 获取指定ID的租户详情
     *
     * @param id the id of the tenantProfileDTO to retrieve. 要检索的租户详情DTO的ID
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the tenantProfileDTO, or with status {@code 404 (Not Found)}.
     * 返回状态码为200的响应和租户详情DTO，如果找不到则返回404错误
     */
    @GetMapping("/{id}")
    public ResponseEntity<TenantProfileDTO> getTenantProfile(@PathVariable("id") Long id) {
        LOG.debug("REST request to get TenantProfile : {}", id);
        Optional<TenantProfileDTO> tenantProfileDTO = tenantProfileService.findOne(id);
        return ResponseUtil.wrapOrNotFound(tenantProfileDTO);
    }

    /**
     * {@code DELETE  /tenant-profiles/:id} : delete the "id" tenant profile.
     * 删除指定ID的租户详情
     *
     * @param id the id of the tenantProfileDTO to delete. 要删除的租户详情DTO的ID
     * @return the {@link ResponseEntity} with status {@code 204 (NO_CONTENT)}.
     * 返回状态码为204的响应（无内容）
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteTenantProfile(@PathVariable("id") Long id) {
        LOG.debug("REST request to delete TenantProfile : {}", id);
        tenantProfileService.delete(id);
        return ResponseEntity.noContent()
            .headers(HeaderUtil.createEntityDeletionAlert(applicationName, true, ENTITY_NAME, id.toString()))
            .build();
    }

    /**
     * {@code GET  /tenant-profiles/validate-registration-number} : validate registration number.
     * 验证注册号的唯一性
     *
     * @param registrationNumber the registration number to validate. 要验证的注册号
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} if valid,
     * or with status {@code 400 (Bad Request)} if the registration number already exists.
     * 如果注册号有效则返回状态码为200的响应，如果注册号已存在则返回400错误
     */
    @GetMapping("/validate/registration/number")
    public ResponseEntity<Void> validateRegistrationNumber(@RequestParam String registrationNumber) {
        LOG.debug("REST request to validate registration number : {}", registrationNumber);
        tenantProfileService.validateRegistrationNumber(registrationNumber);
        return ResponseEntity.ok().build();
    }


    /**
     * {@code GET  /tenant-profiles/tenant/:tenantId} : get the "tenantId" tenant profile.
     * 获取指定租户的租户详情
     *
     * @param tenantId the id of the tenant to retrieve. 要检索的租户ID
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the tenantProfileDTO, or with status {@code 404 (Not Found)}.
     * 返回状态码为200的响应和租户详情DTO，如果找不到则返回404错误
     */
    @GetMapping("/tenant/{tenantId}")
    public ResponseEntity<TenantProfileDTO> getTenantProfileByTenant(@PathVariable("tenantId") Long tenantId) {
        LOG.debug("REST request to get TenantProfile by tenant : {}", tenantId);
        Optional<TenantProfileDTO> tenantProfileDTO = tenantProfileService.findByTenantId(tenantId);
        return ResponseUtil.wrapOrNotFound(tenantProfileDTO);
    }

}

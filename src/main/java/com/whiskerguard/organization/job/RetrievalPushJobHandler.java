package com.whiskerguard.organization.job;

import com.whiskerguard.organization.client.dto.DocumentRecordDTO;
import com.whiskerguard.organization.service.DocumentSyncService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 向量推送任务处理器
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/12
 */
@Component
public class RetrievalPushJobHandler {

    private static final Logger LOG = LoggerFactory.getLogger(RetrievalPushJobHandler.class);

    private final DocumentSyncService documentSyncService;

    public RetrievalPushJobHandler(DocumentSyncService documentSyncService) {
        this.documentSyncService = documentSyncService;
    }

    /**
     * 企业组织单元推送向量数据库
     * 任务名称：orgUnitPushJob
     * 执行周期：每7天凌晨1点执行
     * 功能：按租户一次性推送所有部门到向量数据库
     */
    @XxlJob("orgUnitPushJob")
    public void orgUnitPushJob() {
        // 获取任务参数
        String param = XxlJobHelper.getJobParam();
        LOG.info("开始执行企业组织单元按租户一次性推送向量数据库任务，参数:{}", param);
        try {
            List<DocumentRecordDTO> list = documentSyncService.syncOrgUnits();
            LOG.info("企业组织单元按租户一次性推送向量数据库任务执行完成，共处理 {} 个租户", list.size());
        } catch (Exception e) {
            LOG.error("企业组织单元按租户一次性推送向量数据库任务执行失败", e);
            throw e;
        }
    }


}

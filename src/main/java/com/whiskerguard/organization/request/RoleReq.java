package com.whiskerguard.organization.request;

import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;

/**
 * 描述：角色查询请求对象
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/21
 */
public class RoleReq extends PageRequest {

    @Schema(description = "搜索关键字")
    private String keyword;

    @Schema(description = "页码")
    private Integer page;

    @Schema(description = "每页大小")
    private Integer size;

    protected RoleReq(int page, int size) {
        super(page, size, Sort.by(Sort.Direction.DESC, "createdAt"));
    }

    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    public Integer getPage() {
        return page;
    }

    public void setPage(Integer page) {
        this.page = page;
    }

    public Integer getSize() {
        return size;
    }

    public void setSize(Integer size) {
        this.size = size;
    }
}

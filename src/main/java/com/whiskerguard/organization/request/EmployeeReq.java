package com.whiskerguard.organization.request;

import com.whiskerguard.organization.domain.enumeration.EmployeeStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;

/**
 * 描述： 员工查询请求对象
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/21
 */
public class EmployeeReq extends PageRequest {

    @Schema(description = "搜索关键字")
    private String keyword;

    @Schema(description = "员工状态")
    private EmployeeStatus status;

    @Schema(description = "用户类型：1、普通用户 2、管理员")
    private Integer userType;

    @Schema(description = "组织单元ID")
    private Long orgUnitId;

    @Schema(description = "页码")
    private Integer page;

    @Schema(description = "每页大小")
    private Integer size;

    protected EmployeeReq(int page, int size) {
        super(page, size, Sort.by(Sort.Direction.DESC, "createdAt"));
    }

    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    public EmployeeStatus getStatus() {
        return status;
    }

    public void setStatus(EmployeeStatus status) {
        this.status = status;
    }

    public Integer getUserType() {
        return userType;
    }

    public void setUserType(Integer userType) {
        this.userType = userType;
    }

    public Long getOrgUnitId() {
        return orgUnitId;
    }

    public void setOrgUnitId(Long orgUnitId) {
        this.orgUnitId = orgUnitId;
    }

    public Integer getPage() {
        return page;
    }

    public void setPage(Integer page) {
        this.page = page;
    }

    public Integer getSize() {
        return size;
    }

    public void setSize(Integer size) {
        this.size = size;
    }
}

package com.whiskerguard.organization.request;

import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;

/**
 * 描述：风险模型请求实体类。
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/8/4
 */
public class RiskModelReq extends PageRequest {

    @Schema(description = "风险模型ID")
    private Long categoryId;

    @Schema(description = "风险模型名称")
    private String name;

    @Schema(description = "创建开始时间")
    private String createdAtStart;

    @Schema(description = "创建结束时间")
    private String createdAtEnd;

    @Schema(description = "页码")
    private Integer page;

    @Schema(description = "每页大小")
    private Integer size;

    protected RiskModelReq(int page, int size) {
        super(page, size, Sort.by(Sort.Direction.DESC, "createdAt"));
    }

    public Long getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Long categoryId) {
        this.categoryId = categoryId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCreatedAtStart() {
        return createdAtStart;
    }

    public void setCreatedAtStart(String createdAtStart) {
        this.createdAtStart = createdAtStart;
    }

    public String getCreatedAtEnd() {
        return createdAtEnd;
    }

    public void setCreatedAtEnd(String createdAtEnd) {
        this.createdAtEnd = createdAtEnd;
    }

    public Integer getPage() {
        return page;
    }

    public void setPage(Integer page) {
        this.page = page;
    }

    public Integer getSize() {
        return size;
    }

    public void setSize(Integer size) {
        this.size = size;
    }
}

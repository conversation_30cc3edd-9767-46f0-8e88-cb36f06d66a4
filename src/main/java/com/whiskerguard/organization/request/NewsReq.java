package com.whiskerguard.organization.request;

import com.whiskerguard.organization.domain.enumeration.NewsStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;

/**
 * 描述：新闻动态请求参数
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/8/2
 */
public class NewsReq extends PageRequest {

    @Schema(description = "搜索关键字")
    private String keyword;

    @Schema(description = "状态")
    private NewsStatus status;

    @Schema(description = "发布开始时间")
    private String publishStartDate;

    @Schema(description = "发布结束时间")
    private String publishEndDate;

    @Schema(description = "页码")
    private Integer page;

    @Schema(description = "每页大小")
    private Integer size;

    protected NewsReq(int page, int size) {
        super(page, size, Sort.by(Sort.Direction.DESC, "createdAt"));
    }

    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    public Integer getPage() {
        return page;
    }

    public void setPage(Integer page) {
        this.page = page;
    }

    public Integer getSize() {
        return size;
    }

    public void setSize(Integer size) {
        this.size = size;
    }

    public NewsStatus getStatus() {
        return status;
    }

    public void setStatus(NewsStatus status) {
        this.status = status;
    }

    public String getPublishStartDate() {
        return publishStartDate;
    }

    public void setPublishStartDate(String publishStartDate) {
        this.publishStartDate = publishStartDate;
    }

    public String getPublishEndDate() {
        return publishEndDate;
    }

    public void setPublishEndDate(String publishEndDate) {
        this.publishEndDate = publishEndDate;
    }
}

package com.whiskerguard.organization.domain;

import jakarta.persistence.*;
import jakarta.validation.constraints.*;
import java.io.Serializable;
import java.time.Instant;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

/**
 * 投诉与建议附件表，用于存储合规案例相关的附件信息
 */
@Entity
@Table(name = "complaint_suggestion_attachment")
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
@SuppressWarnings("common-java:DuplicatedBlocks")
public class ComplaintSuggestionAttachment implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    /**
     * 租户ID
     */
    @Column(name = "tenant_id")
    private Long tenantId;

    /**
     * 关联投诉与建议ID
     */
    @NotNull
    @Column(name = "suggestion_id", nullable = false)
    private Long suggestionId;

    /**
     * 附件名称
     */
    @NotNull
    @Size(max = 256)
    @Column(name = "file_name", length = 256, nullable = false)
    private String fileName;

    /**
     * 附件存储路径或URL
     */
    @NotNull
    @Size(max = 512)
    @Column(name = "file_path", length = 512, nullable = false)
    private String filePath;

    /**
     * 附件类型
     */
    @NotNull
    @Size(max = 32)
    @Column(name = "file_type", length = 32, nullable = false)
    private String fileType;

    /**
     * 附件大小
     */
    @Size(max = 32)
    @Column(name = "file_size", length = 32)
    private String fileSize;

    /**
     * 附件描述
     */
    @Size(max = 512)
    @Column(name = "file_desc", length = 512)
    private String fileDesc;

    /**
     * 补充字段
     */
    @Lob
    @Column(name = "metadata")
    private String metadata;

    /**
     * 当前版本号
     */
    @Column(name = "version")
    private Integer version;

    /**
     * 上传者
     */
    @NotNull
    @Size(max = 64)
    @Column(name = "uploaded_by", length = 64, nullable = false)
    private String uploadedBy;

    /**
     * 上传时间
     */
    @Column(name = "uploaded_at")
    private Instant uploadedAt;

    /**
     * 是否删除：0 表示正常 1 表示已删除
     */
    @Column(name = "is_deleted")
    private Boolean isDeleted;

    // jhipster-needle-entity-add-field - JHipster will add fields here

    public Long getId() {
        return this.id;
    }

    public ComplaintSuggestionAttachment id(Long id) {
        this.setId(id);
        return this;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTenantId() {
        return this.tenantId;
    }

    public ComplaintSuggestionAttachment tenantId(Long tenantId) {
        this.setTenantId(tenantId);
        return this;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public Long getSuggestionId() {
        return this.suggestionId;
    }

    public ComplaintSuggestionAttachment suggestionId(Long suggestionId) {
        this.setSuggestionId(suggestionId);
        return this;
    }

    public void setSuggestionId(Long suggestionId) {
        this.suggestionId = suggestionId;
    }

    public String getFileName() {
        return this.fileName;
    }

    public ComplaintSuggestionAttachment fileName(String fileName) {
        this.setFileName(fileName);
        return this;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getFilePath() {
        return this.filePath;
    }

    public ComplaintSuggestionAttachment filePath(String filePath) {
        this.setFilePath(filePath);
        return this;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    public String getFileType() {
        return this.fileType;
    }

    public ComplaintSuggestionAttachment fileType(String fileType) {
        this.setFileType(fileType);
        return this;
    }

    public void setFileType(String fileType) {
        this.fileType = fileType;
    }

    public String getFileSize() {
        return this.fileSize;
    }

    public ComplaintSuggestionAttachment fileSize(String fileSize) {
        this.setFileSize(fileSize);
        return this;
    }

    public void setFileSize(String fileSize) {
        this.fileSize = fileSize;
    }

    public String getFileDesc() {
        return this.fileDesc;
    }

    public ComplaintSuggestionAttachment fileDesc(String fileDesc) {
        this.setFileDesc(fileDesc);
        return this;
    }

    public void setFileDesc(String fileDesc) {
        this.fileDesc = fileDesc;
    }

    public String getMetadata() {
        return this.metadata;
    }

    public ComplaintSuggestionAttachment metadata(String metadata) {
        this.setMetadata(metadata);
        return this;
    }

    public void setMetadata(String metadata) {
        this.metadata = metadata;
    }

    public Integer getVersion() {
        return this.version;
    }

    public ComplaintSuggestionAttachment version(Integer version) {
        this.setVersion(version);
        return this;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public String getUploadedBy() {
        return this.uploadedBy;
    }

    public ComplaintSuggestionAttachment uploadedBy(String uploadedBy) {
        this.setUploadedBy(uploadedBy);
        return this;
    }

    public void setUploadedBy(String uploadedBy) {
        this.uploadedBy = uploadedBy;
    }

    public Instant getUploadedAt() {
        return this.uploadedAt;
    }

    public ComplaintSuggestionAttachment uploadedAt(Instant uploadedAt) {
        this.setUploadedAt(uploadedAt);
        return this;
    }

    public void setUploadedAt(Instant uploadedAt) {
        this.uploadedAt = uploadedAt;
    }

    public Boolean getIsDeleted() {
        return this.isDeleted;
    }

    public ComplaintSuggestionAttachment isDeleted(Boolean isDeleted) {
        this.setIsDeleted(isDeleted);
        return this;
    }

    public void setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
    }

    // jhipster-needle-entity-add-getters-setters - JHipster will add getters and setters here

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof ComplaintSuggestionAttachment)) {
            return false;
        }
        return getId() != null && getId().equals(((ComplaintSuggestionAttachment) o).getId());
    }

    @Override
    public int hashCode() {
        // see https://vladmihalcea.com/how-to-implement-equals-and-hashcode-using-the-jpa-entity-identifier/
        return getClass().hashCode();
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "ComplaintSuggestionAttachment{" +
            "id=" + getId() +
            ", tenantId=" + getTenantId() +
            ", suggestionId=" + getSuggestionId() +
            ", fileName='" + getFileName() + "'" +
            ", filePath='" + getFilePath() + "'" +
            ", fileType='" + getFileType() + "'" +
            ", fileSize='" + getFileSize() + "'" +
            ", fileDesc='" + getFileDesc() + "'" +
            ", metadata='" + getMetadata() + "'" +
            ", version=" + getVersion() +
            ", uploadedBy='" + getUploadedBy() + "'" +
            ", uploadedAt='" + getUploadedAt() + "'" +
            ", isDeleted='" + getIsDeleted() + "'" +
            "}";
    }
}

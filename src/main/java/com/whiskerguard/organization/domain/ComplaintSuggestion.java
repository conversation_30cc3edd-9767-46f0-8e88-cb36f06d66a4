package com.whiskerguard.organization.domain;

import jakarta.persistence.*;
import jakarta.validation.constraints.*;
import java.io.Serializable;
import java.time.Instant;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

/**
 * 投诉与建议表
 */
@Entity
@Table(name = "complaint_suggestion")
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
@SuppressWarnings("common-java:DuplicatedBlocks")
public class ComplaintSuggestion implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    /**
     * 租户ID
     */
    @Column(name = "tenant_id")
    private Long tenantId;

    /**
     * 员工ID
     */
    @Column(name = "employee_id")
    private Long employeeId;

    /**
     * 详情
     */
    @Size(max = 500)
    @Column(name = "detail", length = 500)
    private String detail;

    /**
     * 是否匿名：0、否 1、是
     */
    @Column(name = "is_anonymous")
    private Integer isAnonymous;

    /**
     * 联系方式
     */
    @Size(max = 32)
    @Column(name = "contact_way", length = 32)
    private String contactWay;

    /**
     * 扩展元数据（JSONB）
     */
    @Lob
    @Column(name = "metadata")
    private String metadata;

    /**
     * 乐观锁版本
     */
    @NotNull
    @Column(name = "version", nullable = false)
    private Integer version;

    /**
     * 创建者
     */
    @Size(max = 64)
    @Column(name = "created_by", length = 64)
    private String createdBy;

    /**
     * 创建时间
     */
    @NotNull
    @Column(name = "created_at", nullable = false)
    private Instant createdAt;

    /**
     * 更新者
     */
    @Size(max = 64)
    @Column(name = "updated_by", length = 64)
    private String updatedBy;

    /**
     * 更新时间
     */
    @NotNull
    @Column(name = "updated_at", nullable = false)
    private Instant updatedAt;

    /**
     * 软删除标志
     */
    @NotNull
    @Column(name = "is_deleted", nullable = false)
    private Boolean isDeleted;

    // jhipster-needle-entity-add-field - JHipster will add fields here

    public Long getId() {
        return this.id;
    }

    public ComplaintSuggestion id(Long id) {
        this.setId(id);
        return this;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTenantId() {
        return this.tenantId;
    }

    public ComplaintSuggestion tenantId(Long tenantId) {
        this.setTenantId(tenantId);
        return this;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public Long getEmployeeId() {
        return this.employeeId;
    }

    public ComplaintSuggestion employeeId(Long employeeId) {
        this.setEmployeeId(employeeId);
        return this;
    }

    public void setEmployeeId(Long employeeId) {
        this.employeeId = employeeId;
    }

    public String getDetail() {
        return this.detail;
    }

    public ComplaintSuggestion detail(String detail) {
        this.setDetail(detail);
        return this;
    }

    public void setDetail(String detail) {
        this.detail = detail;
    }

    public Integer getIsAnonymous() {
        return this.isAnonymous;
    }

    public ComplaintSuggestion isAnonymous(Integer isAnonymous) {
        this.setIsAnonymous(isAnonymous);
        return this;
    }

    public void setIsAnonymous(Integer isAnonymous) {
        this.isAnonymous = isAnonymous;
    }

    public String getContactWay() {
        return this.contactWay;
    }

    public ComplaintSuggestion contactWay(String contactWay) {
        this.setContactWay(contactWay);
        return this;
    }

    public void setContactWay(String contactWay) {
        this.contactWay = contactWay;
    }

    public String getMetadata() {
        return this.metadata;
    }

    public ComplaintSuggestion metadata(String metadata) {
        this.setMetadata(metadata);
        return this;
    }

    public void setMetadata(String metadata) {
        this.metadata = metadata;
    }

    public Integer getVersion() {
        return this.version;
    }

    public ComplaintSuggestion version(Integer version) {
        this.setVersion(version);
        return this;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public String getCreatedBy() {
        return this.createdBy;
    }

    public ComplaintSuggestion createdBy(String createdBy) {
        this.setCreatedBy(createdBy);
        return this;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public Instant getCreatedAt() {
        return this.createdAt;
    }

    public ComplaintSuggestion createdAt(Instant createdAt) {
        this.setCreatedAt(createdAt);
        return this;
    }

    public void setCreatedAt(Instant createdAt) {
        this.createdAt = createdAt;
    }

    public String getUpdatedBy() {
        return this.updatedBy;
    }

    public ComplaintSuggestion updatedBy(String updatedBy) {
        this.setUpdatedBy(updatedBy);
        return this;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    public Instant getUpdatedAt() {
        return this.updatedAt;
    }

    public ComplaintSuggestion updatedAt(Instant updatedAt) {
        this.setUpdatedAt(updatedAt);
        return this;
    }

    public void setUpdatedAt(Instant updatedAt) {
        this.updatedAt = updatedAt;
    }

    public Boolean getIsDeleted() {
        return this.isDeleted;
    }

    public ComplaintSuggestion isDeleted(Boolean isDeleted) {
        this.setIsDeleted(isDeleted);
        return this;
    }

    public void setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
    }

    // jhipster-needle-entity-add-getters-setters - JHipster will add getters and setters here

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof ComplaintSuggestion)) {
            return false;
        }
        return getId() != null && getId().equals(((ComplaintSuggestion) o).getId());
    }

    @Override
    public int hashCode() {
        // see https://vladmihalcea.com/how-to-implement-equals-and-hashcode-using-the-jpa-entity-identifier/
        return getClass().hashCode();
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "ComplaintSuggestion{" +
            "id=" + getId() +
            ", tenantId=" + getTenantId() +
            ", employeeId=" + getEmployeeId() +
            ", detail='" + getDetail() + "'" +
            ", isAnonymous=" + getIsAnonymous() +
            ", contactWay='" + getContactWay() + "'" +
            ", metadata='" + getMetadata() + "'" +
            ", version=" + getVersion() +
            ", createdBy='" + getCreatedBy() + "'" +
            ", createdAt='" + getCreatedAt() + "'" +
            ", updatedBy='" + getUpdatedBy() + "'" +
            ", updatedAt='" + getUpdatedAt() + "'" +
            ", isDeleted='" + getIsDeleted() + "'" +
            "}";
    }
}

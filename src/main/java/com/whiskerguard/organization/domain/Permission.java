package com.whiskerguard.organization.domain;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.whiskerguard.organization.domain.enumeration.ResourceType;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import java.io.Serializable;
import java.time.Instant;

/**
 * 权限（Permission）实体
 */
@Entity
@Table(name = "permission")
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
@SuppressWarnings("common-java:DuplicatedBlocks")
public class Permission implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键 ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    /**
     * 租户 ID
     */
    @NotNull
    @Column(name = "tenant_id", nullable = false)
    private Long tenantId;

    /**
     * 服务名称
     */
    @Size(max = 64)
    @Column(name = "service_name", length = 64, nullable = false)
    private String serviceName;

    /**
     * 权限编码
     */
    @Size(max = 64)
    @Column(name = "code", length = 64, nullable = false)
    private String code;

    /**
     * 权限名称
     */
    @NotNull
    @Size(max = 128)
    @Column(name = "name", length = 128, nullable = false)
    private String name;

    /**
     * 资源类型
     */
    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "resource_type", nullable = false)
    private ResourceType resourceType;

    /**
     * URL 模式
     */
    @Size(max = 256)
    @Column(name = "url_pattern", length = 256)
    private String urlPattern;

    /**
     * HTTP 方法
     */
    @Size(max = 16)
    @Column(name = "method", length = 16)
    private String method;

    /**
     * 前端路由
     */
    @Size(max = 128)
    @Column(name = "frontend_route", length = 128)
    private String frontendRoute;

    /**
     * 后端接口
     */
    @Size(max = 128)
    @Column(name = "backend_url", length = 128)
    private String backendUrl;

    /**
     * 图标
     */
    @Size(max = 64)
    @Column(name = "icon", length = 64)
    private String icon;

    /**
     * 排序
     */
    @Column(name = "sort_order")
    private Integer sortOrder;

    /**
     * 组件路径
     */
    @Column(name = "component")
    private String component;

    /**
     * 重定向
     */
    @Column(name = "redirect")
    private String redirect;

    /**
     * 描述
     */
    @Column(name = "description")
    private String description;

    /**
     * 扩展元数据
     */
    @Lob
    @Column(name = "metadata")
    private String metadata;

    /**
     * 乐观锁版本
     */
    @NotNull
    @Column(name = "version", nullable = false)
    private Integer version;

    /**
     * 创建者
     */
    @Column(name = "created_by")
    private String createdBy;

    /**
     * 创建时间
     */
    @NotNull
    @Column(name = "created_at", nullable = false)
    private Instant createdAt;

    /**
     * 更新者
     */
    @Column(name = "updated_by")
    private String updatedBy;

    /**
     * 更新时间
     */
    @NotNull
    @Column(name = "updated_at", nullable = false)
    private Instant updatedAt;

    /**
     * 软删除标志
     */
    @NotNull
    @Column(name = "is_deleted", nullable = false)
    private Boolean isDeleted;

    /**
     * 是否租户可用
     */
    @NotNull
    @Column(name = "is_available", nullable = false, columnDefinition = "tinyint DEFAULT '1' COMMENT '是否租户可用 0、否 1、是'")
    private Boolean isAvailable;

    /**
     * 是否展示
     */
    @Column(name = "is_show")
    private Boolean isShow;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonIgnoreProperties(value = {"parent"}, allowSetters = true)
    private Permission parent;

    // jhipster-needle-entity-add-field - JHipster will add fields here

    public Long getId() {
        return this.id;
    }

    public Permission id(Long id) {
        this.setId(id);
        return this;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTenantId() {
        return this.tenantId;
    }

    public Permission tenantId(Long tenantId) {
        this.setTenantId(tenantId);
        return this;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public String getServiceName() {
        return this.serviceName;
    }

    public Permission serviceName(String serviceName) {
        this.setServiceName(serviceName);
        return this;
    }

    public void setServiceName(String serviceName) {
        this.serviceName = serviceName;
    }

    public String getCode() {
        return this.code;
    }

    public Permission code(String code) {
        this.setCode(code);
        return this;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return this.name;
    }

    public Permission name(String name) {
        this.setName(name);
        return this;
    }

    public void setName(String name) {
        this.name = name;
    }

    public ResourceType getResourceType() {
        return this.resourceType;
    }

    public Permission resourceType(ResourceType resourceType) {
        this.setResourceType(resourceType);
        return this;
    }

    public void setResourceType(ResourceType resourceType) {
        this.resourceType = resourceType;
    }

    public String getUrlPattern() {
        return this.urlPattern;
    }

    public Permission urlPattern(String urlPattern) {
        this.setUrlPattern(urlPattern);
        return this;
    }

    public void setUrlPattern(String urlPattern) {
        this.urlPattern = urlPattern;
    }

    public String getMethod() {
        return this.method;
    }

    public Permission method(String method) {
        this.setMethod(method);
        return this;
    }

    public void setMethod(String method) {
        this.method = method;
    }

    public String getFrontendRoute() {
        return this.frontendRoute;
    }

    public Permission frontendRoute(String frontendRoute) {
        this.setFrontendRoute(frontendRoute);
        return this;
    }

    public void setFrontendRoute(String frontendRoute) {
        this.frontendRoute = frontendRoute;
    }

    public String getBackendUrl() {
        return this.backendUrl;
    }

    public Permission backendUrl(String backendUrl) {
        this.setBackendUrl(backendUrl);
        return this;
    }

    public void setBackendUrl(String backendUrl) {
        this.backendUrl = backendUrl;
    }

    public String getIcon() {
        return this.icon;
    }

    public Permission icon(String icon) {
        this.setIcon(icon);
        return this;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public Integer getSortOrder() {
        return this.sortOrder;
    }

    public Permission sortOrder(Integer sortOrder) {
        this.setSortOrder(sortOrder);
        return this;
    }

    public void setSortOrder(Integer sortOrder) {
        this.sortOrder = sortOrder;
    }

    public String getComponent() {
        return this.component;
    }

    public Permission component(String component) {
        this.setComponent(component);
        return this;
    }

    public void setComponent(String component) {
        this.component = component;
    }

    public String getRedirect() {
        return this.redirect;
    }

    public Permission redirect(String redirect) {
        this.setRedirect(redirect);
        return this;
    }

    public void setRedirect(String redirect) {
        this.redirect = redirect;
    }

    public String getDescription() {
        return this.description;
    }

    public Permission description(String description) {
        this.setDescription(description);
        return this;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getMetadata() {
        return this.metadata;
    }

    public Permission metadata(String metadata) {
        this.setMetadata(metadata);
        return this;
    }

    public void setMetadata(String metadata) {
        this.metadata = metadata;
    }

    public Integer getVersion() {
        return this.version;
    }

    public Permission version(Integer version) {
        this.setVersion(version);
        return this;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public String getCreatedBy() {
        return this.createdBy;
    }

    public Permission createdBy(String createdBy) {
        this.setCreatedBy(createdBy);
        return this;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public Instant getCreatedAt() {
        return this.createdAt;
    }

    public Permission createdAt(Instant createdAt) {
        this.setCreatedAt(createdAt);
        return this;
    }

    public void setCreatedAt(Instant createdAt) {
        this.createdAt = createdAt;
    }

    public String getUpdatedBy() {
        return this.updatedBy;
    }

    public Permission updatedBy(String updatedBy) {
        this.setUpdatedBy(updatedBy);
        return this;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    public Instant getUpdatedAt() {
        return this.updatedAt;
    }

    public Permission updatedAt(Instant updatedAt) {
        this.setUpdatedAt(updatedAt);
        return this;
    }

    public void setUpdatedAt(Instant updatedAt) {
        this.updatedAt = updatedAt;
    }

    public Boolean getIsDeleted() {
        return this.isDeleted;
    }

    public Permission isDeleted(Boolean isDeleted) {
        this.setIsDeleted(isDeleted);
        return this;
    }

    public void setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
    }

    public Boolean getIsAvailable() {
        return this.isAvailable;
    }

    public Permission isAvailable(Boolean isAvailable) {
        this.setIsAvailable(isAvailable);
        return this;
    }

    public void setIsAvailable(Boolean isAvailable) {
        this.isAvailable = isAvailable;
    }

    public Boolean getIsShow() {
        return this.isShow;
    }

    public Permission isShow(Boolean isShow) {
        this.setIsShow(isShow);
        return this;
    }

    public void setIsShow(Boolean isShow) {
        this.isShow = isShow;
    }

    public Permission getParent() {
        return this.parent;
    }

    public void setParent(Permission permission) {
        this.parent = permission;
    }

    public Permission parent(Permission permission) {
        this.setParent(permission);
        return this;
    }

    // jhipster-needle-entity-add-getters-setters - JHipster will add getters and setters here

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof Permission)) {
            return false;
        }
        return getId() != null && getId().equals(((Permission) o).getId());
    }

    @Override
    public int hashCode() {
        // see https://vladmihalcea.com/how-to-implement-equals-and-hashcode-using-the-jpa-entity-identifier/
        return getClass().hashCode();
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "Permission{" +
            "id=" + getId() +
            ", tenantId=" + getTenantId() +
            ", serviceName='" + getServiceName() + "'" +
            ", code='" + getCode() + "'" +
            ", name='" + getName() + "'" +
            ", resourceType='" + getResourceType() + "'" +
            ", urlPattern='" + getUrlPattern() + "'" +
            ", method='" + getMethod() + "'" +
            ", frontendRoute='" + getFrontendRoute() + "'" +
            ", backendUrl='" + getBackendUrl() + "'" +
            ", icon='" + getIcon() + "'" +
            ", sortOrder=" + getSortOrder() +
            ", component='" + getComponent() + "'" +
            ", redirect='" + getRedirect() + "'" +
            ", description='" + getDescription() + "'" +
            ", metadata='" + getMetadata() + "'" +
            ", version=" + getVersion() +
            ", createdBy='" + getCreatedBy() + "'" +
            ", createdAt='" + getCreatedAt() + "'" +
            ", updatedBy='" + getUpdatedBy() + "'" +
            ", updatedAt='" + getUpdatedAt() + "'" +
            ", isDeleted='" + getIsDeleted() + "'" +
            ", isAvailable='" + getIsAvailable() + "'" +
            ", isShow='" + getIsShow() + "'" +
            "}";
    }
}

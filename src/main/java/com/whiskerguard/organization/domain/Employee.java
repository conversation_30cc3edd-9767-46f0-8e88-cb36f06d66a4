package com.whiskerguard.organization.domain;

import com.whiskerguard.organization.domain.enumeration.EmployeeGender;
import com.whiskerguard.organization.domain.enumeration.EmployeeStatus;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import java.io.Serializable;
import java.time.Instant;
import java.time.LocalDate;

/**
 * 员工（Employee）实体
 */
@Entity
@Table(name = "employee")
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
@SuppressWarnings("common-java:DuplicatedBlocks")
public class Employee implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键 ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    /**
     * 租户 ID
     */
    @NotNull
    @Column(name = "tenant_id", nullable = false)
    private Long tenantId;

    /**
     * 登录用户名
     */
    @NotNull
    @Size(max = 64)
    @Column(name = "username", length = 64, nullable = false)
    private String username;

    /**
     * 登录密码（加密存储）
     */
    @NotNull
    @Size(max = 128)
    @Column(name = "password", length = 128, nullable = false)
    private String password;

    /**
     * 密码盐值
     */
    @Size(max = 32)
    @Column(name = "salt", length = 32)
    private String salt;

    /**
     * 真实姓名
     */
    @NotNull
    @Size(max = 128)
    @Column(name = "real_name", length = 128, nullable = false)
    private String realName;

    /**
     * 头像
     */
    @Size(max = 255)
    @Column(name = "avatar", length = 255, nullable = false)
    private String avatar;

    /**
     * 邮箱地址
     */
    @Size(max = 128)
    @Column(name = "email", length = 128, nullable = false)
    private String email;

    /**
     * 手机号
     */
    @Size(max = 32)
    @Column(name = "phone", length = 32)
    private String phone;

    /**
     * 性别
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "gender")
    private EmployeeGender gender;

    /**
     * 生日
     */
    @Column(name = "birth_date")
    private LocalDate birthDate;

    /**
     * 身份证号
     */
    @Size(max = 18)
    @Column(name = "id_card", length = 18)
    private String idCard;

    /**
     * 员工编号（工号）
     */
    @Size(max = 64)
    @Column(name = "employee_no", length = 64)
    private String employeeNo;

    /**
     * 员工状态
     */
    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private EmployeeStatus status;

    /**
     * 入职日期
     */
    @Column(name = "hire_date")
    private LocalDate hireDate;

    /**
     * 离职日期
     */
    @Column(name = "leave_date")
    private LocalDate leaveDate;

    /**
     * 扩展元数据
     */
    @Lob
    @Column(name = "metadata")
    private String metadata;

    /**
     * 乐观锁版本
     */
    @NotNull
    @Column(name = "version", nullable = false)
    private Integer version;

    /**
     * 创建者
     */
    @Column(name = "created_by")
    private String createdBy;

    /**
     * 创建时间
     */
    @NotNull
    @Column(name = "created_at", nullable = false)
    private Instant createdAt;

    /**
     * 更新者
     */
    @Column(name = "updated_by")
    private String updatedBy;

    /**
     * 更新时间
     */
    @NotNull
    @Column(name = "updated_at", nullable = false)
    private Instant updatedAt;

    /**
     * 软删除标志
     */
    @NotNull
    @Column(name = "is_deleted", nullable = false)
    private Boolean isDeleted;

    /**
     * 最后登录时间
     */
    @Column(name = "last_login_time")
    private Instant lastLoginTime;

    /**
     * 最后登录IP
     */
    @Size(max = 64)
    @Column(name = "last_login_ip", length = 64)
    private String lastLoginIp;

    /**
     * 登录失败次数
     */
    @Column(name = "login_failure_count")
    private Integer loginFailureCount;

    /**
     * 账号锁定时间
     */
    @Column(name = "account_locked_time")
    private Instant accountLockedTime;

    /**
     * 密码修改时间
     */
    @Column(name = "password_changed_time")
    private Instant passwordChangedTime;

    /**
     * 密码过期时间
     */
    @Column(name = "password_expired_time")
    private Instant passwordExpiredTime;

    /**
     * 是否首次登录
     */
    @NotNull
    @Column(name = "is_first_login", nullable = false)
    private Boolean isFirstLogin;

    /**
     * 是否强制修改密码
     */
    @NotNull
    @Column(name = "force_change_password", nullable = false)
    private Boolean forceChangePassword;

    /**
     * 微信OpenID
     */
    @Size(max = 64)
    @Column(name = "wechat_open_id", length = 64)
    private String wechatOpenId;

    /**
     * 微信UnionID
     */
    @Size(max = 64)
    @Column(name = "wechat_union_id", length = 64)
    private String wechatUnionId;

    // jhipster-needle-entity-add-field - JHipster will add fields here

    public Long getId() {
        return this.id;
    }

    public Employee id(Long id) {
        this.setId(id);
        return this;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTenantId() {
        return this.tenantId;
    }

    public Employee tenantId(Long tenantId) {
        this.setTenantId(tenantId);
        return this;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public String getUsername() {
        return this.username;
    }

    public Employee username(String username) {
        this.setUsername(username);
        return this;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return this.password;
    }

    public Employee password(String password) {
        this.setPassword(password);
        return this;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getSalt() {
        return this.salt;
    }

    public Employee salt(String salt) {
        this.setSalt(salt);
        return this;
    }

    public void setSalt(String salt) {
        this.salt = salt;
    }

    public String getRealName() {
        return this.realName;
    }

    public Employee realName(String realName) {
        this.setRealName(realName);
        return this;
    }

    public void setRealName(String realName) {
        this.realName = realName;
    }

    public String getAvatar() {
        return this.avatar;
    }

    public Employee avatar(String avatar) {
        this.setAvatar(avatar);
        return this;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public String getEmail() {
        return this.email;
    }

    public Employee email(String email) {
        this.setEmail(email);
        return this;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getPhone() {
        return this.phone;
    }

    public Employee phone(String phone) {
        this.setPhone(phone);
        return this;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public EmployeeGender getGender() {
        return this.gender;
    }

    public Employee gender(EmployeeGender gender) {
        this.setGender(gender);
        return this;
    }

    public void setGender(EmployeeGender gender) {
        this.gender = gender;
    }

    public LocalDate getBirthDate() {
        return this.birthDate;
    }

    public Employee birthDate(LocalDate birthDate) {
        this.setBirthDate(birthDate);
        return this;
    }

    public void setBirthDate(LocalDate birthDate) {
        this.birthDate = birthDate;
    }

    public String getIdCard() {
        return this.idCard;
    }

    public Employee idCard(String idCard) {
        this.setIdCard(idCard);
        return this;
    }

    public void setIdCard(String idCard) {
        this.idCard = idCard;
    }

    public String getEmployeeNo() {
        return this.employeeNo;
    }

    public Employee employeeNo(String employeeNo) {
        this.setEmployeeNo(employeeNo);
        return this;
    }

    public void setEmployeeNo(String employeeNo) {
        this.employeeNo = employeeNo;
    }

    public EmployeeStatus getStatus() {
        return this.status;
    }

    public Employee status(EmployeeStatus status) {
        this.setStatus(status);
        return this;
    }

    public void setStatus(EmployeeStatus status) {
        this.status = status;
    }

    public LocalDate getHireDate() {
        return this.hireDate;
    }

    public Employee hireDate(LocalDate hireDate) {
        this.setHireDate(hireDate);
        return this;
    }

    public void setHireDate(LocalDate hireDate) {
        this.hireDate = hireDate;
    }

    public LocalDate getLeaveDate() {
        return this.leaveDate;
    }

    public Employee leaveDate(LocalDate leaveDate) {
        this.setLeaveDate(leaveDate);
        return this;
    }

    public void setLeaveDate(LocalDate leaveDate) {
        this.leaveDate = leaveDate;
    }

    public String getMetadata() {
        return this.metadata;
    }

    public Employee metadata(String metadata) {
        this.setMetadata(metadata);
        return this;
    }

    public void setMetadata(String metadata) {
        this.metadata = metadata;
    }

    public Integer getVersion() {
        return this.version;
    }

    public Employee version(Integer version) {
        this.setVersion(version);
        return this;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public String getCreatedBy() {
        return this.createdBy;
    }

    public Employee createdBy(String createdBy) {
        this.setCreatedBy(createdBy);
        return this;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public Instant getCreatedAt() {
        return this.createdAt;
    }

    public Employee createdAt(Instant createdAt) {
        this.setCreatedAt(createdAt);
        return this;
    }

    public void setCreatedAt(Instant createdAt) {
        this.createdAt = createdAt;
    }

    public String getUpdatedBy() {
        return this.updatedBy;
    }

    public Employee updatedBy(String updatedBy) {
        this.setUpdatedBy(updatedBy);
        return this;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    public Instant getUpdatedAt() {
        return this.updatedAt;
    }

    public Employee updatedAt(Instant updatedAt) {
        this.setUpdatedAt(updatedAt);
        return this;
    }

    public void setUpdatedAt(Instant updatedAt) {
        this.updatedAt = updatedAt;
    }

    public Boolean getIsDeleted() {
        return this.isDeleted;
    }

    public Employee isDeleted(Boolean isDeleted) {
        this.setIsDeleted(isDeleted);
        return this;
    }

    public void setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
    }

    public Instant getLastLoginTime() {
        return this.lastLoginTime;
    }

    public Employee lastLoginTime(Instant lastLoginTime) {
        this.setLastLoginTime(lastLoginTime);
        return this;
    }

    public void setLastLoginTime(Instant lastLoginTime) {
        this.lastLoginTime = lastLoginTime;
    }

    public String getLastLoginIp() {
        return this.lastLoginIp;
    }

    public Employee lastLoginIp(String lastLoginIp) {
        this.setLastLoginIp(lastLoginIp);
        return this;
    }

    public void setLastLoginIp(String lastLoginIp) {
        this.lastLoginIp = lastLoginIp;
    }

    public Integer getLoginFailureCount() {
        return this.loginFailureCount;
    }

    public Employee loginFailureCount(Integer loginFailureCount) {
        this.setLoginFailureCount(loginFailureCount);
        return this;
    }

    public void setLoginFailureCount(Integer loginFailureCount) {
        this.loginFailureCount = loginFailureCount;
    }

    public Instant getAccountLockedTime() {
        return this.accountLockedTime;
    }

    public Employee accountLockedTime(Instant accountLockedTime) {
        this.setAccountLockedTime(accountLockedTime);
        return this;
    }

    public void setAccountLockedTime(Instant accountLockedTime) {
        this.accountLockedTime = accountLockedTime;
    }

    public Instant getPasswordChangedTime() {
        return this.passwordChangedTime;
    }

    public Employee passwordChangedTime(Instant passwordChangedTime) {
        this.setPasswordChangedTime(passwordChangedTime);
        return this;
    }

    public void setPasswordChangedTime(Instant passwordChangedTime) {
        this.passwordChangedTime = passwordChangedTime;
    }

    public Instant getPasswordExpiredTime() {
        return this.passwordExpiredTime;
    }

    public Employee passwordExpiredTime(Instant passwordExpiredTime) {
        this.setPasswordExpiredTime(passwordExpiredTime);
        return this;
    }

    public void setPasswordExpiredTime(Instant passwordExpiredTime) {
        this.passwordExpiredTime = passwordExpiredTime;
    }

    public Boolean getIsFirstLogin() {
        return this.isFirstLogin;
    }

    public Employee isFirstLogin(Boolean isFirstLogin) {
        this.setIsFirstLogin(isFirstLogin);
        return this;
    }

    public void setIsFirstLogin(Boolean isFirstLogin) {
        this.isFirstLogin = isFirstLogin;
    }

    public Boolean getForceChangePassword() {
        return this.forceChangePassword;
    }

    public Employee forceChangePassword(Boolean forceChangePassword) {
        this.setForceChangePassword(forceChangePassword);
        return this;
    }

    public void setForceChangePassword(Boolean forceChangePassword) {
        this.forceChangePassword = forceChangePassword;
    }

    public String getWechatOpenId() {
        return this.wechatOpenId;
    }

    public Employee wechatOpenId(String wechatOpenId) {
        this.setWechatOpenId(wechatOpenId);
        return this;
    }

    public void setWechatOpenId(String wechatOpenId) {
        this.wechatOpenId = wechatOpenId;
    }

    public String getWechatUnionId() {
        return this.wechatUnionId;
    }

    public Employee wechatUnionId(String wechatUnionId) {
        this.setWechatUnionId(wechatUnionId);
        return this;
    }

    public void setWechatUnionId(String wechatUnionId) {
        this.wechatUnionId = wechatUnionId;
    }

    // jhipster-needle-entity-add-getters-setters - JHipster will add getters and setters here

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof Employee)) {
            return false;
        }
        return getId() != null && getId().equals(((Employee) o).getId());
    }

    @Override
    public int hashCode() {
        // see https://vladmihalcea.com/how-to-implement-equals-and-hashcode-using-the-jpa-entity-identifier/
        return getClass().hashCode();
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "Employee{" +
            "id=" + getId() +
            ", tenantId=" + getTenantId() +
            ", username='" + getUsername() + "'" +
            ", password='" + getPassword() + "'" +
            ", salt='" + getSalt() + "'" +
            ", realName='" + getRealName() + "'" +
            ", avatar='" + getAvatar() + "'" +
            ", email='" + getEmail() + "'" +
            ", phone='" + getPhone() + "'" +
            ", gender='" + getGender() + "'" +
            ", birthDate='" + getBirthDate() + "'" +
            ", idCard='" + getIdCard() + "'" +
            ", employeeNo='" + getEmployeeNo() + "'" +
            ", status='" + getStatus() + "'" +
            ", hireDate='" + getHireDate() + "'" +
            ", leaveDate='" + getLeaveDate() + "'" +
            ", metadata='" + getMetadata() + "'" +
            ", version=" + getVersion() +
            ", createdBy='" + getCreatedBy() + "'" +
            ", createdAt='" + getCreatedAt() + "'" +
            ", updatedBy='" + getUpdatedBy() + "'" +
            ", updatedAt='" + getUpdatedAt() + "'" +
            ", isDeleted='" + getIsDeleted() + "'" +
            ", lastLoginTime='" + getLastLoginTime() + "'" +
            ", lastLoginIp='" + getLastLoginIp() + "'" +
            ", loginFailureCount=" + getLoginFailureCount() +
            ", accountLockedTime='" + getAccountLockedTime() + "'" +
            ", passwordChangedTime='" + getPasswordChangedTime() + "'" +
            ", passwordExpiredTime='" + getPasswordExpiredTime() + "'" +
            ", isFirstLogin='" + getIsFirstLogin() + "'" +
            ", forceChangePassword='" + getForceChangePassword() + "'" +
            ", wechatOpenId='" + getWechatOpenId() + "'" +
            ", wechatUnionId='" + getWechatUnionId() + "'" +
            "}";
    }
}

package com.whiskerguard.organization.domain;

import com.whiskerguard.organization.domain.enumeration.EnterpriseType;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;

/**
 * 租户详情（TenantProfile）实体
 */
@Entity
@Table(name = "tenant_profile")
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
@SuppressWarnings("common-java:DuplicatedBlocks")
public class TenantProfile implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键 ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    /**
     * 工商注册号
     */
    @Column(name = "registration_number")
    private String registrationNumber;

    /**
     * 注册日期
     */
    @Column(name = "registration_date")
    private LocalDate registrationDate;

    /**
     * 注册资本
     */
    @Column(name = "registered_capital", precision = 21, scale = 2)
    private BigDecimal registeredCapital;

    /**
     * 公司类型
     */
    @Column(name = "company_type")
    private String companyType;

    /**
     * 经营范围
     */
    @Lob
    @Column(name = "business_scope")
    private String businessScope;

    /**
     * 所属行业
     */
    @Column(name = "industry")
    private String industry;

    /**
     * 税务登记号
     */
    @Column(name = "tax_registration_number")
    private String taxRegistrationNumber;

    /**
     * 组织机构代码
     */
    @Column(name = "organization_code")
    private String organizationCode;

    /**
     * 注册地址
     */
    @Column(name = "registered_address")
    private String registeredAddress;

    /**
     * 邮政编码
     */
    @Column(name = "postal_code")
    private String postalCode;

    /**
     * 官网
     */
    @Column(name = "website")
    private String website;

    /**
     * 传真
     */
    @Column(name = "fax")
    private String fax;

    /**
     * 联系人
     */
    @Column(name = "contact_person")
    private String contactPerson;

    /**
     * 联系人手机
     */
    @Column(name = "contact_mobile")
    private String contactMobile;

    /**
     * 联系人邮箱
     */
    @Column(name = "contact_email")
    private String contactEmail;

    /**
     * 开户行
     */
    @Column(name = "bank_name")
    private String bankName;

    /**
     * 银行账号
     */
    @Column(name = "bank_account")
    private String bankAccount;

    /**
     * 营业执照路径
     */
    @Column(name = "business_license_path")
    private String businessLicensePath;

    /**
     * 法人代表
     */
    @Column(name = "legal_person")
    private String legalPerson;

    /**
     * 法人证件号
     */
    @Column(name = "legal_person_id")
    private String legalPersonId;

    /**
     * 企业类型
     */
    @Column(name = "enterprise_type")
    private Integer enterpriseType = 3;

    /**
     * 扩展元数据
     */
    @Lob
    @Column(name = "metadata")
    private String metadata;

    /**
     * 乐观锁版本
     */
    @NotNull
    @Column(name = "version", nullable = false)
    private Integer version;

    /**
     * 创建者
     */
    @Column(name = "created_by")
    private String createdBy;

    /**
     * 创建时间
     */
    @NotNull
    @Column(name = "created_at", nullable = false)
    private Instant createdAt;

    /**
     * 更新者
     */
    @Column(name = "updated_by")
    private String updatedBy;

    /**
     * 更新时间
     */
    @NotNull
    @Column(name = "updated_at", nullable = false)
    private Instant updatedAt;

    /**
     * 软删除标志
     */
    @NotNull
    @Column(name = "is_deleted", nullable = false)
    private Boolean isDeleted;

    @ManyToOne(fetch = FetchType.LAZY)
    private Tenant tenant;

    // jhipster-needle-entity-add-field - JHipster will add fields here

    public Long getId() {
        return this.id;
    }

    public TenantProfile id(Long id) {
        this.setId(id);
        return this;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getRegistrationNumber() {
        return this.registrationNumber;
    }

    public TenantProfile registrationNumber(String registrationNumber) {
        this.setRegistrationNumber(registrationNumber);
        return this;
    }

    public void setRegistrationNumber(String registrationNumber) {
        this.registrationNumber = registrationNumber;
    }

    public LocalDate getRegistrationDate() {
        return this.registrationDate;
    }

    public TenantProfile registrationDate(LocalDate registrationDate) {
        this.setRegistrationDate(registrationDate);
        return this;
    }

    public void setRegistrationDate(LocalDate registrationDate) {
        this.registrationDate = registrationDate;
    }

    public BigDecimal getRegisteredCapital() {
        return this.registeredCapital;
    }

    public TenantProfile registeredCapital(BigDecimal registeredCapital) {
        this.setRegisteredCapital(registeredCapital);
        return this;
    }

    public void setRegisteredCapital(BigDecimal registeredCapital) {
        this.registeredCapital = registeredCapital;
    }

    public String getCompanyType() {
        return this.companyType;
    }

    public TenantProfile companyType(String companyType) {
        this.setCompanyType(companyType);
        return this;
    }

    public void setCompanyType(String companyType) {
        this.companyType = companyType;
    }

    public String getBusinessScope() {
        return this.businessScope;
    }

    public TenantProfile businessScope(String businessScope) {
        this.setBusinessScope(businessScope);
        return this;
    }

    public void setBusinessScope(String businessScope) {
        this.businessScope = businessScope;
    }

    public String getIndustry() {
        return this.industry;
    }

    public TenantProfile industry(String industry) {
        this.setIndustry(industry);
        return this;
    }

    public void setIndustry(String industry) {
        this.industry = industry;
    }

    public String getTaxRegistrationNumber() {
        return this.taxRegistrationNumber;
    }

    public TenantProfile taxRegistrationNumber(String taxRegistrationNumber) {
        this.setTaxRegistrationNumber(taxRegistrationNumber);
        return this;
    }

    public void setTaxRegistrationNumber(String taxRegistrationNumber) {
        this.taxRegistrationNumber = taxRegistrationNumber;
    }

    public String getOrganizationCode() {
        return this.organizationCode;
    }

    public TenantProfile organizationCode(String organizationCode) {
        this.setOrganizationCode(organizationCode);
        return this;
    }

    public void setOrganizationCode(String organizationCode) {
        this.organizationCode = organizationCode;
    }

    public String getRegisteredAddress() {
        return this.registeredAddress;
    }

    public TenantProfile registeredAddress(String registeredAddress) {
        this.setRegisteredAddress(registeredAddress);
        return this;
    }

    public void setRegisteredAddress(String registeredAddress) {
        this.registeredAddress = registeredAddress;
    }

    public String getPostalCode() {
        return this.postalCode;
    }

    public TenantProfile postalCode(String postalCode) {
        this.setPostalCode(postalCode);
        return this;
    }

    public void setPostalCode(String postalCode) {
        this.postalCode = postalCode;
    }

    public String getWebsite() {
        return this.website;
    }

    public TenantProfile website(String website) {
        this.setWebsite(website);
        return this;
    }

    public void setWebsite(String website) {
        this.website = website;
    }

    public String getFax() {
        return this.fax;
    }

    public TenantProfile fax(String fax) {
        this.setFax(fax);
        return this;
    }

    public void setFax(String fax) {
        this.fax = fax;
    }

    public String getContactPerson() {
        return this.contactPerson;
    }

    public TenantProfile contactPerson(String contactPerson) {
        this.setContactPerson(contactPerson);
        return this;
    }

    public void setContactPerson(String contactPerson) {
        this.contactPerson = contactPerson;
    }

    public String getContactMobile() {
        return this.contactMobile;
    }

    public TenantProfile contactMobile(String contactMobile) {
        this.setContactMobile(contactMobile);
        return this;
    }

    public void setContactMobile(String contactMobile) {
        this.contactMobile = contactMobile;
    }

    public String getContactEmail() {
        return this.contactEmail;
    }

    public TenantProfile contactEmail(String contactEmail) {
        this.setContactEmail(contactEmail);
        return this;
    }

    public void setContactEmail(String contactEmail) {
        this.contactEmail = contactEmail;
    }

    public String getBankName() {
        return this.bankName;
    }

    public TenantProfile bankName(String bankName) {
        this.setBankName(bankName);
        return this;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public String getBankAccount() {
        return this.bankAccount;
    }

    public TenantProfile bankAccount(String bankAccount) {
        this.setBankAccount(bankAccount);
        return this;
    }

    public void setBankAccount(String bankAccount) {
        this.bankAccount = bankAccount;
    }

    public String getBusinessLicensePath() {
        return this.businessLicensePath;
    }

    public TenantProfile businessLicensePath(String businessLicensePath) {
        this.setBusinessLicensePath(businessLicensePath);
        return this;
    }

    public void setBusinessLicensePath(String businessLicensePath) {
        this.businessLicensePath = businessLicensePath;
    }

    public String getLegalPerson() {
        return this.legalPerson;
    }

    public TenantProfile legalPerson(String legalPerson) {
        this.setLegalPerson(legalPerson);
        return this;
    }

    public void setLegalPerson(String legalPerson) {
        this.legalPerson = legalPerson;
    }

    public String getLegalPersonId() {
        return this.legalPersonId;
    }

    public TenantProfile legalPersonId(String legalPersonId) {
        this.setLegalPersonId(legalPersonId);
        return this;
    }

    public void setLegalPersonId(String legalPersonId) {
        this.legalPersonId = legalPersonId;
    }

    public EnterpriseType getEnterpriseType() {
        return this.enterpriseType != null ? EnterpriseType.fromValue(this.enterpriseType) : null;
    }

    public TenantProfile enterpriseType(EnterpriseType enterpriseType) {
        this.setEnterpriseType(enterpriseType);
        return this;
    }

    public void setEnterpriseType(EnterpriseType enterpriseType) {
        this.enterpriseType = enterpriseType != null ? enterpriseType.getValue() : null;
    }

    public Integer getEnterpriseTypeValue() {
        return this.enterpriseType;
    }

    public void setEnterpriseTypeValue(Integer enterpriseType) {
        this.enterpriseType = enterpriseType;
    }

    public String getMetadata() {
        return this.metadata;
    }

    public TenantProfile metadata(String metadata) {
        this.setMetadata(metadata);
        return this;
    }

    public void setMetadata(String metadata) {
        this.metadata = metadata;
    }

    public Integer getVersion() {
        return this.version;
    }

    public TenantProfile version(Integer version) {
        this.setVersion(version);
        return this;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public String getCreatedBy() {
        return this.createdBy;
    }

    public TenantProfile createdBy(String createdBy) {
        this.setCreatedBy(createdBy);
        return this;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public Instant getCreatedAt() {
        return this.createdAt;
    }

    public TenantProfile createdAt(Instant createdAt) {
        this.setCreatedAt(createdAt);
        return this;
    }

    public void setCreatedAt(Instant createdAt) {
        this.createdAt = createdAt;
    }

    public String getUpdatedBy() {
        return this.updatedBy;
    }

    public TenantProfile updatedBy(String updatedBy) {
        this.setUpdatedBy(updatedBy);
        return this;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    public Instant getUpdatedAt() {
        return this.updatedAt;
    }

    public TenantProfile updatedAt(Instant updatedAt) {
        this.setUpdatedAt(updatedAt);
        return this;
    }

    public void setUpdatedAt(Instant updatedAt) {
        this.updatedAt = updatedAt;
    }

    public Boolean getIsDeleted() {
        return this.isDeleted;
    }

    public TenantProfile isDeleted(Boolean isDeleted) {
        this.setIsDeleted(isDeleted);
        return this;
    }

    public void setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
    }

    public Tenant getTenant() {
        return this.tenant;
    }

    public void setTenant(Tenant tenant) {
        this.tenant = tenant;
    }

    public TenantProfile tenant(Tenant tenant) {
        this.setTenant(tenant);
        return this;
    }

    // jhipster-needle-entity-add-getters-setters - JHipster will add getters and setters here

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof TenantProfile)) {
            return false;
        }
        return getId() != null && getId().equals(((TenantProfile) o).getId());
    }

    @Override
    public int hashCode() {
        // see https://vladmihalcea.com/how-to-implement-equals-and-hashcode-using-the-jpa-entity-identifier/
        return getClass().hashCode();
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "TenantProfile{" +
            "id=" + getId() +
            ", registrationNumber='" + getRegistrationNumber() + "'" +
            ", registrationDate='" + getRegistrationDate() + "'" +
            ", registeredCapital=" + getRegisteredCapital() +
            ", companyType='" + getCompanyType() + "'" +
            ", businessScope='" + getBusinessScope() + "'" +
            ", industry='" + getIndustry() + "'" +
            ", taxRegistrationNumber='" + getTaxRegistrationNumber() + "'" +
            ", organizationCode='" + getOrganizationCode() + "'" +
            ", registeredAddress='" + getRegisteredAddress() + "'" +
            ", postalCode='" + getPostalCode() + "'" +
            ", website='" + getWebsite() + "'" +
            ", fax='" + getFax() + "'" +
            ", contactPerson='" + getContactPerson() + "'" +
            ", contactMobile='" + getContactMobile() + "'" +
            ", contactEmail='" + getContactEmail() + "'" +
            ", bankName='" + getBankName() + "'" +
            ", bankAccount='" + getBankAccount() + "'" +
            ", businessLicensePath='" + getBusinessLicensePath() + "'" +
            ", legalPerson='" + getLegalPerson() + "'" +
            ", legalPersonId='" + getLegalPersonId() + "'" +
            ", enterpriseType='" + getEnterpriseType() + "'" +
            ", metadata='" + getMetadata() + "'" +
            ", version=" + getVersion() +
            ", createdBy='" + getCreatedBy() + "'" +
            ", createdAt='" + getCreatedAt() + "'" +
            ", updatedBy='" + getUpdatedBy() + "'" +
            ", updatedAt='" + getUpdatedAt() + "'" +
            ", isDeleted='" + getIsDeleted() + "'" +
            "}";
    }
}

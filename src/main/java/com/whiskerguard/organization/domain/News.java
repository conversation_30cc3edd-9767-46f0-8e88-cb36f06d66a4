package com.whiskerguard.organization.domain;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.whiskerguard.organization.domain.enumeration.NewsStatus;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

import java.io.Serializable;
import java.time.Instant;

/**
 * A News.
 */
@Entity
@Table(name = "news")
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
@SuppressWarnings("common-java:DuplicatedBlocks")
public class News implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    /**
     * 租户 ID
     */
    @NotNull
    @Column(name = "tenant_id", nullable = false)
    private Long tenantId;

    /**
     * 状态
     */
    @NotNull
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private NewsStatus status;

    /**
     * 排序序号
     */
    @Column(name = "sort_order")
    private Integer sortOrder;

    /**
     * 副标题
     */
    @Column(name = "subtitle")
    private String subtitle;

    /**
     * 标题
     */
    @NotNull
    @Column(name = "title", nullable = false)
    private String title;

    /**
     * 摘要
     */
    @Column(name = "summary")
    private String summary;

    /**
     * 关键词（用于SEO，全局搜索）
     */
    @Column(name = "keywords")
    private String keywords;

    /**
     * 正文内容
     */
    @Lob
    @Column(name = "content")
    private String content;

    /**
     * 发布时间
     */
    @Column(name = "publish_date")
    private Instant publishDate;

    /**
     * 正式发布时戳
     */
    @Column(name = "published_at")
    private Instant publishedAt;

    /**
     * 浏览量
     */
    @Column(name = "view_count")
    private Integer viewCount;

    /**
     * 点赞数
     */
    @Column(name = "like_count")
    private Integer likeCount;

    /**
     * 评论数
     */
    @Column(name = "comment_count")
    private Integer commentCount;

    /**
     * 分享数
     */
    @Column(name = "share_count")
    private Integer shareCount;

    /**
     * 封面图 URL
     */
    @Column(name = "cover_image_url")
    private String coverImageUrl;

    /**
     * 是否置顶
     */
    @Column(name = "is_sticky")
    private Boolean isSticky;

    /**
     * 置顶开始时间
     */
    @Column(name = "sticky_start_time")
    private Instant stickyStartTime;

    /**
     * 置顶结束时间
     */
    @Column(name = "sticky_end_time")
    private Instant stickyEndTime;

    /**
     * 扩展元数据（JSONB）
     */
    @Lob
    @Column(name = "metadata")
    private String metadata;

    /**
     * 乐观锁版本
     */
    @NotNull
    @Column(name = "version", nullable = false)
    private Integer version;

    /**
     * 创建者
     */
    @Column(name = "created_by")
    private String createdBy;

    /**
     * 创建时间
     */
    @NotNull
    @Column(name = "created_at", nullable = false)
    private Instant createdAt;

    /**
     * 更新者
     */
    @Column(name = "updated_by")
    private String updatedBy;

    /**
     * 更新时间
     */
    @NotNull
    @Column(name = "updated_at", nullable = false)
    private Instant updatedAt;

    /**
     * 软删除标志
     */
    @NotNull
    @Column(name = "is_deleted", nullable = false)
    private Boolean isDeleted;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonIgnoreProperties(value = {"orgUnit", "parent"}, allowSetters = true)
    private NewsCategory category;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonIgnoreProperties(value = {"parent"}, allowSetters = true)
    private OrgUnit orgUnit;

    @ManyToOne(fetch = FetchType.LAZY)
    private Employee author;

    public Long getId() {
        return this.id;
    }

    public News id(Long id) {
        this.setId(id);
        return this;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTenantId() {
        return this.tenantId;
    }

    public News tenantId(Long tenantId) {
        this.setTenantId(tenantId);
        return this;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public NewsStatus getStatus() {
        return this.status;
    }

    public News status(NewsStatus status) {
        this.setStatus(status);
        return this;
    }

    public void setStatus(NewsStatus status) {
        this.status = status;
    }

    public Integer getSortOrder() {
        return this.sortOrder;
    }

    public News sortOrder(Integer sortOrder) {
        this.setSortOrder(sortOrder);
        return this;
    }

    public void setSortOrder(Integer sortOrder) {
        this.sortOrder = sortOrder;
    }

    public String getSubtitle() {
        return this.subtitle;
    }

    public News subtitle(String subtitle) {
        this.setSubtitle(subtitle);
        return this;
    }

    public void setSubtitle(String subtitle) {
        this.subtitle = subtitle;
    }

    public String getTitle() {
        return this.title;
    }

    public News title(String title) {
        this.setTitle(title);
        return this;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getSummary() {
        return this.summary;
    }

    public News summary(String summary) {
        this.setSummary(summary);
        return this;
    }

    public void setSummary(String summary) {
        this.summary = summary;
    }

    public String getKeywords() {
        return this.keywords;
    }

    public News keywords(String keywords) {
        this.setKeywords(keywords);
        return this;
    }

    public void setKeywords(String keywords) {
        this.keywords = keywords;
    }

    public String getContent() {
        return this.content;
    }

    public News content(String content) {
        this.setContent(content);
        return this;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Instant getPublishDate() {
        return this.publishDate;
    }

    public News publishDate(Instant publishDate) {
        this.setPublishDate(publishDate);
        return this;
    }

    public void setPublishDate(Instant publishDate) {
        this.publishDate = publishDate;
    }

    public Instant getPublishedAt() {
        return this.publishedAt;
    }

    public News publishedAt(Instant publishedAt) {
        this.setPublishedAt(publishedAt);
        return this;
    }

    public void setPublishedAt(Instant publishedAt) {
        this.publishedAt = publishedAt;
    }

    public Integer getViewCount() {
        return this.viewCount;
    }

    public News viewCount(Integer viewCount) {
        this.setViewCount(viewCount);
        return this;
    }

    public void setViewCount(Integer viewCount) {
        this.viewCount = viewCount;
    }

    public Integer getLikeCount() {
        return this.likeCount;
    }

    public News likeCount(Integer likeCount) {
        this.setLikeCount(likeCount);
        return this;
    }

    public void setLikeCount(Integer likeCount) {
        this.likeCount = likeCount;
    }

    public Integer getCommentCount() {
        return this.commentCount;
    }

    public News commentCount(Integer commentCount) {
        this.setCommentCount(commentCount);
        return this;
    }

    public void setCommentCount(Integer commentCount) {
        this.commentCount = commentCount;
    }

    public Integer getShareCount() {
        return this.shareCount;
    }

    public News shareCount(Integer shareCount) {
        this.setShareCount(shareCount);
        return this;
    }

    public void setShareCount(Integer shareCount) {
        this.shareCount = shareCount;
    }

    public String getCoverImageUrl() {
        return this.coverImageUrl;
    }

    public News coverImageUrl(String coverImageUrl) {
        this.setCoverImageUrl(coverImageUrl);
        return this;
    }

    public void setCoverImageUrl(String coverImageUrl) {
        this.coverImageUrl = coverImageUrl;
    }

    public Boolean getIsSticky() {
        return this.isSticky;
    }

    public News isSticky(Boolean isSticky) {
        this.setIsSticky(isSticky);
        return this;
    }

    public void setIsSticky(Boolean isSticky) {
        this.isSticky = isSticky;
    }

    public Instant getStickyStartTime() {
        return this.stickyStartTime;
    }

    public News stickyStartTime(Instant stickyStartTime) {
        this.setStickyStartTime(stickyStartTime);
        return this;
    }

    public void setStickyStartTime(Instant stickyStartTime) {
        this.stickyStartTime = stickyStartTime;
    }

    public Instant getStickyEndTime() {
        return this.stickyEndTime;
    }

    public News stickyEndTime(Instant stickyEndTime) {
        this.setStickyEndTime(stickyEndTime);
        return this;
    }

    public void setStickyEndTime(Instant stickyEndTime) {
        this.stickyEndTime = stickyEndTime;
    }

    public String getMetadata() {
        return this.metadata;
    }

    public News metadata(String metadata) {
        this.setMetadata(metadata);
        return this;
    }

    public void setMetadata(String metadata) {
        this.metadata = metadata;
    }

    public Integer getVersion() {
        return this.version;
    }

    public News version(Integer version) {
        this.setVersion(version);
        return this;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public String getCreatedBy() {
        return this.createdBy;
    }

    public News createdBy(String createdBy) {
        this.setCreatedBy(createdBy);
        return this;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public Instant getCreatedAt() {
        return this.createdAt;
    }

    public News createdAt(Instant createdAt) {
        this.setCreatedAt(createdAt);
        return this;
    }

    public void setCreatedAt(Instant createdAt) {
        this.createdAt = createdAt;
    }

    public String getUpdatedBy() {
        return this.updatedBy;
    }

    public News updatedBy(String updatedBy) {
        this.setUpdatedBy(updatedBy);
        return this;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    public Instant getUpdatedAt() {
        return this.updatedAt;
    }

    public News updatedAt(Instant updatedAt) {
        this.setUpdatedAt(updatedAt);
        return this;
    }

    public void setUpdatedAt(Instant updatedAt) {
        this.updatedAt = updatedAt;
    }

    public Boolean getIsDeleted() {
        return this.isDeleted;
    }

    public News isDeleted(Boolean isDeleted) {
        this.setIsDeleted(isDeleted);
        return this;
    }

    public void setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
    }

    public NewsCategory getCategory() {
        return this.category;
    }

    public void setCategory(NewsCategory newsCategory) {
        this.category = newsCategory;
    }

    public News category(NewsCategory newsCategory) {
        this.setCategory(newsCategory);
        return this;
    }

    public OrgUnit getOrgUnit() {
        return this.orgUnit;
    }

    public void setOrgUnit(OrgUnit orgUnit) {
        this.orgUnit = orgUnit;
    }

    public News orgUnit(OrgUnit orgUnit) {
        this.setOrgUnit(orgUnit);
        return this;
    }

    public Employee getAuthor() {
        return this.author;
    }

    public void setAuthor(Employee employee) {
        this.author = employee;
    }

    public News author(Employee employee) {
        this.setAuthor(employee);
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof News)) {
            return false;
        }
        return getId() != null && getId().equals(((News) o).getId());
    }

    @Override
    public int hashCode() {
        // see https://vladmihalcea.com/how-to-implement-equals-and-hashcode-using-the-jpa-entity-identifier/
        return getClass().hashCode();
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "News{" +
            "id=" + getId() +
            ", tenantId=" + getTenantId() +
            ", status='" + getStatus() + "'" +
            ", sortOrder=" + getSortOrder() +
            ", subtitle='" + getSubtitle() + "'" +
            ", title='" + getTitle() + "'" +
            ", summary='" + getSummary() + "'" +
            ", keywords='" + getKeywords() + "'" +
            ", content='" + getContent() + "'" +
            ", publishDate='" + getPublishDate() + "'" +
            ", publishedAt='" + getPublishedAt() + "'" +
            ", viewCount=" + getViewCount() +
            ", likeCount=" + getLikeCount() +
            ", commentCount=" + getCommentCount() +
            ", shareCount=" + getShareCount() +
            ", coverImageUrl='" + getCoverImageUrl() + "'" +
            ", isSticky='" + getIsSticky() + "'" +
            ", stickyStartTime='" + getStickyStartTime() + "'" +
            ", stickyEndTime='" + getStickyEndTime() + "'" +
            ", metadata='" + getMetadata() + "'" +
            ", version=" + getVersion() +
            ", createdBy='" + getCreatedBy() + "'" +
            ", createdAt='" + getCreatedAt() + "'" +
            ", updatedBy='" + getUpdatedBy() + "'" +
            ", updatedAt='" + getUpdatedAt() + "'" +
            ", isDeleted='" + getIsDeleted() + "'" +
            "}";
    }
}

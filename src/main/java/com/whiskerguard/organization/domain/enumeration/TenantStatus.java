package com.whiskerguard.organization.domain.enumeration;

/**
 * 租户状态枚举
 */
public enum TenantStatus {
    TRIAL(0, "试用期"),
    ACTIVE(1, "正式"),
    SUSPENDED(2, "已暂停"),
    EXPIRED(3, "已过期"),
    DELETED(4, "已删除");

    private final int value;
    private final String description;

    TenantStatus(int value, String description) {
        this.value = value;
        this.description = description;
    }

    public int getValue() {
        return value;
    }

    public String getDescription() {
        return description;
    }

    public static TenantStatus fromValue(int value) {
        for (TenantStatus status : values()) {
            if (status.getValue() == value) {
                return status;
            }
        }
        throw new IllegalArgumentException("Invalid tenant status value: " + value);
    }
}

package com.whiskerguard.organization.domain.enumeration;

/**
 * 员工状态枚举
 */
public enum EmployeeStatus {
    ACTIVE(1, "在职"),
    INACTIVE(0, "离职"),
    FROZEN(2, "冻结");

    private final int value;
    private final String description;

    EmployeeStatus(int value, String description) {
        this.value = value;
        this.description = description;
    }

    public int getValue() {
        return value;
    }

    public String getDescription() {
        return description;
    }

    public static EmployeeStatus fromValue(int value) {
        for (EmployeeStatus status : values()) {
            if (status.getValue() == value) {
                return status;
            }
        }
        throw new IllegalArgumentException("Invalid employee status value: " + value);
    }

    public static EmployeeStatus fromString(String name) {
        try {
            return EmployeeStatus.valueOf(name);
        } catch (IllegalArgumentException e) {
            throw new IllegalArgumentException("Invalid employee status name: " + name);
        }
    }
}

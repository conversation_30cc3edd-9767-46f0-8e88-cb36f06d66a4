package com.whiskerguard.organization.domain.enumeration;

/**
 * 组织单元类型枚举
 * 定义组织单元的分类及层级关系
 */
public enum OrgUnitType {
    /**
     * 公司类型 (顶级组织)
     */
    COMPANY(0, "company"),

    /**
     * 子公司 (具有独立法人资格的分支机构)
     */
    SUBSIDIARY(1, "subsidiary"),

    /**
     * 事业群 (跨部门的业务单元)
     */
    BUSINESS_GROUP(2, "business_group"),

    /**
     * 部门 (常规职能部门)
     */
    DEPARTMENT(3, "department"),

    /**
     * 团队 (最小工作单元)
     */
    TEAM(4, "team");

    private final int level;
    private final String code;

    OrgUnitType(int level, String code) {
        this.level = level;
        this.code = code;
    }

    public int getLevel() {
        return level;
    }

    public String getCode() {
        return code;
    }

    /**
     * 判断当前类型是否允许包含下级类型
     */
    public boolean canContain(OrgUnitType childType) {
        return childType != null && childType.level > this.level;
    }

    /**
     * 通过code获取枚举实例（兼容旧代码）
     */
    public static OrgUnitType fromCode(String code) {
        for (OrgUnitType type : values()) {
            if (type.code.equals(code)) {
                return type;
            }
        }
        return valueOf(code.toUpperCase());
    }
}

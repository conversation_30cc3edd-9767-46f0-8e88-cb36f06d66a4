package com.whiskerguard.organization.domain.enumeration;

/**
 * 企业类型枚举
 */
public enum EnterpriseType {
    MICRO(1, "小微企业"),
    SMALL_MEDIUM(2, "中小型企业"),
    LARGE(3, "大型企业"),
    GROUP(4, "集团");

    private final int value;
    private final String description;

    EnterpriseType(int value, String description) {
        this.value = value;
        this.description = description;
    }

    public int getValue() {
        return value;
    }

    public String getDescription() {
        return description;
    }

    public static EnterpriseType fromValue(int value) {
        for (EnterpriseType type : values()) {
            if (type.getValue() == value) {
                return type;
            }
        }
        throw new IllegalArgumentException("Invalid enterprise type value: " + value);
    }
}

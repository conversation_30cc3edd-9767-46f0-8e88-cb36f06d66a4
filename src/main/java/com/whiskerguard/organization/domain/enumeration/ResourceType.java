package com.whiskerguard.organization.domain.enumeration;

/**
 * 资源类型枚举
 * 区分后端接口权限与前端菜单/按钮/字段权限
 */
public enum ResourceType {
    /**
     * 后端API接口 (格式: HTTP方法+路径 如 POST:/api/orgs)
     */
    API("api", "接口权限"),

    /**
     * 前端菜单项 (对应前端路由配置)
     */
    MENU("menu", "菜单权限"),

    /**
     * 页面操作按钮 (如新增、导出等按钮)
     */
    BUTTON("button", "按钮权限"),

    /**
     * 数据字段 (控制字段可见/可编辑)
     */
    FIELD("field", "字段权限"),

    /**
     * 数据范围 (控制可访问的数据范围)
     */
    DATA_SCOPE("data_scope", "数据范围"),

    /**
     * 文件/附件 (控制文件访问权限)
     */
    FILE("file", "文件权限");

    private final String code;
    private final String description;

    ResourceType(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    /**
     * 判断是否为前端资源类型
     */
    public boolean isFrontendResource() {
        return this == MENU || this == BUTTON || this == FIELD;
    }

    /**
     * 判断是否为后端资源类型
     */
    public boolean isBackendResource() {
        return this == API || this == DATA_SCOPE || this == FILE;
    }

    /**
     * 通过code获取枚举实例（兼容旧代码）
     */
    public static ResourceType fromCode(String code) {
        for (ResourceType type : values()) {
            if (type.code.equals(code)) {
                return type;
            }
        }
        return valueOf(code.toUpperCase());
    }
}

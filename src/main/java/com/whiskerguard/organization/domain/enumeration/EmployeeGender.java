package com.whiskerguard.organization.domain.enumeration;

/**
 * 员工性别枚举
 */
public enum EmployeeGender {
    /**
     * 未知性别
     */
    UNKNOWN(-1, "未知"),
    /**
     * 男性
     */
    MALE(1, "男"),
    /**
     * 女性
     */
    FEMALE(0, "女");

    private final int value;
    private final String description;

    EmployeeGender(int value, String description) {
        this.value = value;
        this.description = description;
    }

    public int getValue() {
        return value;
    }

    public String getDescription() {
        return description;
    }

    public static EmployeeGender fromValue(int value) {
        for (EmployeeGender gender : values()) {
            if (gender.getValue() == value) {
                return gender;
            }
        }
        throw new IllegalArgumentException("Invalid employee gender value: " + value);
    }

    public static EmployeeGender fromString(String name) {
        try {
            return EmployeeGender.valueOf(name);
        } catch (IllegalArgumentException e) {
            throw new IllegalArgumentException("Invalid employee gender name: " + name);
        }
    }
}

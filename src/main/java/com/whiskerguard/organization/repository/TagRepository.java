package com.whiskerguard.organization.repository;

import com.whiskerguard.organization.domain.Tag;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 描述：标签的数据访问层接口
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/18
 */
@Repository
public interface TagRepository extends JpaRepository<Tag, Long> {
    default Optional<Tag> findOneWithEagerRelationships(Long id) {
        return this.findOneWithToOneRelationships(id);
    }

    default List<Tag> findAllWithEagerRelationships() {
        return this.findAllWithToOneRelationships();
    }

    default Page<Tag> findAllWithEagerRelationships(Pageable pageable) {
        return this.findAllWithToOneRelationships(pageable);
    }

    default Page<Tag> findAllWithEagerRelationshipsAndNotDeleted(Long tenantId, Pageable pageable) {
        return this.findAllWithToOneRelationshipsAndNotDeleted(tenantId, pageable);
    }

    @Query(value = "select tag from Tag tag left join fetch tag.category", countQuery = "select count(tag) from Tag tag")
    Page<Tag> findAllWithToOneRelationships(Pageable pageable);

    @Query("select tag from Tag tag left join fetch tag.category")
    List<Tag> findAllWithToOneRelationships();

    @Query("select tag from Tag tag left join fetch tag.category where tag.id =:id")
    Optional<Tag> findOneWithToOneRelationships(@Param("id") Long id);

    /**
     * Find all non-deleted tags with pagination
     */
    @Query("select tag from Tag tag where tag.tenantId = :tenantId and tag.isDeleted = false")
    Page<Tag> findByIsDeletedFalse(Long tenantId, Pageable pageable);

    /**
     * Find all non-deleted tags with eager relationships and pagination
     */
    @Query(value = "select tag from Tag tag left join fetch tag.category where tag.isDeleted = false and tag.tenantId = :tenantId",
        countQuery = "select count(tag) from Tag tag where tag.isDeleted = false and tag.tenantId = :tenantId")
    Page<Tag> findAllWithToOneRelationshipsAndNotDeleted(Long tenantId, Pageable pageable);

    @Query("select tag from Tag tag where tag.name = :name and tag.tenantId = :tenantId and tag.isDeleted = false")
    Tag findByNameAndTenantId(String name, Long tenantId);
}

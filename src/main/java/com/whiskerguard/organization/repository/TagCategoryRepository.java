package com.whiskerguard.organization.repository;

import com.whiskerguard.organization.domain.TagCategory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

/**
 * 描述：标签分类的数据访问层接口
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/18
 */
@SuppressWarnings("unused")
@Repository
public interface TagCategoryRepository extends JpaRepository<TagCategory, Long> {
    /**
     * Find all non-deleted tag categories with pagination
     */
    @Query("select tagCategory from TagCategory tagCategory where tagCategory.tenantId = :tenantId and tagCategory.isDeleted = false")
    Page<TagCategory> findByIsDeletedFalse(Long tenantId, Pageable pageable);

    @Query("select tagCategory from TagCategory tagCategory where tagCategory.name = :name and tagCategory.tenantId = :tenantId and tagCategory.isDeleted = false")
    TagCategory findByNameAndTenantId(String name, Long tenantId);
}

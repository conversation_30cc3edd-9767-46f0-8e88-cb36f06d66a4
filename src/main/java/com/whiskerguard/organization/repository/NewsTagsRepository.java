package com.whiskerguard.organization.repository;

import com.whiskerguard.organization.domain.NewsTags;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 描述：新闻标签关联的数据访问层接口
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/18
 */
@SuppressWarnings("unused")
@Repository
public interface NewsTagsRepository extends JpaRepository<NewsTags, Long> {

    /**
     * 方法名称：findByNewsId
     * 描述：根据新闻ID查询新闻标签关联记录（未删除）
     *
     * @param id 新闻ID
     * @return 新闻标签关联实体列表
     * @since 1.0
     */
    @Query("select newsTags from NewsTags newsTags where newsTags.news.id = ?1 and newsTags.isDeleted = false")
    List<NewsTags> findByNewsId(Long id);
}

package com.whiskerguard.organization.repository;

import com.whiskerguard.organization.domain.NewsCategory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 描述：新闻分类的数据访问层接口
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/18
 */
@Repository
public interface NewsCategoryRepository extends JpaRepository<NewsCategory, Long> {
    default Optional<NewsCategory> findOneWithEagerRelationships(Long id) {
        return this.findOneWithToOneRelationships(id);
    }

    default List<NewsCategory> findAllWithEagerRelationships() {
        return this.findAllWithToOneRelationships();
    }

    default Page<NewsCategory> findAllWithEagerRelationships(Pageable pageable) {
        return this.findAllWithToOneRelationships(pageable);
    }

    @Query(
        value = "select newsCategory from NewsCategory newsCategory left join fetch newsCategory.orgUnit left join fetch newsCategory.parent",
        countQuery = "select count(newsCategory) from NewsCategory newsCategory"
    )
    Page<NewsCategory> findAllWithToOneRelationships(Pageable pageable);

    @Query("select newsCategory from NewsCategory newsCategory left join fetch newsCategory.orgUnit left join fetch newsCategory.parent")
    List<NewsCategory> findAllWithToOneRelationships();

    @Query(
        "select newsCategory from NewsCategory newsCategory left join fetch newsCategory.orgUnit left join fetch newsCategory.parent where newsCategory.id =:id"
    )
    Optional<NewsCategory> findOneWithToOneRelationships(@Param("id") Long id);

    /**
     * Find all non-deleted news categories with pagination
     */
    @Query("select newsCategory from NewsCategory newsCategory where newsCategory.tenantId = :tenantId and newsCategory.isDeleted = false")
    Page<NewsCategory> findByIsDeletedFalse(Long tenantId, Pageable pageable);

    /**
     * Find all non-deleted news categories with eager relationships and pagination
     */
    @Query(
        value = "select newsCategory from NewsCategory newsCategory left join fetch newsCategory.orgUnit left join fetch newsCategory.parent where " +
            "newsCategory.tenantId = :tenantId and newsCategory.isDeleted = false",
        countQuery = "select count(newsCategory) from NewsCategory newsCategory where newsCategory.isDeleted = false")
    Page<NewsCategory> findAllWithToOneRelationshipsAndNotDeleted(Long tenantId, Pageable pageable);

    default Page<NewsCategory> findAllWithEagerRelationshipsAndNotDeleted(Long tenantId, Pageable pageable) {
        return this.findAllWithToOneRelationshipsAndNotDeleted(tenantId, pageable);
    }

    @Query("SELECT nc FROM NewsCategory nc WHERE nc.name = :name AND nc.tenantId = :tenantId AND nc.isDeleted = false")
    NewsCategory findByNameAndTenantId(String name, Long tenantId);
}

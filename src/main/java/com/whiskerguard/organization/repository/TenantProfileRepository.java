package com.whiskerguard.organization.repository;

import com.whiskerguard.organization.domain.TenantProfile;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * 描述：租户详情的数据访问层接口
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/18
 */
@SuppressWarnings("unused")
@Repository
public interface TenantProfileRepository extends JpaRepository<TenantProfile, Long> {

    /**
     * 根据注册号查找租户详情
     *
     * @param registrationNumber 注册号
     * @return 租户详情
     */
    Optional<TenantProfile> findByRegistrationNumber(String registrationNumber);

    /**
     * 根据是否删除查询所有租户详情，并进行分页
     *
     * @param tenantId 租户ID
     * @param pageable 分页信息
     * @return 租户详情分页结果
     */
    Page<TenantProfile> findByIsDeletedFalse(Long tenantId, Pageable pageable);

    /**
     * 根据ID查找未删除的租户详情
     *
     * @param tenantId 租户ID
     * @return 租户信息
     */
    @Query("SELECT tp FROM TenantProfile tp WHERE tp.tenant.id = :tenantId AND tp.isDeleted = false")
    Optional<TenantProfile> findByTenantId(Long tenantId);
}

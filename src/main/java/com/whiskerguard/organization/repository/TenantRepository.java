package com.whiskerguard.organization.repository;

import com.whiskerguard.organization.domain.Tenant;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 描述：租户的数据访问层接口
 * 提供对租户数据的基本CRUD操作和自定义查询方法
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/18
 */
@SuppressWarnings("unused")
@Repository
public interface TenantRepository extends JpaRepository<Tenant, Long> {
    /**
     * Find all tenants by status with pagination.
     * 根据状态查询所有租户，并进行分页
     *
     * @param status   the status to search for 要搜索的状态
     * @param pageable the pagination information 分页信息
     * @return a page of tenants 租户分页结果
     */
    Page<Tenant> findAllByStatus(Integer status, Pageable pageable);

    /**
     * Find a tenant by name.
     * 根据名称查找租户
     *
     * @param name the name to search for 要搜索的名称
     * @return an optional tenant 可选的租户
     */
    @Query("SELECT t FROM Tenant t WHERE t.name = :name AND t.isDeleted = false")
    Optional<Tenant> findByName(String name);

    /**
     * 根据是否删除查询所有租户，并进行分页
     *
     * @param pageable the pagination information 分页信息
     * @return a page of tenants 租户分页结果
     */
    Page<Tenant> findByIsDeletedFalse(Pageable pageable);

    /**
     * Find a tenant by tenant code and not deleted.
     * 根据租户编码查找未删除的租户
     *
     * @param tenantCode the tenant code to search for 要搜索的租户编码
     * @return an optional tenant 可选的租户
     */
    Optional<Tenant> findByTenantCodeAndIsDeletedFalse(String tenantCode);

    /**
     * 根据名称或租户编码查找未删除的租户
     *
     * @param name       租户名称
     * @param tenantCode 租户编码
     * @return 租户信息
     */
    @Query("SELECT t FROM Tenant t WHERE (t.name = :name OR t.tenantCode = :tenantCode) AND t.isDeleted = false")
    Tenant findByNameOrTenantCode(String name, String tenantCode);

    /**
     * 根据ID查找未删除的租户
     *
     * @param id 租户ID
     * @return 租户信息
     */
    Optional<Tenant> findByIdAndIsDeletedFalse(Long id);

    /**
     * 根据父级租户ID查找所有下级租户
     *
     * @param parentId 父级租户ID
     * @return 下级租户列表
     */
    @Query("SELECT t FROM Tenant t WHERE t.parentId = :parentId AND t.isDeleted = false ORDER BY t.createdAt ASC")
    List<Tenant> findByParentIdAndIsDeletedFalse(@Param("parentId") Long parentId);

    /**
     * 查找所有顶级租户（没有父级的租户）
     *
     * @return 顶级租户列表
     */
    @Query("SELECT t FROM Tenant t WHERE t.parentId IS NULL AND t.isDeleted = false ORDER BY t.createdAt ASC")
    List<Tenant> findTopLevelTenants();
}

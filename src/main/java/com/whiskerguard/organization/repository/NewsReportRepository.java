package com.whiskerguard.organization.repository;

import com.whiskerguard.organization.domain.NewsReport;
import java.util.List;
import java.util.Optional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.*;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

/**
 * 描述：新闻举报的数据访问层接口
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/18
 */
@Repository
public interface NewsReportRepository extends JpaRepository<NewsReport, Long> {
    default Optional<NewsReport> findOneWithEagerRelationships(Long id) {
        return this.findOneWithToOneRelationships(id);
    }

    default List<NewsReport> findAllWithEagerRelationships() {
        return this.findAllWithToOneRelationships();
    }

    default Page<NewsReport> findAllWithEagerRelationships(Pageable pageable) {
        return this.findAllWithToOneRelationships(pageable);
    }

    default Page<NewsReport> findAllWithEagerRelationshipsAndNotDeleted(Pageable pageable) {
        return this.findAllWithToOneRelationshipsAndNotDeleted(pageable);
    }

    @Query(
        value = "select newsReport from NewsReport newsReport left join fetch newsReport.news left join fetch newsReport.reporter",
        countQuery = "select count(newsReport) from NewsReport newsReport"
    )
    Page<NewsReport> findAllWithToOneRelationships(Pageable pageable);

    @Query("select newsReport from NewsReport newsReport left join fetch newsReport.news left join fetch newsReport.reporter")
    List<NewsReport> findAllWithToOneRelationships();

    @Query(
        "select newsReport from NewsReport newsReport left join fetch newsReport.news left join fetch newsReport.reporter where newsReport.id =:id"
    )
    Optional<NewsReport> findOneWithToOneRelationships(@Param("id") Long id);

    /**
     * Find all non-deleted news reports with pagination
     */
    Page<NewsReport> findByIsDeletedFalse(Pageable pageable);

    /**
     * Find all non-deleted news reports with eager relationships and pagination
     */
    @Query(
        value = "select newsReport from NewsReport newsReport left join fetch newsReport.news left join fetch newsReport.reporter where newsReport.isDeleted = false",
        countQuery = "select count(newsReport) from NewsReport newsReport where newsReport.isDeleted = false"
    )
    Page<NewsReport> findAllWithToOneRelationshipsAndNotDeleted(Pageable pageable);
}

package com.whiskerguard.organization.repository;

import com.whiskerguard.organization.domain.RiskCategory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 描述：风险分类的数据访问层接口
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/18
 */
@Repository
public interface RiskCategoryRepository extends JpaRepository<RiskCategory, Long> {

    default Optional<RiskCategory> findOneWithEagerRelationships(Long id) {
        return this.findOneWithToOneRelationships(id);
    }

    @Query("select riskCategory from RiskCategory riskCategory left join fetch riskCategory.riskModel where riskCategory.id =:id")
    Optional<RiskCategory> findOneWithToOneRelationships(@Param("id") Long id);

    @Query("select riskCategory from RiskCategory riskCategory where riskCategory.name = :name and riskCategory.tenantId = :tenantId and riskCategory.isDeleted = false")
    RiskCategory findByNameAndTenantId(String name, Long tenantId);

    @Query("select riskCategory from RiskCategory riskCategory where riskCategory.riskModel.id = :id and riskCategory.isDeleted = false")
    List<RiskCategory> findByRiskModelId(Long id);

    @Query("select riskCategory from RiskCategory riskCategory where riskCategory.riskModel.id = :riskModelId and riskCategory.isDeleted = false")
    Page<RiskCategory> findAllByRiskModelId(Long riskModelId, Pageable pageable);
}

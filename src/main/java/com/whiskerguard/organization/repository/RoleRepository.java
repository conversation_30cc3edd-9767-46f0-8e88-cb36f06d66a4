package com.whiskerguard.organization.repository;

import com.whiskerguard.organization.domain.Role;
import com.whiskerguard.organization.req.RoleReq;
import jakarta.persistence.criteria.Predicate;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.Set;

/**
 * 描述：角色的数据访问层接口
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/18
 */
@SuppressWarnings("unused")
@Repository
public interface RoleRepository extends JpaRepository<Role, Long>, JpaSpecificationExecutor<Role> {
    /**
     * 根据租户ID查询角色（分页）
     *
     * @param tenantId 租户ID
     * @param pageable 分页参数
     * @return 角色分页列表
     */
    Page<Role> findByTenantId(Long tenantId, Pageable pageable);

    /**
     * 根据租户ID和编码查找未删除的角色
     *
     * @param tenantId 租户ID
     * @param code     角色编码
     * @return 角色信息
     */
    @Query("SELECT r FROM Role r WHERE r.tenantId = :tenantId AND r.code = :code AND r.isDeleted = false ORDER BY r.createdAt DESC LIMIT 1")
    Optional<Role> findByTenantIdAndCode(Long tenantId, String code);

    /**
     * 根据租户ID和编码查找未删除的角色
     *
     * @param tenantId 租户ID
     * @param code     角色编码
     * @return 角色信息
     */
    Optional<Role> findByTenantIdAndCodeAndIsDeletedFalse(Long tenantId, String code);

    /**
     * 根据租户ID和角色ID集合查询角色
     *
     * @param tenantId 租户ID
     * @param roleIds  角色ID集合
     * @return 角色列表
     */
    @Query("SELECT r FROM Role r WHERE r.tenantId = :tenantId AND r.id IN :roleIds AND r.isDeleted = false")
    List<Role> findByTenantIdAndIdIn(Long tenantId, Set<Long> roleIds);

    /**
     * 根据租户ID和角色名称模糊查询未删除的角色
     *
     * @param tenantId 租户ID tenant ID
     * @param name     角色名称关键词 role name keyword
     * @return 角色列表 role list
     */
    List<Role> findByTenantIdAndNameContainingIgnoreCaseAndIsDeletedFalse(Long tenantId, String name);

    /**
     * 根据租户ID和角色名称查找未删除的角色
     *
     * @param tenantId 租户ID
     * @param name     角色名称
     * @return 角色信息
     */
    Optional<Role> findByTenantIdAndNameAndIsDeletedFalse(Long tenantId, String name);

    /**
     * 根据条件查询角色
     *
     * @param tenantId 租户ID
     * @param pageable 分页参数
     * @param roleReq  角色查询请求对象
     * @return 角色分页列表
     */
    default Page<Role> searchByCondition(Long tenantId, Pageable pageable, RoleReq roleReq) {
        return findAll((root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();
            predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("tenantId"), tenantId));

            if (StringUtils.isNotBlank(roleReq.getKeyword())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.or(
                    criteriaBuilder.like(root.get("name"), "%" + roleReq.getKeyword() + "%"),
                    criteriaBuilder.like(root.get("code"), "%" + roleReq.getKeyword() + "%")
                ));
            }

            predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("isDeleted"), Boolean.FALSE));
            return predicate;
        }, pageable);
    }
}

package com.whiskerguard.organization.repository;

import com.whiskerguard.organization.domain.EmployeeOrg;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 描述：员工组织关系的数据访问层接口
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/18
 */
@SuppressWarnings("unused")
@Repository
public interface EmployeeOrgRepository extends JpaRepository<EmployeeOrg, Long> {
    /**
     * 根据员工ID和组织单元ID查找员工-组织关联
     *
     * @param employeeId 员工ID
     * @param orgUnitId  组织单元ID
     * @return 员工-组织关联
     */
    Optional<EmployeeOrg> findByEmployeeIdAndOrgUnitId(Long employeeId, Long orgUnitId);

    /**
     * 根据租户ID查询员工-组织关联（分页）
     *
     * @param tenantId 租户ID
     * @param pageable 分页参数
     * @return 员工-组织关联分页列表
     */
    Page<EmployeeOrg> findByTenantId(Long tenantId, Pageable pageable);

    /**
     * 根据员工ID查询员工-组织关联
     *
     * @param employeeId 员工ID
     * @return 员工-组织关联列表
     */
    @Query("SELECT eo FROM EmployeeOrg eo WHERE eo.employee.id = :employeeId AND eo.isDeleted = false")
    List<EmployeeOrg> findByEmployeeId(Long employeeId);

    /**
     * 根据租户ID查询未删除的员工-组织关联（分页）
     *
     * @param tenantId 租户ID
     * @param pageable 分页参数
     * @return 员工-组织关联分页列表
     */
    Page<EmployeeOrg> findByTenantIdAndIsDeletedFalse(Long tenantId, Pageable pageable);

    /**
     * 根据员工ID集合查询未删除的员工-组织关联
     *
     * @param employeeIds 员工ID集合
     * @return 员工-组织关联列表
     */
    List<EmployeeOrg> findByEmployeeIdInAndIsDeletedFalse(List<Long> employeeIds);

    /**
     * 根据员工ID删除员工-组织关联
     *
     * @param employeeId 员工ID
     */
    @Modifying
    @Query("UPDATE EmployeeOrg eo SET eo.isDeleted = true WHERE eo.employee.id = :employeeId")
    void deleteByEmployeeId(Long employeeId);

    /**
     * 根据组织单元ID查询员工-组织关联
     *
     * @param orgUnitId 组织单元ID
     * @return 员工-组织关联列表
     */
    @Query("SELECT eo FROM EmployeeOrg eo WHERE eo.orgUnit.id = :orgUnitId AND eo.isDeleted = false")
    List<EmployeeOrg> findByOrgUnitId(Long orgUnitId);

    /**
     * 根据组织单元ID查询员工ID
     *
     * @param orgUnitId 组织单元ID
     * @return 员工ID列表
     */
    @Query("SELECT eo.employee.id FROM EmployeeOrg eo WHERE eo.orgUnit.id = :orgUnitId AND eo.isDeleted = false")
    List<Long> findEmployeeIdsByOrgUnitId(Long orgUnitId);

    /**
     * 根据职位ID集合查询员工数量
     *
     * @param positionIds 职位ID集合
     * @return 员工数量
     */
//    @Query("SELECT eo.position.id, COUNT(eo) FROM EmployeeOrg eo WHERE eo.position.id IN :positionIds AND eo.isDeleted = false GROUP BY eo.position.id")
    @Query("SELECT eo.position.id, COUNT(eo) FROM EmployeeOrg eo LEFT JOIN eo.employee e WHERE eo.position.id IN :positionIds " +
        "AND eo.isDeleted = false AND e.status = 'ACTIVE' GROUP BY eo.position.id")
    List<Object[]> findPositionEmployeeCountByPositionIds(List<Long> positionIds);
}

package com.whiskerguard.organization.repository;

import com.whiskerguard.organization.domain.Employee;
import com.whiskerguard.organization.domain.enumeration.EmployeeStatus;
import com.whiskerguard.organization.req.EmployeeReq;
import jakarta.persistence.criteria.Predicate;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 描述：员工的数据访问层接口
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/18
 */
@SuppressWarnings("unused")
@Repository
public interface EmployeeRepository extends JpaRepository<Employee, Long>, JpaSpecificationExecutor<Employee> {
    /**
     * 根据组织单元ID查询员工
     *
     * @param orgUnitId 组织单元ID
     * @param tenantId  租户ID
     * @param pageable  分页参数
     * @return 员工分页列表
     */
    @Query("SELECT e FROM Employee e JOIN EmployeeOrg eo ON e.id = eo.employee.id WHERE eo.orgUnit.id = :orgUnitId AND eo.isDeleted = false AND e.tenantId = :tenantId" +
        " ORDER BY e.createdAt DESC")
    Page<Employee> findByOrgUnitId(Long orgUnitId, Long tenantId, Pageable pageable);

    /**
     * 根据状态查询员工
     *
     * @param status   员工状态
     * @param tenantId 租户ID
     * @param pageable 分页参数
     * @return 员工分页列表
     */
    @Query("SELECT e FROM Employee e WHERE e.status = :status AND e.tenantId = :tenantId AND e.isDeleted = false ORDER BY e.createdAt DESC")
    Page<Employee> findByStatus(EmployeeStatus status, Long tenantId, Pageable pageable);

    /**
     * 根据用户名查询员工
     *
     * @param username 用户名
     * @return 员工信息
     */
    @Query("SELECT e FROM Employee e WHERE e.username = :username AND e.isDeleted = false")
    Optional<Employee> findByUsername(String username);

    /**
     * 根据用户名查找未删除的员工
     *
     * @param username 用户名
     * @return 员工信息
     */
    Optional<Employee> findOneByUsernameAndIsDeletedFalse(String username);

    /**
     * 根据用户名和密码查询员工
     *
     * @param username 用户名
     * @param password 密码
     * @param status   状态（在职、离职、冻结）@link EmployeeStatus
     * @return 员工信息
     */
    Optional<Employee> findByUsernameAndPasswordAndStatusAndIsDeletedFalse(String username, String password, EmployeeStatus status);

    /**
     * 根据租户ID查询员工（分页）
     *
     * @param tenantId 租户ID
     * @param pageable 分页参数
     * @return 员工分页列表
     */
    Page<Employee> findByTenantId(Long tenantId, Pageable pageable);

    /**
     * 根据租户ID查询未删除的员工（全部）
     *
     * @param tenantId 租户ID
     * @return 员工列表
     */
    List<Employee> findByTenantIdAndIsDeletedFalse(Long tenantId);

    /**
     * 根据手机号查找未删除的员工
     *
     * @param phone 手机号
     * @return 员工信息
     */
    @Query("SELECT e FROM Employee e WHERE e.phone = :phone AND e.isDeleted = false order by e.id desc limit 1")
    Optional<Employee> findByPhoneAndIsDeletedFalse(String phone);

    /**
     * 根据微信OpenID查找未删除的员工
     *
     * @param wechatOpenId 微信OpenID
     * @return 员工信息
     */
    Optional<Employee> findByWechatOpenIdAndIsDeletedFalse(String wechatOpenId);

    /**
     * 根据租户ID统计员工数量
     *
     * @param tenantId 租户ID
     * @return 员工数量
     */
    @Query("SELECT COUNT(e) FROM Employee e WHERE e.tenantId = :tenantId AND e.isDeleted = false")
    int countByTenantId(Long tenantId);

    /**
     * 根据用户名查找未删除的员工
     *
     * @param username 用户名
     * @return 员工信息
     */
    Optional<Employee> findByUsernameAndIsDeletedFalse(String username);

    /**
     * 根据员工ID集合查询未删除的员工
     *
     * @param ids 员工ID集合
     * @return 员工列表
     */
    @Query("SELECT e FROM Employee e WHERE e.id IN :ids AND e.isDeleted = false")
    List<Employee> findByIdIn(List<Long> ids);

    /**
     * 根据条件查询员工
     *
     * @param tenantId    租户ID
     * @param req         员工查询请求对象
     * @param pageable    分页参数
     * @param employeeIds 员工ID集合
     * @return 员工分页列表
     */
    default Page<Employee> searchByCondition(Long tenantId, EmployeeReq req, Pageable pageable, List<Long> employeeIds) {

        return findAll((root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();
            predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("tenantId"), tenantId));

            if (StringUtils.isNotBlank(req.getKeyword())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.or(
                    criteriaBuilder.like(root.get("username"), "%" + req.getKeyword() + "%"),
                    criteriaBuilder.like(root.get("realName"), "%" + req.getKeyword() + "%"),
                    criteriaBuilder.like(root.get("employeeNo"), "%" + req.getKeyword() + "%"),
                    criteriaBuilder.like(root.get("phone"), "%" + req.getKeyword() + "%")
                ));
            }

            if (null != req.getStatus()) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("status"), req.getStatus()));
            }

            if (!CollectionUtils.isEmpty(employeeIds)) {
                predicate = criteriaBuilder.and(predicate, root.get("id").in(employeeIds));
            }

            predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("isDeleted"), Boolean.FALSE));
            return predicate;
        }, pageable);
    }

    /**
     * 根据租户ID和状态统计员工数量
     *
     * @param tenantId 租户ID
     * @param status   员工状态
     * @return 员工数量
     */
    @Query("SELECT COUNT(e) FROM Employee e WHERE e.tenantId = :tenantId AND e.status = :status AND e.isDeleted = false")
    long countByTenantIdAndStatus(@Param("tenantId") Long tenantId, @Param("status") EmployeeStatus status);

    /**
     * 根据租户ID统计各部门员工数量
     *
     * @param tenantId 租户ID
     * @return 部门ID和员工数量的映射
     */
    @Query("SELECT eo.orgUnit.id as departmentId, COUNT(DISTINCT e.id) as employeeCount " +
           "FROM Employee e JOIN EmployeeOrg eo ON e.id = eo.employee.id " +
           "WHERE e.tenantId = :tenantId AND e.isDeleted = false AND eo.isDeleted = false " +
           "GROUP BY eo.orgUnit.id")
    List<Map<String, Object>> countEmployeesByDepartment(@Param("tenantId") Long tenantId);

    /**
     * 统计未分配部门的员工数量
     *
     * @param tenantId 租户ID
     * @return 未分配部门的员工数量
     */
    @Query("SELECT COUNT(e) FROM Employee e WHERE e.tenantId = :tenantId AND e.isDeleted = false " +
           "AND NOT EXISTS (SELECT 1 FROM EmployeeOrg eo WHERE eo.employee.id = e.id AND eo.isDeleted = false)")
    long countEmployeesWithoutDepartment(@Param("tenantId") Long tenantId);

    /**
     * 根据姓名模糊搜索员工（搜索用户名和真实姓名）
     *
     * @param tenantId 租户ID
     * @param name     搜索关键词
     * @return 员工列表
     */
    @Query("SELECT e FROM Employee e WHERE e.tenantId = :tenantId AND e.isDeleted = false " +
           "AND (e.username LIKE %:name% OR e.realName LIKE %:name%) " +
           "ORDER BY e.realName ASC, e.username ASC")
    List<Employee> findByNameFuzzySearch(@Param("tenantId") Long tenantId, @Param("name") String name);
}

package com.whiskerguard.organization.repository;

import com.whiskerguard.organization.domain.Position;
import com.whiskerguard.organization.domain.enumeration.PositionCategory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.Set;

/**
 * 描述：岗位的数据访问层接口
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/18
 */
@SuppressWarnings("unused")
@Repository
public interface PositionRepository extends JpaRepository<Position, Long> {
    /**
     * 根据租户ID查询职位
     *
     * @param tenantId 租户ID
     * @param pageable 分页参数
     * @return 职位分页列表
     */
    Page<Position> findByTenantId(Long tenantId, Pageable pageable);

    /**
     * 根据职位类别查询职位
     *
     * @param category 职位类别
     * @param pageable 分页参数
     * @return 职位分页列表
     */
    Page<Position> findByCategory(PositionCategory category, Pageable pageable);

    /**
     * 根据级别范围查询职位
     *
     * @param minLevel 最小级别
     * @param maxLevel 最大级别
     * @param pageable 分页参数
     * @return 职位分页列表
     */
    Page<Position> findByLevelBetween(Integer minLevel, Integer maxLevel, Pageable pageable);

    /**
     * 根据关键词搜索职位
     *
     * @param keyword  关键词
     * @param pageable 分页参数
     * @return 职位分页列表
     */
    @Query("SELECT p FROM Position p WHERE p.name LIKE %:keyword% OR p.description LIKE %:keyword%")
    Page<Position> searchByKeyword(String keyword, Pageable pageable);

    /**
     * 根据租户ID和职位类别查询职位
     *
     * @param tenantId 租户ID
     * @param category 职位类别
     * @param pageable 分页参数
     * @return 职位分页列表
     */
    Page<Position> findByTenantIdAndCategory(Long tenantId, PositionCategory category, Pageable pageable);

    /**
     * 根据租户ID和级别范围查询职位
     *
     * @param tenantId 租户ID
     * @param minLevel 最小级别
     * @param maxLevel 最大级别
     * @param pageable 分页参数
     * @return 职位分页列表
     */
    Page<Position> findByTenantIdAndLevelBetween(Long tenantId, Integer minLevel, Integer maxLevel, Pageable pageable);

    /**
     * 根据租户ID和职位编码集合查询职位
     *
     * @param tenantId 租户ID
     * @param codes    职位编码集合
     * @return 职位列表
     */
    List<Position> findByTenantIdAndCodeIn(Long tenantId, Set<String> codes);

    /**
     * Find all non-deleted positions with pagination
     */
    Page<Position> findByIsDeletedFalse(Pageable pageable);

    /**
     * Find all non-deleted positions by tenant ID with pagination
     */
    Page<Position> findByTenantIdAndIsDeletedFalse(Long tenantId, Pageable pageable);

    /**
     * 综合搜索职位 - 支持按岗位分类、所属部门、岗位名称、岗位编码进行组合搜索
     *
     * @param tenantId  租户ID
     * @param category  岗位分类（可选）
     * @param orgUnitId 所属部门ID（可选）
     * @param keyword   关键词搜索（岗位名称或编码，可选）
     * @param pageable  分页参数
     * @return 职位分页列表
     */
    @Query(
        "SELECT DISTINCT p FROM Position p " +
            "LEFT JOIN EmployeeOrg eo ON p.id = eo.position.id " +
            "WHERE p.tenantId = :tenantId " +
            "AND p.isDeleted = false " +
            "AND (:category IS NULL OR p.category = :category) " +
            "AND (:orgUnitId IS NULL OR eo.orgUnit.id = :orgUnitId) " +
            "AND (:keyword IS NULL OR :keyword = '' OR " +
            "     LOWER(p.name) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
            "     LOWER(p.code) LIKE LOWER(CONCAT('%', :keyword, '%')))"
    )
    Page<Position> findPositionsWithFilters(
        @Param("tenantId") Long tenantId,
        @Param("category") PositionCategory category,
        @Param("orgUnitId") Long orgUnitId,
        @Param("keyword") String keyword,
        Pageable pageable
    );

    /**
     * 根据租户ID和职位名称或编码查询职位
     *
     * @param tenantId 租户ID
     * @param name     职位名称
     * @param code     职位编码
     * @return 存在则返回true，否则返回false
     */
    @Query("SELECT p FROM Position p WHERE p.tenantId = :tenantId AND (p.name = :name OR p.code = :code) AND p.isDeleted = false")
    Position findByNameOrCode(Long tenantId, String name, String code);

    /**
     * 根据组织单元ID查询职位
     *
     * @param id 组织单元ID
     * @return 职位列表
     */
    @Query("SELECT p FROM Position p WHERE p.orgUnit.id = :id AND p.isDeleted = false")
    List<Position> findByOrgUnitId(Long id);

    /**
     * 根据租户ID和编码查找未删除的职位
     *
     * @param tenantId 租户ID
     * @param code     职位编码
     * @return 职位信息
     */
    Optional<Position> findByTenantIdAndCodeAndIsDeletedFalse(Long tenantId, String code);

    /**
     * 根据租户ID和职位ID集合查询职位
     *
     * @param tenantId  租户ID
     * @param positions 职位ID集合
     * @return 职位列表
     */
    @Query("SELECT p FROM Position p WHERE p.tenantId = :tenantId AND p.id IN :positions AND p.isDeleted = false")
    List<Position> findByTenantIdAndIdIn(Long tenantId, Set<Long> positions);

    /**
     * 根据租户ID和职位名称模糊查询未删除的职位
     * Find positions by tenant ID and name containing (case insensitive) that are not deleted
     *
     * @param tenantId 租户ID tenant ID
     * @param name     职位名称关键词 position name keyword
     * @return 职位列表 position list
     */
    List<Position> findByTenantIdAndNameContainingIgnoreCaseAndIsDeletedFalse(Long tenantId, String name);

    /**
     * 根据租户ID和职位名称查找未删除的职位
     *
     * @param tenantId     租户ID
     * @param positionName 职位名称
     * @return 职位信息
     */
    Optional<Position> findByTenantIdAndNameAndIsDeletedFalse(Long tenantId, String positionName);
}

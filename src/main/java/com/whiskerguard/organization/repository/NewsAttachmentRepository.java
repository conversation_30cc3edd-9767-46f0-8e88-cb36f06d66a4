package com.whiskerguard.organization.repository;

import com.whiskerguard.organization.domain.NewsAttachment;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 描述：新闻附件的数据访问层接口
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/18
 */
@Repository
public interface NewsAttachmentRepository extends JpaRepository<NewsAttachment, Long> {
    default Optional<NewsAttachment> findOneWithEagerRelationships(Long id) {
        return this.findOneWithToOneRelationships(id);
    }

    default List<NewsAttachment> findAllWithEagerRelationships() {
        return this.findAllWithToOneRelationships();
    }

    default Page<NewsAttachment> findAllWithEagerRelationships(Pageable pageable) {
        return this.findAllWithToOneRelationships(pageable);
    }

    default Page<NewsAttachment> findAllWithEagerRelationshipsAndNotDeleted(Pageable pageable) {
        return this.findAllWithToOneRelationshipsAndNotDeleted(pageable);
    }

    @Query(
        value = "select newsAttachment from NewsAttachment newsAttachment left join fetch newsAttachment.news",
        countQuery = "select count(newsAttachment) from NewsAttachment newsAttachment"
    )
    Page<NewsAttachment> findAllWithToOneRelationships(Pageable pageable);

    @Query("select newsAttachment from NewsAttachment newsAttachment left join fetch newsAttachment.news")
    List<NewsAttachment> findAllWithToOneRelationships();

    @Query("select newsAttachment from NewsAttachment newsAttachment left join fetch newsAttachment.news where newsAttachment.id =:id")
    Optional<NewsAttachment> findOneWithToOneRelationships(@Param("id") Long id);

    /**
     * Find all non-deleted news attachments with pagination
     */
    Page<NewsAttachment> findByIsDeletedFalse(Pageable pageable);

    /**
     * Find all non-deleted news attachments with eager relationships and pagination
     */
    @Query(
        value = "select newsAttachment from NewsAttachment newsAttachment left join fetch newsAttachment.news where newsAttachment.isDeleted = false",
        countQuery = "select count(newsAttachment) from NewsAttachment newsAttachment where newsAttachment.isDeleted = false"
    )
    Page<NewsAttachment> findAllWithToOneRelationshipsAndNotDeleted(Pageable pageable);

    @Query("select newsAttachment from NewsAttachment newsAttachment where newsAttachment.news.id = ?1 and newsAttachment.isDeleted = false order by newsAttachment.createdAt desc")
    List<NewsAttachment> findByNewsId(Long id);
}

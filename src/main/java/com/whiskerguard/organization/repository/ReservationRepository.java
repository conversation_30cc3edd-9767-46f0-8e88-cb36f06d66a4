package com.whiskerguard.organization.repository;

import com.whiskerguard.organization.domain.Reservation;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

/**
 * 描述：预约体验的数据访问层接口
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/18
 */
@SuppressWarnings("unused")
@Repository
public interface ReservationRepository extends JpaRepository<Reservation, Long> {

    /**
     * 根据手机号查询预约体验记录
     *
     * @param mobile 手机号
     * @return 预约体验记录
     */
    Reservation findByName(String mobile);
}

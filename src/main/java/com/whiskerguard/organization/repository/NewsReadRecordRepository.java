package com.whiskerguard.organization.repository;

import com.whiskerguard.organization.domain.NewsReadRecord;
import java.util.List;
import java.util.Optional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.*;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

/**
 * 描述：新闻阅读记录的数据访问层接口
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/18
 */
@Repository
public interface NewsReadRecordRepository extends JpaRepository<NewsReadRecord, Long> {
    default Optional<NewsReadRecord> findOneWithEagerRelationships(Long id) {
        return this.findOneWithToOneRelationships(id);
    }

    default List<NewsReadRecord> findAllWithEagerRelationships() {
        return this.findAllWithToOneRelationships();
    }

    default Page<NewsReadRecord> findAllWithEagerRelationships(Pageable pageable) {
        return this.findAllWithToOneRelationships(pageable);
    }

    default Page<NewsReadRecord> findAllWithEagerRelationshipsAndNotDeleted(Pageable pageable) {
        return this.findAllWithToOneRelationshipsAndNotDeleted(pageable);
    }

    @Query(
        value = "select newsReadRecord from NewsReadRecord newsReadRecord left join fetch newsReadRecord.news left join fetch newsReadRecord.reader",
        countQuery = "select count(newsReadRecord) from NewsReadRecord newsReadRecord"
    )
    Page<NewsReadRecord> findAllWithToOneRelationships(Pageable pageable);

    @Query(
        "select newsReadRecord from NewsReadRecord newsReadRecord left join fetch newsReadRecord.news left join fetch newsReadRecord.reader"
    )
    List<NewsReadRecord> findAllWithToOneRelationships();

    @Query(
        "select newsReadRecord from NewsReadRecord newsReadRecord left join fetch newsReadRecord.news left join fetch newsReadRecord.reader where newsReadRecord.id =:id"
    )
    Optional<NewsReadRecord> findOneWithToOneRelationships(@Param("id") Long id);

    /**
     * Find all non-deleted news read records with pagination
     */
    Page<NewsReadRecord> findByIsDeletedFalse(Pageable pageable);

    /**
     * Find all non-deleted news read records with eager relationships and pagination
     */
    @Query(
        value = "select newsReadRecord from NewsReadRecord newsReadRecord left join fetch newsReadRecord.news left join fetch newsReadRecord.reader where newsReadRecord.isDeleted = false",
        countQuery = "select count(newsReadRecord) from NewsReadRecord newsReadRecord where newsReadRecord.isDeleted = false"
    )
    Page<NewsReadRecord> findAllWithToOneRelationshipsAndNotDeleted(Pageable pageable);
}

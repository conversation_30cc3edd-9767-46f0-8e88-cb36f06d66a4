package com.whiskerguard.organization.repository;

import com.whiskerguard.organization.domain.AuditLog;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.*;
import org.springframework.stereotype.Repository;

/**
 * 描述：审计日志的数据访问层接口
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/18
 */
@SuppressWarnings("unused")
@Repository
public interface AuditLogRepository extends JpaRepository<AuditLog, Long> {
    /**
     * 根据实体名称和实体ID查询审计日志
     *
     * @param entityName 实体名称
     * @param entityId 实体ID
     * @param pageable 分页参数
     * @return 审计日志分页结果
     */
    Page<AuditLog> findByEntityNameAndEntityId(String entityName, Long entityId, Pageable pageable);

    /**
     * Find all non-deleted audit logs with pagination
     */
    Page<AuditLog> findByIsDeletedFalse(Pageable pageable);
}

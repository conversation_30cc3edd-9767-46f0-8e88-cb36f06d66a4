package com.whiskerguard.organization.repository;

import com.whiskerguard.organization.domain.Employee;
import com.whiskerguard.organization.domain.EmployeeRole;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 描述：员工角色关系的数据访问层接口
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/18
 */
@SuppressWarnings("unused")
@Repository
public interface EmployeeRoleRepository extends JpaRepository<EmployeeRole, Long> {
    /**
     * 查询员工的所有角色
     *
     * @param employee 员工
     * @return 员工角色列表
     */
    @Query("SELECT er FROM EmployeeRole er WHERE er.employee = :employee AND er.isDeleted = false")
    List<EmployeeRole> findByEmployee(Employee employee);

    /**
     * 根据角色ID查询员工角色关系
     *
     * @param roleId 角色ID
     * @return 员工角色关系列表
     */
    List<EmployeeRole> findByRoleId(Long roleId);

    /**
     * 根据员工ID查询角色关系
     *
     * @param employeeId 员工ID
     * @return 角色关系列表
     */
    @Query("SELECT er FROM EmployeeRole er WHERE er.employee.id = :employeeId AND er.isDeleted = false")
    List<EmployeeRole> findByEmployeeId(Long employeeId);

    /**
     * Find all non-deleted employee roles with pagination
     */
    @Query("SELECT er FROM EmployeeRole er WHERE er.isDeleted = false AND er.tenantId = :tenantId")
    Page<EmployeeRole> findByIsDeletedFalse(Long tenantId, Pageable pageable);

    /**
     * 根据员工ID删除员工角色关系
     *
     * @param employeeId 员工ID
     */
    @Modifying
    @Query("UPDATE EmployeeRole er SET er.isDeleted = true WHERE er.employee.id = :employeeId")
    void deleteByEmployeeId(Long employeeId);

    /**
     * 根据角色编码查询员工ID
     *
     * @param tenantId 租户ID
     * @param roleCode 角色编码
     * @return 员工ID列表
     */
    @Query("SELECT er.employee.id FROM EmployeeRole er WHERE er.role.code = :roleCode AND er.isDeleted = false")
    List<Long> findEmployeeIdsByRoleCode(Long tenantId, String roleCode);

    /**
     * 根据角色编码查询员工ID
     *
     * @param tenantId 租户ID
     * @param roleCode 角色编码
     * @return 员工ID列表
     */
    @Query("SELECT er.employee.id FROM EmployeeRole er WHERE er.role.code != :roleCode AND er.isDeleted = false")
    List<Long> findEmployeeIdsByNotRoleCode(Long tenantId, String roleCode);

    /**
     * 统计具有管理员角色的员工数量
     *
     * @param tenantId 租户ID
     * @return 管理员员工数量
     */
    @Query("SELECT COUNT(DISTINCT er.employee.id) FROM EmployeeRole er " +
        "WHERE er.tenantId = :tenantId AND er.isDeleted = false " +
        "AND er.role.code IN ('TENANT_ADMIN', 'ADMIN', 'SUPER_ADMIN')")
    long countAdminEmployees(@Param("tenantId") Long tenantId);
}

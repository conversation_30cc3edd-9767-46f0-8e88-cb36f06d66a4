package com.whiskerguard.organization.repository;

import com.whiskerguard.organization.domain.TenantAttachment;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 描述：租户附件的数据访问层接口
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/18
 */
@SuppressWarnings("unused")
@Repository
public interface TenantAttachmentRepository extends JpaRepository<TenantAttachment, Long> {

    /**
     * 根据是否删除查询所有租户附件
     *
     * @param pageable 分页信息
     * @return 员工-组织关联
     */
    Page<TenantAttachment> findByIsDeletedFalse(Pageable pageable);

    /**
     * 根据租户ID查询附件
     *
     * @param id 租户ID
     * @return 附件列表
     */
    @Query("select tenantAttachment from TenantAttachment tenantAttachment where tenantAttachment.tenant.id = ?1 and tenantAttachment.isDeleted = false")
    List<TenantAttachment> findByTenantId(Long id);

}

package com.whiskerguard.organization.repository;

import com.whiskerguard.organization.domain.TenantInitialize;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 描述：租户基础信息初始化的数据访问层接口
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/18
 */
@SuppressWarnings("unused")
@Repository
public interface TenantInitializeRepository extends JpaRepository<TenantInitialize, Long> {

    /**
     * 方法名称：findAllByIsDeleted
     * 描述：查询所有租户基础信息初始化记录
     *
     * @param isDeleted 是否删除
     * @return 租户基础信息初始化记录列表
     * @since 1.0
     */
    List<TenantInitialize> findAllByIsDeleted(Boolean isDeleted);
}

package com.whiskerguard.organization.repository;

import com.whiskerguard.organization.domain.LetterCommitment;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * 描述：承诺书的数据访问层接口
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/18
 */
@SuppressWarnings("unused")
@Repository
public interface LetterCommitmentRepository extends JpaRepository<LetterCommitment, Long> {

    /**
     * 根据员工ID查询承诺书
     *
     * @param employeeId 员工ID
     * @return 承诺书
     */
    @Query("SELECT lc FROM LetterCommitment lc WHERE lc.employeeId = :employeeId AND lc.isDeleted = false")
    Optional<LetterCommitment> findByEmployeeId(Long employeeId);
}

package com.whiskerguard.organization.repository;

import com.whiskerguard.organization.domain.NewsLike;
import java.util.List;
import java.util.Optional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.*;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

/**
 * 描述：新闻点赞的数据访问层接口
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/18
 */
@Repository
public interface NewsLikeRepository extends JpaRepository<NewsLike, Long> {
    default Optional<NewsLike> findOneWithEagerRelationships(Long id) {
        return this.findOneWithToOneRelationships(id);
    }

    default List<NewsLike> findAllWithEagerRelationships() {
        return this.findAllWithToOneRelationships();
    }

    default Page<NewsLike> findAllWithEagerRelationships(Pageable pageable) {
        return this.findAllWithToOneRelationships(pageable);
    }

    default Page<NewsLike> findAllWithEagerRelationshipsAndNotDeleted(Pageable pageable) {
        return this.findAllWithToOneRelationshipsAndNotDeleted(pageable);
    }

    @Query(
        value = "select newsLike from NewsLike newsLike left join fetch newsLike.news left join fetch newsLike.user",
        countQuery = "select count(newsLike) from NewsLike newsLike"
    )
    Page<NewsLike> findAllWithToOneRelationships(Pageable pageable);

    @Query("select newsLike from NewsLike newsLike left join fetch newsLike.news left join fetch newsLike.user")
    List<NewsLike> findAllWithToOneRelationships();

    @Query("select newsLike from NewsLike newsLike left join fetch newsLike.news left join fetch newsLike.user where newsLike.id =:id")
    Optional<NewsLike> findOneWithToOneRelationships(@Param("id") Long id);

    /**
     * Find all non-deleted news likes with pagination
     */
    Page<NewsLike> findByIsDeletedFalse(Pageable pageable);

    /**
     * Find all non-deleted news likes with eager relationships and pagination
     */
    @Query(
        value = "select newsLike from NewsLike newsLike left join fetch newsLike.news left join fetch newsLike.user where newsLike.isDeleted = false",
        countQuery = "select count(newsLike) from NewsLike newsLike where newsLike.isDeleted = false"
    )
    Page<NewsLike> findAllWithToOneRelationshipsAndNotDeleted(Pageable pageable);
}

package com.whiskerguard.organization.repository;

import com.whiskerguard.organization.domain.Permission;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 描述：权限的数据访问层接口
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/18
 */
@SuppressWarnings("unused")
@Repository
public interface PermissionRepository extends JpaRepository<Permission, Long> {

    /**
     * 根据租户ID查询权限
     *
     * @param isAvailable 是否租户可用
     * @return 权限列表
     */
    @Query("SELECT p FROM Permission p WHERE p.isAvailable = :isAvailable AND p.isDeleted = false")
    List<Permission> findByIsAvailable(Boolean isAvailable);

    /**
     * 根据租户ID查询未删除的权限（分页）
     *
     * @param isAvailable 是否租户可用
     * @param pageable    分页参数
     * @return 权限分页结果
     */
    @Query("SELECT p FROM Permission p WHERE p.isAvailable = :isAvailable AND p.isDeleted = false")
    Page<Permission> findByIsAvailableAndIsDeletedFalse(Boolean isAvailable, Pageable pageable);

    /**
     * 根据租户ID查询顶级权限
     *
     * @param isAvailable 是否租户可用
     * @return 顶级权限分页结果
     */
    @Query("SELECT p FROM Permission p WHERE p.parent IS NULL AND p.isAvailable = :isAvailable AND p.isDeleted = false")
    List<Permission> findRootPermissionsByIsAvailable(Boolean isAvailable);

    /**
     * 根据父权限ID查询子权限
     *
     * @param parentId 父权限ID
     * @return 子权限列表
     */
    @Query("SELECT p FROM Permission p WHERE p.parent.id = :parentId")
    List<Permission> findByParentId(Long parentId);

    /**
     * 根据员工ID查询员工拥有的权限
     *
     * @param employeeId 员工ID
     * @return 权限列表
     */
    @Query(
        "SELECT DISTINCT p FROM Permission p " +
            "JOIN RolePermission rp ON p.id = rp.permission.id " +
            "JOIN EmployeeRole er ON rp.role.id = er.role.id " +
            "WHERE er.employee.id = :employeeId " +
            "AND er.isDeleted = false " +
            "AND rp.isDeleted = false " +
            "AND p.isDeleted = false"
    )
    List<Permission> findByEmployeeId(Long employeeId);

    /**
     * 根据员工ID查询员工拥有的顶级权限（分页）
     *
     * @param employeeId 员工ID
     * @param pageable   分页参数
     * @return 顶级权限分页结果
     */
    @Query(
        "SELECT DISTINCT p FROM Permission p " +
            "JOIN RolePermission rp ON p.id = rp.permission.id " +
            "JOIN EmployeeRole er ON rp.role.id = er.role.id " +
            "WHERE er.employee.id = :employeeId " +
            "AND er.isDeleted = false " +
            "AND rp.isDeleted = false " +
            "AND p.isDeleted = false " +
            "AND p.parent IS NULL"
    )
    Page<Permission> findRootPermissionsByEmployeeId(Long employeeId, Pageable pageable);

    /**
     * 根据权限名称和租户ID查询权限
     *
     * @param name 权限名称
     * @return 权限信息
     */
    @Query("SELECT p FROM Permission p WHERE p.name = :name AND p.isDeleted = false")
    Permission findByName(String name);

    /**
     * 查询所有未删除的权限
     *
     * @param isAvailable 是否租户可用
     * @return 权限列表
     */
    @Query("SELECT p FROM Permission p WHERE p.isAvailable = :isAvailable AND p.isDeleted = false")
    List<Permission> findAllAndNotDeleted(Boolean isAvailable);

    /**
     * 根据租户ID和权限编码查询权限
     *
     * @param code 权限编码
     * @return 权限信息
     */
    Optional<Permission> findByCodeAndIsAvailable(String code, Boolean isAvailable);

}

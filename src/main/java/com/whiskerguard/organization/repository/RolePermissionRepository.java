package com.whiskerguard.organization.repository;

import com.whiskerguard.organization.domain.RolePermission;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 描述：角色权限关系的数据访问层接口
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/18
 */
@SuppressWarnings("unused")
@Repository
public interface RolePermissionRepository extends JpaRepository<RolePermission, Long> {

    /**
     * 根据角色ID查询权限
     *
     * @param roleId 角色ID
     * @return 角色权限列表
     */
    @Query("SELECT rp FROM RolePermission rp WHERE rp.role.id = :roleId AND rp.isDeleted = false")
    List<RolePermission> findByRoleId(Long roleId);

    /**
     * 根据员工ID查询权限
     *
     * @param employeeId 员工ID
     * @return 权限列表
     */
    @Query(
        "SELECT DISTINCT rp FROM RolePermission rp " +
            "JOIN EmployeeRole er ON rp.role.id = er.role.id " +
            "WHERE er.employee.id = :employeeId " +
            "AND er.isDeleted = false " +
            "AND rp.isDeleted = false"
    )
    List<RolePermission> findByEmployeeId(Long employeeId);

    /**
     * Find all non-deleted role permissions with pagination
     */
    @Query("SELECT rp FROM RolePermission rp WHERE rp.isDeleted = false AND rp.tenantId = :tenantId")
    Page<RolePermission> findByIsDeletedFalse(Long tenantId, Pageable pageable);

    /**
     * 根据角色ID删除角色权限关系
     *
     * @param id 角色ID
     */
    @Modifying
    @Query("UPDATE RolePermission rp SET rp.isDeleted = true WHERE rp.role.id = :id")
    void deleteByRoleId(Long id);

    /**
     * 根据角色ID和权限ID查询角色权限关联
     * Find role permission by role ID and permission ID that is not deleted
     *
     * @param roleId       角色ID role ID
     * @param permissionId 权限ID permission ID
     * @return 角色权限关联 role permission association
     */
    Optional<RolePermission> findByRoleIdAndPermissionIdAndIsDeletedFalse(Long roleId, Long permissionId);
}

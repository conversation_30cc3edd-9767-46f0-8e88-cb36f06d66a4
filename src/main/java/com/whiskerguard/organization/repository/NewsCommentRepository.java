package com.whiskerguard.organization.repository;

import com.whiskerguard.organization.domain.NewsComment;
import java.util.List;
import java.util.Optional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.*;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

/**
 * 描述：新闻评论的数据访问层接口
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/18
 */
@Repository
public interface NewsCommentRepository extends JpaRepository<NewsComment, Long> {
    default Optional<NewsComment> findOneWithEagerRelationships(Long id) {
        return this.findOneWithToOneRelationships(id);
    }

    default List<NewsComment> findAllWithEagerRelationships() {
        return this.findAllWithToOneRelationships();
    }

    default Page<NewsComment> findAllWithEagerRelationships(Pageable pageable) {
        return this.findAllWithToOneRelationships(pageable);
    }

    default Page<NewsComment> findAllWithEagerRelationshipsAndNotDeleted(Pageable pageable) {
        return this.findAllWithToOneRelationshipsAndNotDeleted(pageable);
    }

    @Query(
        value = "select newsComment from NewsComment newsComment left join fetch newsComment.news left join fetch newsComment.commenter",
        countQuery = "select count(newsComment) from NewsComment newsComment"
    )
    Page<NewsComment> findAllWithToOneRelationships(Pageable pageable);

    @Query("select newsComment from NewsComment newsComment left join fetch newsComment.news left join fetch newsComment.commenter")
    List<NewsComment> findAllWithToOneRelationships();

    @Query(
        "select newsComment from NewsComment newsComment left join fetch newsComment.news left join fetch newsComment.commenter where newsComment.id =:id"
    )
    Optional<NewsComment> findOneWithToOneRelationships(@Param("id") Long id);

    /**
     * Find all non-deleted news comments with pagination
     */
    Page<NewsComment> findByIsDeletedFalse(Pageable pageable);

    /**
     * Find all non-deleted news comments with eager relationships and pagination
     */
    @Query(
        value = "select newsComment from NewsComment newsComment left join fetch newsComment.news left join fetch newsComment.commenter where newsComment.isDeleted = false",
        countQuery = "select count(newsComment) from NewsComment newsComment where newsComment.isDeleted = false"
    )
    Page<NewsComment> findAllWithToOneRelationshipsAndNotDeleted(Pageable pageable);
}

package com.whiskerguard.organization.repository;

import com.whiskerguard.organization.domain.RiskRule;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 描述：风险规则的数据访问层接口
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/18
 */
@Repository
public interface RiskRuleRepository extends JpaRepository<RiskRule, Long> {

    default Optional<RiskRule> findOneWithEagerRelationships(Long id) {
        return this.findOneWithToOneRelationships(id);
    }

    @Query("select riskRule from RiskRule riskRule left join fetch riskRule.riskCategory where riskRule.id =:id")
    Optional<RiskRule> findOneWithToOneRelationships(@Param("id") Long id);

    @Query("select riskRule from RiskRule riskRule where riskRule.riskCategory.id =:id")
    List<RiskRule> findByRiskCategoryId(Long id);

    @Query("select riskRule from RiskRule riskRule where riskRule.tenantId = :tenantId and (riskRule.name = :name or riskRule.code = :code) and riskRule.isDeleted = false")
    RiskRule findByTenantIdAndNameOrCode(Long tenantId, String name, String code);

    @Query("select riskRule from RiskRule riskRule where riskRule.riskCategory.id = :riskCategoryId and riskRule.isDeleted = false")
    Page<RiskRule> findAllByRiskCategoryId(Long riskCategoryId, Pageable pageable);
}

package com.whiskerguard.organization.repository;

import com.whiskerguard.organization.domain.RiskModel;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.Instant;
import java.util.List;

/**
 * 描述：风险模型的数据访问层接口
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/18
 */
@SuppressWarnings("unused")
@Repository
public interface RiskModelRepository extends JpaRepository<RiskModel, Long> {

    @Query("SELECT rm FROM RiskModel rm WHERE rm.name = :name AND rm.tenantId = :tenantId AND rm.isDeleted = false")
    RiskModel findByNameAndTenantId(String name, Long tenantId);

    @Query("SELECT rm FROM RiskModel rm WHERE rm.tenantId = :tenantId AND rm.isDeleted = false")
    Page<RiskModel> findAllByTenantId(Long tenantId, Pageable pageable);

    @Query("SELECT rm FROM RiskModel rm WHERE rm.tenantId = :tenantId AND rm.isDefault = :isDefault AND rm.isDeleted = false")
    List<RiskModel> findByTenantIdAndIsDefault(Long tenantId, Boolean isDefault);

    /**
     * 根据条件查询风险模型
     */
    @Query("SELECT rm FROM RiskModel rm WHERE rm.tenantId = :tenantId AND rm.isDeleted = false " +
           "AND (:name IS NULL OR :name = '' OR LOWER(rm.name) LIKE LOWER(CONCAT('%', :name, '%'))) " +
           "AND (:startDate IS NULL OR rm.createdAt >= :startDate) " +
           "AND (:endDate IS NULL OR rm.createdAt <= :endDate)")
    Page<RiskModel> findByCondition(
        @Param("tenantId") Long tenantId,
        @Param("name") String name,
        @Param("startDate") Instant startDate,
        @Param("endDate") Instant endDate,
        Pageable pageable
    );
}

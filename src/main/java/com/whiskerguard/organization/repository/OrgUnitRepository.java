package com.whiskerguard.organization.repository;

import com.whiskerguard.organization.domain.OrgUnit;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.Set;

/**
 * 描述：组织单位的数据访问层接口
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/18
 */
@SuppressWarnings("unused")
@Repository
public interface OrgUnitRepository extends JpaRepository<OrgUnit, Long> {
    /**
     * 根据租户ID查询所有组织单元
     */
    @Query("SELECT o FROM OrgUnit o WHERE o.tenantId = :tenantId AND o.isDeleted = false ORDER BY o.name ASC")
    List<OrgUnit> findAllByTenantId(Long tenantId);

    /**
     * 根据租户ID查询组织单元（分页）
     *
     * @param tenantId 租户ID
     * @param pageable 分页参数
     * @return 组织单元分页列表
     */
    Page<OrgUnit> findByTenantId(Long tenantId, Pageable pageable);

    /**
     * 根据父节点ID查询子组织单元
     */
    List<OrgUnit> findByParentId(Long parentId);

    /**
     * 根据父节点ID查询子组织单元（分页）
     *
     * @param parentId 父节点ID
     * @param pageable 分页参数
     * @return 组织单元分页列表
     */
    Page<OrgUnit> findByParentIdAndIsDeletedFalse(Long parentId, Pageable pageable);

    /**
     * 根据租户ID和父节点ID查询子组织单元（分页）
     *
     * @param tenantId 租户ID
     * @param parentId 父节点ID
     * @param pageable 分页参数
     * @return 组织单元分页列表
     */
    Page<OrgUnit> findByTenantIdAndParentIdAndIsDeletedFalse(Long tenantId, Long parentId, Pageable pageable);

    /**
     * 检查是否存在指定父节点的子组织单元
     */
    boolean existsByParentId(Long parentId);

    /**
     * 根据租户ID和状态查询组织单元
     */
    List<OrgUnit> findByTenantIdAndStatus(Long tenantId, Integer status);

    /**
     * 根据租户ID和组织单元编码查询组织单元
     */
    List<OrgUnit> findByTenantIdAndCodeIn(Long tenantId, Set<String> orgUnits);

    /**
     * 根据租户ID查询未删除的组织单元（分页）
     */
    Page<OrgUnit> findByTenantIdAndIsDeletedFalse(Long tenantId, Pageable pageable);

    /**
     * 根据租户ID、父节点ID和关键词查询组织单元（分页）
     * 关键词搜索组织单元的名称或编码
     *
     * @param tenantId 租户ID
     * @param parentId 父节点ID
     * @param keyword  关键词（搜索名称或编码）
     * @param pageable 分页参数
     * @return 组织单元分页列表
     */
    @Query(
        "SELECT o FROM OrgUnit o WHERE o.tenantId = :tenantId " +
            "AND o.parent.id = :parentId " +
            "AND o.isDeleted = false " +
            "AND (LOWER(o.name) LIKE LOWER(CONCAT('%', :keyword, '%')) " +
            "OR LOWER(o.code) LIKE LOWER(CONCAT('%', :keyword, '%')))"
    )
    Page<OrgUnit> findByTenantIdAndParentIdAndKeywordAndIsDeletedFalse(
        @Param("tenantId") Long tenantId,
        @Param("parentId") Long parentId,
        @Param("keyword") String keyword,
        Pageable pageable
    );

    /**
     * 根据租户ID和编码查找未删除的组织单元
     *
     * @param tenantId 租户ID
     * @param code     组织单元编码
     * @return 组织单元信息
     */
    Optional<OrgUnit> findByTenantIdAndCodeAndIsDeletedFalse(Long tenantId, String code);

    /**
     * 根据租户ID和组织单元ID集合查询组织单元
     *
     * @param tenantId 租户ID
     * @param orgUnits 组织单元ID集合
     * @return 组织单元列表
     */
    @Query("SELECT o FROM OrgUnit o WHERE o.tenantId = :tenantId AND o.id IN :orgUnits AND o.isDeleted = false")
    List<OrgUnit> findByTenantIdAndIdIn(Long tenantId, Set<Long> orgUnits);

    /**
     * 根据租户ID、层级深度和名称或编码查找未删除的组织单元
     *
     * @param tenantId 租户ID
     * @param code     组织单元编码
     * @param level    层级深度
     * @return 组织单元信息
     */
    @Query("SELECT o FROM OrgUnit o WHERE o.tenantId = :tenantId AND o.level = :level AND o.code = :code AND o.isDeleted = false")
    OrgUnit findByTenantIdAndLevelAndCode(Long tenantId, String code, Integer level);

    /**
     * 根据租户ID和组织单元名称模糊查询未删除的组织单元
     *
     * @param tenantId 租户ID tenant ID
     * @param name     组织单元名称关键词 organization unit name keyword
     * @return 组织单元列表 organization unit list
     */
    @Query("SELECT o FROM OrgUnit o WHERE o.tenantId = :tenantId AND LOWER(o.name) LIKE LOWER(CONCAT('%', :name, '%')) AND o.isDeleted = false ORDER BY o.name ASC")
    List<OrgUnit> findByTenantIdAndNameContainingIgnoreCaseAndIsDeletedFalse(Long tenantId, String name);

    /**
     * 根据租户ID和组织单元名称查找未删除的组织单元
     *
     * @param tenantId 租户ID
     * @param name     组织单元名称
     * @return 组织单元信息
     */
    Optional<OrgUnit> findByTenantIdAndNameAndIsDeletedFalse(Long tenantId, String name);

    /**
     * 根据租户ID和层级深度小于等于指定值查询组织单元
     *
     * @param tenantId 租户ID
     * @param level    层级深度
     * @return 组织单元列表
     */
    @Query("SELECT o FROM OrgUnit o WHERE o.tenantId = :tenantId AND o.level <= :level AND o.isDeleted = false")
    List<OrgUnit> findByTenantIdAndLevelLessThanEqual(Long tenantId, int level);

    /**
     * 根据组织单元ID列表查询未删除的组织单元
     *
     * @param ids 组织单元ID列表
     * @return 组织单元列表
     */
    @Query("SELECT o FROM OrgUnit o WHERE o.id IN :ids AND o.isDeleted = false")
    List<OrgUnit> findByIdIn(List<Long> ids);
}

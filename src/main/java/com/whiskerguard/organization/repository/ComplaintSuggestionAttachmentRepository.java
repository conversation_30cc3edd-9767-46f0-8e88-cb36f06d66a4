package com.whiskerguard.organization.repository;

import com.whiskerguard.organization.domain.ComplaintSuggestionAttachment;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 描述：投诉建议附件的数据访问层接口
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/18
 */
@SuppressWarnings("unused")
@Repository
public interface ComplaintSuggestionAttachmentRepository extends JpaRepository<ComplaintSuggestionAttachment, Long> {

    /**
     * 根据投诉与建议ID和附件名称查询附件
     *
     * @param suggestionId 投诉与建议ID
     * @param fileName     附件名称
     * @return 附件
     */
    @Query("select complaintSuggestionAttachment from ComplaintSuggestionAttachment complaintSuggestionAttachment where complaintSuggestionAttachment.suggestionId = ?1 and complaintSuggestionAttachment.fileName = ?2 and complaintSuggestionAttachment.isDeleted = false")
    Optional<ComplaintSuggestionAttachment> findBySuggestionIdAndFileName(Long suggestionId, String fileName);

    /**
     * 根据投诉与建议ID查询附件
     *
     * @param suggestionId 投诉与建议ID
     * @param pageable     分页参数
     * @return 附件分页结果
     */
    @Query("select complaintSuggestionAttachment from ComplaintSuggestionAttachment complaintSuggestionAttachment where complaintSuggestionAttachment.suggestionId = ?1 and complaintSuggestionAttachment.isDeleted = false")
    Page<ComplaintSuggestionAttachment> findAllBySuggestionId(Long suggestionId, Pageable pageable);

    /**
     * 根据投诉与建议ID查询附件
     *
     * @param suggestionId 投诉与建议ID
     * @return 附件列表
     */
    @Query("select complaintSuggestionAttachment from ComplaintSuggestionAttachment complaintSuggestionAttachment where complaintSuggestionAttachment.suggestionId = ?1 and complaintSuggestionAttachment.isDeleted = false")
    List<ComplaintSuggestionAttachment> findBySuggestionId(Long suggestionId);
}

package com.whiskerguard.organization.repository;

import com.whiskerguard.organization.domain.News;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

/**
 * 描述：新闻的数据访问层接口
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/18
 */
@Repository
public interface NewsRepository extends JpaRepository<News, Long> {

    /**
     * Find all non-deleted news with pagination
     */
    @Query("select news from News news where news.tenantId = :tenantId and news.isDeleted = false")
    Page<News> findByIsDeletedFalse(Long tenantId, Pageable pageable);

    @Query(
        value = "select n from News n left join n.category c " +
            "where n.isDeleted = false and n.tenantId = :tenantId and c.id = :categoryId",
        countQuery = "select count(n) from News n left join n.category c " +
            "where n.isDeleted = false and n.tenantId = :tenantId and c.id = :categoryId"
    )
    Page<News> findAllByCategoryId(@Param("tenantId") Long tenantId, @Param("categoryId") Long categoryId, Pageable pageable);

    @Query("select news from News news where news.tenantId = :tenantId and news.isDeleted = false")
    Page<News> findAll(Long tenantId, Pageable pageable);
}

package com.whiskerguard.organization.repository;

import com.whiskerguard.organization.domain.News;
import com.whiskerguard.organization.request.NewsReq;
import jakarta.persistence.criteria.Predicate;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.ZoneId;

/**
 * 描述：新闻的数据访问层接口
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/18
 */
@Repository
public interface NewsRepository extends JpaRepository<News, Long>, JpaSpecificationExecutor<News> {

    @Query("select news from News news where news.tenantId = :tenantId and news.isDeleted = false")
    Page<News> findByIsDeletedFalse(Long tenantId, Pageable pageable);

    @Query(
        value = "select n from News n left join n.category c " +
            "where n.isDeleted = false and n.tenantId = :tenantId and c.id = :categoryId",
        countQuery = "select count(n) from News n left join n.category c " +
            "where n.isDeleted = false and n.tenantId = :tenantId and c.id = :categoryId"
    )
    Page<News> findAllByCategoryId(@Param("tenantId") Long tenantId, @Param("categoryId") Long categoryId, Pageable pageable);

    @Query("select news from News news where news.tenantId = :tenantId and news.isDeleted = false")
    Page<News> findAll(Long tenantId, Pageable pageable);

    default Page<News> search(NewsReq req, Long tenantId, Pageable pageable) {
        return findAll((root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();
            predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("tenantId"), tenantId));

            if (StringUtils.isNotBlank(req.getKeyword())) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.or(
                    criteriaBuilder.like(root.get("title"), "%" + req.getKeyword() + "%"),
                    criteriaBuilder.like(root.get("subtitle"), "%" + req.getKeyword() + "%")
                ));
            }

            if (null != req.getStatus()) {
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("status"), req.getStatus()));
            }

            ZoneId utc = ZoneId.of("UTC");

            if (StringUtils.isNotBlank(req.getPublishStartDate())) {
                LocalDate localDate = LocalDate.parse(req.getPublishStartDate());
                Instant instant = localDate.atStartOfDay(utc).toInstant();
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.greaterThanOrEqualTo(root.get("publishDate"), instant));
            }

            if (StringUtils.isNotBlank(req.getPublishEndDate())) {
                LocalDate localDate = LocalDate.parse(req.getPublishEndDate());
                Instant instant = localDate.atTime(LocalTime.of(23, 59, 59)).atZone(utc).toInstant();
                predicate = criteriaBuilder.and(predicate, criteriaBuilder.lessThanOrEqualTo(root.get("publishDate"), instant));
            }

            predicate = criteriaBuilder.and(predicate, criteriaBuilder.equal(root.get("isDeleted"), Boolean.FALSE));
            return predicate;
        }, pageable);
    }
}

package com.whiskerguard.organization.service;

import com.whiskerguard.organization.service.dto.NewsReadRecordDTO;
import java.util.Optional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

/**
 * Service Interface for managing {@link com.whiskerguard.organization.domain.NewsReadRecord}.
 */
public interface NewsReadRecordService {
    /**
     * Save a newsReadRecord.
     *
     * @param newsReadRecordDTO the entity to save.
     * @return the persisted entity.
     */
    NewsReadRecordDTO save(NewsReadRecordDTO newsReadRecordDTO);

    /**
     * Updates a newsReadRecord.
     *
     * @param newsReadRecordDTO the entity to update.
     * @return the persisted entity.
     */
    NewsReadRecordDTO update(NewsReadRecordDTO newsReadRecordDTO);

    /**
     * Partially updates a newsReadRecord.
     *
     * @param newsReadRecordDTO the entity to update partially.
     * @return the persisted entity.
     */
    Optional<NewsReadRecordDTO> partialUpdate(NewsReadRecordDTO newsReadRecordDTO);

    /**
     * Get all the newsReadRecords.
     *
     * @param pageable the pagination information.
     * @return the list of entities.
     */
    Page<NewsReadRecordDTO> findAll(Pageable pageable);

    /**
     * Get all the newsReadRecords with eager load of many-to-many relationships.
     *
     * @param pageable the pagination information.
     * @return the list of entities.
     */
    Page<NewsReadRecordDTO> findAllWithEagerRelationships(Pageable pageable);

    /**
     * Get the "id" newsReadRecord.
     *
     * @param id the id of the entity.
     * @return the entity.
     */
    Optional<NewsReadRecordDTO> findOne(Long id);

    /**
     * Delete the "id" newsReadRecord.
     *
     * @param id the id of the entity.
     */
    void delete(Long id);
}

package com.whiskerguard.organization.service;

import com.whiskerguard.organization.service.dto.NewsAttachmentDTO;
import java.util.Optional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

/**
 * 描述：新闻附件服务接口
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/18
 */
public interface NewsAttachmentService {

    /**
     * 方法名称：save
     * 描述：保存新闻附件记录
     *
     * @param newsAttachmentDTO 要保存的新闻附件DTO对象
     * @return 保存后的新闻附件DTO对象
     * @since 1.0
     */
    NewsAttachmentDTO save(NewsAttachmentDTO newsAttachmentDTO);

    /**
     * 方法名称：update
     * 描述：更新新闻附件记录
     *
     * @param newsAttachmentDTO 要更新的新闻附件DTO对象
     * @return 更新后的新闻附件DTO对象
     * @since 1.0
     */
    NewsAttachmentDTO update(NewsAttachmentDTO newsAttachmentDTO);

    /**
     * 方法名称：partialUpdate
     * 描述：部分更新新闻附件记录
     *
     * @param newsAttachmentDTO 要部分更新的新闻附件DTO对象
     * @return 更新后的新闻附件DTO对象
     * @since 1.0
     */
    Optional<NewsAttachmentDTO> partialUpdate(NewsAttachmentDTO newsAttachmentDTO);

    /**
     * 方法名称：findAll
     * 描述：查询所有新闻附件记录
     *
     * @param pageable 分页参数
     * @return 新闻附件DTO对象分页结果
     * @since 1.0
     */
    Page<NewsAttachmentDTO> findAll(Pageable pageable);

    /**
     * 方法名称：findAllWithEagerRelationships
     * 描述：查询所有新闻附件记录（包含关联关系的急加载）
     *
     * @param pageable 分页参数
     * @return 新闻附件DTO对象分页结果
     * @since 1.0
     */
    Page<NewsAttachmentDTO> findAllWithEagerRelationships(Pageable pageable);

    /**
     * 方法名称：findOne
     * 描述：根据ID查询新闻附件记录
     *
     * @param id 新闻附件ID
     * @return 新闻附件DTO对象
     * @since 1.0
     */
    Optional<NewsAttachmentDTO> findOne(Long id);

    /**
     * 方法名称：delete
     * 描述：根据ID删除新闻附件记录
     *
     * @param id 新闻附件ID
     * @since 1.0
     */
    void delete(Long id);
}

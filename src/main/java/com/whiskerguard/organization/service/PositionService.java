package com.whiskerguard.organization.service;

import com.whiskerguard.organization.domain.Position;
import com.whiskerguard.organization.domain.enumeration.PositionCategory;
import com.whiskerguard.organization.service.dto.PositionDTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Optional;

/**
 * Service Interface for managing {@link Position}.
 * 职位管理服务接口
 */
public interface PositionService {
    /**
     * Save a position.
     * 保存职位信息
     *
     * @param positionDTO the entity to save. 要保存的职位DTO对象
     * @return the persisted entity. 持久化后的职位DTO对象
     */
    PositionDTO save(PositionDTO positionDTO);

    /**
     * Updates a position.
     * 更新职位信息
     *
     * @param positionDTO the entity to update. 要更新的职位DTO对象
     * @return the persisted entity. 持久化后的职位DTO对象
     */
    PositionDTO update(PositionDTO positionDTO);

    /**
     * Partially updates a position.
     * 部分更新职位信息
     *
     * @param positionDTO the entity to update partially. 要部分更新的职位DTO对象
     * @return the persisted entity. 持久化后的职位DTO对象
     */
    Optional<PositionDTO> partialUpdate(PositionDTO positionDTO);

    /**
     * Get all the positions.
     * 获取所有职位信息
     *
     * @param pageable the pagination information. 分页信息
     * @return the list of entities. 职位DTO对象分页列表
     */
    Page<PositionDTO> findAll(Pageable pageable);

    /**
     * Get the "id" position.
     * 根据ID获取职位信息
     *
     * @param id the id of the entity. 职位ID
     * @return the entity. 职位DTO对象
     */
    Optional<PositionDTO> findOne(Long id);

    /**
     * 根据ID删除职位
     * Delete the "id" position.
     *
     * @param id 职位ID the id of the entity.
     */
    void delete(Long id);

    /**
     * 根据岗位分类查询岗位
     *
     * @param category 岗位分类
     * @param pageable 分页信息
     * @return 岗位列表
     */
    Page<PositionDTO> findByCategory(PositionCategory category, Pageable pageable);

    /**
     * 根据岗位级别范围查询岗位
     *
     * @param minLevel 最小级别
     * @param maxLevel 最大级别
     * @param pageable 分页信息
     * @return 岗位列表
     */
    Page<PositionDTO> findByLevelBetween(Integer minLevel, Integer maxLevel, Pageable pageable);

    /**
     * 根据名称或编码模糊查询岗位
     *
     * @param keyword  关键词
     * @param pageable 分页信息
     * @return 岗位列表
     */
    Page<PositionDTO> searchByKeyword(String keyword, Pageable pageable);

    /**
     * 根据租户ID和岗位分类查询岗位
     *
     * @param tenantId 租户ID
     * @param category 岗位分类
     * @param pageable 分页信息
     * @return 岗位列表
     */
    Page<PositionDTO> findByTenantIdAndCategory(Long tenantId, PositionCategory category, Pageable pageable);

    /**
     * 根据租户ID和岗位级别范围查询岗位
     *
     * @param tenantId 租户ID
     * @param minLevel 最小级别
     * @param maxLevel 最大级别
     * @param pageable 分页信息
     * @return 岗位列表
     */
    Page<PositionDTO> findByTenantIdAndLevelBetween(Long tenantId, Integer minLevel, Integer maxLevel, Pageable pageable);

    /**
     * 综合搜索职位 - 支持按岗位分类、所属部门、岗位名称、岗位编码进行组合搜索
     *
     * @param category  岗位分类（可选）
     * @param orgUnitId 所属部门ID（可选）
     * @param keyword   关键词搜索（岗位名称或编码，可选）
     * @param pageable  分页信息
     * @return 岗位列表
     */
    Page<PositionDTO> findPositionsWithFilters(PositionCategory category, Long orgUnitId, String keyword, Pageable pageable);

    /**
     * 根据组织单元ID查询岗位
     *
     * @param id 组织单元ID
     * @return 岗位列表
     */
    List<PositionDTO> findByOrgUnitId(Long id);

    /**
     * 根据岗位名称模糊查询本租户下的岗位列表
     * Search positions by name (fuzzy search) within current tenant
     *
     * @param name 岗位名称关键词 position name keyword
     * @return 岗位列表 position list
     */
    List<PositionDTO> findByNameContainingIgnoreCase(String name);
}

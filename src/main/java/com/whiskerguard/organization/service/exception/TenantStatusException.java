package com.whiskerguard.organization.service.exception;

import com.whiskerguard.organization.domain.enumeration.TenantStatus;

/**
 * 租户状态变更异常
 */
public class TenantStatusException extends RuntimeException {

    private final TenantStatus currentStatus;
    private final TenantStatus targetStatus;

    public TenantStatusException(TenantStatus currentStatus, TenantStatus targetStatus) {
        super(String.format("无法将租户状态从 %s 更改为 %s", currentStatus, targetStatus));
        this.currentStatus = currentStatus;
        this.targetStatus = targetStatus;
    }

    public TenantStatus getCurrentStatus() {
        return currentStatus;
    }

    public TenantStatus getTargetStatus() {
        return targetStatus;
    }
}

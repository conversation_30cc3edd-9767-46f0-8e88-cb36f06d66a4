package com.whiskerguard.organization.service;

import io.micrometer.core.instrument.MeterRegistry;
import java.time.Instant;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

/**
 * Service for managing JWT tokens.
 * This service provides functionality to store, validate, and invalidate tokens.
 *
 * JWT令牌管理服务。
 * 提供令牌的存储、验证和失效功能。
 */
@Service
public class TokenStoreService {

    private static final Logger log = LoggerFactory.getLogger(TokenStoreService.class);
    private static final String INVALID_TOKENS_METER_NAME = "security.authentication.invalid-tokens";
    private static final String TOKEN_OPERATIONS_METER_NAME = "security.authentication.token-operations";

    private final Map<String, Instant> tokenStore = new ConcurrentHashMap<>();
    private final MeterRegistry meterRegistry;

    public TokenStoreService(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
        // 初始化监控指标
        meterRegistry.counter(INVALID_TOKENS_METER_NAME, "cause", "invalidated");
        meterRegistry.counter(INVALID_TOKENS_METER_NAME, "cause", "expired");
        meterRegistry.counter(TOKEN_OPERATIONS_METER_NAME, "operation", "store");
        meterRegistry.counter(TOKEN_OPERATIONS_METER_NAME, "operation", "invalidate");
        meterRegistry.counter(TOKEN_OPERATIONS_METER_NAME, "operation", "validate");
    }

    /**
     * Store a token with its expiration time.
     * 存储令牌及其过期时间
     *
     * @param token the token to store 要存储的令牌
     * @param expirationTime the expiration time of the token 令牌的过期时间
     */
    public void storeToken(String token, Instant expirationTime) {
        log.debug("Storing token with expiration time: {}", expirationTime);
        tokenStore.put(token, expirationTime);
        meterRegistry.counter(TOKEN_OPERATIONS_METER_NAME, "operation", "store").increment();
    }

    /**
     * Check if a token is valid.
     * 检查令牌是否有效
     *
     * @param token the token to validate 要验证的令牌
     * @return true if the token is valid, false otherwise 如果令牌有效则返回true，否则返回false
     */
    public boolean isValidToken(String token) {
        log.debug("Validating token");
        meterRegistry.counter(TOKEN_OPERATIONS_METER_NAME, "operation", "validate").increment();

        Instant expirationTime = tokenStore.get(token);
        if (expirationTime == null) {
            log.debug("Token not found in store");
            return true; // 如果token不在store中，说明没有被主动失效，返回true
        }

        if (Instant.now().isAfter(expirationTime)) {
            log.debug("Token expired at: {}", expirationTime);
            meterRegistry.counter(INVALID_TOKENS_METER_NAME, "cause", "expired").increment();
            tokenStore.remove(token);
            return false;
        }

        return true;
    }

    /**
     * Invalidate a token.
     * 使令牌失效
     *
     * @param token the token to invalidate 要使其失效的令牌
     */
    public void invalidateToken(String token) {
        log.debug("Invalidating token");
        if (tokenStore.remove(token) != null) {
            meterRegistry.counter(TOKEN_OPERATIONS_METER_NAME, "operation", "invalidate").increment();
            meterRegistry.counter(INVALID_TOKENS_METER_NAME, "cause", "invalidated").increment();
        }
    }

    /**
     * Clean up expired tokens.
     * 清理过期的令牌
     */
    public void cleanupExpiredTokens() {
        log.debug("Cleaning up expired tokens");
        Instant now = Instant.now();
        tokenStore
            .entrySet()
            .removeIf(entry -> {
                if (now.isAfter(entry.getValue())) {
                    meterRegistry.counter(INVALID_TOKENS_METER_NAME, "cause", "expired").increment();
                    return true;
                }
                return false;
            });
    }
}

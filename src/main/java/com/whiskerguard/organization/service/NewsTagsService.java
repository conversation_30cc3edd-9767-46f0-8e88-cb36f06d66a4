package com.whiskerguard.organization.service;

import com.whiskerguard.organization.service.dto.NewsTagsDTO;
import java.util.Optional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

/**
 * 描述：新闻标签关联服务接口
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/18
 */
public interface NewsTagsService {

    /**
     * 方法名称：save
     * 描述：保存新闻标签关联记录
     *
     * @param newsTagsDTO 要保存的新闻标签关联DTO对象
     * @return 保存后的新闻标签关联DTO对象
     * @since 1.0
     */
    NewsTagsDTO save(NewsTagsDTO newsTagsDTO);

    /**
     * 方法名称：update
     * 描述：更新新闻标签关联记录
     *
     * @param newsTagsDTO 要更新的新闻标签关联DTO对象
     * @return 更新后的新闻标签关联DTO对象
     * @since 1.0
     */
    NewsTagsDTO update(NewsTagsDTO newsTagsDTO);

    /**
     * 方法名称：partialUpdate
     * 描述：部分更新新闻标签关联记录
     *
     * @param newsTagsDTO 要部分更新的新闻标签关联DTO对象
     * @return 更新后的新闻标签关联DTO对象
     * @since 1.0
     */
    Optional<NewsTagsDTO> partialUpdate(NewsTagsDTO newsTagsDTO);

    /**
     * 方法名称：findAll
     * 描述：查询所有新闻标签关联记录
     *
     * @param pageable 分页参数
     * @return 新闻标签关联DTO对象分页结果
     * @since 1.0
     */
    Page<NewsTagsDTO> findAll(Pageable pageable);

    /**
     * 方法名称：findOne
     * 描述：根据ID查询新闻标签关联记录
     *
     * @param id 新闻标签关联ID
     * @return 新闻标签关联DTO对象
     * @since 1.0
     */
    Optional<NewsTagsDTO> findOne(Long id);

    /**
     * 方法名称：delete
     * 描述：根据ID删除新闻标签关联记录
     *
     * @param id 新闻标签关联ID
     * @since 1.0
     */
    void delete(Long id);
}

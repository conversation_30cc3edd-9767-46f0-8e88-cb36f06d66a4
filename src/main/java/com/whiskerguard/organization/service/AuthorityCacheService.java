package com.whiskerguard.organization.service;

import java.util.Set;
import org.springframework.security.core.GrantedAuthority;

/**
 * 描述：权限缓存服务接口
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/18
 */
public interface AuthorityCacheService {

    /**
     * 方法名称：getAuthorities
     * 描述：获取用户的权限集合
     *
     * @param username 用户名
     * @return 该用户拥有的所有权限集合
     * @since 1.0
     */
    Set<GrantedAuthority> getAuthorities(String username);

    /**
     * 方法名称：cacheAuthorities
     * 描述：缓存用户的权限集合
     *
     * @param username    用户名
     * @param authorities 用户的最新权限集合
     * @since 1.0
     */
    void cacheAuthorities(String username, Set<GrantedAuthority> authorities);

    /**
     * 方法名称：evictAuthorities
     * 描述：清除指定用户的权限缓存
     *
     * @param username 需要清除缓存的用户名
     * @since 1.0
     */
    void evictAuthorities(String username);

    /**
     * 方法名称：evictAllAuthorities
     * 描述：清除所有用户的权限缓存
     *
     * @since 1.0
     */
    void evictAllAuthorities();
}

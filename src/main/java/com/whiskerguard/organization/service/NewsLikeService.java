package com.whiskerguard.organization.service;

import com.whiskerguard.organization.service.dto.NewsLikeDTO;
import java.util.Optional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

/**
 * Service Interface for managing {@link com.whiskerguard.organization.domain.NewsLike}.
 */
public interface NewsLikeService {
    /**
     * Save a newsLike.
     *
     * @param newsLikeDTO the entity to save.
     * @return the persisted entity.
     */
    NewsLikeDTO save(NewsLikeDTO newsLikeDTO);

    /**
     * Updates a newsLike.
     *
     * @param newsLikeDTO the entity to update.
     * @return the persisted entity.
     */
    NewsLikeDTO update(NewsLikeDTO newsLikeDTO);

    /**
     * Partially updates a newsLike.
     *
     * @param newsLikeDTO the entity to update partially.
     * @return the persisted entity.
     */
    Optional<NewsLikeDTO> partialUpdate(NewsLikeDTO newsLikeDTO);

    /**
     * Get all the newsLikes.
     *
     * @param pageable the pagination information.
     * @return the list of entities.
     */
    Page<NewsLikeDTO> findAll(Pageable pageable);

    /**
     * Get all the newsLikes with eager load of many-to-many relationships.
     *
     * @param pageable the pagination information.
     * @return the list of entities.
     */
    Page<NewsLikeDTO> findAllWithEagerRelationships(Pageable pageable);

    /**
     * Get the "id" newsLike.
     *
     * @param id the id of the entity.
     * @return the entity.
     */
    Optional<NewsLikeDTO> findOne(Long id);

    /**
     * Delete the "id" newsLike.
     *
     * @param id the id of the entity.
     */
    void delete(Long id);
}

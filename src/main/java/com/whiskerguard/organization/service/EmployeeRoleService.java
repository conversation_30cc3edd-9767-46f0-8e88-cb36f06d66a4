package com.whiskerguard.organization.service;

import com.whiskerguard.organization.service.dto.EmployeeRoleDTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Optional;


/**
 * 描述：员工角色关联服务接口
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/18
 */
public interface EmployeeRoleService {

    /**
     * 方法名称：save
     * 描述：保存员工角色关联记录
     *
     * @param employeeRoleDTO 要保存的员工角色关联DTO对象
     * @return 保存后的员工角色关联DTO对象
     * @since 1.0
     */
    EmployeeRoleDTO save(EmployeeRoleDTO employeeRoleDTO);

    /**
     * 方法名称：update
     * 描述：更新员工角色关联记录
     *
     * @param employeeRoleDTO 要更新的员工角色关联DTO对象
     * @return 更新后的员工角色关联DTO对象
     * @since 1.0
     */
    EmployeeRoleDTO update(EmployeeRoleDTO employeeRoleDTO);

    /**
     * 方法名称：partialUpdate
     * 描述：部分更新员工角色关联记录
     *
     * @param employeeRoleDTO 要部分更新的员工角色关联DTO对象
     * @return 更新后的员工角色关联DTO对象
     * @since 1.0
     */
    Optional<EmployeeRoleDTO> partialUpdate(EmployeeRoleDTO employeeRoleDTO);

    /**
     * 方法名称：findAll
     * 描述：查询所有员工角色关联记录
     *
     * @param pageable 分页参数
     * @return 员工角色关联DTO对象分页结果
     * @since 1.0
     */
    Page<EmployeeRoleDTO> findAll(Pageable pageable);

    /**
     * 方法名称：findOne
     * 描述：根据ID查询员工角色关联记录
     *
     * @param id 员工角色关联ID
     * @return 员工角色关联DTO对象
     * @since 1.0
     */
    Optional<EmployeeRoleDTO> findOne(Long id);

    /**
     * 方法名称：delete
     * 描述：根据ID删除员工角色关联记录
     *
     * @param id 员工角色关联ID
     * @since 1.0
     */
    void delete(Long id);

    /**
     * 方法名称：findByEmployeeId
     * 描述：根据员工ID查询员工角色关联记录
     *
     * @param employeeId 员工ID
     * @return 员工角色关联DTO对象列表
     * @since 1.0
     */
    List<EmployeeRoleDTO> findByEmployeeId(Long employeeId);

    /**
     * 方法名称：findEmployeeIdsByRoleCode
     * 描述：根据角色编码查询员工ID列表
     *
     * @param tenantId 租户ID
     * @param userType 角色编码
     * @return 员工ID列表
     * @since 1.0
     */
    List<Long> findEmployeeIdsByRoleCode(Long tenantId, Integer userType);
}

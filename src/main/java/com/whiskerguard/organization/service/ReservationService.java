package com.whiskerguard.organization.service;

import com.whiskerguard.organization.service.dto.ReservationDTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

/**
 * 描述：预约体验表服务接口
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/17
 */
public interface ReservationService {

    /**
     * 方法名称：save
     * 描述：保存预约体验记录
     *
     * @param reservationDTO 要保存的预约体验记录DTO
     * @return 保存后的预约体验记录DTO
     * @since 1.0
     */
    ReservationDTO save(ReservationDTO reservationDTO);

    /**
     * 方法名称：findAll
     * 描述：查询所有预约体验记录
     *
     * @param pageable 分页参数
     * @return 预约体验记录DTO分页结果
     * @since 1.0
     */
    Page<ReservationDTO> findAll(Pageable pageable);

}

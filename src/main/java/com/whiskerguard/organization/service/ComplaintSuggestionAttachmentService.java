package com.whiskerguard.organization.service;

import com.whiskerguard.organization.service.dto.ComplaintSuggestionAttachmentDTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.Optional;

/**
 * 描述：投诉与建议附件服务接口
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/20
 */
public interface ComplaintSuggestionAttachmentService {
    /**
     * 方法名称：save
     * 描述：保存投诉与建议附件记录
     *
     * @param complaintSuggestionAttachmentDTO 要保存的投诉与建议附件DTO
     * @return 保存后的投诉与建议附件DTO
     * @since 1.0
     */
    ComplaintSuggestionAttachmentDTO save(ComplaintSuggestionAttachmentDTO complaintSuggestionAttachmentDTO);

    /**
     * 方法名称：partialUpdate
     * 描述：部分更新投诉与建议附件记录
     *
     * @param complaintSuggestionAttachmentDTO 要更新的投诉与建议附件DTO
     * @return 更新后的投诉与建议附件DTO
     * @since 1.0
     */
    Optional<ComplaintSuggestionAttachmentDTO> partialUpdate(ComplaintSuggestionAttachmentDTO complaintSuggestionAttachmentDTO);

    /**
     * 方法名称：findAll
     * 描述：查询所有投诉与建议附件记录
     *
     * @param suggestionId 投诉与建议ID
     * @param pageable     分页参数
     * @return 投诉与建议附件DTO分页结果
     * @since 1.0
     */
    Page<ComplaintSuggestionAttachmentDTO> findAll(Long suggestionId, Pageable pageable);

    /**
     * 方法名称：findOne
     * 描述：根据ID查询投诉与建议附件记录
     *
     * @param id 投诉与建议附件ID
     * @return 投诉与建议附件DTO
     * @since 1.0
     */
    Optional<ComplaintSuggestionAttachmentDTO> findOne(Long id);

    /**
     * 方法名称：delete
     * 描述：删除投诉与建议附件记录
     *
     * @param id 投诉与建议附件ID
     * @since 1.0
     */
    void delete(Long id);
}

package com.whiskerguard.organization.service;

import com.whiskerguard.organization.service.dto.MenuDTO;
import com.whiskerguard.organization.service.dto.PermissionDTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Optional;

/**
 * 权限管理服务接口。
 * 负责系统权限的创建、更新、查询等管理操作，
 * 作为系统权限控制的基础服务。
 */
public interface PermissionService {
    /**
     * 保存权限信息。
     * 需要 PERM_PERMISSION_MANAGE 权限。
     *
     * @param menuDTO 需要保存的权限DTO对象
     * @return 保存后的权限DTO对象
     */
    MenuDTO save(MenuDTO menuDTO);

    /**
     * 部分更新权限信息。
     * 需要 PERM_PERMISSION_MANAGE 权限。
     *
     * @param menuDTO 包含部分更新字段的菜单DTO对象
     * @return 更新后的权限DTO对象（Optional包装）
     */
    Optional<MenuDTO> partialUpdate(MenuDTO menuDTO);

    /**
     * 分页获取所有权限信息。
     * 需要 PERM_PERMISSION_VIEW 权限。
     *
     * @param pageable 分页参数
     * @return 权限DTO对象的分页结果
     */
    Page<PermissionDTO> findAll(Pageable pageable);

    /**
     * 根据ID查询权限信息。
     * 需要 PERM_PERMISSION_VIEW 权限。
     *
     * @param id 权限ID
     * @return 权限DTO对象（Optional包装）
     */
    Optional<PermissionDTO> findOne(Long id);

    /**
     * 根据ID删除权限。
     * 需要 PERM_PERMISSION_MANAGE 权限。
     *
     * @param id 需要删除的权限ID
     */
    void delete(Long id);

    /**
     * 根据租户ID获取树形结构的权限（分页）。
     * 该方法会返回分页的顶级权限，每个顶级权限都包含其完整的子权限树。
     * 需要 PERM_PERMISSION_VIEW 权限。
     *
     * @return 树形结构权限数据
     */
    List<PermissionDTO> findTreeByTenantId();

    /**
     * 根据父权限ID获取子权限。
     * 需要 PERM_PERMISSION_VIEW 权限。
     *
     * @param parentId 父权限ID
     * @return 子权限列表
     */
    List<PermissionDTO> findByParentId(Long parentId);

    /**
     * 根据员工ID获取菜单树形结构。
     * 如果员工角色是管理员，则返回该租户的所有菜单权限；否则返回员工拥有的菜单权限。
     * 该方法会将权限数据转换为前端菜单系统所需的格式，并将button权限赋值给auths字段。
     *
     * @param employeeId 员工ID
     * @return 菜单树形结构列表
     */
    List<MenuDTO> findMenuTreeByEmployeeId(Long employeeId);

    /**
     * 获取菜单树形结构。
     * 该方法会将权限数据转换为前端菜单系统所需的格式，并将button权限赋值给auths字段。
     *
     * @return 菜单树形结构列表
     */
    List<MenuDTO> findAllMenu();

    /**
     * 根据角色ID获取菜单树形结构。
     * 该方法会将权限数据转换为前端菜单系统所需的格式，并将button权限赋值给auths字段。
     *
     * @param roleId 角色ID
     * @return 菜单树形结构列表
     */
    List<MenuDTO> findMenuTreeByByRoleId(Long roleId);

    /**
     * 保存菜单数据
     *
     * @param list 菜单数据列表
     * @return 保存后的菜单数据列表
     */
    List<MenuDTO> saveAll(List<MenuDTO> list);
}

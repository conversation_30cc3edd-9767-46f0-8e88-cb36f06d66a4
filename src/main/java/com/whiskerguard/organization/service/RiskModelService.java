package com.whiskerguard.organization.service;

import com.whiskerguard.organization.request.RiskModelReq;
import com.whiskerguard.organization.service.dto.RiskModelDTO;
import org.springframework.data.domain.Page;

import java.util.Optional;

/**
 * 描述：风险模型服务接口
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/20
 */
public interface RiskModelService {

    /**
     * 方法名称：save
     * 描述：保存风险模型记录
     *
     * @param riskModelDTO 要保存的风险模型DTO
     * @return 保存后的风险模型DTO
     * @since 1.0
     */
    RiskModelDTO save(RiskModelDTO riskModelDTO);

    /**
     * 方法名称：partialUpdate
     * 描述：部分更新风险模型记录
     *
     * @param riskModelDTO 要更新的风险模型DTO
     * @return 更新后的风险模型DTO
     * @since 1.0
     */
    Optional<RiskModelDTO> partialUpdate(RiskModelDTO riskModelDTO);

    /**
     * 方法名称：findAll
     * 描述：查询所有风险模型记录
     *
     * @param req 风险模型请求参数
     * @return 风险模型DTO分页结果
     * @since 1.0
     */
    Page<RiskModelDTO> findAll(RiskModelReq req);

    /**
     * 方法名称：findOne
     * 描述：根据ID查询风险模型记录
     *
     * @param id 风险模型ID
     * @return 风险模型DTO
     * @since 1.0
     */
    Optional<RiskModelDTO> findOne(Long id);

    /**
     * 方法名称：delete
     * 描述：删除风险模型记录
     *
     * @param id 风险模型ID
     * @since 1.0
     */
    void delete(Long id);

    /**
     * 方法名称：analyzeRiskModel
     * 描述：根据指定的内容结合风险模型，进行AI分析后，得到多种风险级别
     *
     * @param content 要分析的内容
     * @return 分析结果
     * @since 1.0
     */
    String analyzeRiskModel(String content);
}

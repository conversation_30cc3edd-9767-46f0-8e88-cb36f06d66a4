//package com.whiskerguard.organization.service;
//
//import com.whiskerguard.organization.domain.*;
//import com.whiskerguard.organization.domain.enumeration.ResourceType;
//import com.whiskerguard.organization.repository.EmployeeRepository;
//import com.whiskerguard.organization.repository.EmployeeRoleRepository;
//import com.whiskerguard.organization.repository.RolePermissionRepository;
//import org.apache.commons.lang3.StringUtils;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.boot.context.event.ApplicationReadyEvent;
//import org.springframework.context.event.EventListener;
//import org.springframework.security.core.GrantedAuthority;
//import org.springframework.security.core.authority.SimpleGrantedAuthority;
//import org.springframework.stereotype.Service;
//import org.springframework.transaction.annotation.Transactional;
//
//import java.util.HashSet;
//import java.util.List;
//import java.util.Set;
//
/// **
// * 权限缓存预热服务。
// * 负责在应用启动时，预先加载所有用户的权限数据到缓存中，
// * 避免系统启动后的首次访问出现性能延迟。
// */
//@Service
//public class AuthorityCacheWarmupService {
//
//    private static final Logger LOG = LoggerFactory.getLogger(AuthorityCacheWarmupService.class);
//
//    // 员工信息仓库
//    private final EmployeeRepository employeeRepository;
//    // 员工角色关系仓库
//    private final EmployeeRoleRepository employeeRoleRepository;
//    // 角色权限关系仓库
//    private final RolePermissionRepository rolePermissionRepository;
//    // 权限缓存服务
//    private final AuthorityCacheService authorityCacheService;
//
//    @Value("${spring.profiles.active}")
//    private String activeProfiles;
//
//    /**
//     * 构造方法，注入所需的仓库和服务组件。
//     *
//     * @param employeeRepository       员工信息仓库
//     * @param employeeRoleRepository   员工角色关系仓库
//     * @param rolePermissionRepository 角色权限关系仓库
//     * @param authorityCacheService    权限缓存服务
//     */
//    public AuthorityCacheWarmupService(
//        EmployeeRepository employeeRepository,
//        EmployeeRoleRepository employeeRoleRepository,
//        RolePermissionRepository rolePermissionRepository,
//        AuthorityCacheService authorityCacheService
//    ) {
//        this.employeeRepository = employeeRepository;
//        this.employeeRoleRepository = employeeRoleRepository;
//        this.rolePermissionRepository = rolePermissionRepository;
//        this.authorityCacheService = authorityCacheService;
//    }
//
//    /**
//     * 应用启动完成后，自动预热权限缓存。
//     * 该方法会查询所有员工的角色和权限信息，
//     * 并将其加载到权限缓存中，提高系统启动后的访问性能。
//     */
//    @EventListener(ApplicationReadyEvent.class)
//    @Transactional(readOnly = true)
//    public void warmupCache() {
//        LOG.info("开始预热权限缓存...");
//        if ("dev".equals(activeProfiles)) {
//            LOG.info("当前为开发环境，跳过权限缓存预热");
//            return;
//        }
//
//        // 获取所有员工
//        List<Employee> employees = employeeRepository.findAll();
//
//        for (Employee employee : employees) {
//            // 获取员工的所有角色
//            List<EmployeeRole> employeeRoles = employeeRoleRepository.findByEmployee(employee);
//
//            // 收集所有权限
//            Set<GrantedAuthority> authorities = new HashSet<>();
//
//            // 添加角色权限
//            for (EmployeeRole employeeRole : employeeRoles) {
//                Role role = employeeRole.getRole();
//                if (role != null) {
//                    // 添加角色权限
//                    authorities.add(new SimpleGrantedAuthority("ROLE_" + role.getCode()));
//
//                    // 获取角色的所有权限
//                    List<RolePermission> rolePermissions = rolePermissionRepository.findByRoleId(role.getId());
//                    for (RolePermission rolePermission : rolePermissions) {
//                        Permission permission = rolePermission.getPermission();
//                        if (permission != null) {
//                            // 添加功能权限
//                            authorities.add(new SimpleGrantedAuthority("PERM_" + permission.getCode()));
//                            // 添加数据范围权限
//                            authorities.add(new SimpleGrantedAuthority("SCOPE_" + permission.getCode()));
//                            // 添加按钮权限
//                            if (permission.getResourceType() == ResourceType.BUTTON &&
//                                StringUtils.isNotEmpty(permission.getBackendUrl())) {
//                                authorities.add(new SimpleGrantedAuthority(permission.getBackendUrl()));
//                            }
//                        }
//                    }
//                }
//            }
//
//            // 缓存权限
//            authorityCacheService.cacheAuthorities(employee.getUsername(), authorities);
//            LOG.debug("Warmed up authorities cache for user: {}", employee.getUsername());
//        }
//
//        LOG.info("Completed authority cache warmup");
//    }
//}

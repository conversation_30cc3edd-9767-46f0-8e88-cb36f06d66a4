package com.whiskerguard.organization.service;

import com.whiskerguard.organization.domain.enumeration.AuditOperation;
import com.whiskerguard.organization.service.dto.AuditLogDTO;
import java.util.Optional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.security.access.prepost.PreAuthorize;

/**
 * 描述：审计日志服务接口
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/18
 */
public interface AuditLogService {

    /**
     * 方法名称：save
     * 描述：保存审计日志记录
     *
     * @param auditLogDTO 要保存的审计日志DTO对象
     * @return 保存后的审计日志DTO对象
     * @since 1.0
     */
    AuditLogDTO save(AuditLogDTO auditLogDTO);

    /**
     * 方法名称：update
     * 描述：更新审计日志记录
     *
     * @param auditLogDTO 要更新的审计日志DTO对象
     * @return 更新后的审计日志DTO对象
     * @since 1.0
     */
    AuditLogDTO update(AuditLogDTO auditLogDTO);

    /**
     * 方法名称：partialUpdate
     * 描述：部分更新审计日志记录
     *
     * @param auditLogDTO 要部分更新的审计日志DTO对象
     * @return 更新后的审计日志DTO对象
     * @since 1.0
     */
    Optional<AuditLogDTO> partialUpdate(AuditLogDTO auditLogDTO);

    /**
     * 方法名称：findAll
     * 描述：查询所有审计日志记录
     *
     * @param pageable 分页参数
     * @return 审计日志DTO对象分页结果
     * @since 1.0
     */
    Page<AuditLogDTO> findAll(Pageable pageable);

    /**
     * 方法名称：findOne
     * 描述：根据ID查询审计日志记录
     *
     * @param id 审计日志ID
     * @return 审计日志DTO对象
     * @since 1.0
     */
    Optional<AuditLogDTO> findOne(Long id);

    /**
     * 方法名称：delete
     * 描述：根据ID删除审计日志记录
     *
     * @param id 审计日志ID
     * @since 1.0
     */
    void delete(Long id);

    /**
     * 方法名称：log
     * 描述：记录审计日志
     *
     * @param entityName 实体名称
     * @param entityId   实体ID
     * @param operation  操作类型
     * @param diff       变更内容
     * @return 审计日志DTO对象
     * @since 1.0
     */
    AuditLogDTO log(String entityName, Long entityId, AuditOperation operation, String diff);

    /**
     * 方法名称：findByEntity
     * 描述：根据实体信息查询审计日志记录
     *
     * @param entityName 实体名称
     * @param entityId   实体ID
     * @param pageable   分页参数
     * @return 审计日志分页结果
     * @since 1.0
     */
    Page<AuditLogDTO> findByEntity(String entityName, Long entityId, Pageable pageable);
}

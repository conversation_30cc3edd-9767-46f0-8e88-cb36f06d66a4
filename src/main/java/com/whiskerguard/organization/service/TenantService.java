package com.whiskerguard.organization.service;

import com.whiskerguard.common.dto.ImportResultDTO;
import com.whiskerguard.organization.domain.enumeration.TenantStatus;
import com.whiskerguard.organization.service.dto.TenantDTO;
import com.whiskerguard.organization.service.dto.TenantImportDTO;
import com.whiskerguard.organization.service.dto.TenantTreeDTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Optional;

/**
 * 描述：租户服务接口
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/18
 */
public interface TenantService {

    /**
     * Save a tenant.
     * 保存租户信息
     *
     * @param tenantDTO the entity to save. 要保存的租户DTO对象
     * @return the persisted entity. 持久化后的租户DTO对象
     */
    TenantDTO save(TenantDTO tenantDTO);

    /**
     * Updates a tenant.
     * 更新租户信息
     *
     * @param tenantDTO the entity to update. 要更新的租户DTO对象
     * @return the persisted entity. 持久化后的租户DTO对象
     */
    TenantDTO update(TenantDTO tenantDTO);

    /**
     * Partially updates a tenant.
     * 部分更新租户信息
     *
     * @param tenantDTO the entity to update partially. 要部分更新的租户DTO对象
     * @return the persisted entity. 持久化后的租户DTO对象
     */
    Optional<TenantDTO> partialUpdate(TenantDTO tenantDTO);

    /**
     * Get all the tenants.
     * 获取所有租户信息
     *
     * @param pageable the pagination information. 分页信息
     * @return the list of entities. 租户DTO对象分页列表
     */
    Page<TenantDTO> findAll(Pageable pageable);

    /**
     * Get the "id" tenant.
     * 根据ID获取租户信息
     *
     * @param id the id of the entity. 租户ID
     * @return the entity. 租户DTO对象
     */
    Optional<TenantDTO> findOne(Long id);

    /**
     * Delete the "id" tenant.
     * 根据ID删除租户
     *
     * @param id the id of the entity. 租户ID
     */
    void delete(Long id);

    /**
     * Change the status of a tenant.
     * 变更租户状态
     *
     * @param id           the id of the tenant to change status. 要变更状态的租户ID
     * @param targetStatus the target status to change to. 目标状态
     * @return the updated tenant. 更新后的租户
     */
    TenantDTO changeStatus(Long id, TenantStatus targetStatus);

    /**
     * Get all the tenants by status.
     * 根据状态获取所有租户
     *
     * @param status   the status of the tenants to retrieve. 要检索的租户状态
     * @param pageable the pagination information. 分页信息
     * @return the list of entities. 租户DTO对象分页列表
     */
    Page<TenantDTO> findAllByStatus(TenantStatus status, Pageable pageable);

    /**
     * Check if a tenant exists by name.
     * 检查指定名称的租户是否存在
     *
     * @param name the name to check 要检查的名称
     * @return true if tenant exists, false otherwise 如果租户存在则返回true，否则返回false
     */
    boolean existsByName(String name);

    /**
     * Validate tenant name.
     * Throws BadRequestAlertException if name already exists.
     * 验证租户名称，如果名称已存在则抛出BadRequestAlertException异常
     *
     * @param name the name to validate 要验证的名称
     * @throws com.whiskerguard.organization.web.rest.errors.BadRequestAlertException if validation fails 如果验证失败则抛出异常
     */
    void validateName(String name);

    /**
     * Import tenants from Excel file.
     * 从Excel文件导入租户
     *
     * @param file the Excel file to import. 要导入的Excel文件
     * @return the import result. 导入结果
     */
    ImportResultDTO<TenantImportDTO> importFromExcel(MultipartFile file);

    /**
     * 检查当前租户是否可以访问系统
     *
     * @param tenantId 租户ID
     * @return 如果可以访问则返回true，否则返回false
     */
    Boolean checkTenantAccess(Long tenantId);

    /**
     * 获取当前租户的所有下级租户树结构
     *
     * @return 下级租户树结构列表
     */
    List<TenantTreeDTO> getSubTenantTree();
}

package com.whiskerguard.organization.service;

import com.whiskerguard.organization.client.dto.DocumentRecordDTO;

import java.util.List;

/**
 * 描述：文档同步服务接口
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/18
 */
public interface DocumentSyncService {

    /**
     * 方法名称：syncOrgUnits
     * 描述：同步所有组织单元到检索服务
     *
     * @return 同步结果列表
     * @since 1.0
     */
    List<DocumentRecordDTO> syncOrgUnits();

}

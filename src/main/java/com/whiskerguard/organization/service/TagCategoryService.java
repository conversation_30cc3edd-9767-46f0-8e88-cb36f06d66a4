package com.whiskerguard.organization.service;

import com.whiskerguard.organization.service.dto.TagCategoryDTO;
import java.util.Optional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

/**
 * 标签分类管理服务接口
 * Service Interface for managing {@link com.whiskerguard.organization.domain.TagCategory}.
 */
public interface TagCategoryService {
    /**
     * 保存标签分类信息
     * Save a tagCategory.
     *
     * @param tagCategoryDTO 要保存的标签分类DTO对象 the entity to save.
     * @return 持久化后的标签分类DTO对象 the persisted entity.
     */
    TagCategoryDTO save(TagCategoryDTO tagCategoryDTO);

    /**
     * 更新标签分类信息
     * Updates a tagCategory.
     *
     * @param tagCategoryDTO 要更新的标签分类DTO对象 the entity to update.
     * @return 持久化后的标签分类DTO对象 the persisted entity.
     */
    TagCategoryDTO update(TagCategoryDTO tagCategoryDTO);

    /**
     * 部分更新标签分类信息
     * Partially updates a tagCategory.
     *
     * @param tagCategoryDTO 要部分更新的标签分类DTO对象 the entity to update partially.
     * @return 持久化后的标签分类DTO对象 the persisted entity.
     */
    Optional<TagCategoryDTO> partialUpdate(TagCategoryDTO tagCategoryDTO);

    /**
     * 获取所有标签分类信息
     * Get all the tagCategories.
     *
     * @param pageable 分页信息 the pagination information.
     * @return 标签分类DTO对象分页列表 the list of entities.
     */
    Page<TagCategoryDTO> findAll(Pageable pageable);

    /**
     * 根据ID获取标签分类信息
     * Get the "id" tagCategory.
     *
     * @param id 标签分类ID the id of the entity.
     * @return 标签分类DTO对象 the entity.
     */
    Optional<TagCategoryDTO> findOne(Long id);

    /**
     * 根据ID删除标签分类
     * Delete the "id" tagCategory.
     *
     * @param id 标签分类ID the id of the entity.
     */
    void delete(Long id);
}

package com.whiskerguard.organization.service;

import com.whiskerguard.organization.service.dto.LetterCommitmentDTO;

import java.util.Optional;

/**
 * 描述：承诺书服务接口
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/18
 */
public interface LetterCommitmentService {

    /**
     * 方法名称：save
     * 描述：保存承诺书记录
     *
     * @param letterCommitmentDTO 要保存的承诺书DTO对象
     * @return 保存后的承诺书DTO对象
     * @since 1.0
     */
    LetterCommitmentDTO save(LetterCommitmentDTO letterCommitmentDTO);

    /**
     * 方法名称：findLetterCommitmentByEmployee
     * 描述：根据员工查询承诺书记录
     *
     * @return 承诺书DTO对象
     * @since 1.0
     */
    Optional<LetterCommitmentDTO> findLetterCommitmentByEmployee();
}

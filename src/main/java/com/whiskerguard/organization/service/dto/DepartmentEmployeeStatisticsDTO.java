package com.whiskerguard.organization.service.dto;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 部门员工分布统计DTO
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/21
 */
@Schema(description = "部门员工分布统计")
public class DepartmentEmployeeStatisticsDTO {

    @Schema(description = "部门ID")
    private Long departmentId;

    @Schema(description = "部门名称")
    private String departmentName;

    @Schema(description = "部门编码")
    private String departmentCode;

    @Schema(description = "员工数量")
    private Long employeeCount;

    public DepartmentEmployeeStatisticsDTO() {
    }

    public DepartmentEmployeeStatisticsDTO(Long departmentId, String departmentName, String departmentCode, Long employeeCount) {
        this.departmentId = departmentId;
        this.departmentName = departmentName;
        this.departmentCode = departmentCode;
        this.employeeCount = employeeCount;
    }

    public Long getDepartmentId() {
        return departmentId;
    }

    public void setDepartmentId(Long departmentId) {
        this.departmentId = departmentId;
    }

    public String getDepartmentName() {
        return departmentName;
    }

    public void setDepartmentName(String departmentName) {
        this.departmentName = departmentName;
    }

    public String getDepartmentCode() {
        return departmentCode;
    }

    public void setDepartmentCode(String departmentCode) {
        this.departmentCode = departmentCode;
    }

    public Long getEmployeeCount() {
        return employeeCount;
    }

    public void setEmployeeCount(Long employeeCount) {
        this.employeeCount = employeeCount;
    }

    @Override
    public String toString() {
        return "DepartmentEmployeeStatisticsDTO{" +
            "departmentId=" + departmentId +
            ", departmentName='" + departmentName + '\'' +
            ", departmentCode='" + departmentCode + '\'' +
            ", employeeCount=" + employeeCount +
            '}';
    }
}

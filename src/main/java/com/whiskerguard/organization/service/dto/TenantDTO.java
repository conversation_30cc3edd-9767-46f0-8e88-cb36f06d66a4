package com.whiskerguard.organization.service.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.*;
import lombok.Data;

import java.io.Serializable;
import java.time.Instant;
import java.time.LocalDate;
import java.util.List;
import java.util.Objects;

/**
 * 租户DTO
 */
@Data
public class TenantDTO implements Serializable {

    @Schema(description = "主键 ID")
    private Long id;

    @NotBlank(message = "租户编码不能为空")
    @Size(max = 64, message = "租户编码长度不能超过64个字符")
    @Schema(description = "租户编码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String tenantCode;

    @NotBlank(message = "租户名称不能为空")
    @Size(max = 128, message = "租户名称长度不能超过128个字符")
    @Schema(description = "租户名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String name;

    @Schema(description = "租户状态", requiredMode = Schema.RequiredMode.REQUIRED)
    @Min(value = 0, message = "租户状态值必须大于等于0")
    @Max(value = 4, message = "租户状态值必须小于等于4")
    private Integer status = 0;

    @Size(max = 64, message = "套餐类型长度不能超过64个字符")
    @Schema(description = "套餐类型")
    private String subscriptionPlan;

    @Schema(description = "套餐开始日期")
    private LocalDate subscriptionStart;

    @Schema(description = "套餐结束日期")
    private LocalDate subscriptionEnd;

    @Size(max = 128, message = "联系人邮箱长度不能超过128个字符")
    @Schema(description = "联系人邮箱")
    private String contactEmail;

    @Size(max = 32, message = "联系人电话长度不能超过32个字符")
    @Schema(description = "联系人电话")
    private String contactPhone;

    @Schema(description = "扩展元数据")
    private String metadata;

    @Schema(description = "乐观锁版本", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer version;

    @Schema(description = "创建者")
    private String createdBy;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Instant createdAt;

    @Schema(description = "更新者")
    private String updatedBy;

    @Schema(description = "更新时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Instant updatedAt;

    @Schema(description = "软删除标志", requiredMode = Schema.RequiredMode.REQUIRED)
    private Boolean isDeleted;

    @Schema(description = "父级租户ID")
    private Long parentId;

    @Schema(description = "父级租户")
    private TenantDTO parent;

    @NotNull(message = "租户详情不能为空")
    @Schema(description = "租户详情")
    private TenantProfileDTO profile;

    @Schema(description = "附件列表")
    public List<TenantAttachmentDTO> attachments;

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof TenantDTO tenantDTO)) {
            return false;
        }

        if (this.id == null) {
            return false;
        }
        return Objects.equals(this.id, tenantDTO.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(this.id);
    }

}

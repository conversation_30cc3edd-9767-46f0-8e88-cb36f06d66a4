package com.whiskerguard.organization.service.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.whiskerguard.organization.domain.enumeration.ResourceType;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

import java.io.Serializable;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * A DTO for the {@link com.whiskerguard.organization.domain.Permission} entity.
 */
@Schema(description = "权限（Permission）实体")
@SuppressWarnings("common-java:DuplicatedBlocks")
public class PermissionDTO implements Serializable {

    @Schema(description = "主键 ID")
    private Long id;

    @NotBlank(message = "服务名称不能为空")
    @Size(max = 64, message = "服务名称长度不能超过64个字符")
    @Schema(description = "服务名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String serviceName;

    @NotBlank(message = "权限编码不能为空")
    @Size(max = 64, message = "权限编码长度不能超过64个字符")
    @Schema(description = "权限编码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String code;

    @NotBlank(message = "权限名称不能为空")
    @Size(max = 128, message = "权限名称长度不能超过128个字符")
    @Schema(description = "权限名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String name;

    @NotNull(message = "资源类型不能为空")
    @Schema(description = "资源类型", requiredMode = Schema.RequiredMode.REQUIRED)
    private ResourceType resourceType;

    @Size(max = 256, message = "URL模式长度不能超过256个字符")
    @Schema(description = "URL 模式")
    private String urlPattern;

    @Size(max = 16, message = "HTTP方法长度不能超过16个字符")
    @Schema(description = "HTTP 方法")
    private String method;

    @Size(max = 128, message = "前端路由长度不能超过128个字符")
    @Schema(description = "前端路由")
    private String frontendRoute;

    @Size(max = 128, message = "后端接口长度不能超过128个字符")
    @Schema(description = "后端接口")
    private String backendUrl;

    @Size(max = 64, message = "图标长度不能超过64个字符")
    @Schema(description = "图标")
    private String icon;

    @Min(value = 0, message = "排序不能小于0")
    @Schema(description = "排序")
    private Integer sortOrder;

    @Schema(description = "组件路径")
    private String component;

    @Schema(description = "重定向")
    private String redirect;

    @Schema(description = "描述")
    private String description;

    @Schema(description = "扩展元数据")
    private String metadata;

    @Schema(description = "乐观锁版本", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer version;

    @Schema(description = "创建者")
    private String createdBy;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Instant createdAt;

    @Schema(description = "更新者")
    private String updatedBy;

    @Schema(description = "更新时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Instant updatedAt;

    @Schema(description = "软删除标志", requiredMode = Schema.RequiredMode.REQUIRED)
    private Boolean isDeleted;

    @NotNull(message = "是否租户可用不能为空")
    @Schema(description = "是否租户可用", requiredMode = Schema.RequiredMode.REQUIRED)
    private Boolean isAvailable;

    private PermissionDTO parent;

    @Schema(description = "子权限列表")
    private List<PermissionDTO> children = new ArrayList<>();

    @Schema(description = "是否显示在前端菜单")
    private Boolean isShow;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getServiceName() {
        return serviceName;
    }

    public void setServiceName(String serviceName) {
        this.serviceName = serviceName;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public ResourceType getResourceType() {
        return resourceType;
    }

    public void setResourceType(ResourceType resourceType) {
        this.resourceType = resourceType;
    }

    public String getUrlPattern() {
        return urlPattern;
    }

    public void setUrlPattern(String urlPattern) {
        this.urlPattern = urlPattern;
    }

    public String getMethod() {
        return method;
    }

    public void setMethod(String method) {
        this.method = method;
    }

    public String getFrontendRoute() {
        return frontendRoute;
    }

    public void setFrontendRoute(String frontendRoute) {
        this.frontendRoute = frontendRoute;
    }

    public String getBackendUrl() {
        return backendUrl;
    }

    public void setBackendUrl(String backendUrl) {
        this.backendUrl = backendUrl;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public Integer getSortOrder() {
        return sortOrder;
    }

    public void setSortOrder(Integer sortOrder) {
        this.sortOrder = sortOrder;
    }

    public String getComponent() {
        return component;
    }

    public void setComponent(String component) {
        this.component = component;
    }

    public String getRedirect() {
        return redirect;
    }

    public void setRedirect(String redirect) {
        this.redirect = redirect;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getMetadata() {
        return metadata;
    }

    public void setMetadata(String metadata) {
        this.metadata = metadata;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public Instant getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Instant createdAt) {
        this.createdAt = createdAt;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    public Instant getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Instant updatedAt) {
        this.updatedAt = updatedAt;
    }

    public Boolean getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
    }

    public Boolean getIsAvailable() {
        return isAvailable;
    }

    public void setIsAvailable(Boolean isAvailable) {
        this.isAvailable = isAvailable;
    }

    public PermissionDTO getParent() {
        return parent;
    }

    public void setParent(PermissionDTO parent) {
        this.parent = parent;
    }

    public List<PermissionDTO> getChildren() {
        return children;
    }

    public void setChildren(List<PermissionDTO> children) {
        this.children = children;
    }

    public Boolean getIsShow() {
        return isShow;
    }

    public void setIsShow(Boolean isShow) {
        this.isShow = isShow;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof PermissionDTO permissionDTO)) {
            return false;
        }

        if (this.id == null) {
            return false;
        }
        return Objects.equals(this.id, permissionDTO.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(this.id);
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "PermissionDTO{" +
            "id=" + getId() +
            ", serviceName='" + getServiceName() + "'" +
            ", code='" + getCode() + "'" +
            ", name='" + getName() + "'" +
            ", resourceType='" + getResourceType() + "'" +
            ", urlPattern='" + getUrlPattern() + "'" +
            ", method='" + getMethod() + "'" +
            ", frontendRoute='" + getFrontendRoute() + "'" +
            ", backendUrl='" + getBackendUrl() + "'" +
            ", icon='" + getIcon() + "'" +
            ", sortOrder=" + getSortOrder() +
            ", component='" + getComponent() + "'" +
            ", redirect='" + getRedirect() + "'" +
            ", description='" + getDescription() + "'" +
            ", metadata='" + getMetadata() + "'" +
            ", version=" + getVersion() +
            ", createdBy='" + getCreatedBy() + "'" +
            ", createdAt='" + getCreatedAt() + "'" +
            ", updatedBy='" + getUpdatedBy() + "'" +
            ", updatedAt='" + getUpdatedAt() + "'" +
            ", isDeleted='" + getIsDeleted() + "'" +
            ", isAvailable='" + getIsAvailable() + "'" +
            ", isShow='" + getIsShow() + "'" +
            ", children=" + getChildren() +
            "}";
    }
}

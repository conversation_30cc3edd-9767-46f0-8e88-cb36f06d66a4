package com.whiskerguard.organization.service.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.io.Serializable;

/**
 * Excel导入角色的DTO
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/20
 */
@Data
public class RoleImportDTO implements Serializable {

    @Schema(description = "角色编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "角色编码不能为空")
    private String code;

    @Schema(description = "角色名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "角色名称不能为空")
    private String name;

    @Schema(description = "角色描述")
    private String description;

    @Schema(description = "权限编码列表，多个权限用逗号分隔")
    private String permissionCodes;

}

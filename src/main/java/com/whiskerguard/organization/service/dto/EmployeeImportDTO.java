package com.whiskerguard.organization.service.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.io.Serializable;

/**
 * Excel导入员工的DTO
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/20
 */
@Data
@Schema(description = "员工Excel导入DTO")
public class EmployeeImportDTO implements Serializable {

    @Schema(description = "登录用户名", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "登录用户名不能为空")
    private String username;

    @Schema(description = "真实姓名", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "真实姓名不能为空")
    private String realName;

    @Schema(description = "邮箱地址", requiredMode = Schema.RequiredMode.REQUIRED)
    private String email;

    @Schema(description = "手机号")
    private String phone;

    @Schema(description = "性别")
    private String gender;

    @Schema(description = "生日")
    private String birthDate;

    @Schema(description = "身份证号")
    private String idCard;

    @Schema(description = "员工编号（工号）")
    private String employeeNo;

    @Schema(description = "入职日期")
    private String hireDate;

    @Schema(description = "任职开始日期")
    private String startDate;

    @Schema(description = "组织单元名称")
    private String orgUnitName;

    @Schema(description = "职位名称")
    private String positionName;

    @Schema(description = "角色名称列表（多个用逗号分隔）")
    private String roleNames;

}

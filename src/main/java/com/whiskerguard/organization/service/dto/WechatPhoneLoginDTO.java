package com.whiskerguard.organization.service.dto;

import jakarta.validation.constraints.NotBlank;

/**
 * 微信手机号登录DTO
 * * <AUTHOR>
 * * @version 1.0
 * * @date 2025/6/5
 */
public class WechatPhoneLoginDTO {

    /**
     * 微信登录凭证code
     */
    @NotBlank(message = "微信登录凭证code不能为空")
    private String code;

    /**
     * 加密的手机号数据
     */
    @NotBlank(message = "加密的手机号数据不能为空")
    private String encryptedData;

    /**
     * 初始向量
     */
    @NotBlank(message = "初始向量不能为空")
    private String iv;

    public WechatPhoneLoginDTO(String code, String encryptedData, String iv) {
        this.code = code;
        this.encryptedData = encryptedData;
        this.iv = iv;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getEncryptedData() {
        return encryptedData;
    }

    public void setEncryptedData(String encryptedData) {
        this.encryptedData = encryptedData;
    }

    public String getIv() {
        return iv;
    }

    public void setIv(String iv) {
        this.iv = iv;
    }

}

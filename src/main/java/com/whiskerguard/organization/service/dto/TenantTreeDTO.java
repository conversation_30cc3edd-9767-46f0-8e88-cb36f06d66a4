package com.whiskerguard.organization.service.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.Instant;
import java.time.LocalDate;
import java.util.List;
import java.util.Objects;

/**
 * 租户树结构DTO
 * 用于表示租户的层级关系
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/22
 */
@Schema(description = "租户树结构")
@Data
public class TenantTreeDTO implements Serializable {

    @Schema(description = "主键 ID")
    private Long id;

    @Schema(description = "租户编码")
    private String tenantCode;

    @Schema(description = "租户名称")
    private String name;

    @Schema(description = "租户状态")
    private Integer status;

    @Schema(description = "套餐类型")
    private String subscriptionPlan;

    @Schema(description = "套餐开始日期")
    private LocalDate subscriptionStart;

    @Schema(description = "套餐结束日期")
    private LocalDate subscriptionEnd;

    @Schema(description = "联系人邮箱")
    private String contactEmail;

    @Schema(description = "联系人电话")
    private String contactPhone;

    @Schema(description = "扩展元数据")
    private String metadata;

    @Schema(description = "父级租户ID")
    private Long parentId;

    @Schema(description = "创建者")
    private String createdBy;

    @Schema(description = "创建时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Instant createdAt;

    @Schema(description = "更新者")
    private String updatedBy;

    @Schema(description = "更新时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Instant updatedAt;

    @Schema(description = "子租户列表")
    private List<TenantTreeDTO> children;

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof TenantTreeDTO tenantTreeDTO)) {
            return false;
        }

        if (this.id == null) {
            return false;
        }
        return Objects.equals(this.id, tenantTreeDTO.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(this.id);
    }

}

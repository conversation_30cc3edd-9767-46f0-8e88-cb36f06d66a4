package com.whiskerguard.organization.service.dto;

import com.whiskerguard.organization.domain.enumeration.OrgUnitType;
import com.whiskerguard.organization.domain.enumeration.PositionCategory;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.io.Serializable;

/**
 * Excel导入组织架构（部门和岗位）的DTO
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/20
 */
@Data
@Schema(description = "组织架构Excel导入DTO")
public class OrgStructureImportDTO implements Serializable {

    // "ORGUNIT" 或 "POSITION"
    @Schema(description = "数据类型", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "数据类型不能为空")
    private String dataType;

    // 组织单元字段
    @Schema(description = "组织单元编码")
    private String orgUnitCode;

    @Schema(description = "组织单元名称")
    private String orgUnitName;

    @Schema(description = "组织单元类型")
    private String orgUnitType;

    @Schema(description = "父级组织单元编码")
    private String parentOrgUnitCode;

    @Schema(description = "组织单元层级")
    private Integer orgUnitLevel;

    @Schema(description = "组织单元排序")
    private Integer orgUnitSortOrder;

    @Schema(description = "组织单元描述")
    private String orgUnitDescription;

    // 岗位字段
    @Schema(description = "岗位编码")
    private String positionCode;

    @Schema(description = "岗位名称")
    private String positionName;

    @Schema(description = "岗位级别")
    private Integer positionLevel;

    @Schema(description = "岗位分类")
    private String positionCategory;

    @Schema(description = "所属组织单元编码")
    private String belongsToOrgUnitCode;

    @Schema(description = "岗位描述")
    private String positionDescription;

    /**
     * 解析组织单元类型字符串为枚举
     */
    public OrgUnitType parseOrgUnitType() {
        if (orgUnitType == null || orgUnitType.trim().isEmpty()) {
            return OrgUnitType.DEPARTMENT; // 默认为部门
        }

        String typeStr = orgUnitType.trim().toUpperCase();
        return switch (typeStr) {
            case "公司", "COMPANY" -> OrgUnitType.COMPANY;
            case "子公司", "SUBSIDIARY" -> OrgUnitType.SUBSIDIARY;
            case "事业群", "BUSINESS_GROUP" -> OrgUnitType.BUSINESS_GROUP;
            case "部门", "DEPARTMENT" -> OrgUnitType.DEPARTMENT;
            default -> OrgUnitType.TEAM;
        };
    }

    /**
     * 解析岗位分类字符串为枚举
     */
    public PositionCategory parsePositionCategory() {
        if (positionCategory == null || positionCategory.trim().isEmpty()) {
            return PositionCategory.TECHNICAL; // 默认为技术类
        }

        String categoryStr = positionCategory.trim().toUpperCase();
        return switch (categoryStr) {
            case "管理类", "MANAGEMENT" -> PositionCategory.MANAGEMENT;
            case "技术类", "TECHNICAL" -> PositionCategory.TECHNICAL;
            case "业务类", "BUSINESS" -> PositionCategory.BUSINESS;
            default -> PositionCategory.SUPPORT;
        };
    }

    /**
     * 检查是否为组织单元数据
     */
    public boolean isOrgUnitData() {
        return "ORGUNIT".equalsIgnoreCase(dataType);
    }

    /**
     * 检查是否为岗位数据
     */
    public boolean isPositionData() {
        return "POSITION".equalsIgnoreCase(dataType);
    }

}

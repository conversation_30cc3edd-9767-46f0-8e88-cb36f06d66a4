package com.whiskerguard.organization.service.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

/**
 * 登录请求 DTO
 */
@Schema(description = "登录请求")
public class LoginDTO {

    @NotBlank
    @Size(min = 1, max = 64)
    @Schema(description = "用户名", required = true)
    private String username;

    @NotBlank
    @Size(min = 1, max = 128)
    @Schema(description = "密码", required = true)
    private String password;

    @Schema(description = "记住我")
    private boolean rememberMe;

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public boolean isRememberMe() {
        return rememberMe;
    }

    public void setRememberMe(boolean rememberMe) {
        this.rememberMe = rememberMe;
    }
}

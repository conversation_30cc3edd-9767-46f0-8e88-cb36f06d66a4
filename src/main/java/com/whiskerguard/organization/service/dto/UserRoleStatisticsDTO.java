package com.whiskerguard.organization.service.dto;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 用户角色分布统计DTO
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/21
 */
@Schema(description = "用户角色分布统计")
public class UserRoleStatisticsDTO {

    @Schema(description = "管理员用户数量")
    private Long adminUsers;

    @Schema(description = "普通员工数量")
    private Long regularUsers;

    public UserRoleStatisticsDTO() {
    }

    public UserRoleStatisticsDTO(Long adminUsers, Long regularUsers) {
        this.adminUsers = adminUsers;
        this.regularUsers = regularUsers;
    }

    public Long getAdminUsers() {
        return adminUsers;
    }

    public void setAdminUsers(Long adminUsers) {
        this.adminUsers = adminUsers;
    }

    public Long getRegularUsers() {
        return regularUsers;
    }

    public void setRegularUsers(Long regularUsers) {
        this.regularUsers = regularUsers;
    }

    @Override
    public String toString() {
        return "UserRoleStatisticsDTO{" +
            "adminUsers=" + adminUsers +
            ", regularUsers=" + regularUsers +
            '}';
    }
}

package com.whiskerguard.organization.service.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.io.Serializable;
import java.time.Instant;
import java.util.List;

/**
 * A DTO for the {@link com.whiskerguard.organization.domain.ComplaintSuggestion} entity.
 */
@Schema(description = "投诉与建议表")
@Data
public class ComplaintSuggestionDTO implements Serializable {

    private Long id;

    @Schema(description = "员工ID")
    private Long employeeId;

    @Size(max = 500, message = "详情长度不能超过500个字符")
    @Schema(description = "详情")
    @NotBlank(message = "详情不能为空")
    private String detail;

    @Schema(description = "是否匿名：0、否 1、是")
    private Integer isAnonymous;

    @Size(max = 32)
    @Schema(description = "联系方式")
    private String contactWay;

    @Schema(description = "创建者")
    private String createdBy;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Instant createdAt;

    @Schema(description = "附件")
    private List<ComplaintSuggestionAttachmentDTO> attachments;

}

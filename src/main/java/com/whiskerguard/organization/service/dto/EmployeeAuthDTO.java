package com.whiskerguard.organization.service.dto;

import java.util.Set;

/**
 * 员工认证信息DTO
 * 用于在服务之间传输员工认证数据，特别是供认证服务获取员工角色和权限信息
 */
public class EmployeeAuthDTO {

    /**
     * 员工ID
     */
    private Long id;

    /**
     * 用户名
     */
    private String username;

    /**
     * 真实姓名
     */
    private String realName;

    /**
     * 角色代码集合
     */
    private Set<String> roles;

    /**
     * 权限代码集合
     */
    private Set<String> permissions;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 账号是否激活
     */
    private Boolean isActive;

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getRealName() {
        return realName;
    }

    public void setRealName(String realName) {
        this.realName = realName;
    }

    public Set<String> getRoles() {
        return roles;
    }

    public void setRoles(Set<String> roles) {
        this.roles = roles;
    }

    public Set<String> getPermissions() {
        return permissions;
    }

    public void setPermissions(Set<String> permissions) {
        this.permissions = permissions;
    }

    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public Boolean getIsActive() {
        return isActive;
    }

    public void setIsActive(Boolean isActive) {
        this.isActive = isActive;
    }
}

package com.whiskerguard.organization.service.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * A DTO for importing Tenant and TenantProfile from Excel.
 * Excel导入租户和租户详情的DTO
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/20
 */
@Schema(description = "租户Excel导入DTO")
@Data
public class TenantImportDTO implements Serializable {

    @Schema(description = "租户名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "租户名称不能为空")
    private String name;

    @Schema(description = "联系人邮箱")
    private String contactEmail;

    @Schema(description = "联系人电话")
    private String contactPhone;

    /**
     * TenantProfile 字段
     */
    @Schema(description = "工商注册号")
    private String registrationNumber;

    @Schema(description = "注册日期")
    private String registrationDate;

    @Schema(description = "注册资本")
    private BigDecimal registeredCapital;

    @Schema(description = "公司类型")
    private String companyType;

    @Schema(description = "经营范围")
    private String businessScope;

    @Schema(description = "所属行业")
    private String industry;

    @Schema(description = "税务登记号")
    private String taxRegistrationNumber;

    @Schema(description = "组织机构代码")
    private String organizationCode;

    @Schema(description = "注册地址")
    private String registeredAddress;

    @Schema(description = "邮政编码")
    private String postalCode;

    @Schema(description = "官网")
    private String website;

    @Schema(description = "传真")
    private String fax;

    @Schema(description = "联系人")
    private String contactPerson;

    @Schema(description = "联系人手机")
    private String contactMobile;

    @Schema(description = "联系人邮箱（详情）")
    private String profileContactEmail;

    @Schema(description = "开户行")
    private String bankName;

    @Schema(description = "银行账号")
    private String bankAccount;

    @Schema(description = "营业执照路径")
    private String businessLicensePath;

    @Schema(description = "法人代表")
    private String legalPerson;

    @Schema(description = "法人证件号")
    private String legalPersonId;

}

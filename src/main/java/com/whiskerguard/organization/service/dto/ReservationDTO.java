package com.whiskerguard.organization.service.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

import java.io.Serializable;
import java.time.Instant;
import java.util.Objects;

/**
 * A DTO for the {@link com.whiskerguard.organization.domain.Reservation} entity.
 */
@Schema(description = "预约体验（Reservation）实体")
@SuppressWarnings("common-java:DuplicatedBlocks")
public class ReservationDTO implements Serializable {

    @Schema(description = "主键ID")
    private Long id;

    @NotBlank(message = "请填写您的姓名")
    @Size(max = 64)
    @Schema(description = "您的姓名", requiredMode = Schema.RequiredMode.REQUIRED)
    private String name;

    @NotBlank(message = "请填写您的职位")
    @Size(max = 64)
    @Schema(description = "职位", requiredMode = Schema.RequiredMode.REQUIRED)
    private String position;

    @NotBlank(message = "请填写您的手机号码")
    @Size(max = 32)
    @Schema(description = "手机号码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String mobile;

    @NotBlank(message = "请填写您的电子邮箱")
    @Size(max = 128)
    @Schema(description = "电子邮箱", requiredMode = Schema.RequiredMode.REQUIRED)
    private String email;

    @NotBlank(message = "请填写您的公司名称")
    @Size(max = 128)
    @Schema(description = "公司名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String company;

    @NotBlank(message = "请填写您的所属行业")
    @Size(max = 64)
    @Schema(description = "所属行业", requiredMode = Schema.RequiredMode.REQUIRED)
    private String industry;

    @NotBlank(message = "请填写您的企业规模")
    @Schema(description = "企业规模", requiredMode = Schema.RequiredMode.REQUIRED)
    private String companySize;

    @NotBlank(message = "请填写您的最关注合规管理需求")
    @Schema(description = "最关注合规管理需求", requiredMode = Schema.RequiredMode.REQUIRED)
    private String focusNeed;

    @Schema(description = "其他需求说明")
    private String otherDesc;

    @Schema(description = "补充字段")
    private String metadata;

    @Schema(description = "当前版本号", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer version;

    @Size(max = 64)
    @Schema(description = "创建者账号或姓名")
    private String createdBy;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private Instant createdAt;

    @Size(max = 64)
    @Schema(description = "最后修改者")
    private String updatedBy;

    @Schema(description = "最后更新时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private Instant updatedAt;

    @Schema(description = "是否删除：0 表示正常 1 表示已删除", requiredMode = Schema.RequiredMode.REQUIRED)
    private Boolean isDeleted;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPosition() {
        return position;
    }

    public void setPosition(String position) {
        this.position = position;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getCompany() {
        return company;
    }

    public void setCompany(String company) {
        this.company = company;
    }

    public String getIndustry() {
        return industry;
    }

    public void setIndustry(String industry) {
        this.industry = industry;
    }

    public String getCompanySize() {
        return companySize;
    }

    public void setCompanySize(String companySize) {
        this.companySize = companySize;
    }

    public String getFocusNeed() {
        return focusNeed;
    }

    public void setFocusNeed(String focusNeed) {
        this.focusNeed = focusNeed;
    }

    public String getOtherDesc() {
        return otherDesc;
    }

    public void setOtherDesc(String otherDesc) {
        this.otherDesc = otherDesc;
    }

    public String getMetadata() {
        return metadata;
    }

    public void setMetadata(String metadata) {
        this.metadata = metadata;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public Instant getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Instant createdAt) {
        this.createdAt = createdAt;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    public Instant getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Instant updatedAt) {
        this.updatedAt = updatedAt;
    }

    public Boolean getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof ReservationDTO reservationDTO)) {
            return false;
        }

        if (this.id == null) {
            return false;
        }
        return Objects.equals(this.id, reservationDTO.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(this.id);
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "ReservationDTO{" +
            "id=" + getId() +
            ", name='" + getName() + "'" +
            ", position='" + getPosition() + "'" +
            ", mobile='" + getMobile() + "'" +
            ", email='" + getEmail() + "'" +
            ", company='" + getCompany() + "'" +
            ", industry='" + getIndustry() + "'" +
            ", companySize='" + getCompanySize() + "'" +
            ", focusNeed='" + getFocusNeed() + "'" +
            ", otherDesc='" + getOtherDesc() + "'" +
            ", metadata='" + getMetadata() + "'" +
            ", version=" + getVersion() +
            ", createdBy='" + getCreatedBy() + "'" +
            ", createdAt='" + getCreatedAt() + "'" +
            ", updatedBy='" + getUpdatedBy() + "'" +
            ", updatedAt='" + getUpdatedAt() + "'" +
            ", isDeleted='" + getIsDeleted() + "'" +
            "}";
    }
}

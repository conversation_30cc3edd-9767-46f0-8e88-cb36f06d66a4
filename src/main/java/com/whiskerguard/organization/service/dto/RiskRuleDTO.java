package com.whiskerguard.organization.service.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.whiskerguard.organization.domain.enumeration.RiskRuleType;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

import java.io.Serializable;
import java.time.Instant;
import java.util.Objects;

/**
 * A DTO for the {@link com.whiskerguard.organization.domain.RiskRule} entity.
 */
@Schema(description = "具体规则（关键词、阈值、脚本等）")
@SuppressWarnings("common-java:DuplicatedBlocks")
public class RiskRuleDTO implements Serializable {

    private Long id;

    @NotBlank(message = "规则编码不能为空")
    @Size(min = 1, max = 64, message = "规则编码长度不能超过64个字符")
    @Schema(description = "规则编码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String code;

    @NotBlank(message = "规则名称不能为空")
    @Size(max = 128, message = "规则名称长度不能超过128个字符")
    @Schema(description = "规则名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String name;

    @NotNull(message = "规则类型不能为空")
    @Schema(description = "规则类型", requiredMode = Schema.RequiredMode.REQUIRED)
    private RiskRuleType ruleType;

    @Size(max = 2048, message = "规则参数长度不能超过2048个字符")
    @Schema(description = "规则参数")
    private String params;

    @Size(max = 512, message = "描述信息长度不能超过512个字符")
    @Schema(description = "描述信息")
    private String description;

    @Schema(description = "评分/权重")
    private Integer score;

    @Schema(description = "乐观锁版本")
    private Integer version;

    @Schema(description = "创建者")
    private String createdBy;

    @Schema(description = "创建时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Instant createdAt;

    @Schema(description = "更新者")
    private String updatedBy;

    @Schema(description = "更新时间")
    private Instant updatedAt;

    @Schema(description = "软删除标志")
    private Boolean isDeleted;

    @Schema(description = "RiskRule ➜ RiskCategory")
    private RiskCategoryDTO riskCategory;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public RiskRuleType getRuleType() {
        return ruleType;
    }

    public void setRuleType(RiskRuleType ruleType) {
        this.ruleType = ruleType;
    }

    public String getParams() {
        return params;
    }

    public void setParams(String params) {
        this.params = params;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Integer getScore() {
        return score;
    }

    public void setScore(Integer score) {
        this.score = score;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public Instant getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Instant createdAt) {
        this.createdAt = createdAt;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    public Instant getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Instant updatedAt) {
        this.updatedAt = updatedAt;
    }

    public Boolean getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
    }

    public RiskCategoryDTO getRiskCategory() {
        return riskCategory;
    }

    public void setRiskCategory(RiskCategoryDTO riskCategory) {
        this.riskCategory = riskCategory;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof RiskRuleDTO)) {
            return false;
        }

        RiskRuleDTO riskRuleDTO = (RiskRuleDTO) o;
        if (this.id == null) {
            return false;
        }
        return Objects.equals(this.id, riskRuleDTO.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(this.id);
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "RiskRuleDTO{" +
            "id=" + getId() +
            ", code='" + getCode() + "'" +
            ", name='" + getName() + "'" +
            ", ruleType='" + getRuleType() + "'" +
            ", params='" + getParams() + "'" +
            ", description='" + getDescription() + "'" +
            ", score=" + getScore() +
            ", version=" + getVersion() +
            ", createdBy='" + getCreatedBy() + "'" +
            ", createdAt='" + getCreatedAt() + "'" +
            ", updatedBy='" + getUpdatedBy() + "'" +
            ", updatedAt='" + getUpdatedAt() + "'" +
            ", isDeleted='" + getIsDeleted() + "'" +
            ", riskCategory=" + getRiskCategory() +
            "}";
    }
}

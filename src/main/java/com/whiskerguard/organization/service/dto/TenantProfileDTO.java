package com.whiskerguard.organization.service.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import java.util.Objects;

/**
 * A DTO for the {@link com.whiskerguard.organization.domain.TenantProfile} entity.
 */
@Schema(description = "租户详情（TenantProfile）实体")
@SuppressWarnings("common-java:DuplicatedBlocks")
@Data
public class TenantProfileDTO implements Serializable {

    @Schema(description = "主键 ID")
    private Long id;

    @Schema(description = "工商注册号")
    @Size(min = 18, max = 18, message = "工商注册号长度必须为18位")
    private String registrationNumber;

    @Schema(description = "注册日期")
    @NotNull(message = "注册日期不能为空")
    private LocalDate registrationDate;

    @Schema(description = "注册资本")
    @Min(value = 0, message = "注册资本必须大于或等于0")
    private BigDecimal registeredCapital;

    @Schema(description = "公司类型")
    @NotBlank(message = "公司类型不能为空")
    private String companyType;

    @Schema(description = "经营范围")
    private String businessScope;

    @Schema(description = "所属行业")
    private String industry;

    @Schema(description = "税务登记号")
    @Size(min = 18, max = 18, message = "税务登记号长度必须为18位")
    private String taxRegistrationNumber;

    @Schema(description = "组织机构代码")
    @Size(min = 9, message = "组织机构代码长度必须大于8位")
    private String organizationCode;

    @Schema(description = "注册地址")
    private String registeredAddress;

    @Schema(description = "邮政编码")
    @Size(min = 6, max = 6, message = "邮政编码必须为6位")
    private String postalCode;

    @Schema(description = "官网")
    private String website;

    @Schema(description = "传真")
    private String fax;

    @Schema(description = "联系人")
    private String contactPerson;

    @Schema(description = "联系人手机")
    private String contactMobile;

    @Schema(description = "联系人邮箱")
    private String contactEmail;

    @Schema(description = "开户行")
    private String bankName;

    @Schema(description = "银行账号")
    @Size(min = 16, max = 19, message = "银行账号长度必须在16到19位之间")
    private String bankAccount;

    @Schema(description = "营业执照路径")
    private String businessLicensePath;

    @Schema(description = "法人代表")
    private String legalPerson;

    @Schema(description = "法人证件号")
    private String legalPersonId;

    @Schema(description = "扩展元数据")
    private String metadata;

    @Schema(description = "乐观锁版本", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer version;

    @Schema(description = "创建者")
    private String createdBy;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Instant createdAt;

    @Schema(description = "更新者")
    private String updatedBy;

    @Schema(description = "更新时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Instant updatedAt;

    @Schema(description = "软删除标志", requiredMode = Schema.RequiredMode.REQUIRED)
    private Boolean isDeleted;

    private TenantDTO tenant;

    @Schema(description = "员工数量")
    private Integer employeeCount;

    @Schema(description = "企业类型", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer EnterpriseType;

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof TenantProfileDTO tenantProfileDTO)) {
            return false;
        }

        if (this.id == null) {
            return false;
        }
        return Objects.equals(this.id, tenantProfileDTO.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(this.id);
    }

}

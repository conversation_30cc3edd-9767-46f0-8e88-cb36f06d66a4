package com.whiskerguard.organization.service.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.whiskerguard.organization.domain.enumeration.EmployeeGender;
import com.whiskerguard.organization.domain.enumeration.EmployeeStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

import java.io.Serializable;
import java.time.Instant;
import java.time.LocalDate;
import java.util.List;
import java.util.Objects;

/**
 * A DTO for the {@link com.whiskerguard.organization.domain.Employee} entity.
 */
@Schema(description = "员工（Employee）实体")
@SuppressWarnings("common-java:DuplicatedBlocks")
public class EmployeeDTO implements Serializable {

    @Schema(description = "主键 ID")
    private Long id;

    @Schema(description = "租户 ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long tenantId;

    @Size(max = 64, message = "登录用户名长度不能超过64个字符")
    @Schema(description = "登录用户名", requiredMode = Schema.RequiredMode.REQUIRED)
    private String username;

    @Size(max = 128, message = "登录密码长度不能超过128个字符")
    @Schema(description = "登录密码（加密存储）", requiredMode = Schema.RequiredMode.REQUIRED)
    private String password;

    @Size(max = 32, message = "密码盐值长度不能超过32个字符")
    @Schema(description = "密码盐值")
    private String salt;

    @NotBlank(message = "真实姓名不能为空")
    @Size(max = 128, message = "真实姓名长度不能超过128个字符")
    @Schema(description = "真实姓名", requiredMode = Schema.RequiredMode.REQUIRED)
    private String realName;

    @Size(max = 255, message = "头像长度不能超过255个字符")
    @Schema(description = "头像")
    private String avatar;

    @Size(max = 128, message = "邮箱地址长度不能超过128个字符")
    @Schema(description = "邮箱地址", requiredMode = Schema.RequiredMode.REQUIRED)
    private String email;

    @Size(max = 32, message = "手机号长度不能超过32个字符")
    @Schema(description = "手机号")
    private String phone;

    @Schema(description = "性别")
    private EmployeeGender gender;

    @Schema(description = "生日")
    private LocalDate birthDate;

    @Size(max = 18, message = "身份证号长度不能超过18个字符")
    @Schema(description = "身份证号")
    private String idCard;

    @Size(max = 20, message = "员工编号长度不能超过20个字符")
    @Schema(description = "员工编号（工号）")
    private String employeeNo;

    @Schema(description = "员工状态")
    private EmployeeStatus status;

    @Schema(description = "入职日期")
    private LocalDate hireDate;

    @Schema(description = "离职日期")
    private LocalDate leaveDate;

    @Schema(description = "扩展元数据")
    private String metadata;

    @Schema(description = "乐观锁版本", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer version;

    @Schema(description = "创建者")
    private String createdBy;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Instant createdAt;

    @Schema(description = "更新者")
    private String updatedBy;

    @Schema(description = "更新时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Instant updatedAt;

    @Schema(description = "软删除标志", requiredMode = Schema.RequiredMode.REQUIRED)
    private Boolean isDeleted;

    @Schema(description = "最后登录时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Instant lastLoginTime;

    @Size(max = 64, message = "最后登录IP长度不能超过64个字符")
    @Schema(description = "最后登录IP")
    private String lastLoginIp;

    @Schema(description = "登录失败次数")
    private Integer loginFailureCount;

    @Schema(description = "账号锁定时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Instant accountLockedTime;

    @Schema(description = "密码修改时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Instant passwordChangedTime;

    @Schema(description = "密码过期时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Instant passwordExpiredTime;

    @Schema(description = "是否首次登录", requiredMode = Schema.RequiredMode.REQUIRED)
    private Boolean isFirstLogin;

    @Schema(description = "是否强制修改密码", requiredMode = Schema.RequiredMode.REQUIRED)
    private Boolean forceChangePassword;

    @Schema(description = "微信OpenID")
    private String wechatOpenId;

    @Schema(description = "微信UnionID")
    private String wechatUnionId;

    @Schema(description = "岗位列表")
    private List<PositionDTO> positionList;

    @Schema(description = "组织列表")
    private List<OrgUnitDTO> orgUnitList;

    @Schema(description = "角色列表")
    private List<RoleDTO> roleList;

    @Schema(description = "员工组织关系列表")
    private List<EmployeeOrgDTO> employeeOrgList;

    @Schema(description = "部门名称")
    private String departmentName;

    @Schema(description = "岗位名称")
    private String positionName;

    public String getDepartmentName() {
        return departmentName;
    }

    public void setDepartmentName(String departmentName) {
        this.departmentName = departmentName;
    }

    public String getPositionName() {
        return positionName;
    }

    public void setPositionName(String positionName) {
        this.positionName = positionName;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getSalt() {
        return salt;
    }

    public void setSalt(String salt) {
        this.salt = salt;
    }

    public String getRealName() {
        return realName;
    }

    public void setRealName(String realName) {
        this.realName = realName;
    }

    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public EmployeeGender getGender() {
        return this.gender;
    }

    public void setGender(EmployeeGender gender) {
        this.gender = gender;
    }

    public LocalDate getBirthDate() {
        return birthDate;
    }

    public void setBirthDate(LocalDate birthDate) {
        this.birthDate = birthDate;
    }

    public String getIdCard() {
        return idCard;
    }

    public void setIdCard(String idCard) {
        this.idCard = idCard;
    }

    public String getEmployeeNo() {
        return employeeNo;
    }

    public void setEmployeeNo(String employeeNo) {
        this.employeeNo = employeeNo;
    }

    public EmployeeStatus getStatus() {
        return this.status;
    }

    public void setStatus(EmployeeStatus status) {
        this.status = status;
    }

    public LocalDate getHireDate() {
        return hireDate;
    }

    public void setHireDate(LocalDate hireDate) {
        this.hireDate = hireDate;
    }

    public LocalDate getLeaveDate() {
        return leaveDate;
    }

    public void setLeaveDate(LocalDate leaveDate) {
        this.leaveDate = leaveDate;
    }

    public String getMetadata() {
        return metadata;
    }

    public void setMetadata(String metadata) {
        this.metadata = metadata;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public Instant getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Instant createdAt) {
        this.createdAt = createdAt;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    public Instant getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Instant updatedAt) {
        this.updatedAt = updatedAt;
    }

    public Boolean getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
    }

    public Instant getLastLoginTime() {
        return lastLoginTime;
    }

    public void setLastLoginTime(Instant lastLoginTime) {
        this.lastLoginTime = lastLoginTime;
    }

    public String getLastLoginIp() {
        return lastLoginIp;
    }

    public void setLastLoginIp(String lastLoginIp) {
        this.lastLoginIp = lastLoginIp;
    }

    public Integer getLoginFailureCount() {
        return loginFailureCount;
    }

    public void setLoginFailureCount(Integer loginFailureCount) {
        this.loginFailureCount = loginFailureCount;
    }

    public Instant getAccountLockedTime() {
        return accountLockedTime;
    }

    public void setAccountLockedTime(Instant accountLockedTime) {
        this.accountLockedTime = accountLockedTime;
    }

    public Instant getPasswordChangedTime() {
        return passwordChangedTime;
    }

    public void setPasswordChangedTime(Instant passwordChangedTime) {
        this.passwordChangedTime = passwordChangedTime;
    }

    public Instant getPasswordExpiredTime() {
        return passwordExpiredTime;
    }

    public void setPasswordExpiredTime(Instant passwordExpiredTime) {
        this.passwordExpiredTime = passwordExpiredTime;
    }

    public Boolean getIsFirstLogin() {
        return isFirstLogin;
    }

    public void setIsFirstLogin(Boolean isFirstLogin) {
        this.isFirstLogin = isFirstLogin;
    }

    public Boolean getForceChangePassword() {
        return forceChangePassword;
    }

    public void setForceChangePassword(Boolean forceChangePassword) {
        this.forceChangePassword = forceChangePassword;
    }

    public String getWechatOpenId() {
        return wechatOpenId;
    }

    public void setWechatOpenId(String wechatOpenId) {
        this.wechatOpenId = wechatOpenId;
    }

    public String getWechatUnionId() {
        return wechatUnionId;
    }

    public void setWechatUnionId(String wechatUnionId) {
        this.wechatUnionId = wechatUnionId;
    }

    public List<PositionDTO> getPositionList() {
        return positionList;
    }

    public void setPositionList(List<PositionDTO> positionList) {
        this.positionList = positionList;
    }

    public List<OrgUnitDTO> getOrgUnitList() {
        return orgUnitList;
    }

    public void setOrgUnitList(List<OrgUnitDTO> orgUnitList) {
        this.orgUnitList = orgUnitList;
    }

    public List<RoleDTO> getRoleList() {
        return roleList;
    }

    public List<EmployeeOrgDTO> getEmployeeOrgList() {
        return employeeOrgList;
    }

    public void setEmployeeOrgList(List<EmployeeOrgDTO> employeeOrgList) {
        this.employeeOrgList = employeeOrgList;
    }

    public void setRoleList(List<RoleDTO> roleList) {
        this.roleList = roleList;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof EmployeeDTO employeeDTO)) {
            return false;
        }

        if (this.id == null) {
            return false;
        }
        return Objects.equals(this.id, employeeDTO.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(this.id);
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "EmployeeDTO{" +
            "id=" + getId() +
            ", tenantId=" + getTenantId() +
            ", username='" + getUsername() + "'" +
            ", password='" + getPassword() + "'" +
            ", salt='" + getSalt() + "'" +
            ", realName='" + getRealName() + "'" +
            ", email='" + getEmail() + "'" +
            ", phone='" + getPhone() + "'" +
            ", gender='" + getGender() + "'" +
            ", birthDate='" + getBirthDate() + "'" +
            ", idCard='" + getIdCard() + "'" +
            ", employeeNo='" + getEmployeeNo() + "'" +
            ", status='" + getStatus() + "'" +
            ", hireDate='" + getHireDate() + "'" +
            ", leaveDate='" + getLeaveDate() + "'" +
            ", metadata='" + getMetadata() + "'" +
            ", version=" + getVersion() +
            ", createdBy='" + getCreatedBy() + "'" +
            ", createdAt='" + getCreatedAt() + "'" +
            ", updatedBy='" + getUpdatedBy() + "'" +
            ", updatedAt='" + getUpdatedAt() + "'" +
            ", isDeleted='" + getIsDeleted() + "'" +
            ", lastLoginTime='" + getLastLoginTime() + "'" +
            ", lastLoginIp='" + getLastLoginIp() + "'" +
            ", loginFailureCount=" + getLoginFailureCount() +
            ", accountLockedTime='" + getAccountLockedTime() + "'" +
            ", passwordChangedTime='" + getPasswordChangedTime() + "'" +
            ", passwordExpiredTime='" + getPasswordExpiredTime() + "'" +
            ", isFirstLogin='" + getIsFirstLogin() + "'" +
            ", forceChangePassword='" + getForceChangePassword() + "'" +
            ", wechatOpenId='" + getWechatOpenId() + "'" +
            ", wechatUnionId='" + getWechatUnionId() + "'" +
            "}";
    }
}

package com.whiskerguard.organization.service.dto;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 用户状态统计DTO
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/21
 */
@Schema(description = "用户状态统计")
public class UserStatusStatisticsDTO {

    @Schema(description = "启用用户数量（ACTIVE状态）")
    private Long activeUsers;

    @Schema(description = "禁用用户数量（INACTIVE和FROZEN状态）")
    private Long inactiveUsers;

    public UserStatusStatisticsDTO() {
    }

    public UserStatusStatisticsDTO(Long activeUsers, Long inactiveUsers) {
        this.activeUsers = activeUsers;
        this.inactiveUsers = inactiveUsers;
    }

    public Long getActiveUsers() {
        return activeUsers;
    }

    public void setActiveUsers(Long activeUsers) {
        this.activeUsers = activeUsers;
    }

    public Long getInactiveUsers() {
        return inactiveUsers;
    }

    public void setInactiveUsers(Long inactiveUsers) {
        this.inactiveUsers = inactiveUsers;
    }

    @Override
    public String toString() {
        return "UserStatusStatisticsDTO{" +
            "activeUsers=" + activeUsers +
            ", inactiveUsers=" + inactiveUsers +
            '}';
    }
}

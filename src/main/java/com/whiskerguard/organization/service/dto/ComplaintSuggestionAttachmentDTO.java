package com.whiskerguard.organization.service.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.io.Serializable;
import java.time.Instant;

/**
 * A DTO for the {@link com.whiskerguard.organization.domain.ComplaintSuggestionAttachment} entity.
 */
@Schema(description = "投诉与建议附件表，用于存储合规案例相关的附件信息")
@Data
public class ComplaintSuggestionAttachmentDTO implements Serializable {

    private Long id;

    @Schema(description = "关联投诉与建议ID", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long suggestionId;

    @NotBlank(message = "附件名称不能为空")
    @Size(max = 256, message = "附件名称长度不能超过256个字符")
    @Schema(description = "附件名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String fileName;

    @NotBlank(message = "附件存储路径或URL不能为空")
    @Size(max = 512, message = "附件存储路径或URL长度不能超过512个字符")
    @Schema(description = "附件存储路径或URL", requiredMode = Schema.RequiredMode.REQUIRED)
    private String filePath;

    @NotBlank(message = "附件类型不能为空")
    @Size(max = 32, message = "附件类型长度不能超过32个字符")
    @Schema(description = "附件类型", requiredMode = Schema.RequiredMode.REQUIRED)
    private String fileType;

    @Size(max = 32, message = "附件大小长度不能超过32个字符")
    @Schema(description = "附件大小")
    private String fileSize;

    @Size(max = 512, message = "附件描述长度不能超过512个字符")
    @Schema(description = "附件描述")
    private String fileDesc;

    @Schema(description = "上传者", requiredMode = Schema.RequiredMode.REQUIRED)
    private String uploadedBy;

    @Schema(description = "上传时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Instant uploadedAt;

}

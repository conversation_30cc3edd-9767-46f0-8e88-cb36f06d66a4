package com.whiskerguard.organization.service.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.whiskerguard.organization.domain.enumeration.NewsStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Min;

import java.io.Serializable;
import java.time.Instant;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * A DTO for the {@link com.whiskerguard.organization.domain.News} entity.
 */
@SuppressWarnings("common-java:DuplicatedBlocks")
public class NewsDTO implements Serializable {

    private Long id;

    @Schema(description = "状态", requiredMode = Schema.RequiredMode.REQUIRED)
    private NewsStatus status;

    @Min(value = 0, message = "排序序号不能小于0")
    @Schema(description = "排序序号")
    private Integer sortOrder;

    @Schema(description = "副标题")
    private String subtitle;

    @Schema(description = "标题", requiredMode = Schema.RequiredMode.REQUIRED)
    private String title;

    @Schema(description = "摘要")
    private String summary;

    @Schema(description = "关键词（用于SEO，全局搜索）")
    private String keywords;

    @Schema(description = "正文内容")
    private String content;

    @Schema(description = "发布时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Instant publishDate;

    @Schema(description = "正式发布时戳")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Instant publishedAt;

    @Min(value = 0, message = "浏览量不能小于0")
    @Schema(description = "浏览量")
    private Integer viewCount;

    @Min(value = 0, message = "点赞数不能小于0")
    @Schema(description = "点赞数")
    private Integer likeCount;

    @Min(value = 0, message = "评论数不能小于0")
    @Schema(description = "评论数")
    private Integer commentCount;

    @Min(value = 0, message = "分享数不能小于0")
    @Schema(description = "分享数")
    private Integer shareCount;

    @Schema(description = "封面图 URL")
    private String coverImageUrl;

    @Schema(description = "是否置顶")
    private Boolean isSticky;

    @Schema(description = "置顶开始时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Instant stickyStartTime;

    @Schema(description = "置顶结束时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Instant stickyEndTime;

    @Schema(description = "扩展元数据（JSONB）")
    private String metadata;

    @Schema(description = "乐观锁版本", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer version;

    @Schema(description = "创建者")
    private String createdBy;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Instant createdAt;

    @Schema(description = "更新者")
    private String updatedBy;

    @Schema(description = "更新时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Instant updatedAt;

    @Schema(description = "软删除标志", requiredMode = Schema.RequiredMode.REQUIRED)
    private Boolean isDeleted;

    private NewsCategoryDTO category;

    private OrgUnitDTO orgUnit;

    private EmployeeDTO author;

    private Set<TagDTO> tags = new HashSet<>();

    @Schema(description = "附件列表")
    private List<NewsAttachmentDTO> attachments;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public NewsStatus getStatus() {
        return status;
    }

    public void setStatus(NewsStatus status) {
        this.status = status;
    }

    public Integer getSortOrder() {
        return sortOrder;
    }

    public void setSortOrder(Integer sortOrder) {
        this.sortOrder = sortOrder;
    }

    public String getSubtitle() {
        return subtitle;
    }

    public void setSubtitle(String subtitle) {
        this.subtitle = subtitle;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getSummary() {
        return summary;
    }

    public void setSummary(String summary) {
        this.summary = summary;
    }

    public String getKeywords() {
        return keywords;
    }

    public void setKeywords(String keywords) {
        this.keywords = keywords;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Instant getPublishDate() {
        return publishDate;
    }

    public void setPublishDate(Instant publishDate) {
        this.publishDate = publishDate;
    }

    public Instant getPublishedAt() {
        return publishedAt;
    }

    public void setPublishedAt(Instant publishedAt) {
        this.publishedAt = publishedAt;
    }

    public Integer getViewCount() {
        return viewCount;
    }

    public void setViewCount(Integer viewCount) {
        this.viewCount = viewCount;
    }

    public Integer getLikeCount() {
        return likeCount;
    }

    public void setLikeCount(Integer likeCount) {
        this.likeCount = likeCount;
    }

    public Integer getCommentCount() {
        return commentCount;
    }

    public void setCommentCount(Integer commentCount) {
        this.commentCount = commentCount;
    }

    public Integer getShareCount() {
        return shareCount;
    }

    public void setShareCount(Integer shareCount) {
        this.shareCount = shareCount;
    }

    public String getCoverImageUrl() {
        return coverImageUrl;
    }

    public void setCoverImageUrl(String coverImageUrl) {
        this.coverImageUrl = coverImageUrl;
    }

    public Boolean getIsSticky() {
        return isSticky;
    }

    public void setIsSticky(Boolean isSticky) {
        this.isSticky = isSticky;
    }

    public Instant getStickyStartTime() {
        return stickyStartTime;
    }

    public void setStickyStartTime(Instant stickyStartTime) {
        this.stickyStartTime = stickyStartTime;
    }

    public Instant getStickyEndTime() {
        return stickyEndTime;
    }

    public void setStickyEndTime(Instant stickyEndTime) {
        this.stickyEndTime = stickyEndTime;
    }

    public String getMetadata() {
        return metadata;
    }

    public void setMetadata(String metadata) {
        this.metadata = metadata;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public Instant getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Instant createdAt) {
        this.createdAt = createdAt;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    public Instant getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Instant updatedAt) {
        this.updatedAt = updatedAt;
    }

    public Boolean getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
    }

    public NewsCategoryDTO getCategory() {
        return category;
    }

    public void setCategory(NewsCategoryDTO category) {
        this.category = category;
    }

    public OrgUnitDTO getOrgUnit() {
        return orgUnit;
    }

    public void setOrgUnit(OrgUnitDTO orgUnit) {
        this.orgUnit = orgUnit;
    }

    public EmployeeDTO getAuthor() {
        return author;
    }

    public void setAuthor(EmployeeDTO author) {
        this.author = author;
    }

    public Set<TagDTO> getTags() {
        return tags;
    }

    public void setTags(Set<TagDTO> tags) {
        this.tags = tags;
    }

    public List<NewsAttachmentDTO> getAttachments() {
        return attachments;
    }

    public void setAttachments(List<NewsAttachmentDTO> attachments) {
        this.attachments = attachments;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof NewsDTO)) {
            return false;
        }

        NewsDTO newsDTO = (NewsDTO) o;
        if (this.id == null) {
            return false;
        }
        return Objects.equals(this.id, newsDTO.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(this.id);
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "NewsDTO{" +
            "id=" + getId() +
            ", status='" + getStatus() + "'" +
            ", sortOrder=" + getSortOrder() +
            ", subtitle='" + getSubtitle() + "'" +
            ", title='" + getTitle() + "'" +
            ", summary='" + getSummary() + "'" +
            ", keywords='" + getKeywords() + "'" +
            ", content='" + getContent() + "'" +
            ", publishDate='" + getPublishDate() + "'" +
            ", publishedAt='" + getPublishedAt() + "'" +
            ", viewCount=" + getViewCount() +
            ", likeCount=" + getLikeCount() +
            ", commentCount=" + getCommentCount() +
            ", shareCount=" + getShareCount() +
            ", coverImageUrl='" + getCoverImageUrl() + "'" +
            ", isSticky='" + getIsSticky() + "'" +
            ", stickyStartTime='" + getStickyStartTime() + "'" +
            ", stickyEndTime='" + getStickyEndTime() + "'" +
            ", metadata='" + getMetadata() + "'" +
            ", version=" + getVersion() +
            ", createdBy='" + getCreatedBy() + "'" +
            ", createdAt='" + getCreatedAt() + "'" +
            ", updatedBy='" + getUpdatedBy() + "'" +
            ", updatedAt='" + getUpdatedAt() + "'" +
            ", isDeleted='" + getIsDeleted() + "'" +
            ", category=" + getCategory() +
            ", orgUnit=" + getOrgUnit() +
            ", author=" + getAuthor() +
            ", tags=" + getTags() +
            "}";
    }
}

package com.whiskerguard.organization.service.dto;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 描述：按钮实体
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/10
 */
public class ButtonDTO {

    @Schema(description = "主键 ID")
    private Long id;

    @Schema(description = "按钮名称")
    private String name;

    @Schema(description = "按钮值")
    private String value;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }
}

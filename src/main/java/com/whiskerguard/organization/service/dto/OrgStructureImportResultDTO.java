package com.whiskerguard.organization.service.dto;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * A DTO for organization structure import result.
 * 组织架构导入结果DTO
 */
@Schema(description = "组织架构导入结果DTO")
public class OrgStructureImportResultDTO implements Serializable {

    @Schema(description = "导入的组织单元列表")
    private List<OrgUnitDTO> importedOrgUnits = new ArrayList<>();

    @Schema(description = "导入的岗位列表")
    private List<PositionDTO> importedPositions = new ArrayList<>();

    @Schema(description = "成功导入的组织单元数量")
    private int successOrgUnitCount = 0;

    @Schema(description = "成功导入的岗位数量")
    private int successPositionCount = 0;

    @Schema(description = "失败的记录数量")
    private int failureCount = 0;

    @Schema(description = "错误信息列表")
    private List<String> errorMessages = new ArrayList<>();

    // Constructors
    public OrgStructureImportResultDTO() {}

    // Getters and Setters
    public List<OrgUnitDTO> getImportedOrgUnits() {
        return importedOrgUnits;
    }

    public void setImportedOrgUnits(List<OrgUnitDTO> importedOrgUnits) {
        this.importedOrgUnits = importedOrgUnits;
    }

    public List<PositionDTO> getImportedPositions() {
        return importedPositions;
    }

    public void setImportedPositions(List<PositionDTO> importedPositions) {
        this.importedPositions = importedPositions;
    }

    public int getSuccessOrgUnitCount() {
        return successOrgUnitCount;
    }

    public void setSuccessOrgUnitCount(int successOrgUnitCount) {
        this.successOrgUnitCount = successOrgUnitCount;
    }

    public int getSuccessPositionCount() {
        return successPositionCount;
    }

    public void setSuccessPositionCount(int successPositionCount) {
        this.successPositionCount = successPositionCount;
    }

    public int getFailureCount() {
        return failureCount;
    }

    public void setFailureCount(int failureCount) {
        this.failureCount = failureCount;
    }

    public List<String> getErrorMessages() {
        return errorMessages;
    }

    public void setErrorMessages(List<String> errorMessages) {
        this.errorMessages = errorMessages;
    }

    // Helper methods
    public void addImportedOrgUnit(OrgUnitDTO orgUnit) {
        this.importedOrgUnits.add(orgUnit);
        this.successOrgUnitCount++;
    }

    public void addImportedPosition(PositionDTO position) {
        this.importedPositions.add(position);
        this.successPositionCount++;
    }

    public void addErrorMessage(String errorMessage) {
        this.errorMessages.add(errorMessage);
        this.failureCount++;
    }

    public int getTotalSuccessCount() {
        return successOrgUnitCount + successPositionCount;
    }

    public int getTotalProcessedCount() {
        return getTotalSuccessCount() + failureCount;
    }

    @Override
    public String toString() {
        return "OrgStructureImportResultDTO{" +
            "successOrgUnitCount=" + successOrgUnitCount +
            ", successPositionCount=" + successPositionCount +
            ", failureCount=" + failureCount +
            ", totalSuccessCount=" + getTotalSuccessCount() +
            ", totalProcessedCount=" + getTotalProcessedCount() +
            '}';
    }
}

package com.whiskerguard.organization.service.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;

/**
 * 描述：菜单实体
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/10
 */
@Schema(description = "菜单实体")
public class MenuDTO implements Serializable {

    @Schema(description = "主键 ID")
    private Long id;

    @Schema(description = "父级菜单ID")
    private Long parentId;

    @NotNull
    @Size(max = 128)
    @Schema(description = "菜单路径", requiredMode = Schema.RequiredMode.REQUIRED)
    private String path;

    @Size(max = 256)
    @Schema(description = "重定向路径")
    private String redirect;

    @Size(max = 128)
    @Schema(description = "组件名称")
    private String component;

    @NotNull
    @Size(max = 128)
    @Schema(description = "菜单名称")
    private String name;

    @Schema(description = "菜单别名")
    private String realName;

    @Schema(description = "是否为菜单项")
    private Boolean isMenu;

    @Schema(description = "排序")
    private Integer sort;

    @Schema(description = "菜单标记")
    private Integer mark;

    @Schema(description = "菜单元数据")
    private String meta;

    @Schema(description = "子菜单列表")
    private List<MenuDTO> children;

    @Schema(description = "权限列表")
    private List<ButtonDTO> auths;

    @Schema(description = "是否租户可用")
    private Boolean isAvailable;

    @Schema(description = "是否显示在前端菜单")
    private Boolean isShow;

    // 构造函数
    public MenuDTO() {
    }

    // Getter 和 Setter 方法
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getParentId() {
        return parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public String getRedirect() {
        return redirect;
    }

    public void setRedirect(String redirect) {
        this.redirect = redirect;
    }

    public String getComponent() {
        return component;
    }

    public void setComponent(String component) {
        this.component = component;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getRealName() {
        return realName;
    }

    public void setRealName(String realName) {
        this.realName = realName;
    }

    public Boolean getIsMenu() {
        return isMenu;
    }

    public void setIsMenu(Boolean isMenu) {
        this.isMenu = isMenu;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public Integer getMark() {
        return mark;
    }

    public void setMark(Integer mark) {
        this.mark = mark;
    }

    public String getMeta() {
        return meta;
    }

    public void setMeta(String meta) {
        this.meta = meta;
    }

    public List<MenuDTO> getChildren() {
        return children;
    }

    public void setChildren(List<MenuDTO> children) {
        this.children = children;
    }

    public List<ButtonDTO> getAuths() {
        return auths;
    }

    public void setAuths(List<ButtonDTO> auths) {
        this.auths = auths;
    }

    public Boolean getIsAvailable() {
        return isAvailable;
    }

    public void setIsAvailable(Boolean isAvailable) {
        this.isAvailable = isAvailable;
    }

    public Boolean getIsShow() {
        return isShow;
    }
    
    public void setIsShow(Boolean isShow) {
        this.isShow = isShow;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof MenuDTO menuDTO)) {
            return false;
        }
        return Objects.equals(getId(), menuDTO.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getId());
    }

    @Override
    public String toString() {
        return "MenuDTO{" +
            "id=" + getId() +
            ", parentId=" + getParentId() +
            ", path='" + getPath() + "'" +
            ", redirect='" + getRedirect() + "'" +
            ", component='" + getComponent() + "'" +
            ", name='" + getName() + "'" +
            ", isMenu=" + getIsMenu() +
            ", sort=" + getSort() +
            ", mark=" + getMark() +
            ", meta=" + getMeta() +
            ", children=" + getChildren() +
            ", auths=" + getAuths() +
            "}";
    }
}

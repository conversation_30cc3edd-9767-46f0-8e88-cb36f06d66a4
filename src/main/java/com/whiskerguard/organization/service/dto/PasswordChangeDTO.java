package com.whiskerguard.organization.service.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * 修改密码 DTO
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/10
 */
@Data
public class PasswordChangeDTO {

    @Size(min = 1, max = 128, message = "当前密码长度不能超过128个字符")
    @Schema(description = "当前密码")
    private String currentPassword;

    @Size(min = 1, max = 128, message = "新密码长度不能超过128个字符")
    @Schema(description = "新密码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String newPassword;

    @Size(min = 1, max = 128, message = "确认新密码长度不能超过128个字符")
    @Schema(description = "确认新密码")
    private String confirmPassword;

    @Size(min = 1, max = 6, message = "验证码长度不能超过6个字符")
    @Schema(description = "验证码")
    private String verifyCode;

    @Size(min = 1, max = 32, message = "手机号长度不能超过32个字符")
    @Schema(description = "手机号")
    private String phone;

}

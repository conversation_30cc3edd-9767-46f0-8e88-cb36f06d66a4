package com.whiskerguard.organization.service.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.whiskerguard.organization.domain.enumeration.OrgUnitType;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

import java.io.Serializable;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * A DTO for the {@link com.whiskerguard.organization.domain.OrgUnit} entity.
 * 组织单元（OrgUnit）实体\n管理公司、部门、事业群、团队等组织层级，并维护树形结构
 */
@Schema(description = "组织单元（OrgUnit）实体\n管理公司、部门、事业群、团队等组织层级，并维护树形结构")
@SuppressWarnings("common-java:DuplicatedBlocks")
public class OrgUnitDTO implements Serializable {

    @Schema(description = "主键 ID")
    private Long id;

    @NotBlank(message = "组织单位名称不能为空")
    @Size(max = 128, message = "组织单位名称长度不能超过128个字符")
    @Schema(description = "组织单位名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String name;

    @NotBlank(message = "组织单元编码不能为空")
    @Size(max = 64, message = "组织单元编码长度不能超过64个字符")
    @Schema(description = "唯一编码，用于外部系统对接或导入映射", requiredMode = Schema.RequiredMode.REQUIRED)
    private String code;

    @NotNull
    @Schema(description = "组织单元类型", requiredMode = Schema.RequiredMode.REQUIRED)
    private OrgUnitType type;

    @NotNull(message = "层级深度不能为空")
    @Min(value = 1, message = "层级深度必须在1到10之间")
    @Max(value = 10, message = "层级深度必须在1到10之间")
    @Schema(description = "层级深度，根节点为 1", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer level;

    @NotNull(message = "状态不能为空")
    @Min(value = 0, message = "状态值必须在0到1之间")
    @Max(value = 1, message = "状态值必须在0到1之间")
    @Schema(description = "状态：1=启用，0=禁用", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer status;

    @Min(value = 0, message = "排序序号不能小于0")
    @Schema(description = "排序序号")
    private Integer sortOrder;

    @Size(max = 255, message = "描述信息长度不能超过255个字符")
    @Schema(description = "描述信息")
    private String description;

    @Schema(description = "扩展元数据（JSON）")
    private String metadata;

    @Schema(description = "乐观锁版本", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer version;

    @Schema(description = "创建者")
    private String createdBy;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Instant createdAt;

    @Schema(description = "更新者")
    private String updatedBy;

    @Schema(description = "更新时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Instant updatedAt;

    @Schema(description = "软删除标志", requiredMode = Schema.RequiredMode.REQUIRED)
    private Boolean isDeleted;

    @Schema(description = "父节点")
    private Long parentId;

    @Schema(description = "子节点")
    private List<OrgUnitDTO> children = new ArrayList<>();

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public OrgUnitType getType() {
        return type;
    }

    public void setType(OrgUnitType type) {
        this.type = type;
    }

    public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getSortOrder() {
        return sortOrder;
    }

    public void setSortOrder(Integer sortOrder) {
        this.sortOrder = sortOrder;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getMetadata() {
        return metadata;
    }

    public void setMetadata(String metadata) {
        this.metadata = metadata;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public Instant getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Instant createdAt) {
        this.createdAt = createdAt;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    public Instant getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Instant updatedAt) {
        this.updatedAt = updatedAt;
    }

    public Boolean getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Boolean isDeleted) {
        this.isDeleted = isDeleted;
    }

    public Long getParentId() {
        return parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    public List<OrgUnitDTO> getChildren() {
        return children;
    }

    public void setChildren(List<OrgUnitDTO> children) {
        this.children = children;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof OrgUnitDTO orgUnitDTO)) {
            return false;
        }

        if (this.id == null) {
            return false;
        }
        return Objects.equals(this.id, orgUnitDTO.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(this.id);
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "OrgUnitDTO{" +
            "id=" + getId() +
            ", name='" + getName() + "'" +
            ", code='" + getCode() + "'" +
            ", type='" + getType() + "'" +
            ", level=" + getLevel() +
            ", status=" + getStatus() +
            ", sortOrder=" + getSortOrder() +
            ", description='" + getDescription() + "'" +
            ", metadata='" + getMetadata() + "'" +
            ", version=" + getVersion() +
            ", createdBy='" + getCreatedBy() + "'" +
            ", createdAt='" + getCreatedAt() + "'" +
            ", updatedBy='" + getUpdatedBy() + "'" +
            ", updatedAt='" + getUpdatedAt() + "'" +
            ", isDeleted='" + getIsDeleted() + "'" +
            ", parentId=" + getParentId() +
            "}";
    }
}

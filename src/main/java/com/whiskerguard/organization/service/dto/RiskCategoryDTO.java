package com.whiskerguard.organization.service.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.whiskerguard.organization.domain.enumeration.RiskLevel;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.io.Serializable;
import java.time.Instant;
import java.util.List;

/**
 * A DTO for the {@link com.whiskerguard.organization.domain.RiskCategory} entity.
 */
@Schema(description = "风险类别（高/中/低等，可自定义）")
@Data
public class RiskCategoryDTO implements Serializable {

    private Long id;

    @NotBlank(message = "类别名称不能为空")
    @Size(max = 128, message = "类别名称长度不能超过128个字符")
    @Schema(description = "类别名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String name;

    @NotNull(message = "风险等级不能为空")
    @Schema(description = "风险等级", requiredMode = Schema.RequiredMode.REQUIRED)
    private RiskLevel level;

    @NotBlank(message = "入组表达式不能为空")
    @Size(max = 1024)
    @Schema(description = "入组表达式", requiredMode = Schema.RequiredMode.REQUIRED)
    private String expression;

    @Size(max = 512, message = "描述信息长度不能超过512个字符")
    @Schema(description = "描述信息")
    private String description;

    @Schema(description = "乐观锁版本", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer version;

    @Schema(description = "创建者")
    private String createdBy;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Shanghai")
    private Instant createdAt;

    @Schema(description = "更新者")
    private String updatedBy;

    @Schema(description = "更新时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private Instant updatedAt;

    @Schema(description = "软删除标志", requiredMode = Schema.RequiredMode.REQUIRED)
    private Boolean isDeleted;

    @Schema(description = "RiskCategory ➜ RiskModel")
    private RiskModelDTO riskModel;

    @Schema(description = "风险规则列表")
    private List<RiskRuleDTO> riskRules;
}

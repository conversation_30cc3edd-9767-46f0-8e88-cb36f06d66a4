package com.whiskerguard.organization.service.dto;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

/**
 * 用户统计信息DTO
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/21
 */
@Schema(description = "用户统计信息")
public class UserStatisticsDTO {

    @Schema(description = "总用户数")
    private Long totalUsers;

    @Schema(description = "用户角色分布统计")
    private UserRoleStatisticsDTO roleStatistics;

    @Schema(description = "用户状态统计")
    private UserStatusStatisticsDTO statusStatistics;

    @Schema(description = "部门员工分布统计")
    private List<DepartmentEmployeeStatisticsDTO> departmentStatistics;

    public UserStatisticsDTO() {
    }

    public UserStatisticsDTO(Long totalUsers, UserRoleStatisticsDTO roleStatistics, 
                           UserStatusStatisticsDTO statusStatistics, 
                           List<DepartmentEmployeeStatisticsDTO> departmentStatistics) {
        this.totalUsers = totalUsers;
        this.roleStatistics = roleStatistics;
        this.statusStatistics = statusStatistics;
        this.departmentStatistics = departmentStatistics;
    }

    public Long getTotalUsers() {
        return totalUsers;
    }

    public void setTotalUsers(Long totalUsers) {
        this.totalUsers = totalUsers;
    }

    public UserRoleStatisticsDTO getRoleStatistics() {
        return roleStatistics;
    }

    public void setRoleStatistics(UserRoleStatisticsDTO roleStatistics) {
        this.roleStatistics = roleStatistics;
    }

    public UserStatusStatisticsDTO getStatusStatistics() {
        return statusStatistics;
    }

    public void setStatusStatistics(UserStatusStatisticsDTO statusStatistics) {
        this.statusStatistics = statusStatistics;
    }

    public List<DepartmentEmployeeStatisticsDTO> getDepartmentStatistics() {
        return departmentStatistics;
    }

    public void setDepartmentStatistics(List<DepartmentEmployeeStatisticsDTO> departmentStatistics) {
        this.departmentStatistics = departmentStatistics;
    }

    @Override
    public String toString() {
        return "UserStatisticsDTO{" +
            "totalUsers=" + totalUsers +
            ", roleStatistics=" + roleStatistics +
            ", statusStatistics=" + statusStatistics +
            ", departmentStatistics=" + departmentStatistics +
            '}';
    }
}

package com.whiskerguard.organization.service;

import com.whiskerguard.organization.service.dto.NewsCategoryDTO;
import java.util.Optional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

/**
 * 新闻分类管理服务接口
 * Service Interface for managing {@link com.whiskerguard.organization.domain.NewsCategory}.
 */
public interface NewsCategoryService {
    /**
     * 保存新闻分类信息
     * Save a newsCategory.
     *
     * @param newsCategoryDTO 要保存的新闻分类DTO对象 the entity to save.
     * @return 持久化后的新闻分类DTO对象 the persisted entity.
     */
    NewsCategoryDTO save(NewsCategoryDTO newsCategoryDTO);

    /**
     * 更新新闻分类信息
     * Updates a newsCategory.
     *
     * @param newsCategoryDTO 要更新的新闻分类DTO对象 the entity to update.
     * @return 持久化后的新闻分类DTO对象 the persisted entity.
     */
    NewsCategoryDTO update(NewsCategoryDTO newsCategoryDTO);

    /**
     * 部分更新新闻分类信息
     * Partially updates a newsCategory.
     *
     * @param newsCategoryDTO 要部分更新的新闻分类DTO对象 the entity to update partially.
     * @return 持久化后的新闻分类DTO对象 the persisted entity.
     */
    Optional<NewsCategoryDTO> partialUpdate(NewsCategoryDTO newsCategoryDTO);

    /**
     * 获取所有新闻分类信息
     * Get all the newsCategories.
     *
     * @param pageable 分页信息 the pagination information.
     * @return 新闻分类DTO对象分页列表 the list of entities.
     */
    Page<NewsCategoryDTO> findAll(Pageable pageable);

    /**
     * 获取所有新闻分类信息（包含关联关系的急加载）
     * Get all the newsCategories with eager load of many-to-many relationships.
     *
     * @param pageable 分页信息 the pagination information.
     * @return 新闻分类DTO对象分页列表 the list of entities.
     */
    Page<NewsCategoryDTO> findAllWithEagerRelationships(Pageable pageable);

    /**
     * 根据ID获取新闻分类信息
     * Get the "id" newsCategory.
     *
     * @param id 新闻分类ID the id of the entity.
     * @return 新闻分类DTO对象 the entity.
     */
    Optional<NewsCategoryDTO> findOne(Long id);

    /**
     * 根据ID删除新闻分类
     * Delete the "id" newsCategory.
     *
     * @param id 新闻分类ID the id of the entity.
     */
    void delete(Long id);
}

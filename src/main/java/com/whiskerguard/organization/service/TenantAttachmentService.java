package com.whiskerguard.organization.service;

import com.whiskerguard.organization.service.dto.TenantAttachmentDTO;
import java.util.Optional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

/**
 * Service Interface for managing {@link com.whiskerguard.organization.domain.TenantAttachment}.
 * 租户附件管理服务接口
 */
public interface TenantAttachmentService {
    /**
     * Save a tenantAttachment.
     * 保存租户附件信息
     *
     * @param tenantAttachmentDTO the entity to save. 要保存的租户附件DTO对象
     * @return the persisted entity. 持久化后的租户附件DTO对象
     */
    TenantAttachmentDTO save(TenantAttachmentDTO tenantAttachmentDTO);

    /**
     * Updates a tenantAttachment.
     * 更新租户附件信息
     *
     * @param tenantAttachmentDTO the entity to update. 要更新的租户附件DTO对象
     * @return the persisted entity. 持久化后的租户附件DTO对象
     */
    TenantAttachmentDTO update(TenantAttachmentDTO tenantAttachmentDTO);

    /**
     * Partially updates a tenantAttachment.
     * 部分更新租户附件信息
     *
     * @param tenantAttachmentDTO the entity to update partially. 要部分更新的租户附件DTO对象
     * @return the persisted entity. 持久化后的租户附件DTO对象
     */
    Optional<TenantAttachmentDTO> partialUpdate(TenantAttachmentDTO tenantAttachmentDTO);

    /**
     * Get all the tenantAttachments.
     * 获取所有租户附件信息
     *
     * @param pageable the pagination information. 分页信息
     * @return the list of entities. 租户附件DTO对象分页列表
     */
    Page<TenantAttachmentDTO> findAll(Pageable pageable);

    /**
     * Get the "id" tenantAttachment.
     * 根据ID获取租户附件信息
     *
     * @param id the id of the entity. 租户附件ID
     * @return the entity. 租户附件DTO对象
     */
    Optional<TenantAttachmentDTO> findOne(Long id);

    /**
     * Delete the "id" tenantAttachment.
     * 根据ID删除租户附件
     *
     * @param id the id of the entity. 租户附件ID
     */
    void delete(Long id);
}

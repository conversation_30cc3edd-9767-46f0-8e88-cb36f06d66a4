package com.whiskerguard.organization.service;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.api.WxMaUserService;
import cn.binarywang.wx.miniapp.bean.WxMaJscode2SessionResult;
import cn.binarywang.wx.miniapp.bean.WxMaPhoneNumberInfo;
import com.whiskerguard.organization.service.dto.WechatPhoneInfoDTO;
import com.whiskerguard.organization.service.dto.WechatPhoneLoginDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

/**
 * 微信手机号登录服务
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/5
 */
@Service
@ConditionalOnBean(WxMaService.class)
public class WechatPhoneLoginService {

    private static final Logger LOG = LoggerFactory.getLogger(WechatPhoneLoginService.class);

    private final WxMaService wxMaService;

    public WechatPhoneLoginService(WxMaService wxMaService) {
        this.wxMaService = wxMaService;
    }

    /**
     * 获取微信手机号信息
     *
     * @param loginDTO 微信登录DTO
     * @return 手机号信息
     */
    public WechatPhoneInfoDTO getPhoneInfo(WechatPhoneLoginDTO loginDTO) {
        try {
            // 1. 验证输入参数
            if (!validateLoginParams(loginDTO)) {
                return null;
            }

            WxMaUserService userService = wxMaService.getUserService();

            // 2. 通过code获取session信息
            WxMaJscode2SessionResult sessionInfo = userService.getSessionInfo(loginDTO.getCode());
            LOG.debug("WeChat session info : {}", sessionInfo);

            // 验证sessionInfo是否有效
            if (sessionInfo == null || !StringUtils.hasText(sessionInfo.getSessionKey())) {
                LOG.error("Invalid session info from WeChat, sessionInfo: {}", sessionInfo);
                return null;
            }

            // 3. 安全解密手机号信息
            WxMaPhoneNumberInfo phoneNumberInfo = userService.getPhoneNoInfo(sessionInfo.getSessionKey(), loginDTO.getEncryptedData(), loginDTO.getIv());

            if (phoneNumberInfo == null) {
                LOG.error("Failed to decrypt phone info");
                return null;
            }

            LOG.debug("WeChat phone info: phoneNumber={}, purePhoneNumber={}",
                phoneNumberInfo.getPhoneNumber(), phoneNumberInfo.getPurePhoneNumber());

            // 4. 构建返回对象
            WechatPhoneInfoDTO phoneInfoDTO = new WechatPhoneInfoDTO();
            phoneInfoDTO.setPhoneNumber(phoneNumberInfo.getPhoneNumber());
            phoneInfoDTO.setPurePhoneNumber(phoneNumberInfo.getPurePhoneNumber());
            phoneInfoDTO.setCountryCode(phoneNumberInfo.getCountryCode());
            phoneInfoDTO.setOpenId(sessionInfo.getOpenid());
            phoneInfoDTO.setUnionId(sessionInfo.getUnionid());

            return phoneInfoDTO;
        } catch (Exception e) {
            LOG.error("Failed to get WeChat phone info: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 验证微信登录参数
     *
     * @param loginDTO 微信登录DTO
     * @return 是否有效
     */
    private boolean validateLoginParams(WechatPhoneLoginDTO loginDTO) {
        if (loginDTO == null) {
            LOG.error("WechatPhoneLoginDTO is null");
            return false;
        }

        if (!StringUtils.hasText(loginDTO.getCode())) {
            LOG.error("WeChat code is empty");
            return false;
        }

        if (!StringUtils.hasText(loginDTO.getEncryptedData())) {
            LOG.error("WeChat encrypted data is empty");
            return false;
        }

        if (!StringUtils.hasText(loginDTO.getIv())) {
            LOG.error("WeChat IV is empty");
            return false;
        }

        return true;
    }

}

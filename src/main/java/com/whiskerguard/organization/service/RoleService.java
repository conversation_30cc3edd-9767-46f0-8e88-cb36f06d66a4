package com.whiskerguard.organization.service;

import com.whiskerguard.common.dto.ImportResultDTO;
import com.whiskerguard.organization.req.RoleReq;
import com.whiskerguard.organization.service.dto.RoleDTO;
import com.whiskerguard.organization.service.dto.RoleImportDTO;
import org.springframework.data.domain.Page;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Optional;

/**
 * 描述：角色服务接口
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/18
 */
public interface RoleService {

    /**
     * 方法名称：save
     * 描述：保存角色记录
     *
     * @param roleDTO 要保存的角色DTO对象
     * @return 保存后的角色DTO对象
     * @since 1.0
     */
    RoleDTO save(RoleDTO roleDTO);

    /**
     * 方法名称：update
     * 描述：更新角色记录
     *
     * @param roleDTO 要更新的角色DTO对象
     * @return 更新后的角色DTO对象
     * @since 1.0
     */
    RoleDTO update(RoleDTO roleDTO);

    /**
     * 方法名称：partialUpdate
     * 描述：部分更新角色记录
     *
     * @param roleDTO 要部分更新的角色DTO对象
     * @return 更新后的角色DTO对象
     * @since 1.0
     */
    Optional<RoleDTO> partialUpdate(RoleDTO roleDTO);

    /**
     * 方法名称：findAll
     * 描述：查询所有角色记录
     *
     * @param roleReq 角色查询请求对象
     * @return 角色DTO对象分页结果
     * @since 1.0
     */
    Page<RoleDTO> findByCondition(RoleReq roleReq);

    /**
     * 方法名称：findOne
     * 描述：根据ID查询角色记录
     *
     * @param id 角色ID
     * @return 角色DTO对象
     * @since 1.0
     */
    Optional<RoleDTO> findOne(Long id);

    /**
     * 方法名称：delete
     * 描述：根据ID删除角色记录
     *
     * @param id 角色ID
     * @since 1.0
     */
    void delete(Long id);

    /**
     * 方法名称：saveAll
     * 描述：批量保存角色记录
     *
     * @param roleDTOs 要保存的角色DTO对象列表
     * @return 保存后的角色DTO对象列表
     * @since 1.0
     */
    List<RoleDTO> saveAll(List<RoleDTO> roleDTOs);

    /**
     * 方法名称：importFromExcel
     * 描述：从Excel文件导入角色记录
     *
     * @param file 要导入的Excel文件
     * @return 导入结果
     * @since 1.0
     */
    ImportResultDTO<RoleImportDTO> importFromExcel(MultipartFile file);

    /**
     * 方法名称：findByNameContainingIgnoreCase
     * 描述：根据角色名称模糊查询角色记录
     *
     * @param name 角色名称关键词
     * @return 角色DTO对象列表
     * @since 1.0
     */
    List<RoleDTO> findByNameContainingIgnoreCase(String name);

}

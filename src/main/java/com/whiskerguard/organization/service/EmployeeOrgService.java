package com.whiskerguard.organization.service;

import com.whiskerguard.organization.service.dto.EmployeeOrgDTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Optional;


/**
 * 描述：员工组织关联服务接口
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/18
 */
public interface EmployeeOrgService {

    /**
     * 方法名称：save
     * 描述：保存员工组织关联记录
     *
     * @param employeeOrgDTO 要保存的员工组织关联DTO对象
     * @return 保存后的员工组织关联DTO对象
     * @since 1.0
     */
    EmployeeOrgDTO save(EmployeeOrgDTO employeeOrgDTO);

    /**
     * 方法名称：update
     * 描述：更新员工组织关联记录
     *
     * @param employeeOrgDTO 要更新的员工组织关联DTO对象
     * @return 更新后的员工组织关联DTO对象
     * @since 1.0
     */
    EmployeeOrgDTO update(EmployeeOrgDTO employeeOrgDTO);

    /**
     * 方法名称：partialUpdate
     * 描述：部分更新员工组织关联记录
     *
     * @param employeeOrgDTO 要部分更新的员工组织关联DTO对象
     * @return 更新后的员工组织关联DTO对象
     * @since 1.0
     */
    Optional<EmployeeOrgDTO> partialUpdate(EmployeeOrgDTO employeeOrgDTO);

    /**
     * 方法名称：findAll
     * 描述：查询所有员工组织关联记录
     *
     * @param pageable 分页参数
     * @return 员工组织关联DTO对象分页结果
     * @since 1.0
     */
    Page<EmployeeOrgDTO> findAll(Pageable pageable);

    /**
     * 方法名称：findOne
     * 描述：根据ID查询员工组织关联记录
     *
     * @param id 员工组织关联ID
     * @return 员工组织关联DTO对象
     * @since 1.0
     */
    Optional<EmployeeOrgDTO> findOne(Long id);

    /**
     * 方法名称：delete
     * 描述：根据ID删除员工组织关联记录
     *
     * @param id 员工组织关联ID
     * @since 1.0
     */
    void delete(Long id);

    /**
     * 方法名称：findByEmployees
     * 描述：根据员工ID列表查询员工组织关联记录
     *
     * @param employeeIds 员工ID列表
     * @return 员工组织关联DTO对象列表
     * @since 1.0
     */
    List<EmployeeOrgDTO> findByEmployees(List<Long> employeeIds);

}

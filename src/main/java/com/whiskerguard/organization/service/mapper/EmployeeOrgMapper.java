package com.whiskerguard.organization.service.mapper;

import com.whiskerguard.organization.domain.Employee;
import com.whiskerguard.organization.domain.EmployeeOrg;
import com.whiskerguard.organization.domain.OrgUnit;
import com.whiskerguard.organization.domain.Position;
import com.whiskerguard.organization.service.dto.EmployeeDTO;
import com.whiskerguard.organization.service.dto.EmployeeOrgDTO;
import com.whiskerguard.organization.service.dto.OrgUnitDTO;
import com.whiskerguard.organization.service.dto.PositionDTO;
import org.mapstruct.*;

/**
 * Mapper for the entity {@link EmployeeOrg} and its DTO {@link EmployeeOrgDTO}.
 */
@Mapper(componentModel = "spring")
public interface EmployeeOrgMapper extends EntityMapper<EmployeeOrgDTO, EmployeeOrg> {
    @Mapping(target = "employee", source = "employee", qualifiedByName = "employeeId")
    @Mapping(target = "orgUnit", source = "orgUnit", qualifiedByName = "orgUnitId")
    @Mapping(target = "position", source = "position", qualifiedByName = "positionId")
    EmployeeOrgDTO toDto(EmployeeOrg s);

    @Named("employeeId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    EmployeeDTO toDtoEmployeeId(Employee employee);

    @Named("orgUnitId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    OrgUnitDTO toDtoOrgUnitId(OrgUnit orgUnit);

    @Named("positionId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    PositionDTO toDtoPositionId(Position position);
}

package com.whiskerguard.organization.service.mapper;

import com.whiskerguard.organization.domain.Employee;
import com.whiskerguard.organization.service.dto.EmployeeDTO;
import org.mapstruct.*;

/**
 * Mapper for the entity {@link Employee} and its DTO {@link EmployeeDTO}.
 */
@Mapper(componentModel = "spring")
public interface EmployeeMapper extends EntityMapper<EmployeeDTO, Employee> {
    @Mapping(target = "gender", source = "gender")
    @Mapping(target = "status", source = "status")
    EmployeeDTO toDto(Employee s);

    @Mapping(target = "gender", source = "gender")
    @Mapping(target = "status", source = "status")
    Employee toEntity(EmployeeDTO s);

    @Named("gender")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    @Mapping(target = "gender", source = "gender")
    EmployeeDTO toDtoGender(Employee employee);

    @Named("status")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    @Mapping(target = "status", source = "status")
    EmployeeDTO toDtoStatus(Employee employee);
}

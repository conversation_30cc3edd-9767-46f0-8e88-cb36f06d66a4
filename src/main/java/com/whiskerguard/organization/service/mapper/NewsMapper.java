package com.whiskerguard.organization.service.mapper;

import com.whiskerguard.organization.domain.Employee;
import com.whiskerguard.organization.domain.News;
import com.whiskerguard.organization.domain.NewsCategory;
import com.whiskerguard.organization.domain.OrgUnit;
import com.whiskerguard.organization.domain.Tag;
import com.whiskerguard.organization.service.dto.EmployeeDTO;
import com.whiskerguard.organization.service.dto.NewsCategoryDTO;
import com.whiskerguard.organization.service.dto.NewsDTO;
import com.whiskerguard.organization.service.dto.OrgUnitDTO;
import com.whiskerguard.organization.service.dto.TagDTO;
import java.util.Set;
import java.util.stream.Collectors;
import org.mapstruct.*;

/**
 * Mapper for the entity {@link News} and its DTO {@link NewsDTO}.
 */
@Mapper(componentModel = "spring")
public interface NewsMapper extends EntityMapper<NewsDTO, News> {
    @Mapping(target = "category", source = "category", qualifiedByName = "newsCategoryName")
    @Mapping(target = "orgUnit", source = "orgUnit", qualifiedByName = "orgUnitName")
    @Mapping(target = "author", source = "author", qualifiedByName = "employeeUsername")
    @Mapping(target = "tags", ignore = true)
    NewsDTO toDto(News s);

    News toEntity(NewsDTO newsDTO);

    @Named("newsCategoryName")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    @Mapping(target = "name", source = "name")
    NewsCategoryDTO toDtoNewsCategoryName(NewsCategory newsCategory);

    @Named("orgUnitName")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    @Mapping(target = "name", source = "name")
    OrgUnitDTO toDtoOrgUnitName(OrgUnit orgUnit);

    @Named("employeeUsername")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    @Mapping(target = "username", source = "username")
    EmployeeDTO toDtoEmployeeUsername(Employee employee);

    @Named("tagName")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    @Mapping(target = "name", source = "name")
    TagDTO toDtoTagName(Tag tag);

    @Named("tagNameSet")
    default Set<TagDTO> toDtoTagNameSet(Set<Tag> tag) {
        return tag.stream().map(this::toDtoTagName).collect(Collectors.toSet());
    }
}

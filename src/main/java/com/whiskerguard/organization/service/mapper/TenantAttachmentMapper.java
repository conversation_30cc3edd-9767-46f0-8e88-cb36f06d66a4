package com.whiskerguard.organization.service.mapper;

import com.whiskerguard.organization.domain.Tenant;
import com.whiskerguard.organization.domain.TenantAttachment;
import com.whiskerguard.organization.service.dto.TenantAttachmentDTO;
import com.whiskerguard.organization.service.dto.TenantDTO;
import org.mapstruct.*;

/**
 * Mapper for the entity {@link TenantAttachment} and its DTO {@link TenantAttachmentDTO}.
 */
@Mapper(componentModel = "spring")
public interface TenantAttachmentMapper extends EntityMapper<TenantAttachmentDTO, TenantAttachment> {
    @Mapping(target = "tenant", source = "tenant", qualifiedByName = "tenantId")
    TenantAttachmentDTO toDto(TenantAttachment s);

    @Named("tenantId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    TenantDTO toDtoTenantId(Tenant tenant);
}

package com.whiskerguard.organization.service.mapper;

import com.whiskerguard.organization.domain.Tag;
import com.whiskerguard.organization.domain.TagCategory;
import com.whiskerguard.organization.service.dto.TagCategoryDTO;
import com.whiskerguard.organization.service.dto.TagDTO;
import org.mapstruct.BeanMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

/**
 * Mapper for the entity {@link Tag} and its DTO {@link TagDTO}.
 */
@Mapper(componentModel = "spring")
public interface TagMapper extends EntityMapper<TagDTO, Tag> {
    
    @Mapping(target = "category", source = "category", qualifiedByName = "tagCategoryName")
    TagDTO toDto(Tag s);

    Tag toEntity(TagDTO tagDTO);

    @Named("tagCategoryName")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    @Mapping(target = "name", source = "name")
    TagCategoryDTO toDtoTagCategoryName(TagCategory tagCategory);

}

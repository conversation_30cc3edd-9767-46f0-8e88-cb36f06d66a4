package com.whiskerguard.organization.service.mapper;

import com.whiskerguard.organization.domain.RiskCategory;
import com.whiskerguard.organization.domain.RiskModel;
import com.whiskerguard.organization.service.dto.RiskCategoryDTO;
import com.whiskerguard.organization.service.dto.RiskModelDTO;
import org.mapstruct.*;

/**
 * 风险类别对象映射器
 *
 * <p>负责风险类别实体对象与数据传输对象之间的转换，包括：</p>
 * <ul>
 *   <li>实体对象转换为数据传输对象</li>
 *   <li>数据传输对象转换为实体对象</li>
 *   <li>关联对象的映射处理</li>
 * </ul>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@Mapper(componentModel = "spring")
public interface RiskCategoryMapper extends EntityMapper<RiskCategoryDTO, RiskCategory> {

    /**
     * 将风险类别实体转换为数据传输对象
     *
     * @param s 风险类别实体对象
     * @return 风险类别数据传输对象
     */
    @Mapping(target = "riskModel", source = "riskModel", qualifiedByName = "riskModelName")
    RiskCategoryDTO toDto(RiskCategory s);

    /**
     * 将风险模型实体转换为简化的数据传输对象
     *
     * <p>只包含ID和名称字段，用于关联对象的映射。</p>
     *
     * @param riskModel 风险模型实体对象
     * @return 简化的风险模型数据传输对象
     */
    @Named("riskModelName")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    @Mapping(target = "name", source = "name")
    RiskModelDTO toDtoRiskModelName(RiskModel riskModel);
}

package com.whiskerguard.organization.service.mapper;

import com.whiskerguard.organization.domain.OrgUnit;
import com.whiskerguard.organization.service.dto.OrgUnitDTO;
import org.mapstruct.*;

/**
 * Mapper for the entity {@link OrgUnit} and its DTO {@link OrgUnitDTO}.
 */
@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface OrgUnitMapper extends EntityMapper<OrgUnitDTO, OrgUnit> {
    @Mapping(target = "parentId", source = "parent.id")
    @Override
    OrgUnitDTO toDto(OrgUnit entity);

    @Mapping(target = "parent", ignore = true)
    @Override
    OrgUnit toEntity(OrgUnitDTO dto);

    @Mapping(target = "parent", ignore = true)
    @BeanMapping(nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
    @Override
    void partialUpdate(@MappingTarget OrgUnit entity, OrgUnitDTO dto);
}

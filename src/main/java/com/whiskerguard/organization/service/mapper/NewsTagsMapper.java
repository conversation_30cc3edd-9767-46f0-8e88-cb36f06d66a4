package com.whiskerguard.organization.service.mapper;

import com.whiskerguard.organization.domain.News;
import com.whiskerguard.organization.domain.NewsTags;
import com.whiskerguard.organization.domain.Tag;
import com.whiskerguard.organization.service.dto.NewsDTO;
import com.whiskerguard.organization.service.dto.NewsTagsDTO;
import com.whiskerguard.organization.service.dto.TagDTO;
import org.mapstruct.BeanMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

/**
 * 描述：新闻标签关联映射器接口
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/18
 */
@Mapper(componentModel = "spring")
public interface NewsTagsMapper extends EntityMapper<NewsTagsDTO, NewsTags> {

    @Mapping(target = "tags", source = "tags", qualifiedByName = "tagId")
    @Mapping(target = "news", source = "news", qualifiedByName = "newsId")
    NewsTagsDTO toDto(NewsTags s);

    @Named("tagId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    TagDTO toDtoTagId(Tag tag);

    @Named("newsId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    NewsDTO toDtoNewsId(News news);
}

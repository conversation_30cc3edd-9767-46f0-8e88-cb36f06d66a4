package com.whiskerguard.organization.service.mapper;

import com.whiskerguard.organization.domain.Employee;
import com.whiskerguard.organization.domain.EmployeeRole;
import com.whiskerguard.organization.domain.OrgUnit;
import com.whiskerguard.organization.domain.Role;
import com.whiskerguard.organization.service.dto.EmployeeDTO;
import com.whiskerguard.organization.service.dto.EmployeeRoleDTO;
import com.whiskerguard.organization.service.dto.OrgUnitDTO;
import com.whiskerguard.organization.service.dto.RoleDTO;
import org.mapstruct.*;

/**
 * Mapper for the entity {@link EmployeeRole} and its DTO {@link EmployeeRoleDTO}.
 */
@Mapper(componentModel = "spring")
public interface EmployeeRoleMapper extends EntityMapper<EmployeeRoleDTO, EmployeeRole> {
    @Mapping(target = "employee", source = "employee", qualifiedByName = "employeeId")
    @Mapping(target = "role", source = "role", qualifiedByName = "roleId")
    @Mapping(target = "orgUnit", source = "orgUnit", qualifiedByName = "orgUnitId")
    EmployeeRoleDTO toDto(EmployeeRole s);

    @Named("employeeId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    EmployeeDTO toDtoEmployeeId(Employee employee);

    @Named("roleId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    RoleDTO toDtoRoleId(Role role);

    @Named("orgUnitId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    OrgUnitDTO toDtoOrgUnitId(OrgUnit orgUnit);
}

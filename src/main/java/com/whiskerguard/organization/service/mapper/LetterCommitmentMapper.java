package com.whiskerguard.organization.service.mapper;

import com.whiskerguard.organization.domain.LetterCommitment;
import com.whiskerguard.organization.service.dto.LetterCommitmentDTO;
import org.mapstruct.*;

/**
 * Mapper for the entity {@link LetterCommitment} and its DTO {@link LetterCommitmentDTO}.
 */
@Mapper(componentModel = "spring")
public interface LetterCommitmentMapper extends EntityMapper<LetterCommitmentDTO, LetterCommitment> {}

package com.whiskerguard.organization.service.mapper;

import com.whiskerguard.organization.domain.TagCategory;
import com.whiskerguard.organization.service.dto.TagCategoryDTO;
import org.mapstruct.*;

/**
 * Mapper for the entity {@link TagCategory} and its DTO {@link TagCategoryDTO}.
 */
@Mapper(componentModel = "spring")
public interface TagCategoryMapper extends EntityMapper<TagCategoryDTO, TagCategory> {}

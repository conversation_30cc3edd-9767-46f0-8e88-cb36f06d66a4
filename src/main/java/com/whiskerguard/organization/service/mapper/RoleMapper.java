package com.whiskerguard.organization.service.mapper;

import com.whiskerguard.organization.domain.Role;
import com.whiskerguard.organization.service.dto.RoleDTO;
import org.mapstruct.*;

/**
 * Mapper for the entity {@link Role} and its DTO {@link RoleDTO}.
 */
@Mapper(componentModel = "spring")
public interface RoleMapper extends En<PERSON>tyMapper<RoleDTO, Role> {
    @Mapping(target = "parent", source = "parent", qualifiedByName = "roleId")
    RoleDTO toDto(Role s);

    @Named("roleId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    RoleDTO toDtoRoleId(Role role);
}

package com.whiskerguard.organization.service.mapper;

import com.whiskerguard.organization.domain.RiskModel;
import com.whiskerguard.organization.service.dto.RiskModelDTO;
import org.mapstruct.*;

/**
 * 风险模型对象映射器
 *
 * <p>负责风险模型实体对象与数据传输对象之间的转换，包括：</p>
 * <ul>
 *   <li>实体对象转换为数据传输对象</li>
 *   <li>数据传输对象转换为实体对象</li>
 *   <li>部分字段更新映射</li>
 * </ul>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@Mapper(componentModel = "spring")
public interface RiskModelMapper extends EntityMapper<RiskModelDTO, RiskModel> {}

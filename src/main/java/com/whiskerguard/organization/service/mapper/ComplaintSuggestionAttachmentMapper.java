package com.whiskerguard.organization.service.mapper;

import com.whiskerguard.organization.domain.ComplaintSuggestionAttachment;
import com.whiskerguard.organization.service.dto.ComplaintSuggestionAttachmentDTO;
import org.mapstruct.*;

/**
 * Mapper for the entity {@link ComplaintSuggestionAttachment} and its DTO {@link ComplaintSuggestionAttachmentDTO}.
 */
@Mapper(componentModel = "spring")
public interface ComplaintSuggestionAttachmentMapper
    extends EntityMapper<ComplaintSuggestionAttachmentDTO, ComplaintSuggestionAttachment> {}

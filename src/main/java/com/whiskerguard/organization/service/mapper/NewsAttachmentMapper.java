package com.whiskerguard.organization.service.mapper;

import com.whiskerguard.organization.domain.News;
import com.whiskerguard.organization.domain.NewsAttachment;
import com.whiskerguard.organization.service.dto.NewsAttachmentDTO;
import com.whiskerguard.organization.service.dto.NewsDTO;
import org.mapstruct.*;

/**
 * Mapper for the entity {@link NewsAttachment} and its DTO {@link NewsAttachmentDTO}.
 */
@Mapper(componentModel = "spring")
public interface NewsAttachmentMapper extends EntityMapper<NewsAttachmentDTO, NewsAttachment> {
    @Mapping(target = "news", source = "news", qualifiedByName = "newsTitle")
    NewsAttachmentDTO toDto(NewsAttachment s);

    @Named("newsTitle")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    @Mapping(target = "title", source = "title")
    NewsDTO toDtoNewsTitle(News news);
}

package com.whiskerguard.organization.service.mapper;

import com.whiskerguard.organization.domain.Employee;
import com.whiskerguard.organization.domain.News;
import com.whiskerguard.organization.domain.NewsLike;
import com.whiskerguard.organization.service.dto.EmployeeDTO;
import com.whiskerguard.organization.service.dto.NewsDTO;
import com.whiskerguard.organization.service.dto.NewsLikeDTO;
import org.mapstruct.*;

/**
 * Mapper for the entity {@link NewsLike} and its DTO {@link NewsLikeDTO}.
 */
@Mapper(componentModel = "spring")
public interface NewsLikeMapper extends EntityMapper<NewsLikeDTO, NewsLike> {
    @Mapping(target = "news", source = "news", qualifiedByName = "newsTitle")
    @Mapping(target = "user", source = "user", qualifiedByName = "employeeUsername")
    NewsLikeDTO toDto(NewsLike s);

    @Named("newsTitle")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    @Mapping(target = "title", source = "title")
    NewsDTO toDtoNewsTitle(News news);

    @Named("employeeUsername")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    @Mapping(target = "username", source = "username")
    EmployeeDTO toDtoEmployeeUsername(Employee employee);
}

package com.whiskerguard.organization.service.mapper;

import com.whiskerguard.organization.domain.Employee;
import com.whiskerguard.organization.domain.News;
import com.whiskerguard.organization.domain.NewsComment;
import com.whiskerguard.organization.service.dto.EmployeeDTO;
import com.whiskerguard.organization.service.dto.NewsCommentDTO;
import com.whiskerguard.organization.service.dto.NewsDTO;
import org.mapstruct.*;

/**
 * Mapper for the entity {@link NewsComment} and its DTO {@link NewsCommentDTO}.
 */
@Mapper(componentModel = "spring")
public interface NewsCommentMapper extends EntityMapper<NewsCommentDTO, NewsComment> {
    @Mapping(target = "news", source = "news", qualifiedByName = "newsTitle")
    @Mapping(target = "parent", source = "parent", qualifiedByName = "newsCommentId")
    @Mapping(target = "commenter", source = "commenter", qualifiedByName = "employeeUsername")
    NewsCommentDTO toDto(NewsComment s);

    @Named("newsTitle")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    @Mapping(target = "title", source = "title")
    NewsDTO toDtoNewsTitle(News news);

    @Named("newsCommentId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    NewsCommentDTO toDtoNewsCommentId(NewsComment newsComment);

    @Named("employeeUsername")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    @Mapping(target = "username", source = "username")
    EmployeeDTO toDtoEmployeeUsername(Employee employee);
}

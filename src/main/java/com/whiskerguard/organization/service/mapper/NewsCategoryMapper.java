package com.whiskerguard.organization.service.mapper;

import com.whiskerguard.organization.domain.NewsCategory;
import com.whiskerguard.organization.domain.OrgUnit;
import com.whiskerguard.organization.service.dto.NewsCategoryDTO;
import com.whiskerguard.organization.service.dto.OrgUnitDTO;
import org.mapstruct.*;

/**
 * Mapper for the entity {@link NewsCategory} and its DTO {@link NewsCategoryDTO}.
 */
@Mapper(componentModel = "spring")
public interface NewsCategoryMapper extends EntityMapper<NewsCategoryDTO, NewsCategory> {
    @Mapping(target = "orgUnit", source = "orgUnit", qualifiedByName = "orgUnitName")
    @Mapping(target = "parent", source = "parent", qualifiedByName = "newsCategoryName")
    NewsCategoryDTO toDto(NewsCategory s);

    @Named("orgUnitName")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    @Mapping(target = "name", source = "name")
    OrgUnitDTO toDtoOrgUnitName(OrgUnit orgUnit);

    @Named("newsCategoryName")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    @Mapping(target = "name", source = "name")
    NewsCategoryDTO toDtoNewsCategoryName(NewsCategory newsCategory);
}

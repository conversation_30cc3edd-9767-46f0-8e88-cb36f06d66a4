package com.whiskerguard.organization.service.mapper;

import com.whiskerguard.organization.domain.ComplaintSuggestion;
import com.whiskerguard.organization.service.dto.ComplaintSuggestionDTO;
import org.mapstruct.*;

/**
 * Mapper for the entity {@link ComplaintSuggestion} and its DTO {@link ComplaintSuggestionDTO}.
 */
@Mapper(componentModel = "spring")
public interface ComplaintSuggestionMapper extends EntityMapper<ComplaintSuggestionDTO, ComplaintSuggestion> {}

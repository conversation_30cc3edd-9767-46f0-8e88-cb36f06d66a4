package com.whiskerguard.organization.service.mapper;

import com.whiskerguard.organization.domain.Employee;
import com.whiskerguard.organization.domain.News;
import com.whiskerguard.organization.domain.NewsComment;
import com.whiskerguard.organization.domain.NewsReport;
import com.whiskerguard.organization.service.dto.EmployeeDTO;
import com.whiskerguard.organization.service.dto.NewsCommentDTO;
import com.whiskerguard.organization.service.dto.NewsDTO;
import com.whiskerguard.organization.service.dto.NewsReportDTO;
import org.mapstruct.*;

/**
 * Mapper for the entity {@link NewsReport} and its DTO {@link NewsReportDTO}.
 */
@Mapper(componentModel = "spring")
public interface NewsReportMapper extends EntityMapper<NewsReportDTO, NewsReport> {
    @Mapping(target = "news", source = "news", qualifiedByName = "newsTitle")
    @Mapping(target = "comment", source = "comment", qualifiedByName = "newsCommentId")
    @Mapping(target = "reporter", source = "reporter", qualifiedByName = "employeeUsername")
    NewsReportDTO toDto(NewsReport s);

    @Named("newsTitle")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    @Mapping(target = "title", source = "title")
    NewsDTO toDtoNewsTitle(News news);

    @Named("newsCommentId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    NewsCommentDTO toDtoNewsCommentId(NewsComment newsComment);

    @Named("employeeUsername")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    @Mapping(target = "username", source = "username")
    EmployeeDTO toDtoEmployeeUsername(Employee employee);
}

package com.whiskerguard.organization.service.mapper;

import com.whiskerguard.organization.domain.RiskCategory;
import com.whiskerguard.organization.domain.RiskRule;
import com.whiskerguard.organization.service.dto.RiskCategoryDTO;
import com.whiskerguard.organization.service.dto.RiskRuleDTO;
import org.mapstruct.*;

/**
 * 风险规则对象映射器
 *
 * <p>负责风险规则实体对象与数据传输对象之间的转换，包括：</p>
 * <ul>
 *   <li>实体对象转换为数据传输对象</li>
 *   <li>数据传输对象转换为实体对象</li>
 *   <li>部分字段更新映射</li>
 *   <li>关联实体的映射处理</li>
 * </ul>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@Mapper(componentModel = "spring")
public interface RiskRuleMapper extends EntityMapper<RiskRuleDTO, RiskRule> {

    /**
     * 将风险规则实体转换为数据传输对象
     *
     * @param s 风险规则实体
     * @return 风险规则数据传输对象
     */
    @Mapping(target = "riskCategory", source = "riskCategory", qualifiedByName = "riskCategoryName")
    RiskRuleDTO toDto(RiskRule s);

    /**
     * 将风险分类实体转换为简化的数据传输对象（包含ID和名称）
     *
     * @param riskCategory 风险分类实体
     * @return 风险分类数据传输对象（包含ID和名称）
     */
    @Named("riskCategoryName")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    @Mapping(target = "name", source = "name")
    RiskCategoryDTO toDtoRiskCategoryName(RiskCategory riskCategory);
}

package com.whiskerguard.organization.service.mapper;

import com.whiskerguard.organization.domain.Tenant;
import com.whiskerguard.organization.domain.TenantProfile;
import com.whiskerguard.organization.service.dto.TenantDTO;
import com.whiskerguard.organization.service.dto.TenantProfileDTO;
import org.mapstruct.*;

/**
 * Mapper for the entity {@link TenantProfile} and its DTO {@link TenantProfileDTO}.
 */
@Mapper(componentModel = "spring")
public interface TenantProfileMapper extends EntityMapper<TenantProfileDTO, TenantProfile> {
    @Mapping(target = "tenant", source = "tenant", qualifiedByName = "tenantId")
    TenantProfileDTO toDto(TenantProfile s);

    @Named("tenantId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    TenantDTO toDtoTenantId(Tenant tenant);
}

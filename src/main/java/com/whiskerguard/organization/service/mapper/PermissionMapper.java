package com.whiskerguard.organization.service.mapper;

import com.whiskerguard.organization.domain.Permission;
import com.whiskerguard.organization.service.dto.PermissionDTO;
import org.mapstruct.*;

/**
 * Mapper for the entity {@link Permission} and its DTO {@link PermissionDTO}.
 */
@Mapper(componentModel = "spring")
public interface PermissionMapper extends EntityMapper<PermissionDTO, Permission> {

    @Override
    @Mapping(target = "parent", source = "parent", qualifiedByName = "permissionId")
    PermissionDTO toDto(Permission s);

    @Named("permissionId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    PermissionDTO toDtoPermissionId(Permission permission);
}

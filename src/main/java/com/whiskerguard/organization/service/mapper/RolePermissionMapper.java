package com.whiskerguard.organization.service.mapper;

import com.whiskerguard.organization.domain.Permission;
import com.whiskerguard.organization.domain.Role;
import com.whiskerguard.organization.domain.RolePermission;
import com.whiskerguard.organization.service.dto.PermissionDTO;
import com.whiskerguard.organization.service.dto.RoleDTO;
import com.whiskerguard.organization.service.dto.RolePermissionDTO;
import org.mapstruct.*;

/**
 * Mapper for the entity {@link RolePermission} and its DTO {@link RolePermissionDTO}.
 */
@Mapper(componentModel = "spring")
public interface RolePermissionMapper extends EntityMapper<RolePermissionDTO, RolePermission> {
    @Mapping(target = "role", source = "role", qualifiedByName = "roleId")
    @Mapping(target = "permission", source = "permission", qualifiedByName = "permissionId")
    RolePermissionDTO toDto(RolePermission s);

    @Named("roleId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    RoleDTO toDtoRoleId(Role role);

    @Named("permissionId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    PermissionDTO toDtoPermissionId(Permission permission);
}

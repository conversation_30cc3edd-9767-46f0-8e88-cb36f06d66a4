package com.whiskerguard.organization.service.mapper;

import com.whiskerguard.organization.domain.Employee;
import com.whiskerguard.organization.domain.News;
import com.whiskerguard.organization.domain.NewsReadRecord;
import com.whiskerguard.organization.service.dto.EmployeeDTO;
import com.whiskerguard.organization.service.dto.NewsDTO;
import com.whiskerguard.organization.service.dto.NewsReadRecordDTO;
import org.mapstruct.*;

/**
 * Mapper for the entity {@link NewsReadRecord} and its DTO {@link NewsReadRecordDTO}.
 */
@Mapper(componentModel = "spring")
public interface NewsReadRecordMapper extends EntityMapper<NewsReadRecordDTO, NewsReadRecord> {
    @Mapping(target = "news", source = "news", qualifiedByName = "newsTitle")
    @Mapping(target = "reader", source = "reader", qualifiedByName = "employeeUsername")
    NewsReadRecordDTO toDto(NewsReadRecord s);

    @Named("newsTitle")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    @Mapping(target = "title", source = "title")
    NewsDTO toDtoNewsTitle(News news);

    @Named("employeeUsername")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    @Mapping(target = "username", source = "username")
    EmployeeDTO toDtoEmployeeUsername(Employee employee);
}

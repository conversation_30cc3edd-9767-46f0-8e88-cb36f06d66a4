package com.whiskerguard.organization.service.mapper;

import com.whiskerguard.organization.domain.TenantInitialize;
import com.whiskerguard.organization.service.dto.TenantInitializeDTO;
import org.mapstruct.*;

/**
 * Mapper for the entity {@link TenantInitialize} and its DTO {@link TenantInitializeDTO}.
 */
@Mapper(componentModel = "spring")
public interface TenantInitializeMapper extends EntityMapper<TenantInitializeDTO, TenantInitialize> {}

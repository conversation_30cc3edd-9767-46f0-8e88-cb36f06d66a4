package com.whiskerguard.organization.service;

import org.springframework.scheduling.annotation.Async;

import java.util.concurrent.CompletableFuture;

/**
 * 描述：外部服务初始化接口
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/19
 */
public interface InitExternalService {

    /**
     * 异步初始化合规服务
     *
     * @param id       租户ID
     * @param username 操作人
     */
    @Async
    CompletableFuture<Void> initCompliance(Long id, String username);

}

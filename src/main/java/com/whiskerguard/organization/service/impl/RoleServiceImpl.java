package com.whiskerguard.organization.service.impl;

import com.whiskerguard.common.config.RequestConstants;
import com.whiskerguard.common.constant.NumberConstants;
import com.whiskerguard.common.dto.ImportResultDTO;
import com.whiskerguard.common.util.ExcelImportUtil;
import com.whiskerguard.common.util.HttpRequestUtil;
import com.whiskerguard.common.util.WgStringUtil;
import com.whiskerguard.organization.domain.Permission;
import com.whiskerguard.organization.domain.Role;
import com.whiskerguard.organization.domain.RolePermission;
import com.whiskerguard.organization.repository.PermissionRepository;
import com.whiskerguard.organization.repository.RolePermissionRepository;
import com.whiskerguard.organization.repository.RoleRepository;
import com.whiskerguard.organization.request.RoleReq;
import com.whiskerguard.organization.service.RoleService;
import com.whiskerguard.organization.service.dto.RoleDTO;
import com.whiskerguard.organization.service.dto.RoleImportDTO;
import com.whiskerguard.organization.service.mapper.RoleMapper;
import com.whiskerguard.organization.util.ImportValidationUtil;
import com.whiskerguard.organization.web.rest.errors.BadRequestAlertException;
import jakarta.servlet.http.HttpServletRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * 描述：角色管理的服务实现类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/18
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class RoleServiceImpl implements RoleService {

    private static final Logger LOG = LoggerFactory.getLogger(RoleServiceImpl.class);

    private final RoleRepository roleRepository;

    private final RoleMapper roleMapper;

    private final PermissionRepository permissionRepository;

    private final RolePermissionRepository rolePermissionRepository;

    private final HttpServletRequest request;

    public RoleServiceImpl(RoleRepository roleRepository, RoleMapper roleMapper, PermissionRepository permissionRepository,
                           RolePermissionRepository rolePermissionRepository, HttpServletRequest request) {
        this.roleRepository = roleRepository;
        this.roleMapper = roleMapper;
        this.permissionRepository = permissionRepository;
        this.rolePermissionRepository = rolePermissionRepository;
        this.request = request;
    }

    @Override
    public RoleDTO save(RoleDTO roleDTO) {
        LOG.debug("Request to save Role : {}", roleDTO);
        //校验code
        if (validateRoleCode(roleDTO.getCode())) {
            throw new BadRequestAlertException("角色编码存在非法字符", "role", "角色编码存在非法字符");
        }

        Long tenantId = Long.valueOf(HttpRequestUtil.getHeader(request, RequestConstants.X_TENANT_ID));
        //判断当前角色是否已经存在，如果存在则抛出异常
        if (roleRepository.findByTenantIdAndNameAndIsDeletedFalse(tenantId, roleDTO.getName()).isPresent()) {
            throw new BadRequestAlertException("角色已存在", "role", "角色已存在");
        }

        Role role = roleMapper.toEntity(roleDTO);
        String username = HttpRequestUtil.getHeader(request, RequestConstants.X_USER_NAME);
        role.setTenantId(tenantId);
        role.setVersion(NumberConstants.ONE);
        role.setCreatedBy(username);
        role.setUpdatedBy(username);
        role.setCreatedAt(Instant.now());
        role.setUpdatedAt(Instant.now());
        role.setIsDeleted(Boolean.FALSE);
        role = roleRepository.save(role);
        return roleMapper.toDto(role);
    }

    /**
     * 校验角色编码
     *
     * @param code 角色编码
     * @return true表示存在非法字符，false表示合法
     */
    private boolean validateRoleCode(String code) {
        return WgStringUtil.containsChineseChar(code) || WgStringUtil.containsSpecialChar(code) ||
            WgStringUtil.containsEmoji(code) || WgStringUtil.containsHtmlTag(code) ||
            WgStringUtil.containsSqlInjectionRisk(code) || WgStringUtil.containsXssRisk(code);
    }

    @Override
    public RoleDTO update(RoleDTO roleDTO) {
        LOG.debug("Request to update Role : {}", roleDTO);
        //校验code
        if (validateRoleCode(roleDTO.getCode())) {
            throw new BadRequestAlertException("角色编码存在非法字符", "role", "code exists");
        }
        Role role = roleMapper.toEntity(roleDTO);
        role = roleRepository.save(role);
        return roleMapper.toDto(role);
    }

    @Override
    public Optional<RoleDTO> partialUpdate(RoleDTO roleDTO) {
        LOG.debug("Request to partially update Role : {}", roleDTO);
        //校验code
        if (validateRoleCode(roleDTO.getCode())) {
            throw new BadRequestAlertException("角色编码存在非法字符", "role", "code exists");
        }
        Long tenantId = Long.valueOf(HttpRequestUtil.getHeader(request, RequestConstants.X_TENANT_ID));

        //判断当前角色是否已经存在，如果存在则抛出异常
        Optional<Role> optional = roleRepository.findByTenantIdAndNameAndIsDeletedFalse(tenantId, roleDTO.getName());
        Role role = optional.orElse(null);
        if (null != role && !roleDTO.getId().equals(role.getId())) {
            throw new BadRequestAlertException("角色已存在", "role", "role exists");
        }
        return roleRepository
            .findById(roleDTO.getId())
            .map(existingRole -> {
                roleMapper.partialUpdate(existingRole, roleDTO);
                String username = HttpRequestUtil.getHeader(request, RequestConstants.X_USER_NAME);
                existingRole.setUpdatedBy(username);
                existingRole.setUpdatedAt(Instant.now());
                return existingRole;
            })
            .map(roleRepository::save)
            .map(roleMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<RoleDTO> findByCondition(RoleReq roleReq) {
        // 从request header获取租户ID
        Long tenantId = Long.valueOf(HttpRequestUtil.getHeader(request, RequestConstants.X_TENANT_ID));
        LOG.debug("Filtering roles by tenant ID: {}", tenantId);
        //添加排序
        Pageable pageable = PageRequest.of(roleReq.getPageNumber(), roleReq.getPageSize(),
            Sort.by(Sort.Direction.DESC, "id"));
        return roleRepository.searchByCondition(tenantId, pageable, roleReq).map(roleMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<RoleDTO> findOne(Long id) {
        LOG.debug("Request to get Role : {}", id);
        return roleRepository.findById(id).map(roleMapper::toDto);
    }

    @Override
    public void delete(Long id) {
        LOG.debug("Request to delete Role : {}", id);
        // 逻辑删除：将isDeleted设置为true，而不是物理删除
        roleRepository
            .findById(id)
            .ifPresent(role -> {
                role.setIsDeleted(true);
                roleRepository.save(role);
            });
    }

    @Override
    public List<RoleDTO> saveAll(List<RoleDTO> roleDTOs) {
        LOG.debug("Request to batch save Roles : {}", roleDTOs.size());

        if (roleDTOs.isEmpty()) {
            return new ArrayList<>();
        }

        Long tenantId = Long.valueOf(HttpRequestUtil.getHeader(request, RequestConstants.X_TENANT_ID));
        String username = HttpRequestUtil.getHeader(request, RequestConstants.X_USER_NAME);
        Instant now = Instant.now();

        List<RoleDTO> result = new ArrayList<>();

        for (RoleDTO roleDTO : roleDTOs) {
            try {
                // 检查角色名称是否已存在
                Optional<Role> optional = roleRepository.findByTenantIdAndNameAndIsDeletedFalse(tenantId, roleDTO.getName());
                if (optional.isPresent()) {
                    LOG.warn("Role with name '{}' already exists, skipping", roleDTO.getName());
                    continue;
                }

                Role role = roleMapper.toEntity(roleDTO);
                role.setTenantId(tenantId);
                role.setVersion(NumberConstants.ONE);
                role.setCreatedBy(username);
                role.setUpdatedBy(username);
                role.setCreatedAt(now);
                role.setUpdatedAt(now);
                role.setStatus(NumberConstants.ONE);
                role.setIsDeleted(Boolean.FALSE);

                role = roleRepository.save(role);
                result.add(roleMapper.toDto(role));

                LOG.debug("Successfully saved role: {} - {}", role.getCode(), role.getName());
            } catch (Exception e) {
                LOG.error("Failed to save role: {} - {}", roleDTO.getCode(), e.getMessage(), e);
            }
        }

        LOG.info("Batch save completed: {} roles processed, {} roles saved", roleDTOs.size(), result.size());
        return result;
    }

    @Override
    public ImportResultDTO<RoleImportDTO> importFromExcel(MultipartFile file) {
        LOG.debug("Request to import Roles from Excel file: {}", file.getOriginalFilename());

        // 1. 读取Excel文件
        List<RoleImportDTO> importList = ExcelImportUtil.readExcel(file, RoleImportDTO.class);
        LOG.info("从Excel文件中读取到{}条角色数据", importList.size());

        ImportResultDTO<RoleImportDTO> result = new ImportResultDTO<>();

        if (importList.isEmpty()) {
            return result;
        }

        // 2. 验证和转换数据
        Long tenantId = Long.valueOf(HttpRequestUtil.getHeader(request, RequestConstants.X_TENANT_ID));
        String username = HttpRequestUtil.getHeader(request, RequestConstants.X_USER_NAME);
        Instant now = Instant.now();

        for (int i = 0; i < importList.size(); i++) {
            RoleImportDTO importDTO = importList.get(i);
            int rowNumber = i + 2; // Excel行号，第1行是表头，数据从第2行开始

            // 1. 字段级别验证
            List<String> validationErrors = ImportValidationUtil.validateRoleImportData(importDTO, rowNumber);
            if (!validationErrors.isEmpty()) {
                for (String error : validationErrors) {
                    result.addFailed(error);
                }
                continue;
            }

            // 2. 业务逻辑验证 - 检查角色名称是否已存在
            Optional<Role> existingRole = roleRepository.findByTenantIdAndNameAndIsDeletedFalse(tenantId, importDTO.getName());
            if (existingRole.isPresent()) {
                LOG.warn("角色名称已存在，跳过: {}", importDTO.getName());
                result.addFailed(ImportValidationUtil.formatErrorMessage(rowNumber, "角色名称", "已存在: " + importDTO.getName()));
                continue;
            }

            // 3. 业务逻辑验证 - 检查角色编码是否已存在
            Optional<Role> existingCodeRole = roleRepository.findByTenantIdAndCodeAndIsDeletedFalse(tenantId, importDTO.getCode());
            if (existingCodeRole.isPresent()) {
                LOG.warn("角色编码已存在，跳过: {}", importDTO.getCode());
                result.addFailed(ImportValidationUtil.formatErrorMessage(rowNumber, "角色编码", "已存在: " + importDTO.getCode()));
                continue;
            }

            // 4. 创建并保存角色
            try {
                Role role = createAndSaveRole(importDTO, tenantId, username, now);
                result.addSuccess();
                LOG.debug("成功导入角色: {} - {}", role.getCode(), role.getName());
            } catch (Exception e) {
                LOG.error("导入角色失败: {}, 错误: {}", importDTO.getName(), e.getMessage(), e);
                result.addFailed(ImportValidationUtil.formatErrorMessage(rowNumber, "角色", "保存失败: " + e.getMessage()));
            }
        }

        LOG.info("导入完成: 成功导入{}个角色", result.getSuccessCount());
        return result;
    }

    @Override
    @Transactional(readOnly = true)
    public List<RoleDTO> findByNameContainingIgnoreCase(String name) {
        Long tenantId = Long.valueOf(HttpRequestUtil.getHeader(request, RequestConstants.X_TENANT_ID));
        LOG.debug("Searching roles by name containing '{}' for tenant ID: {}", name, tenantId);
        List<Role> roles = roleRepository.findByTenantIdAndNameContainingIgnoreCaseAndIsDeletedFalse(tenantId, name);
        return roles.stream().map(roleMapper::toDto).toList();
    }

    /**
     * 创建并保存单个角色及其权限关联
     */
    private Role createAndSaveRole(RoleImportDTO importDTO, Long tenantId, String username, Instant now) {
        // 创建并保存角色
        Role role = new Role();
        role.setTenantId(tenantId);
        role.setCode(importDTO.getCode());
        role.setName(importDTO.getName());
        role.setDescription(importDTO.getDescription());
        role.setStatus(NumberConstants.ONE);
        role.setVersion(NumberConstants.ONE);
        role.setCreatedBy(username);
        role.setUpdatedBy(username);
        role.setCreatedAt(now);
        role.setUpdatedAt(now);
        role.setIsDeleted(Boolean.FALSE);

        role = roleRepository.save(role);

        // 处理权限关联
        if (importDTO.getPermissionCodes() != null && !importDTO.getPermissionCodes().trim().isEmpty()) {
            assignPermissionsToRole(role, importDTO.getPermissionCodes(), tenantId, username, now);
        }

        return role;
    }

    /**
     * 为角色分配权限
     */
    private void assignPermissionsToRole(Role role, String permissionCodes, Long tenantId, String username, Instant now) {
        if (permissionCodes == null || permissionCodes.trim().isEmpty()) {
            return;
        }

        // 解析权限编码列表
        String[] codes = permissionCodes.split(",");
        List<RolePermission> rolePermissions = new ArrayList<>();

        for (String code : codes) {
            String trimmedCode = code.trim();
            if (trimmedCode.isEmpty()) {
                continue;
            }

            // 查找权限
            Optional<Permission> permissionOpt = permissionRepository.findByCodeAndIsAvailable(trimmedCode, Boolean.TRUE);
            if (permissionOpt.isEmpty()) {
                LOG.warn("权限不存在: {}, 跳过分配给角色: {}", trimmedCode, role.getCode());
                continue;
            }

            Permission permission = permissionOpt.orElse(null);
            // 检查是否已存在关联
            Optional<RolePermission> existingRolePermission = rolePermissionRepository
                .findByRoleIdAndPermissionIdAndIsDeletedFalse(role.getId(), permission.getId());
            if (existingRolePermission.isPresent()) {
                LOG.debug("角色权限关联已存在: {} - {}", role.getCode(), permission.getCode());
                continue;
            }

            // 创建角色权限关联
            RolePermission rolePermission = new RolePermission();
            rolePermission.setTenantId(tenantId);
            rolePermission.setRole(role);
            rolePermission.setPermission(permission);
            rolePermission.setVersion(NumberConstants.ONE);
            rolePermission.setCreatedBy(username);
            rolePermission.setUpdatedBy(username);
            rolePermission.setCreatedAt(now);
            rolePermission.setUpdatedAt(now);
            rolePermission.setIsDeleted(Boolean.FALSE);

            rolePermissions.add(rolePermission);
        }
        if (!rolePermissions.isEmpty()) {
            rolePermissionRepository.saveAll(rolePermissions);
            LOG.debug("为角色 {} 分配了 {} 个权限", role.getCode(), rolePermissions.size());
        }
    }
}

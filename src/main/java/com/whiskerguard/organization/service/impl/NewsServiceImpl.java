package com.whiskerguard.organization.service.impl;

import com.whiskerguard.common.config.RequestConstants;
import com.whiskerguard.common.constant.NumberConstants;
import com.whiskerguard.common.util.HttpRequestUtil;
import com.whiskerguard.organization.domain.*;
import com.whiskerguard.organization.domain.enumeration.NewsStatus;
import com.whiskerguard.organization.repository.*;
import com.whiskerguard.organization.request.NewsReq;
import com.whiskerguard.organization.service.NewsService;
import com.whiskerguard.organization.service.dto.NewsAttachmentDTO;
import com.whiskerguard.organization.service.dto.NewsDTO;
import com.whiskerguard.organization.service.dto.TagDTO;
import com.whiskerguard.organization.service.mapper.EmployeeMapper;
import com.whiskerguard.organization.service.mapper.NewsAttachmentMapper;
import com.whiskerguard.organization.service.mapper.NewsCategoryMapper;
import com.whiskerguard.organization.service.mapper.NewsMapper;
import com.whiskerguard.organization.web.rest.errors.BadRequestAlertException;
import jakarta.servlet.http.HttpServletRequest;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 描述：新闻管理的服务实现类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/18
 */
@Service
@Transactional
public class NewsServiceImpl implements NewsService {

    private static final Logger LOG = LoggerFactory.getLogger(NewsServiceImpl.class);

    private final NewsRepository newsRepository;

    private final NewsMapper newsMapper;

    private final HttpServletRequest request;

    private final NewsAttachmentRepository newsAttachmentRepository;

    private final NewsAttachmentMapper newsAttachmentMapper;

    private final NewsCategoryRepository newsCategoryRepository;

    private final EmployeeRepository employeeRepository;

    private final TagRepository tagRepository;

    private final NewsTagsRepository newsTagsRepository;

    private final NewsCategoryMapper newsCategoryMapper;

    private final EmployeeMapper employeeMapper;

    public NewsServiceImpl(NewsRepository newsRepository, NewsMapper newsMapper, HttpServletRequest request,
                           NewsAttachmentRepository newsAttachmentRepository, NewsAttachmentMapper newsAttachmentMapper,
                           NewsCategoryRepository newsCategoryRepository, EmployeeRepository employeeRepository,
                           TagRepository tagRepository, NewsTagsRepository newsTagsRepository,
                           NewsCategoryMapper newsCategoryMapper, EmployeeMapper employeeMapper) {
        this.newsRepository = newsRepository;
        this.newsMapper = newsMapper;
        this.request = request;
        this.newsAttachmentRepository = newsAttachmentRepository;
        this.newsAttachmentMapper = newsAttachmentMapper;
        this.newsCategoryRepository = newsCategoryRepository;
        this.employeeRepository = employeeRepository;
        this.tagRepository = tagRepository;
        this.newsTagsRepository = newsTagsRepository;
        this.newsCategoryMapper = newsCategoryMapper;
        this.employeeMapper = employeeMapper;
    }

    @Override
    public NewsDTO save(NewsDTO newsDTO) {
        LOG.debug("Request to save News : {}", newsDTO);
        if (StringUtils.isBlank(newsDTO.getTitle())) {
            throw new BadRequestAlertException("新闻标题不能为空", "news", "title不能为空");
        }

        News news = newsMapper.toEntity(newsDTO);
        String username = HttpRequestUtil.getHeader(request, RequestConstants.X_USER_NAME);
        Long tenantId = Long.valueOf(HttpRequestUtil.getHeader(request, RequestConstants.X_TENANT_ID));
        if (newsDTO.getStatus() != null && newsDTO.getStatus() == NewsStatus.PUBLISHED) {
            newsDTO.setStatus(NewsStatus.PUBLISHED);
            newsDTO.setPublishDate(Instant.now());
        } else {
            newsDTO.setStatus(newsDTO.getStatus() == null ? NewsStatus.PENDING_REVIEW : newsDTO.getStatus());
        }

        news.setTenantId(tenantId);
        news.setVersion(NumberConstants.ONE);
        news.setCreatedBy(username);
        news.setUpdatedBy(username);
        news.setCreatedAt(Instant.now());
        news.setUpdatedAt(Instant.now());
        news.setIsDeleted(Boolean.FALSE);

        //添加分类
        if (newsDTO.getCategory() != null) {
            Optional<NewsCategory> optional = newsCategoryRepository.findById(newsDTO.getCategory().getId());
            if (optional.isEmpty()) {
                throw new BadRequestAlertException("新闻分类不存在", "newsCategory", "新闻分类不存在");
            }
            news.setCategory(optional.orElse(null));
        }

        //添加作者
        if (newsDTO.getAuthor() != null) {
            Optional<Employee> optional = employeeRepository.findById(newsDTO.getAuthor().getId());
            if (optional.isEmpty()) {
                throw new BadRequestAlertException("作者不存在", "employee", "作者不存在");
            }
            news.setAuthor(optional.orElse(null));
        }
        // 先保存新闻实体以获取ID
        news = newsRepository.save(news);

        //添加标签
        if (CollectionUtils.isNotEmpty(newsDTO.getTags())) {
            for (TagDTO tag : newsDTO.getTags()) {
                Optional<Tag> optional = tagRepository.findById(tag.getId());
                if (optional.isEmpty()) {
                    //标签不存在，则跳过
                    continue;
                }
                NewsTags newsTags = new NewsTags();
                newsTags.setTenantId(tenantId);
                newsTags.setVersion(NumberConstants.ONE);
                newsTags.setCreatedBy(username);
                newsTags.setUpdatedBy(username);
                newsTags.setCreatedAt(Instant.now());
                newsTags.setUpdatedAt(Instant.now());
                newsTags.setIsDeleted(Boolean.FALSE);
                newsTags.setTags(optional.orElse(null));
                newsTags.setNews(news);
                newsTagsRepository.save(newsTags);
            }
        }
        //保存附件
        if (newsDTO.getAttachments() != null) {
            for (NewsAttachmentDTO attachment : newsDTO.getAttachments()) {
                NewsAttachment newsAttachment = newsAttachmentMapper.toEntity(attachment);
                newsAttachment.setNews(news);
                newsAttachment.setTenantId(tenantId);
                newsAttachment.setVersion(NumberConstants.ONE);
                newsAttachment.setCreatedBy(username);
                newsAttachment.setUpdatedBy(username);
                newsAttachment.setCreatedAt(Instant.now());
                newsAttachment.setUpdatedAt(Instant.now());
                newsAttachment.setIsDeleted(Boolean.FALSE);
                newsAttachmentRepository.save(newsAttachment);
            }
        }
        return newsMapper.toDto(news);
    }

    @Override
    public Optional<NewsDTO> partialUpdate(NewsDTO newsDTO) {
        LOG.debug("Request to partially update News : {}", newsDTO);

        return newsRepository
            .findById(newsDTO.getId())
            .map(existingNews -> {
                newsMapper.partialUpdate(existingNews, newsDTO);
                String username = HttpRequestUtil.getHeader(request, RequestConstants.X_USER_NAME);
                if (newsDTO.getStatus() != null && newsDTO.getStatus() == NewsStatus.PUBLISHED) {
                    newsDTO.setPublishDate(Instant.now());
                }
                existingNews.setUpdatedBy(username);
                existingNews.setUpdatedAt(Instant.now());
                return existingNews;
            })
            .map(newsRepository::save)
            .map(newsMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<NewsDTO> findAll(Pageable pageable, Long categoryId) {
        Long tenantId = Long.valueOf(HttpRequestUtil.getHeader(request, RequestConstants.X_TENANT_ID));
        LOG.debug("Request to get all News by tenant ID: {}", tenantId);
        Page<NewsDTO> newsPage;
        if (categoryId != null) {
            newsPage = newsRepository.findAllByCategoryId(tenantId, categoryId, pageable).map(newsMapper::toDto);
        } else {
            newsPage = newsRepository.findByIsDeletedFalse(tenantId, pageable).map(newsMapper::toDto);
        }
        return newsPage;
    }

    @Override
    public Page<NewsDTO> findAllWithEagerRelationships(Pageable pageable, Long categoryId) {
        Long tenantId = Long.valueOf(HttpRequestUtil.getHeader(request, RequestConstants.X_TENANT_ID));
        Page<NewsDTO> newsPage;
        if (categoryId != null) {
            newsPage = newsRepository.findAllByCategoryId(tenantId, categoryId, pageable).map(newsMapper::toDto);
        } else {
            newsPage = newsRepository.findAll(tenantId, pageable).map(newsMapper::toDto);
        }
        return newsPage;
    }

    @Override
    public Optional<NewsDTO> findOne(Long id) {
        LOG.debug("Request to get News : {}", id);
        Optional<News> optional = newsRepository.findById(id);
        News news = optional.orElseThrow(() -> new BadRequestAlertException("新闻未找到", "news", "news not found"));
        Integer viewCount = news.getViewCount();
        if (viewCount == null) {
            viewCount = NumberConstants.ZERO;
        }
        news.setViewCount(viewCount + NumberConstants.ONE);
        News save = newsRepository.save(news);

        NewsDTO dto = newsMapper.toDto(save);

        //添加分类
        if (save.getCategory() != null) {
            Optional<NewsCategory> optionalCategory = newsCategoryRepository.findById(save.getCategory().getId());
            if (optionalCategory.isEmpty()) {
                throw new BadRequestAlertException("新闻分类不存在", "newsCategory", "newsCategory not found");
            }
            dto.setCategory(newsCategoryMapper.toDto(optionalCategory.orElse(null)));
        }
        //添加作者
        if (save.getAuthor() != null) {
            Optional<Employee> optionalAuthor = employeeRepository.findById(save.getAuthor().getId());
            if (optionalAuthor.isEmpty()) {
                throw new BadRequestAlertException("作者不存在", "employee", "employee not found");
            }
            dto.setAuthor(employeeMapper.toDto(optionalAuthor.orElse(null)));
        }
        //添加附件
        dto.setAttachments(newsAttachmentRepository.findByNewsId(id).stream().map(newsAttachmentMapper::toDto).toList());
        //添加标签
        Set<TagDTO> tags = newsTagsRepository.findByNewsId(id).stream().map(NewsTags::getTags).map(newsMapper::toDtoTagName).collect(Collectors.toSet());
        dto.setTags(tags);
        return Optional.of(dto);
    }

    @Override
    public void delete(Long id) {
        LOG.debug("Request to delete News : {}", id);
        // 逻辑删除：将isDeleted设置为true，而不是物理删除
        newsRepository
            .findById(id)
            .ifPresent(news -> {
                news.setIsDeleted(true);
                newsRepository.save(news);
            });
    }

    @Override
    public Page<NewsDTO> findByCondition(NewsReq newsReq) {
        Long tenantId = Long.valueOf(HttpRequestUtil.getHeader(request, RequestConstants.X_TENANT_ID));
        LOG.debug("Request to get News by condition: {}", newsReq);

        // 默认按创建时间倒序排列
        Sort sort = Sort.by(Sort.Direction.DESC, "createdAt");
        Pageable pageable = PageRequest.of(newsReq.getPageNumber(), newsReq.getPageSize(), sort);

        return newsRepository.search(newsReq, tenantId, pageable).map(newsMapper::toDto);
    }

}

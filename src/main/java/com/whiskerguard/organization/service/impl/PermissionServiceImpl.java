package com.whiskerguard.organization.service.impl;

import com.whiskerguard.common.config.RequestConstants;
import com.whiskerguard.common.constant.NumberConstants;
import com.whiskerguard.common.constant.SystemConstant;
import com.whiskerguard.common.util.HttpRequestUtil;
import com.whiskerguard.organization.domain.Permission;
import com.whiskerguard.organization.domain.RolePermission;
import com.whiskerguard.organization.domain.Tenant;
import com.whiskerguard.organization.domain.enumeration.ResourceType;
import com.whiskerguard.organization.repository.PermissionRepository;
import com.whiskerguard.organization.repository.RolePermissionRepository;
import com.whiskerguard.organization.repository.TenantRepository;
import com.whiskerguard.organization.service.EmployeeRoleService;
import com.whiskerguard.organization.service.EmployeeService;
import com.whiskerguard.organization.service.PermissionService;
import com.whiskerguard.organization.service.dto.*;
import com.whiskerguard.organization.service.mapper.PermissionMapper;
import com.whiskerguard.organization.web.rest.errors.BadRequestAlertException;
import jakarta.servlet.http.HttpServletRequest;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Duration;
import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 描述：权限管理的服务实现类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/18
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class PermissionServiceImpl implements PermissionService {

    private static final Logger LOG = LoggerFactory.getLogger(PermissionServiceImpl.class);

    private final PermissionRepository permissionRepository;

    private final PermissionMapper permissionMapper;

    private final EmployeeService employeeService;

    private final EmployeeRoleService employeeRoleService;

    private final HttpServletRequest request;

    private final RolePermissionRepository rolePermissionRepository;

    private final TenantRepository tenantRepository;

    private final RedissonClient redissonClient;

    public PermissionServiceImpl(PermissionRepository permissionRepository, PermissionMapper permissionMapper, EmployeeService employeeService, EmployeeRoleService employeeRoleService, HttpServletRequest request, RolePermissionRepository rolePermissionRepository, TenantRepository tenantRepository, RedissonClient redissonClient) {
        this.permissionRepository = permissionRepository;
        this.permissionMapper = permissionMapper;
        this.employeeService = employeeService;
        this.employeeRoleService = employeeRoleService;
        this.request = request;
        this.rolePermissionRepository = rolePermissionRepository;
        this.tenantRepository = tenantRepository;
        this.redissonClient = redissonClient;
    }

    @Override
    public MenuDTO save(MenuDTO menuDTO) {
        LOG.debug("Request to save Permission : {}", menuDTO);
        Long tenantId = Long.valueOf(HttpRequestUtil.getHeader(request, RequestConstants.X_TENANT_ID));
        //判断当前权限是否存在
        if (null != permissionRepository.findByName(menuDTO.getName())) {
            throw new IllegalArgumentException("权限已存在");
        }
        String username = HttpRequestUtil.getHeader(request, RequestConstants.X_USER_NAME);
        //保存菜单级权限
        Permission permission = new Permission();
        permission.setParent(menuDTO.getParentId() == null ? null : permissionRepository.findById(menuDTO.getParentId()).orElse(null));
        permission.setTenantId(tenantId);
        permission.setName(menuDTO.getName());
        permission.setRedirect(menuDTO.getRedirect());
        permission.setResourceType(ResourceType.MENU);
        permission.setFrontendRoute(menuDTO.getPath());
        permission.setComponent(menuDTO.getComponent());
        permission.setSortOrder(menuDTO.getSort());
        permission.setMetadata(menuDTO.getMeta());
        permission.setVersion(NumberConstants.ONE);
        permission.setCreatedBy(username);
        permission.setUpdatedBy(username);
        permission.setCreatedAt(Instant.now());
        permission.setUpdatedAt(Instant.now());
        permission.setIsDeleted(Boolean.FALSE);
        permission.setIsAvailable(menuDTO.getIsAvailable() == null ? Boolean.TRUE : menuDTO.getIsAvailable());
        permission = permissionRepository.save(permission);
        //保存按钮级权限
        if (CollectionUtils.isNotEmpty(menuDTO.getAuths())) {
            LOG.debug("Saving {} button permissions", menuDTO.getAuths().size());
            //字符串数组转list
            List<Permission> buttonPermissions = new ArrayList<>();
            for (ButtonDTO button : menuDTO.getAuths()) {
                Permission buttonPermission = new Permission();
                buttonPermission.setTenantId(tenantId);
                buttonPermission.setName(button.getName());
                buttonPermission.setFrontendRoute(button.getValue());
                buttonPermission.setResourceType(ResourceType.BUTTON);
                buttonPermission.setParent(permission);
                buttonPermission.setVersion(NumberConstants.ONE);
                buttonPermission.setCreatedBy(username);
                buttonPermission.setUpdatedBy(username);
                buttonPermission.setCreatedAt(Instant.now());
                buttonPermission.setUpdatedAt(Instant.now());
                buttonPermission.setIsDeleted(Boolean.FALSE);
                buttonPermission.setIsAvailable(menuDTO.getIsAvailable() == null ? Boolean.TRUE : menuDTO.getIsAvailable());
                buttonPermissions.add(buttonPermission);
            }
            permissionRepository.saveAll(buttonPermissions);
        }
        menuDTO.setId(permission.getId());

        //清空缓存
        clearCache(tenantId);
        return menuDTO;
    }

    /**
     * 清除缓存
     *
     * @param tenantId 租户ID
     */
    private void clearCache(Long tenantId) {
        try {
            String header = HttpRequestUtil.getHeader(request, RequestConstants.X_SYSTEM);
            String system = StringUtils.isNotBlank(header) ? header : SystemConstant.ENTERPRISE_BACKEND;
            // 生成Redis缓存键
            String cacheKey = generateMenuCacheKey(tenantId, system);
            redissonClient.getBucket(cacheKey).delete();
        } catch (Exception e) {
            LOG.error("清除缓存失败: {}", e.getMessage(), e);
        }
    }

    @Override
    public Optional<MenuDTO> partialUpdate(MenuDTO menuDTO) {
        LOG.debug("Request to partially update Permission : {}", menuDTO);
        Long tenantId = Long.valueOf(HttpRequestUtil.getHeader(request, RequestConstants.X_TENANT_ID));
        //判断当前权限是否存在
        Permission permission = permissionRepository.findByName(menuDTO.getName());
        if (permission != null && !permission.getId().equals(menuDTO.getId())) {
            throw new BadRequestAlertException("权限已存在", "permission", "permission exists");
        }
        String username = HttpRequestUtil.getHeader(request, RequestConstants.X_USER_NAME);
        Optional<Permission> optional = permissionRepository.findById(menuDTO.getId());
        permission = optional.orElseThrow(() -> new RuntimeException("权限未找到"));
        permission.setParent(menuDTO.getParentId() == null ? null : permissionRepository.findById(menuDTO.getParentId()).orElse(null));
        permission.setName(menuDTO.getName());
        permission.setRedirect(menuDTO.getRedirect());
        permission.setFrontendRoute(menuDTO.getPath());
        permission.setComponent(menuDTO.getComponent());
        permission.setSortOrder(menuDTO.getSort());
        permission.setIsAvailable(menuDTO.getIsAvailable() == null ? Boolean.TRUE : menuDTO.getIsAvailable());
        permission.setMetadata(menuDTO.getMeta());
        permission.setUpdatedBy(username);
        permission.setUpdatedAt(Instant.now());
        permissionRepository.save(permission);
        //更新按钮级权限
        if (CollectionUtils.isNotEmpty(menuDTO.getAuths())) {
            LOG.debug("Updating {} button permissions", menuDTO.getAuths().size());
            List<Permission> buttonPermissions = new ArrayList<>();
            Permission buttonPermission;
            for (ButtonDTO button : menuDTO.getAuths()) {
                if (button.getId() == null) {
                    //新增
                    buttonPermission = new Permission();
                    buttonPermission.setTenantId(tenantId);
                    buttonPermission.setName(button.getName());
                    buttonPermission.setFrontendRoute(button.getValue());
                    buttonPermission.setResourceType(ResourceType.BUTTON);
                    buttonPermission.setParent(permission);
                    buttonPermission.setVersion(NumberConstants.ONE);
                    buttonPermission.setCreatedBy(username);
                    buttonPermission.setUpdatedBy(username);
                    buttonPermission.setCreatedAt(Instant.now());
                    buttonPermission.setUpdatedAt(Instant.now());
                    buttonPermission.setIsDeleted(Boolean.FALSE);
                    buttonPermission.setIsAvailable(Boolean.TRUE);
                    buttonPermissions.add(buttonPermission);
                } else {
                    //更新
                    buttonPermission = permissionRepository.findById(button.getId()).orElseThrow(() -> new RuntimeException("按钮权限未找到"));
                    buttonPermission.setName(button.getName());
                    buttonPermission.setFrontendRoute(button.getValue());
                    buttonPermission.setUpdatedBy(username);
                    buttonPermission.setUpdatedAt(Instant.now());
                    buttonPermissions.add(buttonPermission);
                }
            }
            permissionRepository.saveAll(buttonPermissions);
        }

        //清空缓存
        clearCache(tenantId);
        return Optional.of(menuDTO);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<PermissionDTO> findAll(Pageable pageable) {
        LOG.debug("Request to get all Permissions");
        // 从request header获取租户ID
        String tenantIdHeader = HttpRequestUtil.getHeader(request, RequestConstants.X_TENANT_ID);
        Long tenantId = Long.valueOf(tenantIdHeader);
        LOG.debug("Filtering permissions by tenant ID: {}", tenantId);
        Boolean isSystem = isSystem(tenantId);
        String header = HttpRequestUtil.getHeader(request, RequestConstants.X_SYSTEM);
        String system = StringUtils.isNotBlank(header) ? header : SystemConstant.ENTERPRISE_BACKEND;
        if (SystemConstant.ENTERPRISE_BACKEND.equals(system)) {
            //企业后台，系统租户也可以获取相应菜单
            isSystem = Boolean.FALSE;
        }
        return permissionRepository.findByIsAvailableAndIsDeletedFalse(!isSystem, pageable).map(permissionMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<PermissionDTO> findOne(Long id) {
        LOG.debug("Request to get Permission : {}", id);
        return permissionRepository.findById(id).map(permissionMapper::toDto);
    }

    @Override
    public void delete(Long id) {
        LOG.debug("Request to delete Permission : {}", id);
        // 逻辑删除：将isDeleted设置为true，而不是物理删除
        permissionRepository.findById(id).ifPresent(permission -> {
            permission.setIsDeleted(true);
            permissionRepository.save(permission);
        });
        // 删除redis的存在
        clearCache(Long.valueOf(HttpRequestUtil.getHeader(request, RequestConstants.X_TENANT_ID)));
    }

    @Override
    @Transactional(readOnly = true)
    public List<PermissionDTO> findTreeByTenantId() {
        Long tenantId = Long.valueOf(HttpRequestUtil.getHeader(request, RequestConstants.X_TENANT_ID));
        LOG.debug("Request to get tree structure permissions by tenant ID : {}", tenantId);
        Boolean isSystem = isSystem(tenantId);
        String header = HttpRequestUtil.getHeader(request, RequestConstants.X_SYSTEM);
        String system = StringUtils.isNotBlank(header) ? header : SystemConstant.ENTERPRISE_BACKEND;
        if (SystemConstant.ENTERPRISE_BACKEND.equals(system)) {
            //企业后台，系统租户也可以获取相应菜单
            isSystem = Boolean.FALSE;
        }
        // 获取分页的顶级权限
        List<Permission> rootPermissions = permissionRepository.findRootPermissionsByIsAvailable(!isSystem);
        List<PermissionDTO> rootPermissionDtos = permissionMapper.toDto(rootPermissions);

        // 获取所有权限，用于构建树
        List<Permission> allPermissions = permissionRepository.findByIsAvailable(!isSystem);
        Map<Long, List<PermissionDTO>> permissionMap = new HashMap<>();

        // 将权限按父ID分组
        for (Permission permission : allPermissions) {
            if (permission.getParent() != null) {
                Long parentId = permission.getParent().getId();
                if (!permissionMap.containsKey(parentId)) {
                    permissionMap.put(parentId, new ArrayList<>());
                }
                permissionMap.get(parentId).add(permissionMapper.toDto(permission));
            }
        }

        // 为每个顶级权限构建树形结构
        for (PermissionDTO rootPermission : rootPermissionDtos) {
            buildPermissionTree(rootPermission, permissionMap);
        }
        return rootPermissionDtos;
    }

    @Override
    @Transactional(readOnly = true)
    public List<PermissionDTO> findByParentId(Long parentId) {
        LOG.debug("Request to get permissions by parent ID : {}", parentId);
        List<Permission> permissions = permissionRepository.findByParentId(parentId);
        return permissionMapper.toDto(permissions);
    }

    /**
     * 判断是否是系统租户
     *
     * @param tenantId 租户ID
     * @return 是否是系统租户
     */
    private Boolean isSystem(Long tenantId) {
        Tenant tenant = tenantRepository.findById(tenantId).orElseThrow(() -> new RuntimeException("租户未找到"));
        return tenant.getIsSystem();
    }

    /**
     * 递归构建权限树形结构
     *
     * @param parent        父权限
     * @param permissionMap 按父ID分组的权限映射
     */
    private void buildPermissionTree(PermissionDTO parent, Map<Long, List<PermissionDTO>> permissionMap) {
        List<PermissionDTO> children = permissionMap.get(parent.getId());
        if (children != null && !children.isEmpty()) {
            parent.setChildren(children);
            for (PermissionDTO child : children) {
                buildPermissionTree(child, permissionMap);
            }
        } else {
            parent.setChildren(new ArrayList<>());
        }
    }

    @Override
    public List<MenuDTO> findMenuTreeByEmployeeId(Long employeeId) {
        LOG.debug("Request to get menu tree by employee ID : {}", employeeId);
        // 获取员工的权限
        List<PermissionDTO> permissionTree = findAllPermissionsByEmployeeId(employeeId);
        // 将权限转换为菜单数据
        return convertPermissionsToMenus(permissionTree);
    }

    @Override
    public List<MenuDTO> findAllMenu() {
        LOG.debug("Request to get all menu");
        Long tenantId = Long.valueOf(HttpRequestUtil.getHeader(request, RequestConstants.X_TENANT_ID));
        Boolean isSystem = isSystem(tenantId);
        String header = HttpRequestUtil.getHeader(request, RequestConstants.X_SYSTEM);
        String system = StringUtils.isNotBlank(header) ? header : SystemConstant.ENTERPRISE_BACKEND;
        if (SystemConstant.ENTERPRISE_BACKEND.equals(system)) {
            //企业后台，系统租户也可以获取相应菜单
            isSystem = Boolean.FALSE;
        }

        // 生成Redis缓存键
        String cacheKey = generateMenuCacheKey(tenantId, system);

        try {
            // 使用Redisson获取缓存桶
            RBucket<List<MenuDTO>> bucket = redissonClient.getBucket(cacheKey);

            // 尝试从Redis缓存中获取菜单数据
            List<MenuDTO> cachedMenus = bucket.get();

            if (cachedMenus != null && !cachedMenus.isEmpty()) {
                LOG.debug("Found cached menu data for key: {}, size: {}", cacheKey, cachedMenus.size());
                return cachedMenus;
            }

            LOG.debug("No cached menu data found for key: {}, loading from database", cacheKey);

            // 缓存中没有数据，从数据库加载
            List<PermissionDTO> permissionTree = permissionRepository.findAllAndNotDeleted(!isSystem).stream().map(permissionMapper::toDto).toList();
            List<MenuDTO> menuList = convertPermissionsToMenusOptimized(permissionTree);

            // 将结果存入Redis缓存，设置7天过期时间
            redissonClient.getBucket(cacheKey).set(menuList);
            redissonClient.getBucket(cacheKey).expire(Duration.ofDays(NumberConstants.SEVEN));
            LOG.debug("Cached menu data for key: {}, size: {}", cacheKey, menuList.size());
            return menuList;

        } catch (Exception e) {
            LOG.error("Error accessing Redisson cache for menu data, falling back to database query", e);
            // Redis异常时降级到数据库查询
            List<PermissionDTO> permissionTree = permissionRepository.findAllAndNotDeleted(!isSystem)
                .stream()
                .map(permissionMapper::toDto)
                .toList();
            return convertPermissionsToMenus(permissionTree);
        }
    }

    @Override
    public List<MenuDTO> findMenuTreeByByRoleId(Long roleId) {
        LOG.debug("Request to get menu tree by role ID : {}", roleId);
        //查询角色对应的权限
        List<RolePermission> permissionList = rolePermissionRepository.findByRoleId(roleId);
        if (CollectionUtils.isEmpty(permissionList)) {
            return new ArrayList<>();
        }
        List<PermissionDTO> permissionTree = permissionList.stream().map(rolePermission -> permissionMapper.toDto(rolePermission.getPermission())).toList();
        // 将权限转换为菜单数据
        return convertPermissionsToMenus(permissionTree);
    }

    @Override
    @Transactional
    public List<MenuDTO> saveAll(List<MenuDTO> list) {
        LOG.debug("Request to batch save Menu with hierarchical structure: {}", list.size());

        if (list.isEmpty()) {
            return new ArrayList<>();
        }

        Long tenantId = Long.valueOf(HttpRequestUtil.getHeader(request, RequestConstants.X_TENANT_ID));
        String username = HttpRequestUtil.getHeader(request, RequestConstants.X_USER_NAME);
        Instant now = Instant.now();

        List<MenuDTO> result = new ArrayList<>();

        // 使用Map来存储已保存的菜单，便于子菜单查找父菜单
        Map<String, MenuDTO> savedMenuMap = new HashMap<>();

        try {
            // 递归保存菜单树
            for (MenuDTO menuDTO : list) {
                MenuDTO savedMenu = saveMenuHierarchy(menuDTO, null, tenantId, username, now, savedMenuMap);
                result.add(savedMenu);
            }

            LOG.info("Successfully saved {} menu hierarchies with total {} menus", list.size(), savedMenuMap.size());

        } catch (Exception e) {
            LOG.error("Failed to save menu hierarchies: {}", e.getMessage(), e);
            throw new RuntimeException("批量保存菜单失败: " + e.getMessage(), e);
        }

        return result;
    }

    /**
     * 递归保存菜单层次结构
     *
     * @param menuDTO          要保存的菜单DTO
     * @param parentPermission 父级权限对象
     * @param tenantId         租户ID
     * @param username         用户名
     * @param now              当前时间
     * @param savedMenuMap     已保存菜单的映射表
     * @return 保存后的菜单DTO
     */
    private MenuDTO saveMenuHierarchy(MenuDTO menuDTO, Permission parentPermission, Long tenantId, String username, Instant now, Map<String, MenuDTO> savedMenuMap) {

        LOG.debug("Saving menu: {} with parent: {}", menuDTO.getName(), parentPermission != null ? parentPermission.getName() : "null");

        // 检查菜单名称是否已存在
        Permission existingPermission = permissionRepository.findByName(menuDTO.getName());
        if (existingPermission != null) {
            LOG.warn("Menu with name '{}' already exists in tenant {}, skipping", menuDTO.getName(), tenantId);
            throw new IllegalArgumentException("菜单名称 '" + menuDTO.getName() + "' 已存在");
        }

        // 创建并保存菜单级权限
        Permission permission = createMenuPermission(menuDTO, parentPermission, tenantId, username, now);
        permission = permissionRepository.save(permission);

        // 保存按钮级权限
        saveButtonPermissions(menuDTO, permission, tenantId, username, now);

        // 设置返回的菜单ID
        menuDTO.setId(permission.getId());

        // 将已保存的菜单添加到映射表中
        savedMenuMap.put(menuDTO.getName(), menuDTO);

        // 递归保存子菜单
        if (CollectionUtils.isNotEmpty(menuDTO.getChildren())) {
            LOG.debug("Saving {} child menus for parent: {}", menuDTO.getChildren().size(), menuDTO.getName());

            List<MenuDTO> savedChildren = new ArrayList<>();
            for (MenuDTO childMenu : menuDTO.getChildren()) {
                MenuDTO savedChild = saveMenuHierarchy(childMenu, permission, tenantId, username, now, savedMenuMap);
                savedChildren.add(savedChild);
            }
            menuDTO.setChildren(savedChildren);
        }

        LOG.debug("Successfully saved menu: {} with ID: {}", menuDTO.getName(), permission.getId());
        return menuDTO;
    }

    /**
     * 创建菜单权限对象
     */
    private Permission createMenuPermission(MenuDTO menuDTO, Permission parentPermission, Long tenantId, String username, Instant now) {
        Permission permission = new Permission();
        permission.setParent(parentPermission);
        permission.setTenantId(tenantId);
        permission.setName(menuDTO.getName());
        permission.setRedirect(menuDTO.getRedirect());
        permission.setResourceType(ResourceType.MENU);
        permission.setFrontendRoute(menuDTO.getPath());
        permission.setComponent(menuDTO.getComponent());
        permission.setSortOrder(menuDTO.getSort());
        permission.setDescription(menuDTO.getRealName());
        permission.setMetadata(menuDTO.getMeta());
        permission.setVersion(NumberConstants.ONE);
        permission.setCreatedBy(username);
        permission.setUpdatedBy(username);
        permission.setCreatedAt(now);
        permission.setUpdatedAt(now);
        permission.setIsDeleted(Boolean.FALSE);
        // 默认为可用
        permission.setIsAvailable(menuDTO.getIsAvailable() != null ? menuDTO.getIsAvailable() : Boolean.TRUE);

        return permission;
    }

    /**
     * 保存按钮权限
     */
    private void saveButtonPermissions(MenuDTO menuDTO, Permission menuPermission, Long tenantId, String username, Instant now) {
        if (CollectionUtils.isNotEmpty(menuDTO.getAuths())) {
            LOG.debug("Saving {} button permissions for menu: {}", menuDTO.getAuths().size(), menuDTO.getName());

            List<Permission> buttonPermissions = new ArrayList<>();
            for (ButtonDTO button : menuDTO.getAuths()) {
                Permission buttonPermission = new Permission();
                buttonPermission.setTenantId(tenantId);
                buttonPermission.setName(button.getName());
                buttonPermission.setFrontendRoute(button.getValue());
                buttonPermission.setResourceType(ResourceType.BUTTON);
                buttonPermission.setDescription(menuDTO.getRealName());
                buttonPermission.setParent(menuPermission);
                buttonPermission.setVersion(NumberConstants.ONE);
                buttonPermission.setCreatedBy(username);
                buttonPermission.setUpdatedBy(username);
                buttonPermission.setCreatedAt(now);
                buttonPermission.setUpdatedAt(now);
                buttonPermission.setIsDeleted(Boolean.FALSE);
                buttonPermission.setIsAvailable(menuDTO.getIsAvailable() != null ? menuDTO.getIsAvailable() : Boolean.TRUE);
                buttonPermissions.add(buttonPermission);
            }

            permissionRepository.saveAll(buttonPermissions);
            LOG.debug("Successfully saved {} button permissions", buttonPermissions.size());
        }
    }

    /**
     * 获取员工的所有权限
     */
    public List<PermissionDTO> findAllPermissionsByEmployeeId(Long employeeId) {
        LOG.debug("Request to get all permissions for employee: {}", employeeId);

        // 获取员工信息
        EmployeeDTO employee = employeeService.findOne(employeeId).orElseThrow(() -> new RuntimeException("员工未找到，ID: " + employeeId));

        Long tenantId = employee.getTenantId();

        // 检查员工是否为管理员
        List<EmployeeRoleDTO> employeeRoles = employeeRoleService.findByEmployeeId(employeeId);
        boolean isAdmin = employeeRoles.stream().anyMatch(role -> "ADMIN".equals(role.getRole().getCode()) || "SUPER_ADMIN".equals(role.getRole().getCode()) || "TENANT_ADMIN".equals(role.getRole().getCode()));

        List<Permission> allPermissions;

        if (isAdmin) {
            // 如果是管理员，返回该租户的所有权限
            LOG.debug("Employee {} is admin, returning all tenant permissions", employeeId);
            Boolean isSystem = isSystem(tenantId);
            String header = HttpRequestUtil.getHeader(request, RequestConstants.X_SYSTEM);
            String system = StringUtils.isNotBlank(header) ? header : SystemConstant.ENTERPRISE_BACKEND;
            if (SystemConstant.ENTERPRISE_BACKEND.equals(system)) {
                //企业后台，系统租户也可以获取相应菜单
                isSystem = Boolean.FALSE;
            }
            allPermissions = permissionRepository.findByIsAvailable(!isSystem);
        } else {
            // 如果不是管理员，返回员工拥有的权限
            LOG.debug("Employee {} is not admin, returning employee permissions", employeeId);
            allPermissions = permissionRepository.findByEmployeeId(employeeId);
        }
        return permissionMapper.toDto(allPermissions);
    }

    /**
     * 优化的权限转换为菜单数据方法
     */
    public List<MenuDTO> convertPermissionsToMenusOptimized(List<PermissionDTO> permissions) {
        LOG.debug("Converting {} permissions to menu data (optimized)", permissions.size());

        if (CollectionUtils.isEmpty(permissions)) {
            return new ArrayList<>();
        }

        Map<Long, List<PermissionDTO>> childrenMap = permissions.stream()
            .filter(p -> p.getParent() != null)
            .collect(Collectors.groupingBy(p -> p.getParent().getId()));

        // 获取顶级菜单权限
        List<PermissionDTO> rootMenus = permissions.stream()
            .filter(p -> p.getParent() == null && p.getResourceType() == ResourceType.MENU)
            .sorted(Comparator.comparing(PermissionDTO::getSortOrder, Comparator.nullsLast(Comparator.naturalOrder())))
            .collect(Collectors.toList());

        // 构建菜单树
        return buildMenuTreeOptimized(rootMenus, childrenMap);
    }

    /**
     * 将权限列表转换为菜单数据
     */
    public List<MenuDTO> convertPermissionsToMenus(List<PermissionDTO> permissions) {
        LOG.debug("Converting {} permissions to menu data", permissions.size());

        // 按父ID分组所有权限
        Map<Long, List<PermissionDTO>> permissionMap = permissions.stream().collect(Collectors.groupingBy(p -> p.getParent() == null ? 0L : p.getParent().getId()));

        // 获取顶级菜单权限（parentId为null的菜单）
        List<PermissionDTO> rootPermissions = permissions.stream().filter(p -> p.getParent() == null && p.getResourceType() == ResourceType.MENU).collect(Collectors.toList());

        // 递归构建菜单树
        return buildMenuTreeRecursively(rootPermissions, permissionMap);
    }

    /**
     * 递归构建菜单树形结构
     */
    private List<MenuDTO> buildMenuTreeRecursively(List<PermissionDTO> menuPermissions, Map<Long, List<PermissionDTO>> permissionMap) {
        List<MenuDTO> menus = new ArrayList<>();

        for (PermissionDTO permission : menuPermissions) {
            MenuDTO menu = new MenuDTO();
            menu.setId(permission.getId());
            menu.setParentId(permission.getParent() == null ? 0L : permission.getParent().getId());
            menu.setPath(permission.getFrontendRoute());
            menu.setComponent(permission.getComponent());
            menu.setName(permission.getName());
            menu.setIsMenu(true);
            menu.setSort(permission.getSortOrder());
            menu.setMark(NumberConstants.ONE);
            menu.setMeta(permission.getMetadata());

            // 获取当前菜单的子权限
            List<PermissionDTO> childPermissions = permissionMap.getOrDefault(permission.getId(), new ArrayList<>());

            // 分离子菜单和按钮权限
            List<PermissionDTO> childMenus = childPermissions.stream().filter(p -> p.getResourceType() == ResourceType.MENU).collect(Collectors.toList());

            List<PermissionDTO> childButtons = childPermissions.stream().filter(p -> p.getResourceType() == ResourceType.BUTTON).toList();

            // 递归处理子菜单
            if (!childMenus.isEmpty()) {
                List<MenuDTO> children = buildMenuTreeRecursively(childMenus, permissionMap);
                menu.setChildren(children);
            } else {
                menu.setChildren(new ArrayList<>());
            }

            // 处理按钮权限
            List<ButtonDTO> auths = childButtons.stream().map(buttonPermission -> {
                ButtonDTO button = new ButtonDTO();
                button.setId(buttonPermission.getId());
                button.setName(buttonPermission.getName());
                button.setValue(buttonPermission.getFrontendRoute());
                return button;
            }).collect(Collectors.toList());
            menu.setAuths(auths);

            menus.add(menu);
        }

        return menus;
    }

    /**
     * 优化的菜单树构建方法
     *
     * @param menuPermissions 菜单权限列表
     * @param childrenMap     子权限映射
     * @return 菜单DTO列表
     */
    private List<MenuDTO> buildMenuTreeOptimized(List<PermissionDTO> menuPermissions, Map<Long, List<PermissionDTO>> childrenMap) {
        List<MenuDTO> menus = new ArrayList<>(menuPermissions.size());

        for (PermissionDTO permission : menuPermissions) {
            MenuDTO menu = createMenuDTO(permission);

            // 获取子权限
            List<PermissionDTO> children = childrenMap.getOrDefault(permission.getId(), Collections.emptyList());

            if (!children.isEmpty()) {
                // 分离菜单和按钮权限
                List<PermissionDTO> childMenus = new ArrayList<>();
                List<ButtonDTO> buttons = new ArrayList<>();

                for (PermissionDTO child : children) {
                    if (child.getResourceType() == ResourceType.MENU) {
                        childMenus.add(child);
                    } else if (child.getResourceType() == ResourceType.BUTTON) {
                        buttons.add(createButtonDTO(child));
                    }
                }

                // 排序子菜单
                childMenus.sort(Comparator.comparing(PermissionDTO::getSortOrder, Comparator.nullsLast(Comparator.naturalOrder())));

                // 递归构建子菜单
                if (!childMenus.isEmpty()) {
                    menu.setChildren(buildMenuTreeOptimized(childMenus, childrenMap));
                } else {
                    menu.setChildren(new ArrayList<>());
                }

                // 设置按钮权限
                menu.setAuths(buttons);
            } else {
                menu.setChildren(new ArrayList<>());
                menu.setAuths(new ArrayList<>());
            }

            menus.add(menu);
        }

        return menus;
    }

    /**
     * 创建MenuDTO对象
     *
     * @param permission 权限DTO
     * @return 菜单DTO
     */
    private MenuDTO createMenuDTO(PermissionDTO permission) {
        MenuDTO menu = new MenuDTO();
        menu.setId(permission.getId());
        menu.setParentId(permission.getParent() == null ? 0L : permission.getParent().getId());
        menu.setPath(permission.getFrontendRoute());
        menu.setComponent(permission.getComponent());
        menu.setName(permission.getName());
        menu.setIsMenu(true);
        menu.setSort(permission.getSortOrder());
        menu.setMark(NumberConstants.ONE);
        menu.setMeta(permission.getMetadata());
        menu.setIsShow(permission.getIsShow());
        return menu;
    }

    /**
     * 创建ButtonDTO对象
     *
     * @param permission 权限DTO
     * @return 按钮DTO
     */
    private ButtonDTO createButtonDTO(PermissionDTO permission) {
        ButtonDTO button = new ButtonDTO();
        button.setId(permission.getId());
        button.setName(permission.getName());
        button.setValue(permission.getFrontendRoute());
        return button;
    }

    /**
     * 生成菜单缓存键
     *
     * @param tenantId 租户ID
     * @param system   系统类型
     * @return 缓存键
     */
    private String generateMenuCacheKey(Long tenantId, String system) {
        return String.format("menu:all:tenant:%d:system:%s", tenantId, system);
    }

}

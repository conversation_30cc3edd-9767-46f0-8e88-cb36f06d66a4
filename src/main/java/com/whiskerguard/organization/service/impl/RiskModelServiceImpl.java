package com.whiskerguard.organization.service.impl;

import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.whiskerguard.common.config.RequestConstants;
import com.whiskerguard.common.constant.NumberConstants;
import com.whiskerguard.common.dto.ai.*;
import com.whiskerguard.common.enums.FeatureCode;
import com.whiskerguard.common.util.HttpRequestUtil;
import com.whiskerguard.organization.client.ai.AiServiceFeignClient;
import com.whiskerguard.organization.domain.RiskCategory;
import com.whiskerguard.organization.domain.RiskModel;
import com.whiskerguard.organization.domain.RiskRule;
import com.whiskerguard.organization.repository.RiskCategoryRepository;
import com.whiskerguard.organization.repository.RiskModelRepository;
import com.whiskerguard.organization.repository.RiskRuleRepository;
import com.whiskerguard.organization.request.RiskModelReq;
import com.whiskerguard.organization.service.RiskModelService;
import com.whiskerguard.organization.service.dto.RiskCategoryDTO;
import com.whiskerguard.organization.service.dto.RiskModelDTO;
import com.whiskerguard.organization.service.mapper.RiskCategoryMapper;
import com.whiskerguard.organization.service.mapper.RiskModelMapper;
import com.whiskerguard.organization.web.rest.errors.BadRequestAlertException;
import jakarta.servlet.http.HttpServletRequest;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Example;
import org.springframework.data.domain.ExampleMatcher;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 描述：风险模型的服务实现类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/18
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class RiskModelServiceImpl implements RiskModelService {

    private static final Logger LOG = LoggerFactory.getLogger(RiskModelServiceImpl.class);

    private final RiskModelRepository riskModelRepository;

    private final RiskModelMapper riskModelMapper;

    private final HttpServletRequest request;

    private final RiskCategoryRepository riskCategoryRepository;

    private final RiskCategoryMapper riskCategoryMapper;

    private final RiskRuleRepository riskRuleRepository;

    private final AiServiceFeignClient aiServiceFeignClient;

    public RiskModelServiceImpl(RiskModelRepository riskModelRepository, RiskModelMapper riskModelMapper, HttpServletRequest request,
                                RiskCategoryRepository riskCategoryRepository, RiskCategoryMapper riskCategoryMapper,
                                RiskRuleRepository riskRuleRepository, AiServiceFeignClient aiServiceFeignClient) {
        this.riskModelRepository = riskModelRepository;
        this.riskModelMapper = riskModelMapper;
        this.request = request;
        this.riskCategoryRepository = riskCategoryRepository;
        this.riskCategoryMapper = riskCategoryMapper;
        this.riskRuleRepository = riskRuleRepository;
        this.aiServiceFeignClient = aiServiceFeignClient;
    }

    @Override
    public RiskModelDTO save(RiskModelDTO riskModelDTO) {
        LOG.debug("Request to save RiskModel : {}", riskModelDTO);
        Long tenantId = Long.valueOf(HttpRequestUtil.getHeader(request, RequestConstants.X_TENANT_ID));
        //判断当前风险模型是否已经存在，如果存在则抛出异常
        if (null != riskModelRepository.findByNameAndTenantId(riskModelDTO.getName(), tenantId)) {
            throw new BadRequestAlertException("风险模型已经存在", "riskModel", "riskModel exists");
        }
        String username = HttpRequestUtil.getHeader(request, RequestConstants.X_USER_NAME);
        RiskModel riskModel = riskModelMapper.toEntity(riskModelDTO);
        riskModel.setTenantId(tenantId);
        riskModel.setVersion(NumberConstants.ONE);
        riskModel.setCreatedBy(username);
        riskModel.setUpdatedBy(username);
        riskModel.setCreatedAt(Instant.now());
        riskModel.setUpdatedAt(Instant.now());
        riskModel.setIsDeleted(Boolean.FALSE);
        riskModel = riskModelRepository.save(riskModel);
        //添加风险类别
        if (CollectionUtils.isNotEmpty(riskModelDTO.getCategories())) {
            for (RiskCategoryDTO riskCategoryDTO : riskModelDTO.getCategories()) {
                //判断当前风险类别是否已经存在，如果存在则抛出异常
                if (null != riskCategoryRepository.findByNameAndTenantId(riskCategoryDTO.getName(), tenantId)) {
                    throw new BadRequestAlertException("风险类别已经存在", "riskCategory", "riskCategory exists");
                }
                RiskCategory riskCategory = riskCategoryMapper.toEntity(riskCategoryDTO);
                riskCategory.setTenantId(tenantId);
                riskCategory.setVersion(NumberConstants.ONE);
                riskCategory.setCreatedBy(username);
                riskCategory.setUpdatedBy(username);
                riskCategory.setCreatedAt(Instant.now());
                riskCategory.setUpdatedAt(Instant.now());
                riskCategory.setIsDeleted(Boolean.FALSE);
                riskCategory.setRiskModel(riskModel);
                riskCategoryRepository.save(riskCategory);
            }
        }
        return riskModelMapper.toDto(riskModel);
    }

    @Override
    public Optional<RiskModelDTO> partialUpdate(RiskModelDTO riskModelDTO) {
        LOG.debug("Request to partially update RiskModel : {}", riskModelDTO);
        Long tenantId = Long.valueOf(HttpRequestUtil.getHeader(request, RequestConstants.X_TENANT_ID));
        //判断当前风险模型是否已经存在，如果存在则抛出异常
        RiskModel riskModel = riskModelRepository.findByNameAndTenantId(riskModelDTO.getName(), tenantId);
        if (null != riskModel && !riskModel.getId().equals(riskModelDTO.getId())) {
            throw new BadRequestAlertException("风险模型已经存在", "riskModel", "riskModel exists");
        }
        Optional<RiskModel> optional = riskModelRepository.findById(riskModelDTO.getId());
        riskModel = optional.orElseThrow(() -> new BadRequestAlertException("风险模型不存在", "riskModel", "riskModel not found"));
        riskModelMapper.partialUpdate(riskModel, riskModelDTO);
        String username = HttpRequestUtil.getHeader(request, RequestConstants.X_USER_NAME);
        riskModel.setUpdatedBy(username);
        riskModel.setUpdatedAt(Instant.now());
        riskModelRepository.save(riskModel);
        //更新风险类别
        if (CollectionUtils.isNotEmpty(riskModelDTO.getCategories())) {
            for (RiskCategoryDTO riskCategoryDTO : riskModelDTO.getCategories()) {
                //判断当前风险类别是否已经存在，如果存在则抛出异常
                RiskCategory riskCategory = riskCategoryRepository.findByNameAndTenantId(riskCategoryDTO.getName(), tenantId);
                if (null != riskCategory && !riskCategory.getId().equals(riskCategoryDTO.getId())) {
                    throw new BadRequestAlertException("风险类别已经存在", "riskCategory", "riskCategory exists");
                }
                if (null != riskCategoryDTO.getId()) {
                    riskCategory = riskCategoryRepository.findById(riskCategoryDTO.getId()).orElseThrow(() -> new BadRequestAlertException("风险类别不存在", "riskCategory", "riskCategory not found"));
                    riskCategoryMapper.partialUpdate(riskCategory, riskCategoryDTO);
                    riskCategory.setUpdatedBy(username);
                    riskCategory.setUpdatedAt(Instant.now());
                    riskCategoryRepository.save(riskCategory);
                } else {
                    riskCategory = riskCategoryMapper.toEntity(riskCategoryDTO);
                    riskCategory.setTenantId(tenantId);
                    riskCategory.setVersion(NumberConstants.ONE);
                    riskCategory.setCreatedBy(username);
                    riskCategory.setUpdatedBy(username);
                    riskCategory.setCreatedAt(Instant.now());
                    riskCategory.setUpdatedAt(Instant.now());
                    riskCategory.setIsDeleted(Boolean.FALSE);
                    riskCategory.setRiskModel(riskModel);
                    riskCategoryRepository.save(riskCategory);
                }
            }
        }
        return Optional.of(riskModelMapper.toDto(riskModel));
    }

    @Override
    @Transactional(readOnly = true)
    public Page<RiskModelDTO> findAll(RiskModelReq req) {
        Long tenantId = Long.valueOf(HttpRequestUtil.getHeader(request, RequestConstants.X_TENANT_ID));
        LOG.debug("Request to get all RiskModels by condition: {}", req);

        // 创建分页参数
        Pageable pageable = createPageable(req);

        // 解析日期范围
        Instant startDate = parseDate(req.getCreatedAtStart());
        Instant endDate = parseDate(req.getCreatedAtEnd());

        // 如果结束日期存在，设置为当天的23:59:59
        if (endDate != null) {
            endDate = endDate.plusSeconds(86399); // 加上23小时59分59秒
        }

        // 使用自定义查询方法处理所有条件
        Page<RiskModel> riskModelPage = riskModelRepository.findByCondition(
            tenantId,
            req.getName(),
            startDate,
            endDate,
            pageable
        );

        // 转换为DTO并返回
        return riskModelPage.map(riskModelMapper::toDto);
    }

    /**
     * 使用Example进行简单条件查询的示例方法
     * 适用于简单的精确匹配和模糊匹配场景
     */
    public Page<RiskModelDTO> findAllUsingExample(RiskModelReq req) {
        Long tenantId = Long.valueOf(HttpRequestUtil.getHeader(request, RequestConstants.X_TENANT_ID));
        LOG.debug("Request to get all RiskModels using Example: {}", req);

        // 创建RiskModel实体作为Example的probe（查询模板）
        RiskModel probe = new RiskModel();
        probe.setTenantId(tenantId);
        probe.setIsDeleted(false);

        // 创建ExampleMatcher，配置匹配规则
        ExampleMatcher matcher = ExampleMatcher.matching()
            .withIgnoreNullValues() // 忽略null值字段
            .withIgnoreCase() // 忽略大小写
            .withStringMatcher(ExampleMatcher.StringMatcher.CONTAINING); // 字符串包含匹配

        // 如果有名称搜索，设置到name字段进行模糊匹配
        if (StringUtils.hasText(req.getName())) {
            probe.setName(req.getName());
            // 为name字段配置特定的匹配规则
            matcher = matcher.withMatcher("name",
                ExampleMatcher.GenericPropertyMatchers.contains().ignoreCase());
        }

        // 创建Example对象
        Example<RiskModel> example = Example.of(probe, matcher);

        // 创建分页参数
        Pageable pageable = createPageable(req);

        // 使用Example查询
        Page<RiskModel> riskModelPage = riskModelRepository.findAll(example, pageable);

        // 转换为DTO并返回
        return riskModelPage.map(riskModelMapper::toDto);
    }

    /**
     * 创建分页参数
     */
    private Pageable createPageable(RiskModelReq req) {
        int page = req.getPage() != null ? req.getPage() : 0;
        int size = req.getSize() != null ? req.getSize() : NumberConstants.TEN;

        // 默认按创建时间倒序排列
        Sort sort = Sort.by(Sort.Direction.DESC, "createdAt");

        return PageRequest.of(page, size, sort);
    }

    /**
     * 解析日期字符串为Instant
     */
    private Instant parseDate(String dateStr) {
        if (!StringUtils.hasText(dateStr)) {
            return null;
        }

        try {
            // 假设日期格式为 yyyy-MM-dd
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            LocalDate localDate = LocalDate.parse(dateStr, formatter);
            return localDate.atStartOfDay(ZoneId.systemDefault()).toInstant();
        } catch (Exception e) {
            LOG.warn("日期解析失败: {}", dateStr, e);
            return null;
        }
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<RiskModelDTO> findOne(Long id) {
        LOG.debug("Request to get RiskModel : {}", id);
        Optional<RiskModelDTO> optional = riskModelRepository.findById(id).map(riskModelMapper::toDto);
        RiskModelDTO riskModel = optional.orElseThrow(() -> new BadRequestAlertException("风险模型不存在", "riskModel", "riskModel not found"));
        //查询风险类别
        List<RiskCategory> riskCategories = riskCategoryRepository.findByRiskModelId(id);
        riskModel.setCategories(riskCategoryMapper.toDto(riskCategories));
        return Optional.of(riskModel);
    }

    @Override
    public void delete(Long id) {
        LOG.debug("Request to delete RiskModel : {}", id);
        riskModelRepository.findById(id).ifPresent(riskModel -> {
            riskModel.setIsDeleted(Boolean.TRUE);
            riskModelRepository.save(riskModel);
        });
        //删除风险类别
        riskCategoryRepository.findByRiskModelId(id).forEach(riskCategory -> {
            riskCategory.setIsDeleted(Boolean.TRUE);
            riskCategoryRepository.save(riskCategory);
            //删除风险规则
            riskRuleRepository.findByRiskCategoryId(riskCategory.getId()).forEach(riskRule -> {
                riskRule.setIsDeleted(Boolean.TRUE);
                riskRuleRepository.save(riskRule);
            });
        });
    }

    @Override
    public String analyzeRiskModel(String content) {
        Long tenantId = Long.valueOf(HttpRequestUtil.getHeader(request, RequestConstants.X_TENANT_ID));
        LOG.debug("Request to analyze RiskModel by tenant ID: {}", tenantId);
        //查询正在使用的模型
        List<RiskModel> list = riskModelRepository.findByTenantIdAndIsDefault(tenantId, Boolean.TRUE);
        if (CollectionUtils.isEmpty(list)) {
            return "";
        }

        // 组装风险模型数据为JSON数组
        Gson gson = new Gson();
        JsonArray riskModelArray = new JsonArray();

        for (RiskModel riskModel : list) {
            JsonObject riskModelJson = new JsonObject();
            riskModelJson.addProperty("name", riskModel.getName());
            riskModelJson.addProperty("description", riskModel.getDescription());

            //查询风险类别
            List<RiskCategory> riskCategories = riskCategoryRepository.findByRiskModelId(riskModel.getId());
            if (CollectionUtils.isEmpty(riskCategories)) {
                continue;
            }
            JsonArray categoriesArray = new JsonArray();

            for (RiskCategory riskCategory : riskCategories) {
                JsonObject categoryJson = new JsonObject();
                categoryJson.addProperty("name", riskCategory.getName());
                categoryJson.addProperty("level", riskCategory.getLevel().name());
                categoryJson.addProperty("description", riskCategory.getDescription());
                categoryJson.addProperty("expression", riskCategory.getExpression());
                //查询风险规则
                List<RiskRule> riskRules = riskRuleRepository.findByRiskCategoryId(riskCategory.getId());
                if (CollectionUtils.isEmpty(riskRules)) {
                    continue;
                }
                JsonArray rulesArray = new JsonArray();

                for (RiskRule riskRule : riskRules) {
                    JsonObject ruleJson = new JsonObject();
                    ruleJson.addProperty("name", riskRule.getName());
                    ruleJson.addProperty("description", riskRule.getDescription());
                    ruleJson.addProperty("ruleType", riskRule.getRuleType().name());
                    ruleJson.addProperty("params", riskRule.getParams());
                    ruleJson.addProperty("score", riskRule.getScore());
                    rulesArray.add(ruleJson);
                }
                categoryJson.add("rules", rulesArray);
                categoriesArray.add(categoryJson);
            }
            riskModelJson.add("categories", categoriesArray);
            riskModelArray.add(riskModelJson);
        }

        String riskModelJsonString = gson.toJson(riskModelArray);
        String prompt = "请根据以下风险模型进行分析：" + riskModelJsonString + "，当前内容：" + content +
            "属于风险模型中的哪一种风险等级，如果都不属于，请给出一个结合内容的自定义风险等级，不需要分析过程和内容，" +
            "直接返回，数据格式为：{\"riskLevel\": \"xxx\", \"reason\": \"xxx\"}，riskLevel为HIGH/MEDIUM/LOW，reason为分析原因";

        //调用AI服务进行分析
        AiInvocationRequestDTO aiRequest = new AiInvocationRequestDTO();
        aiRequest.setToolKey("deepseek");
        aiRequest.setPrompt(prompt);
        aiRequest.setTenantId(tenantId);
        String employeeId = HttpRequestUtil.getHeader(request, RequestConstants.X_USER_ID);
        aiRequest.setEmployeeId(Long.valueOf(employeeId));

        //设置提示词模板
        Map<String, Object> metadata = new HashMap<>();
        metadata.put("aiModel", "deepseek");
        metadata.put("templateKey", PromptTemplateKey.GENERAL_ANALYSIS.getKey());
        metadata.put("templateType", PromptTemplateType.GENERAL_ANALYSIS.name());
        metadata.put("templateVariables", Map.of("content_risk_model", prompt));
        metadata.put("useTemplate", Boolean.TRUE);
        metadata.put("featureCode", FeatureCode.AI_THREE_LIST_FEATURE.getCode());
        aiRequest.setMetadata(metadata);
        LOG.info("AI分析风险等级请求: {}", aiRequest);

        ResponseEntity<AiRequestDTO> response = aiServiceFeignClient.invokeAi(aiRequest);
        LOG.info("AI分析结果: {}", response);
        if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
            AiRequestDTO aiRequestDTO = response.getBody();
            if (RequestStatus.SUCCESS.equals(aiRequestDTO.getStatus())) {
                return aiRequestDTO.getResponse();
            }
        }
        return "";
    }
}

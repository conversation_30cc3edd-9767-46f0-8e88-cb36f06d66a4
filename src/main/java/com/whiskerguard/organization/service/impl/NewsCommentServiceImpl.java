package com.whiskerguard.organization.service.impl;

import com.whiskerguard.common.config.RequestConstants;
import com.whiskerguard.common.constant.NumberConstants;
import com.whiskerguard.common.util.HttpRequestUtil;
import com.whiskerguard.organization.domain.NewsComment;
import com.whiskerguard.organization.repository.NewsCommentRepository;
import com.whiskerguard.organization.service.NewsCommentService;
import com.whiskerguard.organization.service.dto.NewsCommentDTO;
import com.whiskerguard.organization.service.mapper.NewsCommentMapper;
import jakarta.servlet.http.HttpServletRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.Optional;

/**
 * 描述：新闻评论的服务实现类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/18
 */
@Service
@Transactional
public class NewsCommentServiceImpl implements NewsCommentService {

    private static final Logger LOG = LoggerFactory.getLogger(NewsCommentServiceImpl.class);

    private final NewsCommentRepository newsCommentRepository;

    private final NewsCommentMapper newsCommentMapper;

    private final HttpServletRequest request;

    public NewsCommentServiceImpl(NewsCommentRepository newsCommentRepository, NewsCommentMapper newsCommentMapper,
                                  HttpServletRequest request) {
        this.newsCommentRepository = newsCommentRepository;
        this.newsCommentMapper = newsCommentMapper;
        this.request = request;
    }

    @Override
    public NewsCommentDTO save(NewsCommentDTO newsCommentDTO) {
        LOG.debug("Request to save NewsComment : {}", newsCommentDTO);
        NewsComment newsComment = newsCommentMapper.toEntity(newsCommentDTO);
        String username = HttpRequestUtil.getHeader(request, RequestConstants.X_USER_NAME);
        newsComment.setTenantId(Long.valueOf(HttpRequestUtil.getHeader(request, RequestConstants.X_TENANT_ID)));
        newsComment.setVersion(NumberConstants.ONE);
        newsComment.setCreatedBy(username);
        newsComment.setUpdatedBy(username);
        newsComment.setCreatedAt(Instant.now());
        newsComment.setUpdatedAt(Instant.now());
        newsComment.setIsDeleted(Boolean.FALSE);
        newsComment = newsCommentRepository.save(newsComment);
        return newsCommentMapper.toDto(newsComment);
    }

    @Override
    public NewsCommentDTO update(NewsCommentDTO newsCommentDTO) {
        LOG.debug("Request to update NewsComment : {}", newsCommentDTO);
        NewsComment newsComment = newsCommentMapper.toEntity(newsCommentDTO);
        newsComment = newsCommentRepository.save(newsComment);
        return newsCommentMapper.toDto(newsComment);
    }

    @Override
    public Optional<NewsCommentDTO> partialUpdate(NewsCommentDTO newsCommentDTO) {
        LOG.debug("Request to partially update NewsComment : {}", newsCommentDTO);

        return newsCommentRepository
            .findById(newsCommentDTO.getId())
            .map(existingNewsComment -> {
                newsCommentMapper.partialUpdate(existingNewsComment, newsCommentDTO);
                String username = HttpRequestUtil.getHeader(request, RequestConstants.X_USER_NAME);
                existingNewsComment.setUpdatedBy(username);
                existingNewsComment.setUpdatedAt(Instant.now());
                return existingNewsComment;
            })
            .map(newsCommentRepository::save)
            .map(newsCommentMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<NewsCommentDTO> findAll(Pageable pageable) {
        LOG.debug("Request to get all NewsComments");
        return newsCommentRepository.findByIsDeletedFalse(pageable).map(newsCommentMapper::toDto);
    }

    @Override
    public Page<NewsCommentDTO> findAllWithEagerRelationships(Pageable pageable) {
        return newsCommentRepository.findAllWithEagerRelationshipsAndNotDeleted(pageable).map(newsCommentMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<NewsCommentDTO> findOne(Long id) {
        LOG.debug("Request to get NewsComment : {}", id);
        return newsCommentRepository.findOneWithEagerRelationships(id).map(newsCommentMapper::toDto);
    }

    @Override
    public void delete(Long id) {
        LOG.debug("Request to delete NewsComment : {}", id);
        // 逻辑删除：将isDeleted设置为true，而不是物理删除
        newsCommentRepository
            .findById(id)
            .ifPresent(newsComment -> {
                newsComment.setIsDeleted(true);
                newsCommentRepository.save(newsComment);
            });
    }
}

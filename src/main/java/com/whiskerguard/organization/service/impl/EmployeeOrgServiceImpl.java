package com.whiskerguard.organization.service.impl;

import com.whiskerguard.common.config.RequestConstants;
import com.whiskerguard.common.constant.NumberConstants;
import com.whiskerguard.common.util.HttpRequestUtil;
import com.whiskerguard.common.util.TenantContextUtil;
import com.whiskerguard.organization.domain.EmployeeOrg;
import com.whiskerguard.organization.repository.EmployeeOrgRepository;
import com.whiskerguard.organization.service.EmployeeOrgService;
import com.whiskerguard.organization.service.dto.EmployeeOrgDTO;
import com.whiskerguard.organization.service.mapper.EmployeeMapper;
import com.whiskerguard.organization.service.mapper.EmployeeOrgMapper;
import com.whiskerguard.organization.service.mapper.OrgUnitMapper;
import com.whiskerguard.organization.service.mapper.PositionMapper;
import jakarta.servlet.http.HttpServletRequest;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * 描述：员工组织关系的服务实现类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/18
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class EmployeeOrgServiceImpl implements EmployeeOrgService {

    private static final Logger LOG = LoggerFactory.getLogger(EmployeeOrgServiceImpl.class);

    private final EmployeeOrgRepository employeeOrgRepository;

    private final EmployeeOrgMapper employeeOrgMapper;

    private final HttpServletRequest request;

    private final EmployeeMapper employeeMapper;

    private final OrgUnitMapper orgUnitMapper;

    private final PositionMapper positionMapper;

    public EmployeeOrgServiceImpl(EmployeeOrgRepository employeeOrgRepository, EmployeeOrgMapper employeeOrgMapper, HttpServletRequest request,
                                  EmployeeMapper employeeMapper, OrgUnitMapper orgUnitMapper, PositionMapper positionMapper) {
        this.employeeOrgRepository = employeeOrgRepository;
        this.employeeOrgMapper = employeeOrgMapper;
        this.request = request;
        this.employeeMapper = employeeMapper;
        this.orgUnitMapper = orgUnitMapper;
        this.positionMapper = positionMapper;

    }

    @Override
    public EmployeeOrgDTO save(EmployeeOrgDTO employeeOrgDTO) {
        LOG.debug("Request to save EmployeeOrg : {}", employeeOrgDTO);
        EmployeeOrg employeeOrg = employeeOrgMapper.toEntity(employeeOrgDTO);
        String username = HttpRequestUtil.getHeader(request, RequestConstants.X_USER_NAME);
        employeeOrg.setTenantId(TenantContextUtil.getCurrentTenantId());
        employeeOrg.setVersion(NumberConstants.ONE);
        employeeOrg.setCreatedBy(username);
        employeeOrg.setUpdatedBy(username);
        employeeOrg.setCreatedAt(Instant.now());
        employeeOrg.setUpdatedAt(Instant.now());
        employeeOrg.setIsDeleted(Boolean.FALSE);
        employeeOrg = employeeOrgRepository.save(employeeOrg);
        return employeeOrgMapper.toDto(employeeOrg);
    }

    @Override
    public EmployeeOrgDTO update(EmployeeOrgDTO employeeOrgDTO) {
        LOG.debug("Request to update EmployeeOrg : {}", employeeOrgDTO);
        EmployeeOrg employeeOrg = employeeOrgMapper.toEntity(employeeOrgDTO);
        employeeOrg = employeeOrgRepository.save(employeeOrg);
        return employeeOrgMapper.toDto(employeeOrg);
    }

    @Override
    public Optional<EmployeeOrgDTO> partialUpdate(EmployeeOrgDTO employeeOrgDTO) {
        LOG.debug("Request to partially update EmployeeOrg : {}", employeeOrgDTO);

        return employeeOrgRepository
            .findById(employeeOrgDTO.getId())
            .map(existingEmployeeOrg -> {
                employeeOrgMapper.partialUpdate(existingEmployeeOrg, employeeOrgDTO);
                String username = HttpRequestUtil.getHeader(request, RequestConstants.X_USER_NAME);
                existingEmployeeOrg.setUpdatedBy(username);
                existingEmployeeOrg.setUpdatedAt(Instant.now());
                return existingEmployeeOrg;
            })
            .map(employeeOrgRepository::save)
            .map(employeeOrgMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<EmployeeOrgDTO> findAll(Pageable pageable) {
        LOG.debug("Request to get all EmployeeOrgs");

        // 从request header获取租户ID
        String tenantIdHeader = HttpRequestUtil.getHeader(request, RequestConstants.X_TENANT_ID);
        Long tenantId = Long.valueOf(tenantIdHeader);
        LOG.debug("Filtering employee-org relations by tenant ID: {}", tenantId);
        return employeeOrgRepository.findByTenantIdAndIsDeletedFalse(tenantId, pageable).map(employeeOrgMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<EmployeeOrgDTO> findOne(Long id) {
        LOG.debug("Request to get EmployeeOrg : {}", id);
        return employeeOrgRepository.findById(id).map(employeeOrgMapper::toDto);
    }

    @Override
    public void delete(Long id) {
        LOG.debug("Request to delete EmployeeOrg : {}", id);
        // 逻辑删除：将isDeleted设置为true，而不是物理删除
        employeeOrgRepository
            .findById(id)
            .ifPresent(employeeOrg -> {
                employeeOrg.setIsDeleted(true);
                employeeOrgRepository.save(employeeOrg);
            });
    }

    @Override
    public List<EmployeeOrgDTO> findByEmployees(List<Long> employeeIds) {
        LOG.debug("Request to get EmployeeOrg by Employee : {}", employeeIds);
        List<EmployeeOrg> list = employeeOrgRepository.findByEmployeeIdInAndIsDeletedFalse(employeeIds);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        List<EmployeeOrgDTO> result = new ArrayList<>();
        for (EmployeeOrg employeeOrg : list) {
            EmployeeOrgDTO dto = employeeOrgMapper.toDto(employeeOrg);
            dto.setEmployee(employeeMapper.toDto(employeeOrg.getEmployee()));
            dto.setOrgUnit(orgUnitMapper.toDto(employeeOrg.getOrgUnit()));
            dto.setPosition(positionMapper.toDto(employeeOrg.getPosition()));
            result.add(dto);
        }
        return result;
    }
}

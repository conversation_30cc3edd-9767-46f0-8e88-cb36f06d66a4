package com.whiskerguard.organization.service.impl;

import com.whiskerguard.common.config.RequestConstants;
import com.whiskerguard.common.constant.NumberConstants;
import com.whiskerguard.common.util.HttpRequestUtil;
import com.whiskerguard.organization.domain.TagCategory;
import com.whiskerguard.organization.repository.TagCategoryRepository;
import com.whiskerguard.organization.service.TagCategoryService;
import com.whiskerguard.organization.service.dto.TagCategoryDTO;
import com.whiskerguard.organization.service.mapper.TagCategoryMapper;
import com.whiskerguard.organization.web.rest.errors.BadRequestAlertException;
import jakarta.servlet.http.HttpServletRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.Optional;

/**
 * 描述：标签分类的服务实现类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/18
 */
@Service
@Transactional
public class TagCategoryServiceImpl implements TagCategoryService {

    private static final Logger LOG = LoggerFactory.getLogger(TagCategoryServiceImpl.class);

    private final TagCategoryRepository tagCategoryRepository;

    private final TagCategoryMapper tagCategoryMapper;

    private final HttpServletRequest request;

    public TagCategoryServiceImpl(TagCategoryRepository tagCategoryRepository, TagCategoryMapper tagCategoryMapper,
                                  HttpServletRequest request) {
        this.tagCategoryRepository = tagCategoryRepository;
        this.tagCategoryMapper = tagCategoryMapper;
        this.request = request;
    }

    @Override
    public TagCategoryDTO save(TagCategoryDTO tagCategoryDTO) {
        LOG.debug("Request to save TagCategory : {}", tagCategoryDTO);
        Long tenantId = Long.valueOf(HttpRequestUtil.getHeader(request, RequestConstants.X_TENANT_ID));
        //判断当前标签分类是否已经存在，如果存在则抛出异常
        if (null != tagCategoryRepository.findByNameAndTenantId(tagCategoryDTO.getName(), tenantId)) {
            throw new BadRequestAlertException("标签分类已存在", "tagCategory", "tagCategory exists");
        }
        TagCategory tagCategory = tagCategoryMapper.toEntity(tagCategoryDTO);
        String username = HttpRequestUtil.getHeader(request, RequestConstants.X_USER_NAME);
        tagCategory.setTenantId(tenantId);
        tagCategory.setVersion(NumberConstants.ONE);
        tagCategory.setCreatedBy(username);
        tagCategory.setUpdatedBy(username);
        tagCategory.setCreatedAt(Instant.now());
        tagCategory.setUpdatedAt(Instant.now());
        tagCategory.setIsDeleted(Boolean.FALSE);
        tagCategory = tagCategoryRepository.save(tagCategory);
        return tagCategoryMapper.toDto(tagCategory);
    }

    @Override
    public TagCategoryDTO update(TagCategoryDTO tagCategoryDTO) {
        LOG.debug("Request to update TagCategory : {}", tagCategoryDTO);
        TagCategory tagCategory = tagCategoryMapper.toEntity(tagCategoryDTO);
        tagCategory = tagCategoryRepository.save(tagCategory);
        return tagCategoryMapper.toDto(tagCategory);
    }

    @Override
    public Optional<TagCategoryDTO> partialUpdate(TagCategoryDTO tagCategoryDTO) {
        LOG.debug("Request to partially update TagCategory : {}", tagCategoryDTO);
        Long tenantId = Long.valueOf(HttpRequestUtil.getHeader(request, RequestConstants.X_TENANT_ID));
        //判断当前标签分类是否已经存在，如果存在则抛出异常
        TagCategory tagCategory = tagCategoryRepository.findByNameAndTenantId(tagCategoryDTO.getName(), tenantId);
        if (null != tagCategory && !tagCategory.getId().equals(tagCategoryDTO.getId())) {
            throw new BadRequestAlertException("标签分类已存在", "tagCategory", "tagCategory exists");
        }

        return tagCategoryRepository
            .findById(tagCategoryDTO.getId())
            .map(existingTagCategory -> {
                tagCategoryMapper.partialUpdate(existingTagCategory, tagCategoryDTO);
                String username = HttpRequestUtil.getHeader(request, RequestConstants.X_USER_NAME);
                existingTagCategory.setUpdatedBy(username);
                existingTagCategory.setUpdatedAt(Instant.now());
                return existingTagCategory;
            })
            .map(tagCategoryRepository::save)
            .map(tagCategoryMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<TagCategoryDTO> findAll(Pageable pageable) {
        Long tenantId = Long.valueOf(HttpRequestUtil.getHeader(request, RequestConstants.X_TENANT_ID));
        LOG.debug("Request to get all TagCategories by tenant ID: {}", tenantId);
        return tagCategoryRepository.findByIsDeletedFalse(tenantId, pageable).map(tagCategoryMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<TagCategoryDTO> findOne(Long id) {
        LOG.debug("Request to get TagCategory : {}", id);
        return tagCategoryRepository.findById(id).map(tagCategoryMapper::toDto);
    }

    @Override
    public void delete(Long id) {
        LOG.debug("Request to delete TagCategory : {}", id);
        // 逻辑删除：将isDeleted设置为true，而不是物理删除
        tagCategoryRepository
            .findById(id)
            .ifPresent(tagCategory -> {
                tagCategory.setIsDeleted(true);
                tagCategoryRepository.save(tagCategory);
            });
    }
}

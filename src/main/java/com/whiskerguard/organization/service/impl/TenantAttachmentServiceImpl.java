package com.whiskerguard.organization.service.impl;

import com.whiskerguard.common.config.RequestConstants;
import com.whiskerguard.common.constant.NumberConstants;
import com.whiskerguard.common.util.HttpRequestUtil;
import com.whiskerguard.organization.domain.TenantAttachment;
import com.whiskerguard.organization.repository.TenantAttachmentRepository;
import com.whiskerguard.organization.service.TenantAttachmentService;
import com.whiskerguard.organization.service.dto.TenantAttachmentDTO;
import com.whiskerguard.organization.service.mapper.TenantAttachmentMapper;
import jakarta.servlet.http.HttpServletRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.Optional;

/**
 * 描述：租户附件的服务实现类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/18
 */
@Service
@Transactional
public class TenantAttachmentServiceImpl implements TenantAttachmentService {

    private static final Logger LOG = LoggerFactory.getLogger(TenantAttachmentServiceImpl.class);

    private final TenantAttachmentRepository tenantAttachmentRepository;

    private final TenantAttachmentMapper tenantAttachmentMapper;

    private final HttpServletRequest request;

    public TenantAttachmentServiceImpl(
        TenantAttachmentRepository tenantAttachmentRepository,
        TenantAttachmentMapper tenantAttachmentMapper,
        HttpServletRequest request
    ) {
        this.tenantAttachmentRepository = tenantAttachmentRepository;
        this.tenantAttachmentMapper = tenantAttachmentMapper;
        this.request = request;
    }

    @Override
    public TenantAttachmentDTO save(TenantAttachmentDTO tenantAttachmentDTO) {
        LOG.debug("Request to save TenantAttachment : {}", tenantAttachmentDTO);
        TenantAttachment tenantAttachment = tenantAttachmentMapper.toEntity(tenantAttachmentDTO);
        String username = HttpRequestUtil.getHeader(request, RequestConstants.X_USER_NAME);
        tenantAttachment.setVersion(NumberConstants.ONE);
        tenantAttachment.setUploadedBy(username);
        tenantAttachment.setUploadedAt(Instant.now());
        tenantAttachment.setUpdatedBy(username);
        tenantAttachment.setUpdatedAt(Instant.now());
        tenantAttachment.setIsDeleted(Boolean.FALSE);
        tenantAttachment = tenantAttachmentRepository.save(tenantAttachment);
        return tenantAttachmentMapper.toDto(tenantAttachment);
    }

    @Override
    public TenantAttachmentDTO update(TenantAttachmentDTO tenantAttachmentDTO) {
        LOG.debug("Request to update TenantAttachment : {}", tenantAttachmentDTO);
        TenantAttachment tenantAttachment = tenantAttachmentMapper.toEntity(tenantAttachmentDTO);
        tenantAttachment = tenantAttachmentRepository.save(tenantAttachment);
        return tenantAttachmentMapper.toDto(tenantAttachment);
    }

    @Override
    public Optional<TenantAttachmentDTO> partialUpdate(TenantAttachmentDTO tenantAttachmentDTO) {
        LOG.debug("Request to partially update TenantAttachment : {}", tenantAttachmentDTO);

        return tenantAttachmentRepository
            .findById(tenantAttachmentDTO.getId())
            .map(existingTenantAttachment -> {
                tenantAttachmentMapper.partialUpdate(existingTenantAttachment, tenantAttachmentDTO);
                String username = HttpRequestUtil.getHeader(request, RequestConstants.X_USER_NAME);
                existingTenantAttachment.setUpdatedBy(username);
                existingTenantAttachment.setUpdatedAt(Instant.now());
                return existingTenantAttachment;
            })
            .map(tenantAttachmentRepository::save)
            .map(tenantAttachmentMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<TenantAttachmentDTO> findAll(Pageable pageable) {
        LOG.debug("Request to get all TenantAttachments");
        return tenantAttachmentRepository.findByIsDeletedFalse(pageable).map(tenantAttachmentMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<TenantAttachmentDTO> findOne(Long id) {
        LOG.debug("Request to get TenantAttachment : {}", id);
        return tenantAttachmentRepository.findById(id).map(tenantAttachmentMapper::toDto);
    }

    @Override
    public void delete(Long id) {
        LOG.debug("Request to delete TenantAttachment : {}", id);
        // 逻辑删除：将isDeleted设置为true，而不是物理删除
        tenantAttachmentRepository
            .findById(id)
            .ifPresent(tenantAttachment -> {
                tenantAttachment.setIsDeleted(true);
                tenantAttachmentRepository.save(tenantAttachment);
            });
    }
}

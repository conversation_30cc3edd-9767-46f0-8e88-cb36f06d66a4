package com.whiskerguard.organization.service.impl;

import com.whiskerguard.common.config.RequestConstants;
import com.whiskerguard.common.constant.NumberConstants;
import com.whiskerguard.common.util.HttpRequestUtil;
import com.whiskerguard.organization.domain.EmployeeRole;
import com.whiskerguard.organization.repository.EmployeeRoleRepository;
import com.whiskerguard.organization.service.EmployeeRoleService;
import com.whiskerguard.organization.service.dto.EmployeeRoleDTO;
import com.whiskerguard.organization.service.mapper.EmployeeMapper;
import com.whiskerguard.organization.service.mapper.EmployeeRoleMapper;
import com.whiskerguard.organization.service.mapper.OrgUnitMapper;
import com.whiskerguard.organization.service.mapper.RoleMapper;
import jakarta.servlet.http.HttpServletRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * 描述：员工角色关系的服务实现类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/18
 */
@Service
@Transactional
public class EmployeeRoleServiceImpl implements EmployeeRoleService {

    private static final Logger LOG = LoggerFactory.getLogger(EmployeeRoleServiceImpl.class);

    private final EmployeeRoleRepository employeeRoleRepository;

    private final EmployeeRoleMapper employeeRoleMapper;

    private final HttpServletRequest request;

    private final EmployeeMapper employeeMapper;

    private final RoleMapper roleMapper;

    private final OrgUnitMapper orgUnitMapper;

    public EmployeeRoleServiceImpl(EmployeeRoleRepository employeeRoleRepository, EmployeeRoleMapper employeeRoleMapper,
                                   HttpServletRequest request, EmployeeMapper employeeMapper,
                                   RoleMapper roleMapper, OrgUnitMapper orgUnitMapper
    ) {
        this.employeeRoleRepository = employeeRoleRepository;
        this.employeeRoleMapper = employeeRoleMapper;
        this.request = request;
        this.employeeMapper = employeeMapper;
        this.roleMapper = roleMapper;
        this.orgUnitMapper = orgUnitMapper;
    }

    @Override
    public EmployeeRoleDTO save(EmployeeRoleDTO employeeRoleDTO) {
        LOG.debug("Request to save EmployeeRole : {}", employeeRoleDTO);
        EmployeeRole employeeRole = employeeRoleMapper.toEntity(employeeRoleDTO);
        String username = HttpRequestUtil.getHeader(request, RequestConstants.X_USER_NAME);
        employeeRole.setTenantId(Long.valueOf(HttpRequestUtil.getHeader(request, RequestConstants.X_TENANT_ID)));
        employeeRole.setAssignedBy(username);
        employeeRole.setAssignedAt(Instant.now());
        employeeRole.setVersion(NumberConstants.ONE);
        employeeRole.setCreatedBy(username);
        employeeRole.setUpdatedBy(username);
        employeeRole.setCreatedAt(Instant.now());
        employeeRole.setUpdatedAt(Instant.now());
        employeeRole.setIsDeleted(Boolean.FALSE);
        employeeRole = employeeRoleRepository.save(employeeRole);
        return employeeRoleMapper.toDto(employeeRole);
    }

    @Override
    public EmployeeRoleDTO update(EmployeeRoleDTO employeeRoleDTO) {
        LOG.debug("Request to update EmployeeRole : {}", employeeRoleDTO);
        EmployeeRole employeeRole = employeeRoleMapper.toEntity(employeeRoleDTO);
        employeeRole = employeeRoleRepository.save(employeeRole);
        return employeeRoleMapper.toDto(employeeRole);
    }

    @Override
    public Optional<EmployeeRoleDTO> partialUpdate(EmployeeRoleDTO employeeRoleDTO) {
        LOG.debug("Request to partially update EmployeeRole : {}", employeeRoleDTO);

        return employeeRoleRepository
            .findById(employeeRoleDTO.getId())
            .map(existingEmployeeRole -> {
                employeeRoleMapper.partialUpdate(existingEmployeeRole, employeeRoleDTO);
                String username = HttpRequestUtil.getHeader(request, RequestConstants.X_USER_NAME);
                existingEmployeeRole.setUpdatedBy(username);
                existingEmployeeRole.setUpdatedAt(Instant.now());
                return existingEmployeeRole;
            })
            .map(employeeRoleRepository::save)
            .map(employeeRoleMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<EmployeeRoleDTO> findAll(Pageable pageable) {
        Long tenantId = Long.valueOf(HttpRequestUtil.getHeader(request, RequestConstants.X_TENANT_ID));
        LOG.debug("Filtering employee-roles by tenant ID: {}", tenantId);
        return employeeRoleRepository.findByIsDeletedFalse(tenantId, pageable).map(employeeRoleMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<EmployeeRoleDTO> findOne(Long id) {
        LOG.debug("Request to get EmployeeRole : {}", id);
        return employeeRoleRepository.findById(id).map(employeeRoleMapper::toDto);
    }

    @Override
    public void delete(Long id) {
        LOG.debug("Request to delete EmployeeRole : {}", id);
        // 逻辑删除：将isDeleted设置为true，而不是物理删除
        employeeRoleRepository
            .findById(id)
            .ifPresent(employeeRole -> {
                employeeRole.setIsDeleted(true);
                employeeRoleRepository.save(employeeRole);
            });

    }

    @Override
    @Transactional(readOnly = true)
    public List<EmployeeRoleDTO> findByEmployeeId(Long employeeId) {
        LOG.debug("Request to get EmployeeRoles by Employee ID : {}", employeeId);
        List<EmployeeRole> list = employeeRoleRepository.findByEmployeeId(employeeId);
        //转为List<EmployeeRoleDTO>，手动转换dto
        List<EmployeeRoleDTO> result = new ArrayList<>();
        for (EmployeeRole employeeRole : list) {
            EmployeeRoleDTO dto = employeeRoleMapper.toDto(employeeRole);
            dto.setEmployee(employeeMapper.toDto(employeeRole.getEmployee()));
            dto.setRole(roleMapper.toDto(employeeRole.getRole()));
            dto.setOrgUnit(orgUnitMapper.toDto(employeeRole.getOrgUnit()));
            result.add(dto);
        }
        return result;
    }

    @Override
    public List<Long> findEmployeeIdsByRoleCode(Long tenantId, Integer userType) {
        LOG.debug("Request to get all Employee IDs by role code: {}", userType);
        if (NumberConstants.ONE == userType) {
            //普通员工
            return employeeRoleRepository.findEmployeeIdsByNotRoleCode(tenantId, "TENANT_ADMIN");
        } else if (NumberConstants.TWO == userType) {
            //管理员
            return employeeRoleRepository.findEmployeeIdsByRoleCode(tenantId, "TENANT_ADMIN");
        } else {
            return List.of();
        }
    }
}

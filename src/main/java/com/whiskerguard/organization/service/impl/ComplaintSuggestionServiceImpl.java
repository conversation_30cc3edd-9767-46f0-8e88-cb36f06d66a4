package com.whiskerguard.organization.service.impl;

import com.whiskerguard.common.config.RequestConstants;
import com.whiskerguard.common.constant.NumberConstants;
import com.whiskerguard.common.util.HttpRequestUtil;
import com.whiskerguard.common.util.TenantContextUtil;
import com.whiskerguard.organization.domain.ComplaintSuggestion;
import com.whiskerguard.organization.domain.ComplaintSuggestionAttachment;
import com.whiskerguard.organization.repository.ComplaintSuggestionAttachmentRepository;
import com.whiskerguard.organization.repository.ComplaintSuggestionRepository;
import com.whiskerguard.organization.service.ComplaintSuggestionService;
import com.whiskerguard.organization.service.dto.ComplaintSuggestionDTO;
import com.whiskerguard.organization.service.mapper.ComplaintSuggestionAttachmentMapper;
import com.whiskerguard.organization.service.mapper.ComplaintSuggestionMapper;
import com.whiskerguard.organization.web.rest.errors.BadRequestAlertException;
import jakarta.servlet.http.HttpServletRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Example;
import org.springframework.data.domain.ExampleMatcher;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.Optional;

/**
 * 描述：投诉与建议的服务实现类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/18
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class ComplaintSuggestionServiceImpl implements ComplaintSuggestionService {

    private static final Logger LOG = LoggerFactory.getLogger(ComplaintSuggestionServiceImpl.class);

    private final ComplaintSuggestionRepository complaintSuggestionRepository;

    private final ComplaintSuggestionMapper complaintSuggestionMapper;

    private final HttpServletRequest request;

    private final ComplaintSuggestionAttachmentRepository complaintSuggestionAttachmentRepository;

    private final ComplaintSuggestionAttachmentMapper complaintSuggestionAttachmentMapper;

    public ComplaintSuggestionServiceImpl(
        ComplaintSuggestionRepository complaintSuggestionRepository,
        ComplaintSuggestionMapper complaintSuggestionMapper,
        HttpServletRequest request,
        ComplaintSuggestionAttachmentRepository complaintSuggestionAttachmentRepository,
        ComplaintSuggestionAttachmentMapper complaintSuggestionAttachmentMapper
    ) {
        this.complaintSuggestionRepository = complaintSuggestionRepository;
        this.complaintSuggestionMapper = complaintSuggestionMapper;
        this.request = request;
        this.complaintSuggestionAttachmentRepository = complaintSuggestionAttachmentRepository;
        this.complaintSuggestionAttachmentMapper = complaintSuggestionAttachmentMapper;
    }

    @Override
    public ComplaintSuggestionDTO save(ComplaintSuggestionDTO complaintSuggestionDTO) {
        Long tenantId = TenantContextUtil.getCurrentTenantId();
        Long userId = Long.valueOf(HttpRequestUtil.getHeader(request, RequestConstants.X_USER_ID));
        LOG.debug("Request to save ComplaintSuggestion : {}", complaintSuggestionDTO);
        ComplaintSuggestion complaintSuggestion = complaintSuggestionMapper.toEntity(complaintSuggestionDTO);
        complaintSuggestion.setTenantId(tenantId);
        complaintSuggestion.setEmployeeId(userId);
        if (complaintSuggestionDTO.getIsAnonymous() == null) {
            complaintSuggestion.setIsAnonymous(NumberConstants.ONE);
        }
        complaintSuggestion.setVersion(NumberConstants.ONE);
        complaintSuggestion.setCreatedBy(TenantContextUtil.getCurrentUserName());
        complaintSuggestion.setUpdatedBy(complaintSuggestion.getCreatedBy());
        complaintSuggestion.setCreatedAt(Instant.now());
        complaintSuggestion.setUpdatedAt(Instant.now());
        complaintSuggestion.setIsDeleted(Boolean.FALSE);
        complaintSuggestion = complaintSuggestionRepository.save(complaintSuggestion);
        Long suggestionId = complaintSuggestion.getId();
        //保存附件
        if (complaintSuggestionDTO.getAttachments() != null) {
            complaintSuggestionDTO.getAttachments().forEach(attachment -> {
                LOG.debug("Request to save ComplaintSuggestionAttachment : {}", attachment);
                //判断当前附件是否已经存在，如果存在则跳过
                if (complaintSuggestionAttachmentRepository.findBySuggestionIdAndFileName(suggestionId, attachment.getFileName()).isPresent()) {
                    LOG.info("ComplaintSuggestionAttachment already exists, skipping");
                    return;
                }
                ComplaintSuggestionAttachment complaintSuggestionAttachment = complaintSuggestionAttachmentMapper.toEntity(attachment);
                complaintSuggestionAttachment.setSuggestionId(suggestionId);
                complaintSuggestionAttachment.setTenantId(tenantId);
                complaintSuggestionAttachment.setVersion(NumberConstants.ONE);
                complaintSuggestionAttachment.setUploadedBy(TenantContextUtil.getCurrentUserName());
                complaintSuggestionAttachment.setUploadedAt(Instant.now());
                complaintSuggestionAttachment.setIsDeleted(Boolean.FALSE);
                complaintSuggestionAttachmentRepository.save(complaintSuggestionAttachment);
            });
        }
        return complaintSuggestionMapper.toDto(complaintSuggestion);
    }

    @Override
    public Optional<ComplaintSuggestionDTO> partialUpdate(ComplaintSuggestionDTO complaintSuggestionDTO) {
        LOG.debug("Request to partially update ComplaintSuggestion : {}", complaintSuggestionDTO);
        Optional<ComplaintSuggestion> optional = complaintSuggestionRepository.findById(complaintSuggestionDTO.getId());
        ComplaintSuggestion complaintSuggestion = optional.orElseThrow(() -> new BadRequestAlertException("当前投诉建议不存在", "complaintSuggestion", "id null"));
        complaintSuggestionMapper.partialUpdate(complaintSuggestion, complaintSuggestionDTO);
        String username = HttpRequestUtil.getHeader(request, RequestConstants.X_USER_NAME);
        complaintSuggestion.setUpdatedBy(username);
        complaintSuggestion.setUpdatedAt(Instant.now());
        complaintSuggestionRepository.save(complaintSuggestion);
        //更新附件
        if (complaintSuggestionDTO.getAttachments() != null) {
            complaintSuggestionDTO.getAttachments().forEach(attachment -> {
                //判断当前附件是否已经存在，如果存在则更新
                Optional<ComplaintSuggestionAttachment> attachmentOptional = complaintSuggestionAttachmentRepository.findBySuggestionIdAndFileName(complaintSuggestionDTO.getId(), attachment.getFileName());
                if (attachmentOptional.isPresent()) {
                    ComplaintSuggestionAttachment complaintSuggestionAttachment = attachmentOptional.orElseThrow(() -> new BadRequestAlertException("当前附件不存在", "complaintSuggestionAttachment", "id null"));
                    complaintSuggestionAttachmentMapper.partialUpdate(complaintSuggestionAttachment, attachment);
                    complaintSuggestionAttachment.setUploadedBy(username);
                    complaintSuggestionAttachment.setUploadedAt(Instant.now());
                    complaintSuggestionAttachmentRepository.save(complaintSuggestionAttachment);
                } else {
                    //不存在则新增
                    ComplaintSuggestionAttachment complaintSuggestionAttachment = complaintSuggestionAttachmentMapper.toEntity(attachment);
                    complaintSuggestionAttachment.setSuggestionId(complaintSuggestionDTO.getId());
                    complaintSuggestionAttachment.setTenantId(TenantContextUtil.getCurrentTenantId());
                    complaintSuggestionAttachment.setVersion(NumberConstants.ONE);
                    complaintSuggestionAttachment.setUploadedBy(username);
                    complaintSuggestionAttachment.setUploadedAt(Instant.now());
                    complaintSuggestionAttachment.setIsDeleted(Boolean.FALSE);
                    complaintSuggestionAttachmentRepository.save(complaintSuggestionAttachment);
                }
            });
        }

        return Optional.of(complaintSuggestionMapper.toDto(complaintSuggestion));
    }

    @Override
    @Transactional(readOnly = true)
    public Page<ComplaintSuggestionDTO> findAll(Pageable pageable) {
        Long userId = Long.valueOf(HttpRequestUtil.getHeader(request, RequestConstants.X_USER_ID));
        LOG.debug("Request to get all ComplaintSuggestions by user ID: {}", userId);
        ComplaintSuggestion exampleEntity = new ComplaintSuggestion();
        exampleEntity.setEmployeeId(userId);
        exampleEntity.setIsDeleted(Boolean.FALSE);
        ExampleMatcher matcher = ExampleMatcher.matching().withIgnoreNullValues();
        Example<ComplaintSuggestion> example = Example.of(exampleEntity, matcher);
        return complaintSuggestionRepository.findAll(example, pageable).map(complaintSuggestionMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<ComplaintSuggestionDTO> findOne(Long id) {
        LOG.debug("Request to get ComplaintSuggestion : {}", id);
        Optional<ComplaintSuggestion> optional = complaintSuggestionRepository.findById(id);
        ComplaintSuggestion complaintSuggestion = optional.orElseThrow(() -> new BadRequestAlertException("当前投诉建议不存在", "complaintSuggestion", "id null"));
        ComplaintSuggestionDTO dto = complaintSuggestionMapper.toDto(complaintSuggestion);
        dto.setAttachments(complaintSuggestionAttachmentRepository.findBySuggestionId(id).stream().map(complaintSuggestionAttachmentMapper::toDto).toList());
        return Optional.of(dto);
    }

    @Override
    public void delete(Long id) {
        LOG.debug("Request to delete ComplaintSuggestion : {}", id);
        complaintSuggestionRepository.findById(id).ifPresent(complaintSuggestion -> {
            complaintSuggestion.setIsDeleted(Boolean.TRUE);
            complaintSuggestionRepository.save(complaintSuggestion);
        });
        //删除附件
        complaintSuggestionAttachmentRepository.findBySuggestionId(id).forEach(complaintSuggestionAttachment -> {
            complaintSuggestionAttachment.setIsDeleted(Boolean.TRUE);
            complaintSuggestionAttachmentRepository.save(complaintSuggestionAttachment);
        });
    }
}

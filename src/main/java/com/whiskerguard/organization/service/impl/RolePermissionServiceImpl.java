package com.whiskerguard.organization.service.impl;

import com.whiskerguard.common.config.RequestConstants;
import com.whiskerguard.common.constant.NumberConstants;
import com.whiskerguard.common.util.HttpRequestUtil;
import com.whiskerguard.organization.domain.Permission;
import com.whiskerguard.organization.domain.RolePermission;
import com.whiskerguard.organization.repository.PermissionRepository;
import com.whiskerguard.organization.repository.RolePermissionRepository;
import com.whiskerguard.organization.service.RolePermissionService;
import com.whiskerguard.organization.service.dto.PermissionDTO;
import com.whiskerguard.organization.service.dto.RolePermissionDTO;
import com.whiskerguard.organization.service.mapper.PermissionMapper;
import com.whiskerguard.organization.service.mapper.RoleMapper;
import com.whiskerguard.organization.service.mapper.RolePermissionMapper;
import com.whiskerguard.organization.web.rest.errors.BadRequestAlertException;
import jakarta.servlet.http.HttpServletRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * 描述：角色权限关系的服务实现类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/18
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class RolePermissionServiceImpl implements RolePermissionService {

    private static final Logger LOG = LoggerFactory.getLogger(RolePermissionServiceImpl.class);

    private final RolePermissionRepository rolePermissionRepository;

    private final RolePermissionMapper rolePermissionMapper;

    private final HttpServletRequest request;

    private final PermissionRepository permissionRepository;

    private final PermissionMapper permissionMapper;

    private final RoleMapper roleMapper;

    public RolePermissionServiceImpl(RolePermissionRepository rolePermissionRepository, RolePermissionMapper rolePermissionMapper,
                                     HttpServletRequest request, PermissionRepository permissionRepository,
                                     PermissionMapper permissionMapper, RoleMapper roleMapper) {
        this.rolePermissionRepository = rolePermissionRepository;
        this.rolePermissionMapper = rolePermissionMapper;
        this.request = request;
        this.permissionRepository = permissionRepository;
        this.permissionMapper = permissionMapper;
        this.roleMapper = roleMapper;
    }

    @Override
    public RolePermissionDTO save(RolePermissionDTO rolePermissionDTO) {
        LOG.debug("Request to save RolePermission : {}", rolePermissionDTO);
        RolePermission rolePermission = rolePermissionMapper.toEntity(rolePermissionDTO);
        String username = HttpRequestUtil.getHeader(request, RequestConstants.X_USER_NAME);
        rolePermission.setTenantId(Long.valueOf(HttpRequestUtil.getHeader(request, RequestConstants.X_TENANT_ID)));
        rolePermission.setVersion(NumberConstants.ONE);
        rolePermission.setCreatedBy(username);
        rolePermission.setUpdatedBy(username);
        rolePermission.setCreatedAt(Instant.now());
        rolePermission.setUpdatedAt(Instant.now());
        rolePermission.setIsDeleted(Boolean.FALSE);
        rolePermission = rolePermissionRepository.save(rolePermission);
        return rolePermissionMapper.toDto(rolePermission);
    }

    @Override
    public List<RolePermissionDTO> saveAll(List<RolePermissionDTO> list) {
        LOG.debug("Request to batch save RolePermissions : {}", list.size());
        String username = HttpRequestUtil.getHeader(request, RequestConstants.X_USER_NAME);
        Long tenantId = Long.valueOf(HttpRequestUtil.getHeader(request, RequestConstants.X_TENANT_ID));
        Instant now = Instant.now();
        //获取所有的权限id
        List<Long> permissionIds = list.stream().map(RolePermissionDTO::getPermission).map(PermissionDTO::getId).toList();
        //查询权限数据
        List<Permission> permissions = permissionRepository.findAllById(permissionIds);
        if (list.size() != permissions.size()) {
            throw new BadRequestAlertException("权限未找到", "RolePermission", "permission not found");
        }
        //删除原来的角色权限数据
        rolePermissionRepository.deleteByRoleId(list.get(NumberConstants.ZERO).getRole().getId());

        List<RolePermission> rolePermissions = new ArrayList<>();
        for (RolePermissionDTO rolePermissionDTO : list) {
            RolePermission rolePermission = new RolePermission();
            rolePermission.setTenantId(tenantId);
            rolePermission.setVersion(NumberConstants.ONE);
            rolePermission.setCreatedBy(username);
            rolePermission.setUpdatedBy(username);
            rolePermission.setCreatedAt(now);
            rolePermission.setUpdatedAt(now);
            rolePermission.setPermission(permissionMapper.toEntity(rolePermissionDTO.getPermission()));
            rolePermission.setRole(roleMapper.toEntity(rolePermissionDTO.getRole()));
            rolePermission.setIsDeleted(Boolean.FALSE);
            rolePermissions.add(rolePermission);
        }

        List<RolePermission> savedRolePermissions = rolePermissionRepository.saveAll(rolePermissions);
        return savedRolePermissions.stream()
            .map(rolePermissionMapper::toDto)
            .toList();
    }

    @Override
    public RolePermissionDTO update(RolePermissionDTO rolePermissionDTO) {
        LOG.debug("Request to update RolePermission : {}", rolePermissionDTO);
        RolePermission rolePermission = rolePermissionMapper.toEntity(rolePermissionDTO);
        rolePermission = rolePermissionRepository.save(rolePermission);
        return rolePermissionMapper.toDto(rolePermission);
    }

    @Override
    public Optional<RolePermissionDTO> partialUpdate(RolePermissionDTO rolePermissionDTO) {
        LOG.debug("Request to partially update RolePermission : {}", rolePermissionDTO);

        return rolePermissionRepository
            .findById(rolePermissionDTO.getId())
            .map(existingRolePermission -> {
                rolePermissionMapper.partialUpdate(existingRolePermission, rolePermissionDTO);

                return existingRolePermission;
            })
            .map(rolePermissionRepository::save)
            .map(rolePermissionMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<RolePermissionDTO> findAll(Pageable pageable) {
        Long tenantId = Long.valueOf(HttpRequestUtil.getHeader(request, RequestConstants.X_TENANT_ID));
        LOG.debug("Request to get all RolePermissions by tenant ID: {}", tenantId);
        return rolePermissionRepository.findByIsDeletedFalse(tenantId, pageable).map(rolePermissionMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<RolePermissionDTO> findOne(Long id) {
        LOG.debug("Request to get RolePermission : {}", id);
        return rolePermissionRepository.findById(id).map(rolePermissionMapper::toDto);
    }

    @Override
    public void delete(Long id) {
        LOG.debug("Request to delete RolePermission : {}", id);
        // 逻辑删除：将isDeleted设置为true，而不是物理删除
        rolePermissionRepository
            .findById(id)
            .ifPresent(rolePermission -> {
                rolePermission.setIsDeleted(true);
                rolePermissionRepository.save(rolePermission);
            });
    }

    @Override
    public void deleteAll(List<Long> ids) {
        LOG.debug("Request to batch delete RolePermissions : {}", ids);
        // 批量逻辑删除：将isDeleted设置为true，而不是物理删除
        List<RolePermission> rolePermissions = rolePermissionRepository.findAllById(ids);
        rolePermissions.forEach(rolePermission -> rolePermission.setIsDeleted(true));
        rolePermissionRepository.saveAll(rolePermissions);
    }

    @Override
    @Transactional(readOnly = true)
    public List<RolePermissionDTO> findByEmployeeId(Long employeeId) {
        LOG.debug("Request to get RolePermissions by Employee ID : {}", employeeId);
        return rolePermissionRepository.findByEmployeeId(employeeId).stream().map(rolePermissionMapper::toDto).toList();
    }
}

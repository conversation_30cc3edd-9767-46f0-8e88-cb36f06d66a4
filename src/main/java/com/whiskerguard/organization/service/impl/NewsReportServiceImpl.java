package com.whiskerguard.organization.service.impl;

import com.whiskerguard.common.config.RequestConstants;
import com.whiskerguard.common.constant.NumberConstants;
import com.whiskerguard.common.util.HttpRequestUtil;
import com.whiskerguard.organization.domain.NewsReport;
import com.whiskerguard.organization.repository.NewsReportRepository;
import com.whiskerguard.organization.service.NewsReportService;
import com.whiskerguard.organization.service.dto.NewsReportDTO;
import com.whiskerguard.organization.service.mapper.NewsReportMapper;
import jakarta.servlet.http.HttpServletRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.Optional;

/**
 * 描述：新闻举报的服务实现类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/18
 */
@Service
@Transactional
public class NewsReportServiceImpl implements NewsReportService {

    private static final Logger LOG = LoggerFactory.getLogger(NewsReportServiceImpl.class);

    private final NewsReportRepository newsReportRepository;

    private final NewsReportMapper newsReportMapper;

    private final HttpServletRequest request;

    public NewsReportServiceImpl(NewsReportRepository newsReportRepository, NewsReportMapper newsReportMapper,
                                 HttpServletRequest request) {
        this.newsReportRepository = newsReportRepository;
        this.newsReportMapper = newsReportMapper;
        this.request = request;
    }

    @Override
    public NewsReportDTO save(NewsReportDTO newsReportDTO) {
        LOG.debug("Request to save NewsReport : {}", newsReportDTO);
        NewsReport newsReport = newsReportMapper.toEntity(newsReportDTO);
        String username = HttpRequestUtil.getHeader(request, RequestConstants.X_USER_NAME);
        newsReport.setTenantId(Long.valueOf(HttpRequestUtil.getHeader(request, RequestConstants.X_TENANT_ID)));
        newsReport.setVersion(NumberConstants.ONE);
        newsReport.setCreatedBy(username);
        newsReport.setUpdatedBy(username);
        newsReport.setCreatedAt(Instant.now());
        newsReport.setUpdatedAt(Instant.now());
        newsReport.setIsDeleted(Boolean.FALSE);
        newsReport = newsReportRepository.save(newsReport);
        return newsReportMapper.toDto(newsReport);
    }

    @Override
    public NewsReportDTO update(NewsReportDTO newsReportDTO) {
        LOG.debug("Request to update NewsReport : {}", newsReportDTO);
        NewsReport newsReport = newsReportMapper.toEntity(newsReportDTO);
        newsReport = newsReportRepository.save(newsReport);
        return newsReportMapper.toDto(newsReport);
    }

    @Override
    public Optional<NewsReportDTO> partialUpdate(NewsReportDTO newsReportDTO) {
        LOG.debug("Request to partially update NewsReport : {}", newsReportDTO);

        return newsReportRepository
            .findById(newsReportDTO.getId())
            .map(existingNewsReport -> {
                newsReportMapper.partialUpdate(existingNewsReport, newsReportDTO);
                String username = HttpRequestUtil.getHeader(request, RequestConstants.X_USER_NAME);
                existingNewsReport.setUpdatedBy(username);
                existingNewsReport.setUpdatedAt(Instant.now());
                return existingNewsReport;
            })
            .map(newsReportRepository::save)
            .map(newsReportMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<NewsReportDTO> findAll(Pageable pageable) {
        LOG.debug("Request to get all NewsReports");
        return newsReportRepository.findByIsDeletedFalse(pageable).map(newsReportMapper::toDto);
    }

    @Override
    public Page<NewsReportDTO> findAllWithEagerRelationships(Pageable pageable) {
        return newsReportRepository.findAllWithEagerRelationshipsAndNotDeleted(pageable).map(newsReportMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<NewsReportDTO> findOne(Long id) {
        LOG.debug("Request to get NewsReport : {}", id);
        return newsReportRepository.findOneWithEagerRelationships(id).map(newsReportMapper::toDto);
    }

    @Override
    public void delete(Long id) {
        LOG.debug("Request to delete NewsReport : {}", id);
        // 逻辑删除：将isDeleted设置为true，而不是物理删除
        newsReportRepository
            .findById(id)
            .ifPresent(newsReport -> {
                newsReport.setIsDeleted(true);
                newsReportRepository.save(newsReport);
            });
    }
}

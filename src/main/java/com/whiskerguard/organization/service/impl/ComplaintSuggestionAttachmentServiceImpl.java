package com.whiskerguard.organization.service.impl;

import com.whiskerguard.common.constant.NumberConstants;
import com.whiskerguard.common.util.TenantContextUtil;
import com.whiskerguard.organization.domain.ComplaintSuggestionAttachment;
import com.whiskerguard.organization.repository.ComplaintSuggestionAttachmentRepository;
import com.whiskerguard.organization.service.ComplaintSuggestionAttachmentService;
import com.whiskerguard.organization.service.dto.ComplaintSuggestionAttachmentDTO;
import com.whiskerguard.organization.service.mapper.ComplaintSuggestionAttachmentMapper;
import com.whiskerguard.organization.web.rest.errors.BadRequestAlertException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.Optional;

/**
 * 描述：投诉与建议附件的服务实现类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/18
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class ComplaintSuggestionAttachmentServiceImpl implements ComplaintSuggestionAttachmentService {

    private static final Logger LOG = LoggerFactory.getLogger(ComplaintSuggestionAttachmentServiceImpl.class);

    private final ComplaintSuggestionAttachmentRepository complaintSuggestionAttachmentRepository;

    private final ComplaintSuggestionAttachmentMapper complaintSuggestionAttachmentMapper;

    public ComplaintSuggestionAttachmentServiceImpl(ComplaintSuggestionAttachmentRepository complaintSuggestionAttachmentRepository,
                                                    ComplaintSuggestionAttachmentMapper complaintSuggestionAttachmentMapper) {
        this.complaintSuggestionAttachmentRepository = complaintSuggestionAttachmentRepository;
        this.complaintSuggestionAttachmentMapper = complaintSuggestionAttachmentMapper;
    }

    @Override
    public ComplaintSuggestionAttachmentDTO save(ComplaintSuggestionAttachmentDTO complaintSuggestionAttachmentDTO) {
        LOG.debug("Request to save ComplaintSuggestionAttachment : {}", complaintSuggestionAttachmentDTO);
        //判断当前附件是否已经存在，如果存在则抛出异常
        if (complaintSuggestionAttachmentRepository.findBySuggestionIdAndFileName(complaintSuggestionAttachmentDTO.getSuggestionId(),
            complaintSuggestionAttachmentDTO.getFileName()).isPresent()) {
            throw new BadRequestAlertException("当前投诉与建议已经存在该附件", "complaintSuggestionAttachment",
                "complaintSuggestionAttachment exists");
        }
        ComplaintSuggestionAttachment complaintSuggestionAttachment = complaintSuggestionAttachmentMapper.toEntity(
            complaintSuggestionAttachmentDTO);
        complaintSuggestionAttachment.setTenantId(TenantContextUtil.getCurrentTenantId());
        complaintSuggestionAttachment.setVersion(NumberConstants.ONE);
        complaintSuggestionAttachment.setUploadedBy(TenantContextUtil.getCurrentUserName());
        complaintSuggestionAttachment.setUploadedAt(Instant.now());
        complaintSuggestionAttachment.setIsDeleted(Boolean.FALSE);
        complaintSuggestionAttachment = complaintSuggestionAttachmentRepository.save(complaintSuggestionAttachment);
        return complaintSuggestionAttachmentMapper.toDto(complaintSuggestionAttachment);
    }

    @Override
    public Optional<ComplaintSuggestionAttachmentDTO> partialUpdate(ComplaintSuggestionAttachmentDTO complaintSuggestionAttachmentDTO) {
        LOG.debug("Request to partially update ComplaintSuggestionAttachment : {}", complaintSuggestionAttachmentDTO);
        //判断当前附件是否已经存在，如果存在则抛出异常
        Optional<ComplaintSuggestionAttachment> optional = complaintSuggestionAttachmentRepository.findBySuggestionIdAndFileName(complaintSuggestionAttachmentDTO.getSuggestionId(),
            complaintSuggestionAttachmentDTO.getFileName());
        ComplaintSuggestionAttachment attachment = optional.orElse(null);
        if (attachment != null && !attachment.getId().equals(complaintSuggestionAttachmentDTO.getId())) {
            throw new BadRequestAlertException("当前投诉与建议已经存在该附件", "complaintSuggestionAttachment",
                "complaintSuggestionAttachment exists");
        }
        return complaintSuggestionAttachmentRepository
            .findById(complaintSuggestionAttachmentDTO.getId())
            .map(existingComplaintSuggestionAttachment -> {
                complaintSuggestionAttachmentMapper.partialUpdate(existingComplaintSuggestionAttachment, complaintSuggestionAttachmentDTO);

                return existingComplaintSuggestionAttachment;
            })
            .map(complaintSuggestionAttachmentRepository::save)
            .map(complaintSuggestionAttachmentMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<ComplaintSuggestionAttachmentDTO> findAll(Long suggestionId, Pageable pageable) {
        LOG.debug("Request to get all ComplaintSuggestionAttachments");
        return complaintSuggestionAttachmentRepository.findAllBySuggestionId(suggestionId, pageable).map(complaintSuggestionAttachmentMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<ComplaintSuggestionAttachmentDTO> findOne(Long id) {
        LOG.debug("Request to get ComplaintSuggestionAttachment : {}", id);
        return complaintSuggestionAttachmentRepository.findById(id).map(complaintSuggestionAttachmentMapper::toDto);
    }

    @Override
    public void delete(Long id) {
        LOG.debug("Request to delete ComplaintSuggestionAttachment : {}", id);
        complaintSuggestionAttachmentRepository.findById(id).ifPresent(complaintSuggestionAttachment -> {
            complaintSuggestionAttachment.setIsDeleted(Boolean.TRUE);
            complaintSuggestionAttachmentRepository.save(complaintSuggestionAttachment);
        });
    }
}

package com.whiskerguard.organization.service.impl;

import com.whiskerguard.common.config.RequestConstants;
import com.whiskerguard.common.constant.NumberConstants;
import com.whiskerguard.common.util.HttpRequestUtil;
import com.whiskerguard.organization.domain.RiskCategory;
import com.whiskerguard.organization.domain.RiskModel;
import com.whiskerguard.organization.domain.RiskRule;
import com.whiskerguard.organization.repository.RiskCategoryRepository;
import com.whiskerguard.organization.repository.RiskModelRepository;
import com.whiskerguard.organization.repository.RiskRuleRepository;
import com.whiskerguard.organization.service.RiskCategoryService;
import com.whiskerguard.organization.service.dto.RiskCategoryDTO;
import com.whiskerguard.organization.service.dto.RiskRuleDTO;
import com.whiskerguard.organization.service.mapper.RiskCategoryMapper;
import com.whiskerguard.organization.service.mapper.RiskRuleMapper;
import com.whiskerguard.organization.web.rest.errors.BadRequestAlertException;
import jakarta.servlet.http.HttpServletRequest;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.List;
import java.util.Optional;

/**
 * 描述：风险类别的服务实现类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/18
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class RiskCategoryServiceImpl implements RiskCategoryService {

    private static final Logger LOG = LoggerFactory.getLogger(RiskCategoryServiceImpl.class);

    private final RiskCategoryRepository riskCategoryRepository;

    private final RiskCategoryMapper riskCategoryMapper;

    private final HttpServletRequest request;

    private final RiskRuleRepository riskRuleRepository;

    private final RiskRuleMapper riskRuleMapper;

    private final RiskModelRepository riskModelRepository;

    public RiskCategoryServiceImpl(RiskCategoryRepository riskCategoryRepository, RiskCategoryMapper riskCategoryMapper,
                                   HttpServletRequest request, RiskRuleRepository riskRuleRepository,
                                   RiskRuleMapper riskRuleMapper, RiskModelRepository riskModelRepository) {
        this.riskCategoryRepository = riskCategoryRepository;
        this.riskCategoryMapper = riskCategoryMapper;
        this.request = request;
        this.riskRuleRepository = riskRuleRepository;
        this.riskRuleMapper = riskRuleMapper;
        this.riskModelRepository = riskModelRepository;
    }

    @Override
    public RiskCategoryDTO save(RiskCategoryDTO riskCategoryDTO) {
        LOG.debug("Request to save RiskCategory : {}", riskCategoryDTO);
        Long tenantId = Long.valueOf(HttpRequestUtil.getHeader(request, RequestConstants.X_TENANT_ID));
        //判断当前风险类别是否已经存在，如果存在则抛出异常
        if (null != riskCategoryRepository.findByNameAndTenantId(riskCategoryDTO.getName(), tenantId)) {
            throw new BadRequestAlertException("风险类别已经存在", "riskCategory", "riskCategory exists");
        }
        //判断RiskModel是否存在
        if (null == riskCategoryDTO.getRiskModel().getId()) {
            throw new BadRequestAlertException("风险模型不能为空", "riskModel", "riskModel is null");
        }
        Optional<RiskModel> optional = riskModelRepository.findById(riskCategoryDTO.getRiskModel().getId());
        RiskModel riskModel = optional.orElseThrow(() -> new BadRequestAlertException("风险模型不存在", "riskModel", "riskModel not found"));
        RiskCategory riskCategory = riskCategoryMapper.toEntity(riskCategoryDTO);
        String username = HttpRequestUtil.getHeader(request, RequestConstants.X_USER_NAME);
        riskCategory.setRiskModel(riskModel);
        riskCategory.setTenantId(tenantId);
        riskCategory.setVersion(NumberConstants.ONE);
        riskCategory.setCreatedBy(username);
        riskCategory.setUpdatedBy(username);
        riskCategory.setCreatedAt(Instant.now());
        riskCategory.setUpdatedAt(Instant.now());
        riskCategory.setIsDeleted(Boolean.FALSE);
        riskCategory = riskCategoryRepository.save(riskCategory);
        //添加风险规则
        if (CollectionUtils.isNotEmpty(riskCategoryDTO.getRiskRules())) {
            for (RiskRuleDTO riskRuleDTO : riskCategoryDTO.getRiskRules()) {
                //判断当前风险规则是否已经存在，如果存在则抛出异常
                if (null != riskRuleRepository.findByTenantIdAndNameOrCode(tenantId, riskRuleDTO.getName(), riskRuleDTO.getCode())) {
                    throw new BadRequestAlertException("风险规则已经存在", "riskRule", "riskRule exists");
                }
                RiskRule riskRule = riskRuleMapper.toEntity(riskRuleDTO);
                riskRule.setTenantId(tenantId);
                riskRule.setVersion(NumberConstants.ONE);
                riskRule.setCreatedBy(username);
                riskRule.setUpdatedBy(username);
                riskRule.setCreatedAt(Instant.now());
                riskRule.setUpdatedAt(Instant.now());
                riskRule.setIsDeleted(Boolean.FALSE);
                riskRule.setRiskCategory(riskCategory);
                riskRuleRepository.save(riskRule);
            }
        }
        return riskCategoryMapper.toDto(riskCategory);
    }

    @Override
    public Optional<RiskCategoryDTO> partialUpdate(RiskCategoryDTO riskCategoryDTO) {
        LOG.debug("Request to partially update RiskCategory : {}", riskCategoryDTO);
        //判断当前风险类别是否已经存在，如果存在则抛出异常
        Long tenantId = Long.valueOf(HttpRequestUtil.getHeader(request, RequestConstants.X_TENANT_ID));
        RiskCategory riskCategory = riskCategoryRepository.findByNameAndTenantId(riskCategoryDTO.getName(), tenantId);
        if (null != riskCategory && !riskCategory.getId().equals(riskCategoryDTO.getId())) {
            throw new BadRequestAlertException("风险类别已经存在", "riskCategory", "riskCategory exists");
        }
        riskCategory = riskCategoryRepository.findById(riskCategoryDTO.getId()).orElseThrow(() -> new BadRequestAlertException("风险类别不存在", "riskCategory", "riskCategory not found"));
        riskCategoryMapper.partialUpdate(riskCategory, riskCategoryDTO);
        String username = HttpRequestUtil.getHeader(request, RequestConstants.X_USER_NAME);
        riskCategory.setUpdatedBy(username);
        riskCategory.setUpdatedAt(Instant.now());
        riskCategoryRepository.save(riskCategory);

        //更新风险规则
        if (CollectionUtils.isNotEmpty(riskCategoryDTO.getRiskRules())) {
            for (RiskRuleDTO riskRuleDTO : riskCategoryDTO.getRiskRules()) {
                //判断当前风险规则是否已经存在，如果存在则抛出异常
                RiskRule riskRule = riskRuleRepository.findByTenantIdAndNameOrCode(tenantId, riskRuleDTO.getName(), riskRuleDTO.getCode());
                if (null != riskRule && !riskRule.getId().equals(riskRuleDTO.getId())) {
                    throw new BadRequestAlertException("风险规则已经存在", "riskRule", "riskRule exists");
                }
                if (null != riskRuleDTO.getId()) {
                    riskRule = riskRuleRepository.findById(riskRuleDTO.getId()).orElseThrow(() -> new BadRequestAlertException("风险规则不存在", "riskRule", "riskRule not found"));
                    riskRuleMapper.partialUpdate(riskRule, riskRuleDTO);
                    riskRule.setUpdatedBy(username);
                    riskRule.setUpdatedAt(Instant.now());
                    riskRuleRepository.save(riskRule);
                } else {
                    riskRule = riskRuleMapper.toEntity(riskRuleDTO);
                    riskRule.setTenantId(tenantId);
                    riskRule.setVersion(NumberConstants.ONE);
                    riskRule.setCreatedBy(username);
                    riskRule.setUpdatedBy(username);
                    riskRule.setCreatedAt(Instant.now());
                    riskRule.setUpdatedAt(Instant.now());
                    riskRule.setIsDeleted(Boolean.FALSE);
                    riskRule.setRiskCategory(riskCategory);
                    riskRuleRepository.save(riskRule);
                }
            }
        }
        return Optional.of(riskCategoryMapper.toDto(riskCategory));
    }

    @Override
    @Transactional(readOnly = true)
    public Page<RiskCategoryDTO> findAll(Long riskModelId, Pageable pageable) {
        LOG.debug("Request to get all RiskCategories by risk model ID: {}", riskModelId);
        return riskCategoryRepository.findAllByRiskModelId(riskModelId, pageable).map(riskCategoryMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<RiskCategoryDTO> findOne(Long id) {
        LOG.debug("Request to get RiskCategory : {}", id);
        Optional<RiskCategoryDTO> optional = riskCategoryRepository.findOneWithEagerRelationships(id).map(riskCategoryMapper::toDto);
        RiskCategoryDTO riskCategory = optional.orElseThrow(() -> new BadRequestAlertException("风险类别不存在", "riskCategory", "riskCategory not found"));
        //查询风险规则
        List<RiskRule> riskRules = riskRuleRepository.findByRiskCategoryId(id);
        riskCategory.setRiskRules(riskRuleMapper.toDto(riskRules));
        return Optional.of(riskCategory);
    }

    @Override
    public void delete(Long id) {
        LOG.debug("Request to delete RiskCategory : {}", id);
        riskCategoryRepository.findById(id).ifPresent(riskCategory -> {
            riskCategory.setIsDeleted(Boolean.TRUE);
            riskCategoryRepository.save(riskCategory);
        });
        //删除风险规则
        riskRuleRepository.findByRiskCategoryId(id).forEach(riskRule -> {
            riskRule.setIsDeleted(Boolean.TRUE);
            riskRuleRepository.save(riskRule);
        });
    }
}

package com.whiskerguard.organization.service.impl;

import com.whiskerguard.common.constant.NumberConstants;
import com.whiskerguard.common.util.TenantContextUtil;
import com.whiskerguard.organization.domain.TenantInitialize;
import com.whiskerguard.organization.repository.TenantInitializeRepository;
import com.whiskerguard.organization.service.TenantInitializeService;
import com.whiskerguard.organization.service.dto.TenantInitializeDTO;
import com.whiskerguard.organization.service.mapper.TenantInitializeMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.Optional;

/**
 * 描述：租户基础信息初始化表的服务实现类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/20
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class TenantInitializeServiceImpl implements TenantInitializeService {

    private static final Logger LOG = LoggerFactory.getLogger(TenantInitializeServiceImpl.class);

    private final TenantInitializeRepository tenantInitializeRepository;

    private final TenantInitializeMapper tenantInitializeMapper;

    public TenantInitializeServiceImpl(
        TenantInitializeRepository tenantInitializeRepository,
        TenantInitializeMapper tenantInitializeMapper
    ) {
        this.tenantInitializeRepository = tenantInitializeRepository;
        this.tenantInitializeMapper = tenantInitializeMapper;
    }

    @Override
    public TenantInitializeDTO save(TenantInitializeDTO tenantInitializeDTO) {
        LOG.debug("Request to save TenantInitialize : {}", tenantInitializeDTO);
        TenantInitialize tenantInitialize = tenantInitializeMapper.toEntity(tenantInitializeDTO);
        tenantInitialize.setTenantId(TenantContextUtil.getCurrentTenantId());
        tenantInitialize.setVersion(NumberConstants.ONE);
        tenantInitialize.setCreatedBy(TenantContextUtil.getCurrentUserName());
        tenantInitialize.setUpdatedBy(tenantInitialize.getCreatedBy());
        tenantInitialize.setCreatedAt(Instant.now());
        tenantInitialize.setUpdatedAt(Instant.now());
        tenantInitialize.setIsDeleted(Boolean.FALSE);
        tenantInitialize = tenantInitializeRepository.save(tenantInitialize);
        return tenantInitializeMapper.toDto(tenantInitialize);
    }


    @Override
    public Optional<TenantInitializeDTO> partialUpdate(TenantInitializeDTO tenantInitializeDTO) {
        LOG.debug("Request to partially update TenantInitialize : {}", tenantInitializeDTO);

        return tenantInitializeRepository
            .findById(tenantInitializeDTO.getId())
            .map(existingTenantInitialize -> {
                tenantInitializeMapper.partialUpdate(existingTenantInitialize, tenantInitializeDTO);
                existingTenantInitialize.setUpdatedBy(TenantContextUtil.getCurrentUserName());
                existingTenantInitialize.setUpdatedAt(Instant.now());
                return existingTenantInitialize;
            })
            .map(tenantInitializeRepository::save)
            .map(tenantInitializeMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<TenantInitializeDTO> findAll(Pageable pageable) {
        LOG.debug("Request to get all TenantInitializes");
        return tenantInitializeRepository.findAll(pageable).map(tenantInitializeMapper::toDto);
    }

    @Override
    public void delete(Long id) {
        LOG.debug("Request to delete TenantInitialize : {}", id);
        tenantInitializeRepository.findById(id).ifPresent(tenantInitialize -> {
            tenantInitialize.setIsDeleted(Boolean.TRUE);
            tenantInitializeRepository.save(tenantInitialize);
        });
    }
}

package com.whiskerguard.organization.service.impl;

import com.whiskerguard.common.config.RequestConstants;
import com.whiskerguard.common.constant.NumberConstants;
import com.whiskerguard.common.util.HttpRequestUtil;
import com.whiskerguard.common.util.WgStringUtil;
import com.whiskerguard.organization.domain.Position;
import com.whiskerguard.organization.domain.enumeration.PositionCategory;
import com.whiskerguard.organization.repository.EmployeeOrgRepository;
import com.whiskerguard.organization.repository.OrgUnitRepository;
import com.whiskerguard.organization.repository.PositionRepository;
import com.whiskerguard.organization.service.PositionService;
import com.whiskerguard.organization.service.dto.PositionDTO;
import com.whiskerguard.organization.service.mapper.PositionMapper;
import jakarta.servlet.http.HttpServletRequest;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 描述：岗位管理的服务实现类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/18
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class PositionServiceImpl implements PositionService {

    private final Logger log = LoggerFactory.getLogger(PositionServiceImpl.class);

    private final PositionRepository positionRepository;

    private final PositionMapper positionMapper;

    private final HttpServletRequest request;

    private final OrgUnitRepository orgUnitRepository;

    private final EmployeeOrgRepository employeeOrgRepository;

    public PositionServiceImpl(PositionRepository positionRepository, PositionMapper positionMapper, HttpServletRequest request,
                               OrgUnitRepository orgUnitRepository, EmployeeOrgRepository employeeOrgRepository) {
        this.positionRepository = positionRepository;
        this.positionMapper = positionMapper;
        this.request = request;
        this.orgUnitRepository = orgUnitRepository;
        this.employeeOrgRepository = employeeOrgRepository;
    }

    @Override
    public PositionDTO save(PositionDTO positionDTO) {
        log.debug("Request to save Position : {}", positionDTO);
        //校验编码
        if (validateCode(positionDTO.getCode())) {
            log.error("Position code contains invalid characters: {}", positionDTO.getCode());
            throw new IllegalArgumentException("岗位编码存在非法字符");
        }

        Long tenantId = Long.parseLong(HttpRequestUtil.getHeader(request, RequestConstants.X_TENANT_ID));
        //判断当前岗位是否已经存在，如果存在则抛出异常
        if (null != positionRepository.findByNameOrCode(tenantId, positionDTO.getName(), positionDTO.getCode())) {
            log.error("Position with code {} already exists for tenant {}", positionDTO.getCode(), tenantId);
            throw new IllegalArgumentException("编码为 " + positionDTO.getCode() + " 的岗位在租户 " + tenantId + " 中已存在");
        }
        //判断当前的部门是否存在
        if (positionDTO.getOrgUnitId() != null) {
            if (!orgUnitRepository.existsById(positionDTO.getOrgUnitId())) {
                log.error("OrgUnit with id {} not found", positionDTO.getOrgUnitId());
                throw new IllegalArgumentException("ID为 " + positionDTO.getOrgUnitId() + " 的组织单元不存在");
            }
        }
        Position position = positionMapper.toEntity(positionDTO);
        String username = HttpRequestUtil.getHeader(request, RequestConstants.X_USER_NAME);
        position.setTenantId(tenantId);
        position.setOrgUnit(orgUnitRepository.findById(positionDTO.getOrgUnitId()).orElse(null));
        position.setVersion(NumberConstants.ONE);
        position.setCreatedBy(username);
        position.setUpdatedBy(username);
        position.setCreatedAt(Instant.now());
        position.setUpdatedAt(Instant.now());
        position.setIsDeleted(Boolean.FALSE);
        position = positionRepository.save(position);
        return positionMapper.toDto(position);
    }

    /**
     * 校验编码
     *
     * @param code 编码
     * @return true表示存在非法字符，false表示合法
     */
    private boolean validateCode(String code) {
        return WgStringUtil.containsChineseChar(code) || WgStringUtil.containsSpecialChar(code) ||
            WgStringUtil.containsEmoji(code) || WgStringUtil.containsHtmlTag(code) ||
            WgStringUtil.containsSqlInjectionRisk(code) || WgStringUtil.containsXssRisk(code);
    }

    @Override
    public PositionDTO update(PositionDTO positionDTO) {
        log.debug("Request to update Position : {}", positionDTO);
        //校验编码
        if (validateCode(positionDTO.getCode())) {
            log.error("Position code contains invalid characters: {}", positionDTO.getCode());
            throw new IllegalArgumentException("岗位编码存在非法字符");
        }
        Position position = positionMapper.toEntity(positionDTO);
        position = positionRepository.save(position);
        return positionMapper.toDto(position);
    }

    @Override
    public Optional<PositionDTO> partialUpdate(PositionDTO positionDTO) {
        log.debug("Request to partially update Position : {}", positionDTO);
        //校验编码
        if (validateCode(positionDTO.getCode())) {
            log.error("Position code contains invalid characters: {}", positionDTO.getCode());
            throw new IllegalArgumentException("岗位编码存在非法字符");
        }

        Long tenantId = Long.parseLong(HttpRequestUtil.getHeader(request, RequestConstants.X_TENANT_ID));
        Position position = positionRepository.findByNameOrCode(tenantId, positionDTO.getName(), positionDTO.getCode());
        if (null != position && !position.getId().equals(positionDTO.getId())) {
            log.error("Position with name {} already exists for tenant {}", positionDTO.getName(), tenantId);
            throw new IllegalArgumentException("名称为 " + positionDTO.getName() + " 的岗位在租户 " + tenantId + " 中已存在");
        }
        return positionRepository
            .findById(positionDTO.getId())
            .map(existingPosition -> {
                positionMapper.partialUpdate(existingPosition, positionDTO);
                String username = HttpRequestUtil.getHeader(request, RequestConstants.X_USER_NAME);
                existingPosition.setUpdatedBy(username);
                existingPosition.setUpdatedAt(Instant.now());
                return existingPosition;
            })
            .map(positionRepository::save)
            .map(positionMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<PositionDTO> findAll(Pageable pageable) {
        log.debug("Request to get all Positions");

        // 从request header获取租户ID
        String tenantIdHeader = HttpRequestUtil.getHeader(request, RequestConstants.X_TENANT_ID);
        if (tenantIdHeader == null || tenantIdHeader.trim().isEmpty()) {
            log.warn("Tenant ID header is null or empty, this may cause data visibility issues");
            throw new IllegalArgumentException("请求头中缺少必需的租户ID");
        }

        Long tenantId;
        try {
            tenantId = Long.valueOf(tenantIdHeader);
        } catch (NumberFormatException e) {
            log.error("Invalid tenant ID format: {}", tenantIdHeader, e);
            throw new IllegalArgumentException("租户ID格式无效: " + tenantIdHeader, e);
        }

        log.debug("Filtering positions by tenant ID: {}", tenantId);
        Page<Position> page = positionRepository.findByTenantIdAndIsDeletedFalse(tenantId, pageable);
        List<Position> content = page.getContent();
        if (content.isEmpty()) {
            return Page.empty(pageable);
        }

        //查询职位关联的员工数
        List<Long> positionIds = content.stream().map(Position::getId).toList();
        List<Object[]> countList = employeeOrgRepository.findPositionEmployeeCountByPositionIds(positionIds);
        Map<Long, Long> positionEmployeeCountMap = null;
        if (!CollectionUtils.isEmpty(countList)) {
            positionEmployeeCountMap = countList.stream().collect(Collectors.toMap(
                row -> (Long) row[NumberConstants.ZERO],
                row -> (Long) row[NumberConstants.ONE]
            ));
        }

        List<PositionDTO> dtoList = new ArrayList<>();
        //手动转换DTO，同时进行赋值
        for (Position position : content) {
            PositionDTO dto = positionMapper.toDto(position);
            if (position.getOrgUnit() != null) {
                dto.setOrgUnitId(position.getOrgUnit().getId());
                dto.setOrgUnitName(position.getOrgUnit().getName());
            }
            if (positionEmployeeCountMap != null) {
                dto.setEmployeeCount(positionEmployeeCountMap.getOrDefault(position.getId(), 0L));
            }

            dtoList.add(dto);
        }
        return new PageImpl<>(dtoList, pageable, page.getTotalElements());
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<PositionDTO> findOne(Long id) {
        log.debug("Request to get Position : {}", id);
        return positionRepository.findById(id).map(positionMapper::toDto);
    }

    @Override
    public void delete(Long id) {
        log.debug("Request to delete Position : {}", id);
        // 逻辑删除：将isDeleted设置为true，而不是物理删除
        positionRepository
            .findById(id)
            .ifPresent(position -> {
                position.setIsDeleted(true);
                positionRepository.save(position);
            });
    }

    @Override
    @Transactional(readOnly = true)
    public Page<PositionDTO> findByCategory(PositionCategory category, Pageable pageable) {
        log.debug("Request to get Positions by category : {}", category);
        return positionRepository.findByCategory(category, pageable).map(positionMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<PositionDTO> findByLevelBetween(Integer minLevel, Integer maxLevel, Pageable pageable) {
        log.debug("Request to get Positions by level between {} and {}", minLevel, maxLevel);
        return positionRepository.findByLevelBetween(minLevel, maxLevel, pageable).map(positionMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<PositionDTO> searchByKeyword(String keyword, Pageable pageable) {
        log.debug("Request to search Positions by keyword : {}", keyword);
        return positionRepository.searchByKeyword(keyword, pageable).map(positionMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<PositionDTO> findByTenantIdAndCategory(Long tenantId, PositionCategory category, Pageable pageable) {
        log.debug("Request to get Positions by tenantId {} and category {}", tenantId, category);
        return positionRepository.findByTenantIdAndCategory(tenantId, category, pageable).map(positionMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<PositionDTO> findByTenantIdAndLevelBetween(Long tenantId, Integer minLevel, Integer
        maxLevel, Pageable pageable) {
        log.debug("Request to get Positions by tenantId {} and level between {} and {}", tenantId, minLevel, maxLevel);
        return positionRepository.findByTenantIdAndLevelBetween(tenantId, minLevel, maxLevel, pageable).map(positionMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<PositionDTO> findPositionsWithFilters(PositionCategory category, Long orgUnitId, String
        keyword, Pageable pageable) {
        log.debug("Request to find Positions with filters - category: {}, orgUnitId: {}, keyword: {}", category, orgUnitId, keyword);

        Long tenantId = Long.valueOf(HttpRequestUtil.getHeader(request, RequestConstants.X_TENANT_ID));

        Page<Position> page = positionRepository.findPositionsWithFilters(tenantId, category, orgUnitId, keyword, pageable);
        List<Position> content = page.getContent();
        if (content.isEmpty()) {
            return Page.empty(pageable);
        }

        //查询职位关联的员工数
        List<Long> positionIds = content.stream().map(Position::getId).toList();
        List<Object[]> countList = employeeOrgRepository.findPositionEmployeeCountByPositionIds(positionIds);
        Map<Long, Long> positionEmployeeCountMap = null;
        if (!CollectionUtils.isEmpty(countList)) {
            positionEmployeeCountMap = countList.stream().collect(Collectors.toMap(
                row -> (Long) row[NumberConstants.ZERO],
                row -> (Long) row[NumberConstants.ONE]
            ));
        }

        List<PositionDTO> dtoList = new ArrayList<>();
        //手动转换DTO，同时进行赋值
        for (Position position : content) {
            PositionDTO dto = positionMapper.toDto(position);
            if (position.getOrgUnit() != null) {
                dto.setOrgUnitId(position.getOrgUnit().getId());
                dto.setOrgUnitName(position.getOrgUnit().getName());
            }
            if (positionEmployeeCountMap != null) {
                dto.setEmployeeCount(positionEmployeeCountMap.getOrDefault(position.getId(), 0L));
            }
            dtoList.add(dto);
        }
        return new PageImpl<>(dtoList, pageable, page.getTotalElements());
    }

    @Override
    public List<PositionDTO> findByOrgUnitId(Long id) {
        log.debug("Request to get Positions by orgUnitId : {}", id);
        return positionRepository.findByOrgUnitId(id).stream().map(positionMapper::toDto).toList();
    }

    @Override
    @Transactional(readOnly = true)
    public List<PositionDTO> findByNameContainingIgnoreCase(String name) {
        log.debug("Request to search Positions by name containing : {}", name);

        // 从request header获取租户ID
        String tenantIdHeader = HttpRequestUtil.getHeader(request, RequestConstants.X_TENANT_ID);
        if (tenantIdHeader == null || tenantIdHeader.trim().isEmpty()) {
            log.warn("Tenant ID header is null or empty, this may cause data visibility issues");
            throw new IllegalArgumentException("请求头中缺少必需的租户ID");
        }

        Long tenantId;
        try {
            tenantId = Long.valueOf(tenantIdHeader);
        } catch (NumberFormatException e) {
            log.error("Invalid tenant ID format: {}", tenantIdHeader, e);
            throw new IllegalArgumentException("租户ID格式无效: " + tenantIdHeader, e);
        }

        log.debug("Searching positions by name containing '{}' for tenant ID: {}", name, tenantId);

        List<Position> positions = positionRepository.findByTenantIdAndNameContainingIgnoreCaseAndIsDeletedFalse(tenantId, name);
        if (positions.isEmpty()) {
            return new ArrayList<>();
        }

        List<PositionDTO> dtoList = new ArrayList<>();
        // 手动转换DTO，同时进行赋值
        positions.forEach(position -> {
            PositionDTO dto = positionMapper.toDto(position);
            if (position.getOrgUnit() != null) {
                dto.setOrgUnitId(position.getOrgUnit().getId());
                dto.setOrgUnitName(position.getOrgUnit().getName());
            }
            dtoList.add(dto);
        });

        return dtoList;
    }
}

package com.whiskerguard.organization.service.impl;

import com.whiskerguard.organization.service.AuthorityCacheService;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 描述：权限缓存的服务实现类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/18
 */
@Service
public class AuthorityCacheServiceImpl implements AuthorityCacheService {

    private static final Logger LOG = LoggerFactory.getLogger(AuthorityCacheServiceImpl.class);
    private static final String CACHE_KEY_PREFIX = "authority:";
    // 1小时
    private static final long CACHE_EXPIRATION = 3600;

    private final RedissonClient redissonClient;

    public AuthorityCacheServiceImpl(RedissonClient redissonClient) {
        this.redissonClient = redissonClient;
    }

    @Override
    public Set<GrantedAuthority> getAuthorities(String username) {
        String key = CACHE_KEY_PREFIX + username;
        RBucket<Set<String>> bucket = redissonClient.getBucket(key);
        Set<String> permissions = bucket.get();
        if (permissions == null) {
            return null;
        }
        return permissions.stream().map(SimpleGrantedAuthority::new)
            .collect(Collectors.toSet());
    }

    @Override
    public void cacheAuthorities(String username, Set<GrantedAuthority> authorities) {
        String key = CACHE_KEY_PREFIX + username;
        Set<String> permissions = authorities.stream()
            .map(GrantedAuthority::getAuthority)
            .collect(Collectors.toSet());
        redissonClient.getBucket(key).set(permissions);
        redissonClient.getBucket(key).expire(Duration.ofSeconds(CACHE_EXPIRATION));
        LOG.debug("Cached authorities for user: {}", username);
    }

    @Override
    public void evictAuthorities(String username) {
        String key = CACHE_KEY_PREFIX + username;
        redissonClient.getBucket(key).delete();
        LOG.debug("Evicted authorities cache for user: {}", username);
    }

    @Override
    public void evictAllAuthorities() {
        redissonClient.getKeys().deleteByPattern(CACHE_KEY_PREFIX + "*");
        LOG.debug("Evicted all authorities cache");
    }

}

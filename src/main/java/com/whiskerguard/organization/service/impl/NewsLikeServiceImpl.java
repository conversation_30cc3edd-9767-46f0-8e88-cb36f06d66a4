package com.whiskerguard.organization.service.impl;

import com.whiskerguard.common.config.RequestConstants;
import com.whiskerguard.common.constant.NumberConstants;
import com.whiskerguard.common.util.HttpRequestUtil;
import com.whiskerguard.organization.domain.NewsLike;
import com.whiskerguard.organization.repository.NewsLikeRepository;
import com.whiskerguard.organization.service.NewsLikeService;
import com.whiskerguard.organization.service.dto.NewsLikeDTO;
import com.whiskerguard.organization.service.mapper.NewsLikeMapper;
import jakarta.servlet.http.HttpServletRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.Optional;

/**
 * 描述：新闻点赞的服务实现类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/18
 */
@Service
@Transactional
public class NewsLikeServiceImpl implements NewsLikeService {

    private static final Logger LOG = LoggerFactory.getLogger(NewsLikeServiceImpl.class);

    private final NewsLikeRepository newsLikeRepository;

    private final NewsLikeMapper newsLikeMapper;

    private final HttpServletRequest request;

    public NewsLikeServiceImpl(NewsLikeRepository newsLikeRepository, NewsLikeMapper newsLikeMapper,
                               HttpServletRequest request) {
        this.newsLikeRepository = newsLikeRepository;
        this.newsLikeMapper = newsLikeMapper;
        this.request = request;
    }

    @Override
    public NewsLikeDTO save(NewsLikeDTO newsLikeDTO) {
        LOG.debug("Request to save NewsLike : {}", newsLikeDTO);
        NewsLike newsLike = newsLikeMapper.toEntity(newsLikeDTO);
        String username = HttpRequestUtil.getHeader(request, RequestConstants.X_USER_NAME);
        newsLike.setTenantId(Long.valueOf(HttpRequestUtil.getHeader(request, RequestConstants.X_TENANT_ID)));
        newsLike.setVersion(NumberConstants.ONE);
        newsLike.setCreatedBy(username);
        newsLike.setUpdatedBy(username);
        newsLike.setCreatedAt(Instant.now());
        newsLike.setUpdatedAt(Instant.now());
        newsLike.setIsDeleted(Boolean.FALSE);
        newsLike = newsLikeRepository.save(newsLike);
        return newsLikeMapper.toDto(newsLike);
    }

    @Override
    public NewsLikeDTO update(NewsLikeDTO newsLikeDTO) {
        LOG.debug("Request to update NewsLike : {}", newsLikeDTO);
        NewsLike newsLike = newsLikeMapper.toEntity(newsLikeDTO);
        newsLike = newsLikeRepository.save(newsLike);
        return newsLikeMapper.toDto(newsLike);
    }

    @Override
    public Optional<NewsLikeDTO> partialUpdate(NewsLikeDTO newsLikeDTO) {
        LOG.debug("Request to partially update NewsLike : {}", newsLikeDTO);

        return newsLikeRepository
            .findById(newsLikeDTO.getId())
            .map(existingNewsLike -> {
                newsLikeMapper.partialUpdate(existingNewsLike, newsLikeDTO);
                String username = HttpRequestUtil.getHeader(request, RequestConstants.X_USER_NAME);
                existingNewsLike.setUpdatedBy(username);
                existingNewsLike.setUpdatedAt(Instant.now());
                return existingNewsLike;
            })
            .map(newsLikeRepository::save)
            .map(newsLikeMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<NewsLikeDTO> findAll(Pageable pageable) {
        LOG.debug("Request to get all NewsLikes");
        return newsLikeRepository.findByIsDeletedFalse(pageable).map(newsLikeMapper::toDto);
    }

    @Override
    public Page<NewsLikeDTO> findAllWithEagerRelationships(Pageable pageable) {
        return newsLikeRepository.findAllWithEagerRelationshipsAndNotDeleted(pageable).map(newsLikeMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<NewsLikeDTO> findOne(Long id) {
        LOG.debug("Request to get NewsLike : {}", id);
        return newsLikeRepository.findOneWithEagerRelationships(id).map(newsLikeMapper::toDto);
    }

    @Override
    public void delete(Long id) {
        LOG.debug("Request to delete NewsLike : {}", id);
        // 逻辑删除：将isDeleted设置为true，而不是物理删除
        newsLikeRepository
            .findById(id)
            .ifPresent(newsLike -> {
                newsLike.setIsDeleted(true);
                newsLikeRepository.save(newsLike);
            });
    }
}

package com.whiskerguard.organization.service.impl;

import cn.hutool.core.date.DatePattern;
import com.whiskerguard.common.config.RequestConstants;
import com.whiskerguard.common.constant.NumberConstants;
import com.whiskerguard.common.dto.ImportResultDTO;
import com.whiskerguard.common.util.ExcelImportUtil;
import com.whiskerguard.common.util.HttpRequestUtil;
import com.whiskerguard.common.util.TenantContextUtil;
import com.whiskerguard.common.util.WgStringUtil;
import com.whiskerguard.organization.client.dto.SmsRequest;
import com.whiskerguard.organization.client.dto.VerificationCodeResponse;
import com.whiskerguard.organization.client.dto.VerificationCodeType;
import com.whiskerguard.organization.client.dto.VerificationCodeValidateRequest;
import com.whiskerguard.organization.client.general.GeneralServiceFeignClient;
import com.whiskerguard.organization.domain.*;
import com.whiskerguard.organization.domain.enumeration.EmployeeGender;
import com.whiskerguard.organization.domain.enumeration.EmployeeStatus;
import com.whiskerguard.organization.repository.*;
import com.whiskerguard.organization.request.EmployeeReq;
import com.whiskerguard.organization.service.EmployeeRoleService;
import com.whiskerguard.organization.service.EmployeeService;
import com.whiskerguard.organization.service.RolePermissionService;
import com.whiskerguard.organization.service.WechatPhoneLoginService;
import com.whiskerguard.organization.service.dto.*;
import com.whiskerguard.organization.service.exception.EmployeeNotFoundException;
import com.whiskerguard.organization.service.exception.InvalidPasswordException;
import com.whiskerguard.organization.service.exception.PasswordComplexityException;
import com.whiskerguard.organization.service.mapper.EmployeeMapper;
import com.whiskerguard.organization.service.mapper.OrgUnitMapper;
import com.whiskerguard.organization.service.mapper.PositionMapper;
import com.whiskerguard.organization.service.mapper.RoleMapper;
import com.whiskerguard.organization.util.ImportValidationUtil;
import com.whiskerguard.organization.util.PasswordUtils;
import com.whiskerguard.organization.web.rest.errors.BadRequestAlertException;
import jakarta.servlet.http.HttpServletRequest;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import java.time.Instant;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 描述：员工管理的服务实现类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/18
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class EmployeeServiceImpl implements EmployeeService {

    private static final Logger LOG = LoggerFactory.getLogger(EmployeeServiceImpl.class);

    private final EmployeeRepository employeeRepository;

    private final EmployeeMapper employeeMapper;

    private final PasswordEncoder passwordEncoder;

    private final EmployeeOrgRepository employeeOrgRepository;

    private final OrgUnitRepository orgUnitRepository;

    private final PositionRepository positionRepository;

    private final EmployeeRoleService employeeRoleService;

    private final RolePermissionService rolePermissionService;

    private final HttpServletRequest request;

    private final RoleRepository roleRepository;

    private final RoleMapper roleMapper;

    private final PositionMapper positionMapper;

    private final OrgUnitMapper orgUnitMapper;

    private final WechatPhoneLoginService wechatPhoneLoginService;

    private final GeneralServiceFeignClient generalServiceFeignClient;

    private final EmployeeRoleRepository employeeRoleRepository;

    @Value("${message.send:false}")
    private Boolean isSend;

    @Value("${mbb.base.password:mbb123456}")
    private String defaultPassword;

    public EmployeeServiceImpl(
        EmployeeRepository employeeRepository,
        EmployeeMapper employeeMapper,
        PasswordEncoder passwordEncoder,
        EmployeeOrgRepository employeeOrgRepository,
        OrgUnitRepository orgUnitRepository,
        PositionRepository positionRepository,
        EmployeeRoleService employeeRoleService,
        RolePermissionService rolePermissionService,
        HttpServletRequest request,
        RoleRepository roleRepository,
        RoleMapper roleMapper,
        PositionMapper positionMapper,
        OrgUnitMapper orgUnitMapper,
        @Autowired(required = false) WechatPhoneLoginService wechatPhoneLoginService,
        GeneralServiceFeignClient generalServiceFeignClient,
        EmployeeRoleRepository employeeRoleRepository) {
        this.employeeRepository = employeeRepository;
        this.employeeMapper = employeeMapper;
        this.passwordEncoder = passwordEncoder;
        this.employeeOrgRepository = employeeOrgRepository;
        this.orgUnitRepository = orgUnitRepository;
        this.positionRepository = positionRepository;
        this.employeeRoleService = employeeRoleService;
        this.rolePermissionService = rolePermissionService;
        this.request = request;
        this.roleRepository = roleRepository;
        this.roleMapper = roleMapper;
        this.positionMapper = positionMapper;
        this.orgUnitMapper = orgUnitMapper;
        this.wechatPhoneLoginService = wechatPhoneLoginService;
        this.generalServiceFeignClient = generalServiceFeignClient;
        this.employeeRoleRepository = employeeRoleRepository;
    }

    @Override
    public EmployeeDTO save(EmployeeDTO employeeDTO) {
        LOG.debug("Request to save Employee : {}", employeeDTO);
        //判断username是否已经存在
        if (employeeRepository.findByUsernameAndIsDeletedFalse(employeeDTO.getUsername()).isPresent()) {
            throw new BadRequestAlertException("用户名已存在", "employee", "用户名已存在");
        }
        //校验员工信息
        validateEmployee(employeeDTO);

        if (employeeRepository.findByPhoneAndIsDeletedFalse(employeeDTO.getPhone()).isPresent()) {
            throw new BadRequestAlertException("手机号已存在", "employee", "手机号已存在");
        }

        Employee employee = employeeMapper.toEntity(employeeDTO);

        // 生成盐值并加密密码
        if (employee.getPassword() != null) {
            String salt = PasswordUtils.generateSalt();
            employee.setSalt(salt);
            employee.setPassword(passwordEncoder.encode(employee.getPassword() + salt));
        }

        String username = HttpRequestUtil.getHeader(request, RequestConstants.X_USER_NAME);
        employee.setTenantId(Long.valueOf(HttpRequestUtil.getHeader(request, RequestConstants.X_TENANT_ID)));
        employee.setStatus(EmployeeStatus.ACTIVE);
        employee.setVersion(NumberConstants.ONE);
        employee.setCreatedBy(username);
        employee.setUpdatedBy(username);
        employee.setCreatedAt(Instant.now());
        employee.setUpdatedAt(Instant.now());
        employee.setIsDeleted(Boolean.FALSE);
        employee.setIsFirstLogin(Boolean.TRUE);
        employee.setForceChangePassword(Boolean.TRUE);

        employee = employeeRepository.save(employee);
        if (isSend) {
            LOG.info("Send sms to phone: {}", employee.getPhone());
            SmsRequest smsRequest = new SmsRequest();
            smsRequest.setRecipient(employee.getPhone());
            smsRequest.setTenantId(employee.getTenantId());
            smsRequest.setTemplateParams(Map.of("password", "您的初始密码为：" + employee.getPassword()));
            try {
                generalServiceFeignClient.sendSms(smsRequest);
            } catch (Exception e) {
                LOG.error("Send sms failed: {}", e.getMessage(), e);
            }
        }
        Long employeeId = employee.getId();
        //添加员工组织关系
        if (!CollectionUtils.isEmpty(employeeDTO.getEmployeeOrgList())) {
            employeeDTO.getEmployeeOrgList().forEach(employeeOrgDTO -> {
                assignToOrgUnit(employeeId, employeeOrgDTO.getOrgUnit().getId(),
                    employeeOrgDTO.getPosition().getId(), employeeOrgDTO.getIsPrimary());
            });
        }
        //添加角色信息
        if (!CollectionUtils.isEmpty(employeeDTO.getRoleList())) {
            List<EmployeeRole> list = new ArrayList<>();
            for (RoleDTO roleDTO : employeeDTO.getRoleList()) {
                EmployeeRole employeeRole = new EmployeeRole();
                employeeRole.setTenantId(employee.getTenantId());
                employeeRole.setAssignedBy(username);
                employeeRole.setAssignedAt(Instant.now());
                employeeRole.setVersion(NumberConstants.ONE);
                employeeRole.setCreatedBy(username);
                employeeRole.setUpdatedBy(username);
                employeeRole.setCreatedAt(Instant.now());
                employeeRole.setUpdatedAt(Instant.now());
                employeeRole.setIsDeleted(Boolean.FALSE);
                employeeRole.setEmployee(employee);
                employeeRole.setRole(roleRepository.findById(roleDTO.getId()).orElse(null));
                list.add(employeeRole);
            }
            employeeRoleRepository.saveAll(list);
        }
        return employeeMapper.toDto(employee);
    }

    /**
     * 校验员工信息
     *
     * @param employeeDTO 员工信息
     */
    private void validateEmployee(EmployeeDTO employeeDTO) {
        if (StringUtils.isNotBlank(employeeDTO.getPhone())) {
            //手机号校验
            if (!WgStringUtil.isValidMobile(employeeDTO.getPhone())) {
                throw new BadRequestAlertException("手机号格式不正确", "employee", "手机号格式不正确");
            }
        }

        // 验证密码复杂度
        if (employeeDTO.getPassword() != null && !PasswordUtils.isPasswordValid(employeeDTO.getPassword())) {
            throw new PasswordComplexityException(PasswordUtils.getPasswordRequirements());
        }

        //邮箱校验
        if (StringUtils.isNotBlank(employeeDTO.getEmail()) && !WgStringUtil.isValidEmail(employeeDTO.getEmail())) {
            throw new BadRequestAlertException("邮箱格式不正确", "employee", "email format error");
        }

        //身份证校验
        if (StringUtils.isNotBlank(employeeDTO.getIdCard()) && !WgStringUtil.isValidIdCard(employeeDTO.getIdCard())) {
            throw new BadRequestAlertException("身份证格式不正确", "employee", "id card format error");
        }

        //入职日期校验
        if (employeeDTO.getHireDate() != null && employeeDTO.getHireDate().isAfter(LocalDate.now())) {
            throw new BadRequestAlertException("入职日期不得晚于今天", "employee", "hire date later than today");
        }
    }

    @Override
    public EmployeeDTO update(EmployeeDTO employeeDTO) {
        LOG.debug("Request to update Employee : {}", employeeDTO);
        //校验员工信息
        validateEmployee(employeeDTO);

        Employee employee = employeeMapper.toEntity(employeeDTO);

        // 如果更新了密码，生成新的盐值并加密
        if (employee.getPassword() != null) {
            String salt = PasswordUtils.generateSalt();
            employee.setSalt(salt);
            employee.setPassword(passwordEncoder.encode(employee.getPassword() + salt));
            employee.setPasswordChangedTime(Instant.now());
        }

        employee = employeeRepository.save(employee);
        return employeeMapper.toDto(employee);
    }

    @Override
    public Optional<EmployeeDTO> partialUpdate(EmployeeDTO employeeDTO) {
        LOG.debug("Request to partially update Employee : {}", employeeDTO);
        Optional<Employee> optional = employeeRepository.findById(employeeDTO.getId());
        Employee employee = optional.orElseThrow(() -> new EmployeeNotFoundException("当前员工不存在"));

        //校验员工信息
        validateEmployee(employeeDTO);
        Optional<Employee> optionalEmployee = employeeRepository.findByPhoneAndIsDeletedFalse(employeeDTO.getPhone());
        Employee employeeByPhone = optionalEmployee.orElse(null);
        if (optionalEmployee.isPresent() && !employeeByPhone.getId().equals(employeeDTO.getId())) {
            throw new BadRequestAlertException("手机号已存在", "employee", "手机号已存在");
        }

        employeeMapper.partialUpdate(employee, employeeDTO);

        // 如果更新了密码，生成新的盐值并加密
        if (employeeDTO.getPassword() != null) {
            String salt = PasswordUtils.generateSalt();
            employee.setSalt(salt);
            employee.setPassword(passwordEncoder.encode(employeeDTO.getPassword() + salt));
            employee.setPasswordChangedTime(Instant.now());
        }
        String username = HttpRequestUtil.getHeader(request, RequestConstants.X_USER_NAME);
        employee.setUpdatedBy(username);
        employee.setUpdatedAt(Instant.now());
        employee = employeeRepository.save(employee);
        Long employeeId = employee.getId();
        //更新员工组织关系
        if (!CollectionUtils.isEmpty(employeeDTO.getEmployeeOrgList())) {
            //先删除员工组织关系
            employeeOrgRepository.deleteByEmployeeId(employeeId);
            employeeDTO.getEmployeeOrgList().forEach(employeeOrgDTO -> {
                assignToOrgUnit(employeeId, employeeOrgDTO.getOrgUnit().getId(),
                    employeeOrgDTO.getPosition().getId(), employeeOrgDTO.getIsPrimary());
            });
        }
        //更新角色信息
        if (!CollectionUtils.isEmpty(employeeDTO.getRoleList())) {
            //先删除员工角色关系
            employeeRoleRepository.deleteByEmployeeId(employeeId);
            List<EmployeeRole> list = new ArrayList<>();
            for (RoleDTO roleDTO : employeeDTO.getRoleList()) {
                EmployeeRole employeeRole = new EmployeeRole();
                employeeRole.setTenantId(employee.getTenantId());
                employeeRole.setAssignedBy(username);
                employeeRole.setAssignedAt(Instant.now());
                employeeRole.setVersion(NumberConstants.ONE);
                employeeRole.setCreatedBy(username);
                employeeRole.setUpdatedBy(username);
                employeeRole.setCreatedAt(Instant.now());
                employeeRole.setUpdatedAt(Instant.now());
                employeeRole.setIsDeleted(Boolean.FALSE);
                employeeRole.setEmployee(employee);
                employeeRole.setRole(roleRepository.findById(roleDTO.getId()).orElse(null));
                list.add(employeeRole);
            }
            employeeRoleRepository.saveAll(list);
        }

        return Optional.of(employeeMapper.toDto(employee));
    }

    @Override
    @Transactional(readOnly = true)
    public Page<EmployeeDTO> findByCondition(EmployeeReq employeeReq) {
        // 从request header获取租户ID
        Long tenantId = Long.valueOf(HttpRequestUtil.getHeader(request, RequestConstants.X_TENANT_ID));
        LOG.debug("Filtering employees by tenant ID: {}", tenantId);

        List<Long> employeeIds = new ArrayList<>();
        //根据用户类型查询对应的员工
        if (employeeReq.getUserType() != null) {
            List<Long> erIds = employeeRoleService.findEmployeeIdsByRoleCode(tenantId, employeeReq.getUserType());
            if (CollectionUtils.isEmpty(erIds)) {
                return Page.empty();
            }
            employeeIds.addAll(erIds);
        }

        if (employeeReq.getOrgUnitId() != null) {
            List<Long> eoIds = employeeOrgRepository.findEmployeeIdsByOrgUnitId(employeeReq.getOrgUnitId());
            if (CollectionUtils.isEmpty(eoIds)) {
                return Page.empty();
            }
            employeeIds.addAll(eoIds);
        }

        if (!CollectionUtils.isEmpty(employeeIds)) {
            employeeIds = employeeIds.stream().distinct().collect(Collectors.toList());
        }

        //添加排序
        Pageable pageable = PageRequest.of(employeeReq.getPageNumber(), employeeReq.getPageSize(),
            employeeReq.getSort());
        Page<Employee> page = employeeRepository.searchByCondition(tenantId, employeeReq, pageable, employeeIds);
        List<Employee> content = page.getContent();
        if (content.isEmpty()) {
            return Page.empty(pageable);
        }
        //获取员工ID集合
        List<Long> ids = content.stream().map(Employee::getId).toList();
        List<EmployeeOrg> orgList = employeeOrgRepository.findByEmployeeIdInAndIsDeletedFalse(ids);
        Map<Employee, List<EmployeeOrg>> map = orgList.stream().collect(Collectors.groupingBy(EmployeeOrg::getEmployee));
        List<EmployeeDTO> dtoList = new ArrayList<>();
        //手动转换DTO，同时进行赋值
        content.forEach(employee -> {
            EmployeeDTO dto = employeeMapper.toDto(employee);
            List<EmployeeOrg> list = map.get(employee);
            if (!CollectionUtils.isEmpty(list)) {
                EmployeeOrg employeeOrg = list.get(NumberConstants.ZERO);
                if (employeeOrg != null) {
                    OrgUnit orgUnit = employeeOrg.getOrgUnit();
                    dto.setDepartmentName(orgUnit == null ? "" : orgUnit.getName());
                    Position position = employeeOrg.getPosition();
                    dto.setPositionName(position == null ? "" : position.getName());
                }
            }
            dtoList.add(dto);
        });
        return new PageImpl<>(dtoList, pageable, page.getTotalElements());
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<EmployeeDTO> findOne(Long id) {
        LOG.debug("Request to get Employee : {}", id);
        Optional<EmployeeDTO> optional = employeeRepository.findById(id).map(employeeMapper::toDto);
        EmployeeDTO employeeDTO = optional.orElseThrow(() -> new EmployeeNotFoundException("员工未找到"));
        Long tenantId = Long.valueOf(HttpRequestUtil.getHeader(request, RequestConstants.X_TENANT_ID));
        List<EmployeeOrg> employeeOrgs = employeeOrgRepository.findByEmployeeId(id);
        if (!CollectionUtils.isEmpty(employeeOrgs)) {
            //获取员工岗位id集合，岗位不存在跳过
            Set<Long> positions = new HashSet<>();
            Set<Long> orgUnits = new HashSet<>();
            for (EmployeeOrg employeeOrg : employeeOrgs) {
                if (employeeOrg.getPosition() != null) {
                    positions.add(employeeOrg.getPosition().getId());
                }
                if (employeeOrg.getOrgUnit() != null) {
                    orgUnits.add(employeeOrg.getOrgUnit().getId());
                }
            }
            if (!CollectionUtils.isEmpty(positions)) {
                //员工岗位信息
                List<PositionDTO> positionList = positionRepository.findByTenantIdAndIdIn(tenantId, positions).stream().map(positionMapper::toDto).collect(Collectors.toList());
                employeeDTO.setPositionList(positionList);
            }
            if (!CollectionUtils.isEmpty(orgUnits)) {
                //员工组织信息
                List<OrgUnitDTO> orgUnitList = orgUnitRepository.findByTenantIdAndIdIn(tenantId, orgUnits).stream().map(orgUnitMapper::toDto).collect(Collectors.toList());
                employeeDTO.setOrgUnitList(orgUnitList);
            }
        }
        List<EmployeeRoleDTO> roleList = employeeRoleService.findByEmployeeId(id);
        if (!CollectionUtils.isEmpty(roleList)) {
            //员工角色信息
            Set<Long> roleIds = roleList.stream().map(employeeRoleDTO -> employeeRoleDTO.getRole().getId()).collect(Collectors.toSet());
            if (!CollectionUtils.isEmpty(roleIds)) {
                List<RoleDTO> roleDTOList = roleRepository.findByTenantIdAndIdIn(tenantId, roleIds).stream().map(roleMapper::toDto).collect(Collectors.toList());
                employeeDTO.setRoleList(roleDTOList);
            }
        }
        return optional;
    }

    @Override
    public void delete(Long id) {
        LOG.debug("Request to delete Employee : {}", id);
        // 逻辑删除：将isDeleted设置为true，而不是物理删除
        employeeRepository
            .findById(id)
            .ifPresent(employee -> {
                employee.setIsDeleted(true);
                employeeRepository.save(employee);
            });
    }

    @Override
    public EmployeeDTO changeStatus(Long id, EmployeeStatus status) {
        LOG.debug("Request to change Employee status : {} to {}", id, status);
        return employeeRepository
            .findById(id)
            .map(employee -> {
                employee.setStatus(status);
                return employeeRepository.save(employee);
            })
            .map(employeeMapper::toDto)
            .orElseThrow(() -> new RuntimeException("员工不存在"));
    }

    @Override
    @Transactional(readOnly = true)
    public Page<EmployeeDTO> findByOrgUnit(Long orgUnitId, Pageable pageable) {
        LOG.debug("Request to get Employees by OrgUnit : {}", orgUnitId);
        // 从request header获取租户ID
        Long tenantId = Long.valueOf(HttpRequestUtil.getHeader(request, RequestConstants.X_TENANT_ID));
        return employeeRepository.findByOrgUnitId(orgUnitId, tenantId, pageable).map(employeeMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<EmployeeDTO> findByStatus(EmployeeStatus status, Pageable pageable) {
        LOG.debug("Request to get Employees by status : {}", status);
        // 从request header获取租户ID
        Long tenantId = Long.valueOf(HttpRequestUtil.getHeader(request, RequestConstants.X_TENANT_ID));
        return employeeRepository.findByStatus(status, tenantId, pageable).map(employeeMapper::toDto);
    }

    @Override
    public EmployeeDTO assignToOrgUnit(Long employeeId, Long orgUnitId, Long positionId, Boolean isPrimary) {
        LOG.debug("Request to assign Employee : {} to OrgUnit : {} with Position : {}", employeeId, orgUnitId, positionId);

        // 获取员工
        Employee employee = employeeRepository.findById(employeeId).orElseThrow(() -> new RuntimeException("员工不存在"));

        // 获取组织单元
        OrgUnit orgUnit = orgUnitRepository.findById(orgUnitId).orElseThrow(() -> new RuntimeException("组织单元不存在"));

        // 获取职位
        Position position = positionRepository.findById(positionId).orElseThrow(() -> new RuntimeException("职位不存在"));

        // 创建员工-组织关联
        EmployeeOrg employeeOrg = new EmployeeOrg();
        employeeOrg.setEmployee(employee);
        employeeOrg.setOrgUnit(orgUnit);
        employeeOrg.setPosition(position);
        employeeOrg.setIsPrimary(isPrimary);
        employeeOrg.setStartDate(java.time.LocalDate.now());
        employeeOrg.setTenantId(employee.getTenantId());
        employeeOrg.setVersion(1);
        employeeOrg.setCreatedAt(java.time.Instant.now());
        employeeOrg.setUpdatedAt(java.time.Instant.now());
        employeeOrg.setIsDeleted(false);

        // 保存关联
        employeeOrgRepository.save(employeeOrg);
        return employeeMapper.toDto(employee);
    }

    @Override
    public void removeFromOrgUnit(Long employeeId, Long orgUnitId) {
        LOG.debug("Request to remove Employee : {} from OrgUnit : {}", employeeId, orgUnitId);

        // 查找并删除员工-组织关联
        employeeOrgRepository
            .findByEmployeeIdAndOrgUnitId(employeeId, orgUnitId)
            .ifPresent(employeeOrg -> {
                employeeOrg.setEndDate(java.time.LocalDate.now());
                employeeOrg.setIsDeleted(true);
                employeeOrg.setUpdatedAt(java.time.Instant.now());
                employeeOrgRepository.save(employeeOrg);
            });
    }

    @Override
    public LoginResponseDTO login(LoginDTO loginDTO, String clientIp) {
        LOG.debug("Request to login Employee : {}", loginDTO.getUsername());
        Employee employee = employeeRepository
            .findByUsername(loginDTO.getUsername()).orElse(null);
        if (employee == null) {
            return null;
        }

        // 验证密码
        if (!passwordEncoder.matches(loginDTO.getPassword() + employee.getSalt(), employee.getPassword())) {
            throw new InvalidPasswordException("密码错误");
        }

        // 更新登录信息
        employee.setLastLoginTime(Instant.now());
        employee.setLastLoginIp(StringUtils.isEmpty(clientIp) ? request.getRemoteAddr() : clientIp);
        employee.setLoginFailureCount(0);
        employee.setIsFirstLogin(false);
        employee.setForceChangePassword(false);
        employee = employeeRepository.save(employee);

        // 构建响应
        LoginResponseDTO response = new LoginResponseDTO();
        response.setEmployeeId(employee.getId());
        response.setUsername(employee.getUsername());
        response.setRealName(employee.getRealName());
        response.setEmail(employee.getEmail());
        response.setIsFirstLogin(employee.getIsFirstLogin());
        response.setForceChangePassword(employee.getForceChangePassword());
        response.setPasswordExpiredTime(employee.getPasswordExpiredTime());
        response.setLastLoginTime(employee.getLastLoginTime());
        response.setLastLoginIp(employee.getLastLoginIp());
        response.setToken("jwt-token-placeholder");
        return response;
    }

    @Override
    public EmployeeDTO wechatPhoneLogin(WechatPhoneLoginDTO loginDTO, String clientIp) {
        LOG.debug("Request to WeChat phone login with code: {}", loginDTO.getCode());

        try {
            // 1. 获取微信手机号信息
            WechatPhoneInfoDTO phoneInfo = wechatPhoneLoginService.getPhoneInfo(loginDTO);
            if (phoneInfo == null) {
                LOG.error("WeChat phone login failed: get phone info failed");
                throw new InvalidPasswordException("微信手机号一键登录失败，请重新尝试登录");
            }

            // 2. 根据手机号查找员工
            Optional<Employee> employeeOpt = employeeRepository.findByPhoneAndIsDeletedFalse(phoneInfo.getPurePhoneNumber());
            Employee employee = employeeOpt.orElseThrow(() -> new EmployeeNotFoundException("使用此手机号的员工不存在: " + phoneInfo.getPurePhoneNumber()));
            // 员工已存在，更新微信信息
            employee.setWechatOpenId(phoneInfo.getOpenId());
            employee.setWechatUnionId(phoneInfo.getUnionId());
            employee.setLastLoginTime(Instant.now());
            employee.setLastLoginIp(clientIp);
            employee.setLoginFailureCount(NumberConstants.ZERO);
            employee = employeeRepository.save(employee);

            try {
                // 绑定微信 - 检查openId是否为空，unionId可以为空
                String openId = phoneInfo.getOpenId();
                String unionId = phoneInfo.getUnionId();

                if (openId != null && !openId.trim().isEmpty()) {
                    ResponseEntity<Map<String, Object>> responseEntity = generalServiceFeignClient.
                        bindUser(openId, unionId, employee.getId(), employee.getTenantId(), employee.getUsername());
                    if (!responseEntity.getStatusCode().is2xxSuccessful() || responseEntity.getBody() == null) {
                        LOG.error("绑定微信失败: {}", responseEntity.getBody());
                    } else {
                        LOG.info("绑定微信成功: openId={}", openId);
                    }
                } else {
                    LOG.warn("微信OpenID为空，跳过绑定微信操作");
                }
            } catch (Exception e) {
                LOG.error("绑定微信失败: {}", e.getMessage(), e);
            }
            return employeeMapper.toDto(employee);
        } catch (Exception e) {
            LOG.error("WeChat phone login failed: {}", e.getMessage(), e);
            throw new InvalidPasswordException("微信手机号一键登录失败，请重新尝试登录");
        }
    }

    @Override
    public Optional<EmployeeDTO> findByPhone(String phone) {
        LOG.debug("Request to find Employee by phone: {}", phone);
        return employeeRepository.findByPhoneAndIsDeletedFalse(phone)
            .map(employeeMapper::toDto);
    }

    @Override
    public EmployeeDTO changePassword(Long employeeId, PasswordChangeDTO passwordChangeDTO) {
        LOG.debug("Request to change password for Employee : {}", employeeId);

        Employee employee = employeeRepository.findById(employeeId).orElseThrow(() -> new EmployeeNotFoundException("员工未找到"));

        // 验证当前密码
        if (!passwordEncoder.matches(passwordChangeDTO.getCurrentPassword() + employee.getSalt(), employee.getPassword())) {
            throw new InvalidPasswordException("当前密码不正确");
        }
        // 验证新密码是否与当前密码相同
        if (passwordChangeDTO.getNewPassword().equals(passwordChangeDTO.getCurrentPassword())) {
            throw new InvalidPasswordException("新密码与当前密码相同");
        }

        // 验证新密码复杂度
        if (!PasswordUtils.isPasswordValid(passwordChangeDTO.getNewPassword())) {
            throw new PasswordComplexityException(PasswordUtils.getPasswordRequirements());
        }

        // 更新密码
        String salt = PasswordUtils.generateSalt();
        employee.setSalt(salt);
        employee.setPassword(passwordEncoder.encode(passwordChangeDTO.getNewPassword() + salt));
        employee.setPasswordChangedTime(Instant.now());
        employee.setForceChangePassword(false);

        // 设置密码过期时间（例如 90 天后）
        employee.setPasswordExpiredTime(Instant.now().plusSeconds(90 * 24 * 60 * 60));

        employee = employeeRepository.save(employee);
        return employeeMapper.toDto(employee);
    }

    @Override
    public EmployeeDTO updateProfile(Long employeeId, EmployeeDTO employeeDTO) {
        LOG.debug("Request to update profile for Employee : {}", employeeId);

        Employee employee = employeeRepository.findById(employeeId).orElseThrow(() -> new EmployeeNotFoundException("Employee not found"));

        // 只允许更新部分字段
        employee.setRealName(employeeDTO.getRealName());
        employee.setEmail(employeeDTO.getEmail());
        employee.setPhone(employeeDTO.getPhone());
        employee.setGender(employeeDTO.getGender());
        employee.setBirthDate(employeeDTO.getBirthDate());
        employee.setIdCard(employeeDTO.getIdCard());
        employee.setEmployeeNo(employeeDTO.getEmployeeNo());

        employee = employeeRepository.save(employee);
        return employeeMapper.toDto(employee);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<EmployeeDTO> findByUsernameAndPassword(String username, String password, EmployeeStatus status) {
        LOG.debug("Request to find Employee by username and password : {}", username);

        return employeeRepository
            .findByUsernameAndPasswordAndStatusAndIsDeletedFalse(username, password, status)
            .map(employeeMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public EmployeeAuthDTO getEmployeeAuthInfo(Long id) {
        LOG.debug("Request to get Employee auth info : {}", id);

        Employee employee = employeeRepository.findById(id).orElseThrow(() -> new EmployeeNotFoundException("Employee not found"));

        EmployeeAuthDTO authDTO = new EmployeeAuthDTO();
        authDTO.setId(employee.getId());
        authDTO.setUsername(employee.getUsername());
        authDTO.setRealName(employee.getRealName());
        authDTO.setTenantId(employee.getTenantId());
        authDTO.setIsActive(EmployeeStatus.ACTIVE.equals(employee.getStatus()));

        // 获取员工角色
        Set<String> roles = employeeRoleService.findByEmployeeId(id).stream().map(er -> er.getRole().getCode()).collect(Collectors.toSet());
        authDTO.setRoles(roles);

        // 获取员工权限
        Set<String> permissions = rolePermissionService
            .findByEmployeeId(id)
            .stream()
            .map(rp -> rp.getPermission().getCode())
            .collect(Collectors.toSet());
        authDTO.setPermissions(permissions);

        return authDTO;
    }

    @Override
    public EmployeeDTO getEmployeeAuthInfoByUsername(String username) {
        LOG.debug("Request to get Employee auth info by username : {}", username);
        Employee employee = employeeRepository.findByUsername(username).orElse(null);
        return employeeMapper.toDto(employee);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ImportResultDTO<EmployeeImportDTO> importFromExcel(MultipartFile file) {
        LOG.debug("Request to import Employees from Excel file: {}", file.getOriginalFilename());
        // 1. 读取Excel文件
        List<EmployeeImportDTO> importList = ExcelImportUtil.readExcel(file, EmployeeImportDTO.class);
        LOG.info("从Excel文件中读取到{}条数据", importList.size());

        // 2. 验证和转换数据
        ImportResultDTO<EmployeeImportDTO> result = new ImportResultDTO<>();
        String username = HttpRequestUtil.getHeader(request, RequestConstants.X_USER_NAME);
        Long tenantId = Long.valueOf(HttpRequestUtil.getHeader(request, RequestConstants.X_TENANT_ID));
        Instant now = Instant.now();

        for (int i = 0; i < importList.size(); i++) {
            EmployeeImportDTO importDTO = importList.get(i);
            int rowNumber = i + 2; // Excel行号，第1行是表头，数据从第2行开始

            // 1. 字段级别验证
            List<String> validationErrors = ImportValidationUtil.validateEmployeeImportData(importDTO, rowNumber);
            if (!validationErrors.isEmpty()) {
                for (String error : validationErrors) {
                    result.addFailed(error);
                }
                continue;
            }

            // 2. 业务逻辑验证 - 验证用户名是否已存在
            Optional<Employee> existingEmployee = employeeRepository.findByUsernameAndIsDeletedFalse(importDTO.getUsername());
            if (existingEmployee.isPresent()) {
                LOG.warn("用户名已存在，跳过: {}", importDTO.getUsername());
                result.addFailed(ImportValidationUtil.formatErrorMessage(rowNumber, "登录用户名", "已存在: " + importDTO.getUsername()));
                continue;
            }

            // 3. 业务逻辑验证 - 验证手机号是否已存在
            if (StringUtils.isNotBlank(importDTO.getPhone())) {
                Optional<Employee> existingPhoneEmployee = employeeRepository.findByPhoneAndIsDeletedFalse(importDTO.getPhone());
                if (existingPhoneEmployee.isPresent()) {
                    LOG.warn("手机号已存在，跳过: {}", importDTO.getPhone());
                    result.addFailed(ImportValidationUtil.formatErrorMessage(rowNumber, "手机号", "已存在: " + importDTO.getPhone()));
                    continue;
                }
            }

            // 创建员工
            Employee employee = new Employee();
            employee.setTenantId(tenantId);
            employee.setUsername(importDTO.getUsername());
            String salt = PasswordUtils.generateSalt();
            employee.setSalt(salt);
            //设置默认密码
            employee.setPassword(passwordEncoder.encode(defaultPassword + salt));

            employee.setRealName(importDTO.getRealName());
            employee.setEmail(importDTO.getEmail());
            employee.setPhone(importDTO.getPhone());
            employee.setGender(EmployeeGender.fromString(importDTO.getGender()));
            employee.setBirthDate(LocalDate.parse(importDTO.getBirthDate(), DateTimeFormatter.ofPattern(DatePattern.NORM_DATE_PATTERN)));
            employee.setIdCard(importDTO.getIdCard());
            employee.setEmployeeNo(importDTO.getEmployeeNo());
            employee.setStatus(EmployeeStatus.ACTIVE);
            employee.setHireDate(LocalDate.parse(importDTO.getHireDate(), DateTimeFormatter.ofPattern(DatePattern.NORM_DATE_PATTERN)));

            // 设置审计字段
            employee.setVersion(NumberConstants.ONE);
            employee.setCreatedBy(username);
            employee.setUpdatedBy(username);
            employee.setCreatedAt(now);
            employee.setUpdatedAt(now);
            employee.setIsDeleted(Boolean.FALSE);
            employee.setIsFirstLogin(Boolean.TRUE);
            employee.setForceChangePassword(Boolean.TRUE);
            employee.setLoginFailureCount(NumberConstants.ZERO);

            // 保存员工
            employee = employeeRepository.save(employee);

            // 处理组织关系
            if (importDTO.getOrgUnitName() != null && !importDTO.getOrgUnitName().trim().isEmpty()) {
                Optional<OrgUnit> orgUnitOpt = orgUnitRepository.findByTenantIdAndNameAndIsDeletedFalse(
                    tenantId, importDTO.getOrgUnitName());
                if (orgUnitOpt.isPresent()) {
                    Long positionId = null;
                    if (importDTO.getPositionName() != null && !importDTO.getPositionName().trim().isEmpty()) {
                        Optional<Position> positionOpt = positionRepository.findByTenantIdAndNameAndIsDeletedFalse(
                            tenantId, importDTO.getPositionName());
                        if (positionOpt.isPresent()) {
                            positionId = positionOpt.orElse(null).getId();
                        } else {
                            LOG.warn("职位不存在: {}", importDTO.getPositionName());
                        }
                    }

                    if (positionId != null) {
                        assignToOrgUnit(employee.getId(), orgUnitOpt.orElse(null).getId(), positionId, true);
                    }
                } else {
                    LOG.warn("组织单元不存在: {}", importDTO.getOrgUnitName());
                }
            }

            // 处理角色关系
            if (importDTO.getRoleNames() != null && !importDTO.getRoleNames().trim().isEmpty()) {
                String[] roleNames = importDTO.getRoleNames().split(",");
                List<EmployeeRole> employeeRoles = new ArrayList<>();

                for (String roleName : roleNames) {
                    roleName = roleName.trim();
                    if (!roleName.isEmpty()) {
                        Optional<Role> roleOpt = roleRepository.findByTenantIdAndNameAndIsDeletedFalse(
                            tenantId, roleName);
                        if (roleOpt.isPresent()) {
                            EmployeeRole employeeRole = new EmployeeRole();
                            employeeRole.setTenantId(tenantId);
                            employeeRole.setEmployee(employee);
                            employeeRole.setRole(roleOpt.orElse(null));
                            employeeRole.setAssignedBy(username);
                            employeeRole.setAssignedAt(now);
                            employeeRole.setVersion(NumberConstants.ONE);
                            employeeRole.setCreatedBy(username);
                            employeeRole.setUpdatedBy(username);
                            employeeRole.setCreatedAt(now);
                            employeeRole.setUpdatedAt(now);
                            employeeRole.setIsDeleted(Boolean.FALSE);
                            employeeRoles.add(employeeRole);
                        } else {
                            LOG.warn("角色不存在: {}", roleName);
                        }
                    }
                }

                if (!employeeRoles.isEmpty()) {
                    employeeRoleRepository.saveAll(employeeRoles);
                }
            }

            result.addSuccess();
            LOG.debug("成功导入员工: {}", importDTO.getUsername());
        }

        LOG.info("成功导入{}条员工", result.getSuccessCount());
        return result;
    }

    /**
     * 验证导入数据
     *
     * @param importDTO 导入数据
     */
    private Boolean validateImportData(EmployeeImportDTO importDTO) {
        if (StringUtils.isNotBlank(importDTO.getPhone())) {
            //手机号校验
            if (!WgStringUtil.isValidMobile(importDTO.getPhone())) {
                return false;
            }
        }

        //邮箱校验
        if (StringUtils.isNotBlank(importDTO.getEmail()) && !WgStringUtil.isValidEmail(importDTO.getEmail())) {
            return false;
        }

        //身份证校验
        if (StringUtils.isNotBlank(importDTO.getIdCard()) && !WgStringUtil.isValidIdCard(importDTO.getIdCard())) {
            return false;
        }

        //入职日期校验
        return !StringUtils.isNotBlank(importDTO.getHireDate()) ||
            !LocalDate.parse(importDTO.getHireDate(), DateTimeFormatter.ofPattern(DatePattern.NORM_DATE_PATTERN)).isAfter(LocalDate.now());
    }

    @Override
    public EmployeeDTO resetPassword(PasswordChangeDTO passwordChangeDTO) {
        Employee employee = employeeRepository.findByPhoneAndIsDeletedFalse(passwordChangeDTO.getPhone()).orElseThrow(() -> new EmployeeNotFoundException("员工未找到"));
        // 验证新密码复杂度
        if (!PasswordUtils.isPasswordValid(passwordChangeDTO.getNewPassword())) {
            throw new PasswordComplexityException(PasswordUtils.getPasswordRequirements());
        }
        //校验新密码是否与当前密码相同
        if (passwordEncoder.matches(passwordChangeDTO.getNewPassword() + employee.getSalt(), employee.getPassword())) {
            throw new InvalidPasswordException("新密码与当前密码相同");
        }

        //校验验证码
        VerificationCodeValidateRequest verificationCodeValidateRequest = new VerificationCodeValidateRequest();
        verificationCodeValidateRequest.setPhoneNumber(employee.getPhone());
        verificationCodeValidateRequest.setCode(passwordChangeDTO.getVerifyCode());
        verificationCodeValidateRequest.setCodeType(VerificationCodeType.RESET_PASSWORD);
        ResponseEntity<VerificationCodeResponse> responseEntity = generalServiceFeignClient.validateVerificationCode(verificationCodeValidateRequest);
        if (!responseEntity.getStatusCode().is2xxSuccessful() || responseEntity.getBody() == null) {
            throw new InvalidPasswordException("验证码无效");
        }

        // 更新密码
        String salt = PasswordUtils.generateSalt();
        employee.setSalt(salt);
        employee.setPassword(passwordEncoder.encode(passwordChangeDTO.getNewPassword() + salt));
        employee.setPasswordChangedTime(Instant.now());
        employee.setForceChangePassword(false);

        // 设置密码过期时间（例如 90 天后）
        employee.setPasswordExpiredTime(Instant.now().plusSeconds(90 * 24 * 60 * 60));

        employee = employeeRepository.save(employee);
        return employeeMapper.toDto(employee);
    }

    @Override
    public void changePhone(PasswordChangeDTO passwordChangeDTO) {
        Long userId = Long.valueOf(HttpRequestUtil.getHeader(request, RequestConstants.X_USER_ID));
        //校验手机号
        if (!WgStringUtil.isValidMobile(passwordChangeDTO.getPhone())) {
            throw new BadRequestAlertException("手机号格式不正确", "employee", "phone format error");
        }

        Employee employee = employeeRepository.findById(userId).orElseThrow(() -> new EmployeeNotFoundException("员工不存在"));
        if (employee.getPhone().equals(passwordChangeDTO.getPhone())) {
            return;
        }
        if (employeeRepository.findByPhoneAndIsDeletedFalse(passwordChangeDTO.getPhone()).isPresent()) {
            throw new BadRequestAlertException("手机号已存在", "employee", "phone exists");
        }
        //校验验证码
        VerificationCodeValidateRequest verificationCodeValidateRequest = new VerificationCodeValidateRequest();
        verificationCodeValidateRequest.setPhoneNumber(passwordChangeDTO.getPhone());
        verificationCodeValidateRequest.setCode(passwordChangeDTO.getVerifyCode());
        verificationCodeValidateRequest.setCodeType(VerificationCodeType.BIND_PHONE);
        ResponseEntity<VerificationCodeResponse> responseEntity = generalServiceFeignClient.validateVerificationCode(verificationCodeValidateRequest);
        if (!responseEntity.getStatusCode().is2xxSuccessful() || responseEntity.getBody() == null) {
            throw new InvalidPasswordException("验证码错误");
        }
        employee.setPhone(passwordChangeDTO.getPhone());
        employeeRepository.save(employee);
    }

    @Override
    public List<EmployeeDTO> findByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return List.of();
        }
        List<Employee> list = employeeRepository.findByIdIn(ids);
        if (!CollectionUtils.isEmpty(list)) {
            return list.stream().map(employeeMapper::toDto).collect(Collectors.toList());
        }
        return List.of();
    }

    @Override
    public EmployeeDTO updateAvatar(Long id, String avatar) {
        Employee employee = employeeRepository.findById(id).orElseThrow(() -> new EmployeeNotFoundException("员工未找到"));
        employee.setAvatar(avatar);
        employee = employeeRepository.save(employee);
        return employeeMapper.toDto(employee);
    }

    @Override
    public List<Long> findEmployeeIdsByTenantId() {
        Long tenantId = TenantContextUtil.getCurrentTenantId();
        LOG.debug("Request to get all Employee IDs by tenant ID: {}", tenantId);
        List<Employee> employees = employeeRepository.findByTenantIdAndIsDeletedFalse(tenantId);
        return employees.stream().map(Employee::getId).collect(Collectors.toList());
    }

    @Override
    public EmployeeDTO bindOpenId(String openId, String unionId) {
        Long userId = Long.valueOf(HttpRequestUtil.getHeader(request, RequestConstants.X_USER_ID));
        Employee employee = employeeRepository.findById(userId).orElseThrow(() -> new EmployeeNotFoundException("当前员工不存在"));
        employee.setWechatOpenId(openId);
        employee.setWechatUnionId(unionId);
        employee = employeeRepository.save(employee);

        try {
            // 绑定微信 - unionId可以为空
            LOG.debug("绑定微信: openId={}, unionId={}", openId, unionId);
            ResponseEntity<Map<String, Object>> responseEntity = generalServiceFeignClient.bindUser(openId, unionId, userId, employee.getTenantId(), employee.getUsername());
            if (!responseEntity.getStatusCode().is2xxSuccessful() || responseEntity.getBody() == null) {
                throw new RuntimeException("绑定微信失败");
            }
            LOG.info("员工{}绑定微信成功: openId={}", employee.getUsername(), openId);
        } catch (Exception e) {
            LOG.error("绑定微信失败: {}", e.getMessage(), e);
            throw new RuntimeException("绑定微信失败: " + e.getMessage());
        }

        return employeeMapper.toDto(employee);
    }

    @Override
    public EmployeeDTO unbindOpenId(String openId) {
        Long userId = Long.valueOf(HttpRequestUtil.getHeader(request, RequestConstants.X_USER_ID));
        Employee employee = employeeRepository.findById(userId).orElseThrow(() -> new EmployeeNotFoundException("当前员工不存在"));
        employee.setWechatOpenId(null);
        employee.setWechatUnionId(null);
        employee = employeeRepository.save(employee);
        // 解绑微信
        ResponseEntity<Map<String, Object>> responseEntity = generalServiceFeignClient.unbindUser(openId);
        if (!responseEntity.getStatusCode().is2xxSuccessful() || responseEntity.getBody() == null) {
            throw new RuntimeException("解绑微信失败");
        }
        LOG.info("员工{}解绑微信成功", employee.getUsername());
        return employeeMapper.toDto(employee);
    }

    @Override
    public List<Long> findEmployeeIdsByOrgUnit(Long orgUnitId) {
        LOG.debug("Request to get all Employee IDs by org unit ID: {}", orgUnitId);
        List<EmployeeOrg> employeeOrgs = employeeOrgRepository.findByOrgUnitId(orgUnitId);
        if (!CollectionUtils.isEmpty(employeeOrgs)) {
            return employeeOrgs.stream().map(EmployeeOrg::getEmployee).map(Employee::getId)
                .distinct().collect(Collectors.toList());
        }
        return List.of();
    }

    @Override
    @Transactional(readOnly = true)
    public UserStatisticsDTO getUserStatistics() {
        LOG.debug("Request to get user statistics");

        // 获取当前租户ID
        Long tenantId = Long.valueOf(HttpRequestUtil.getHeader(request, RequestConstants.X_TENANT_ID));

        // 1. 总用户数统计
        long totalUsers = employeeRepository.countByTenantId(tenantId);

        // 2. 用户角色分布统计
        long adminUsers = employeeRoleRepository.countAdminEmployees(tenantId);
        long regularUsers = totalUsers - adminUsers;
        UserRoleStatisticsDTO roleStatistics = new UserRoleStatisticsDTO(adminUsers, regularUsers);

        // 3. 用户状态统计
        long activeUsers = employeeRepository.countByTenantIdAndStatus(tenantId, EmployeeStatus.ACTIVE);
        long inactiveUsers = totalUsers - activeUsers;
        UserStatusStatisticsDTO statusStatistics = new UserStatusStatisticsDTO(activeUsers, inactiveUsers);

        // 4. 部门员工分布统计
        List<DepartmentEmployeeStatisticsDTO> departmentStatistics = getDepartmentEmployeeStatistics(tenantId);

        return new UserStatisticsDTO(totalUsers, roleStatistics, statusStatistics, departmentStatistics);
    }

    /**
     * 获取部门员工分布统计
     *
     * @param tenantId 租户ID
     * @return 部门员工分布统计列表
     */
    private List<DepartmentEmployeeStatisticsDTO> getDepartmentEmployeeStatistics(Long tenantId) {
        List<DepartmentEmployeeStatisticsDTO> departmentStatistics = new ArrayList<>();

        // 获取各部门员工数量统计
        List<Map<String, Object>> departmentCounts = employeeRepository.countEmployeesByDepartment(tenantId);

        if (!CollectionUtils.isEmpty(departmentCounts)) {
            // 获取部门ID列表
            List<Long> departmentIds = departmentCounts.stream()
                .map(map -> ((Number) map.get("departmentId")).longValue())
                .collect(Collectors.toList());

            // 批量查询部门信息
            List<OrgUnit> departments = orgUnitRepository.findByIdIn(departmentIds);
            Map<Long, OrgUnit> departmentMap = departments.stream()
                .collect(Collectors.toMap(OrgUnit::getId, dept -> dept));

            // 构建部门统计信息
            for (Map<String, Object> countMap : departmentCounts) {
                Long departmentId = ((Number) countMap.get("departmentId")).longValue();
                Long employeeCount = ((Number) countMap.get("employeeCount")).longValue();

                OrgUnit department = departmentMap.get(departmentId);
                if (department != null) {
                    departmentStatistics.add(new DepartmentEmployeeStatisticsDTO(
                        departmentId,
                        department.getName(),
                        department.getCode(),
                        employeeCount
                    ));
                }
            }
        }

        // 添加未分配部门的员工统计
        long unassignedEmployees = employeeRepository.countEmployeesWithoutDepartment(tenantId);
        if (unassignedEmployees > 0) {
            departmentStatistics.add(new DepartmentEmployeeStatisticsDTO(
                null,
                "未分配部门",
                "UNASSIGNED",
                unassignedEmployees
            ));
        }

        return departmentStatistics;
    }

    @Override
    @Transactional(readOnly = true)
    public List<EmployeeDTO> searchByName(String name) {
        LOG.debug("Request to search employees by name: {}", name);

        // 获取当前租户ID
        Long tenantId = Long.valueOf(HttpRequestUtil.getHeader(request, RequestConstants.X_TENANT_ID));

        // 参数校验
        if (StringUtils.isBlank(name)) {
            LOG.debug("Search name is blank, returning empty list");
            return new ArrayList<>();
        }

        // 去除前后空格
        String searchName = name.trim();

        try {
            // 执行模糊搜索
            List<Employee> employees = employeeRepository.findByNameFuzzySearch(tenantId, searchName);

            if (CollectionUtils.isEmpty(employees)) {
                LOG.debug("No employees found for search name: {}", searchName);
                return new ArrayList<>();
            }
            LOG.debug("Found {} employees for search name: {}", employees.size(), searchName);
            return employees.stream()
                .map(employeeMapper::toDto)
                .collect(Collectors.toList());
        } catch (Exception e) {
            LOG.error("Error searching employees by name: {}", searchName, e);
            throw new RuntimeException("搜索员工失败: " + e.getMessage(), e);
        }
    }

}

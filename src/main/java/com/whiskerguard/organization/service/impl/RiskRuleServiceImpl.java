package com.whiskerguard.organization.service.impl;

import com.whiskerguard.common.config.RequestConstants;
import com.whiskerguard.common.constant.NumberConstants;
import com.whiskerguard.common.util.HttpRequestUtil;
import com.whiskerguard.organization.domain.RiskCategory;
import com.whiskerguard.organization.domain.RiskRule;
import com.whiskerguard.organization.repository.RiskCategoryRepository;
import com.whiskerguard.organization.repository.RiskRuleRepository;
import com.whiskerguard.organization.service.RiskRuleService;
import com.whiskerguard.organization.service.dto.RiskRuleDTO;
import com.whiskerguard.organization.service.mapper.RiskRuleMapper;
import com.whiskerguard.organization.web.rest.errors.BadRequestAlertException;
import jakarta.servlet.http.HttpServletRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.Optional;

/**
 * 描述：风险规则的服务实现类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/18
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class RiskRuleServiceImpl implements RiskRuleService {

    private static final Logger LOG = LoggerFactory.getLogger(RiskRuleServiceImpl.class);

    private final RiskRuleRepository riskRuleRepository;

    private final RiskRuleMapper riskRuleMapper;

    private final HttpServletRequest request;

    private final RiskCategoryRepository riskCategoryRepository;

    public RiskRuleServiceImpl(RiskRuleRepository riskRuleRepository, RiskRuleMapper riskRuleMapper,
                               HttpServletRequest request, RiskCategoryRepository riskCategoryRepository) {
        this.riskRuleRepository = riskRuleRepository;
        this.riskRuleMapper = riskRuleMapper;
        this.request = request;
        this.riskCategoryRepository = riskCategoryRepository;
    }

    @Override
    public RiskRuleDTO save(RiskRuleDTO riskRuleDTO) {
        LOG.debug("Request to save RiskRule : {}", riskRuleDTO);
        Long tenantId = Long.valueOf(HttpRequestUtil.getHeader(request, RequestConstants.X_TENANT_ID));
        //判断当前风险规则是否已经存在，如果存在则抛出异常
        if (null != riskRuleRepository.findByTenantIdAndNameOrCode(tenantId, riskRuleDTO.getName(), riskRuleDTO.getCode())) {
            throw new BadRequestAlertException("风险规则已经存在", "riskRule", "riskRule exists");
        }
        //判断RiskCategory是否存在
        if (null == riskRuleDTO.getRiskCategory().getId()) {
            throw new BadRequestAlertException("风险类别不能为空", "riskCategory", "riskCategory is null");
        }
        Optional<RiskCategory> optional = riskCategoryRepository.findById(riskRuleDTO.getRiskCategory().getId());
        RiskCategory riskCategory = optional.orElseThrow(() -> new BadRequestAlertException("风险类别不存在", "riskCategory", "riskCategory not found"));
        RiskRule riskRule = riskRuleMapper.toEntity(riskRuleDTO);
        String username = HttpRequestUtil.getHeader(request, RequestConstants.X_USER_NAME);
        riskRule.setRiskCategory(riskCategory);
        riskRule.setTenantId(tenantId);
        riskRule.setVersion(NumberConstants.ONE);
        riskRule.setCreatedBy(username);
        riskRule.setUpdatedBy(username);
        riskRule.setCreatedAt(Instant.now());
        riskRule.setUpdatedAt(Instant.now());
        riskRule.setIsDeleted(Boolean.FALSE);
        riskRule = riskRuleRepository.save(riskRule);
        return riskRuleMapper.toDto(riskRule);
    }

    @Override
    public Optional<RiskRuleDTO> partialUpdate(RiskRuleDTO riskRuleDTO) {
        LOG.debug("Request to partially update RiskRule : {}", riskRuleDTO);
        //判断当前风险规则是否已经存在，如果存在则抛出异常
        Long tenantId = Long.valueOf(HttpRequestUtil.getHeader(request, RequestConstants.X_TENANT_ID));
        RiskRule riskRule = riskRuleRepository.findByTenantIdAndNameOrCode(tenantId, riskRuleDTO.getName(), riskRuleDTO.getCode());
        if (null != riskRule && !riskRule.getId().equals(riskRuleDTO.getId())) {
            throw new BadRequestAlertException("风险规则已经存在", "riskRule", "riskRule exists");
        }
        riskRule = riskRuleRepository.findById(riskRuleDTO.getId()).orElseThrow(() -> new BadRequestAlertException("风险规则不存在", "riskRule", "riskRule not found"));
        if (null != riskRuleDTO.getRiskCategory() && null != riskRuleDTO.getRiskCategory().getId()) {
            Optional<RiskCategory> optional = riskCategoryRepository.findById(riskRuleDTO.getRiskCategory().getId());
            RiskCategory riskCategory = optional.orElseThrow(() -> new BadRequestAlertException("风险类别不存在", "riskCategory", "riskCategory not found"));
            riskRule.setRiskCategory(riskCategory);
        }
        riskRuleMapper.partialUpdate(riskRule, riskRuleDTO);
        String username = HttpRequestUtil.getHeader(request, RequestConstants.X_USER_NAME);
        riskRule.setUpdatedBy(username);
        riskRule.setUpdatedAt(Instant.now());
        riskRuleRepository.save(riskRule);
        return Optional.of(riskRuleMapper.toDto(riskRule));
    }

    @Override
    @Transactional(readOnly = true)
    public Page<RiskRuleDTO> findAll(Long riskCategoryId, Pageable pageable) {
        LOG.debug("Request to get all RiskRules");
        return riskRuleRepository.findAllByRiskCategoryId(riskCategoryId, pageable).map(riskRuleMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<RiskRuleDTO> findOne(Long id) {
        LOG.debug("Request to get RiskRule : {}", id);
        return riskRuleRepository.findOneWithEagerRelationships(id).map(riskRuleMapper::toDto);
    }

    @Override
    public void delete(Long id) {
        LOG.debug("Request to delete RiskRule : {}", id);
        riskRuleRepository.findById(id).ifPresent(riskRule -> {
            riskRule.setIsDeleted(Boolean.TRUE);
            riskRuleRepository.save(riskRule);
        });
    }
}

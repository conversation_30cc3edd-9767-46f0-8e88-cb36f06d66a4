package com.whiskerguard.organization.service.impl;

import com.whiskerguard.common.config.RequestConstants;
import com.whiskerguard.common.constant.NumberConstants;
import com.whiskerguard.common.util.HttpRequestUtil;
import com.whiskerguard.organization.domain.NewsReadRecord;
import com.whiskerguard.organization.repository.NewsReadRecordRepository;
import com.whiskerguard.organization.service.NewsReadRecordService;
import com.whiskerguard.organization.service.dto.NewsReadRecordDTO;
import com.whiskerguard.organization.service.mapper.NewsReadRecordMapper;
import jakarta.servlet.http.HttpServletRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.Optional;

/**
 * 描述：新闻阅读记录的服务实现类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/18
 */
@Service
@Transactional
public class NewsReadRecordServiceImpl implements NewsReadRecordService {

    private static final Logger LOG = LoggerFactory.getLogger(NewsReadRecordServiceImpl.class);

    private final NewsReadRecordRepository newsReadRecordRepository;

    private final NewsReadRecordMapper newsReadRecordMapper;

    private final HttpServletRequest request;

    public NewsReadRecordServiceImpl(NewsReadRecordRepository newsReadRecordRepository, NewsReadRecordMapper newsReadRecordMapper,
                                     HttpServletRequest request) {
        this.newsReadRecordRepository = newsReadRecordRepository;
        this.newsReadRecordMapper = newsReadRecordMapper;
        this.request = request;
    }

    @Override
    public NewsReadRecordDTO save(NewsReadRecordDTO newsReadRecordDTO) {
        LOG.debug("Request to save NewsReadRecord : {}", newsReadRecordDTO);
        NewsReadRecord newsReadRecord = newsReadRecordMapper.toEntity(newsReadRecordDTO);
        String username = HttpRequestUtil.getHeader(request, RequestConstants.X_USER_NAME);
        newsReadRecord.setTenantId(Long.valueOf(HttpRequestUtil.getHeader(request, RequestConstants.X_TENANT_ID)));
        newsReadRecord.setVersion(NumberConstants.ONE);
        newsReadRecord.setCreatedBy(username);
        newsReadRecord.setUpdatedBy(username);
        newsReadRecord.setCreatedAt(Instant.now());
        newsReadRecord.setUpdatedAt(Instant.now());
        newsReadRecord.setIsDeleted(Boolean.FALSE);
        newsReadRecord = newsReadRecordRepository.save(newsReadRecord);
        return newsReadRecordMapper.toDto(newsReadRecord);
    }

    @Override
    public NewsReadRecordDTO update(NewsReadRecordDTO newsReadRecordDTO) {
        LOG.debug("Request to update NewsReadRecord : {}", newsReadRecordDTO);
        NewsReadRecord newsReadRecord = newsReadRecordMapper.toEntity(newsReadRecordDTO);
        newsReadRecord = newsReadRecordRepository.save(newsReadRecord);
        return newsReadRecordMapper.toDto(newsReadRecord);
    }

    @Override
    public Optional<NewsReadRecordDTO> partialUpdate(NewsReadRecordDTO newsReadRecordDTO) {
        LOG.debug("Request to partially update NewsReadRecord : {}", newsReadRecordDTO);

        return newsReadRecordRepository
            .findById(newsReadRecordDTO.getId())
            .map(existingNewsReadRecord -> {
                newsReadRecordMapper.partialUpdate(existingNewsReadRecord, newsReadRecordDTO);
                String username = HttpRequestUtil.getHeader(request, RequestConstants.X_USER_NAME);
                existingNewsReadRecord.setUpdatedBy(username);
                existingNewsReadRecord.setUpdatedAt(Instant.now());
                return existingNewsReadRecord;
            })
            .map(newsReadRecordRepository::save)
            .map(newsReadRecordMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<NewsReadRecordDTO> findAll(Pageable pageable) {
        LOG.debug("Request to get all NewsReadRecords");
        return newsReadRecordRepository.findByIsDeletedFalse(pageable).map(newsReadRecordMapper::toDto);
    }

    @Override
    public Page<NewsReadRecordDTO> findAllWithEagerRelationships(Pageable pageable) {
        return newsReadRecordRepository.findAllWithEagerRelationshipsAndNotDeleted(pageable).map(newsReadRecordMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<NewsReadRecordDTO> findOne(Long id) {
        LOG.debug("Request to get NewsReadRecord : {}", id);
        return newsReadRecordRepository.findOneWithEagerRelationships(id).map(newsReadRecordMapper::toDto);
    }

    @Override
    public void delete(Long id) {
        LOG.debug("Request to delete NewsReadRecord : {}", id);
        // 逻辑删除：将isDeleted设置为true，而不是物理删除
        newsReadRecordRepository
            .findById(id)
            .ifPresent(newsReadRecord -> {
                newsReadRecord.setIsDeleted(true);
                newsReadRecordRepository.save(newsReadRecord);
            });
    }
}

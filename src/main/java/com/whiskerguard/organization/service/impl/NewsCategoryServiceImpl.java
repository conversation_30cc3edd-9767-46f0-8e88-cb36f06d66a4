package com.whiskerguard.organization.service.impl;

import com.whiskerguard.common.config.RequestConstants;
import com.whiskerguard.common.constant.NumberConstants;
import com.whiskerguard.common.util.HttpRequestUtil;
import com.whiskerguard.organization.domain.NewsCategory;
import com.whiskerguard.organization.repository.NewsCategoryRepository;
import com.whiskerguard.organization.service.NewsCategoryService;
import com.whiskerguard.organization.service.dto.NewsCategoryDTO;
import com.whiskerguard.organization.service.mapper.NewsCategoryMapper;
import com.whiskerguard.organization.web.rest.errors.BadRequestAlertException;
import jakarta.servlet.http.HttpServletRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.Optional;

/**
 * 描述：新闻分类的服务实现类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/18
 */
@Service
@Transactional
public class NewsCategoryServiceImpl implements NewsCategoryService {

    private static final Logger LOG = LoggerFactory.getLogger(NewsCategoryServiceImpl.class);

    private final NewsCategoryRepository newsCategoryRepository;

    private final NewsCategoryMapper newsCategoryMapper;

    private final HttpServletRequest request;

    public NewsCategoryServiceImpl(NewsCategoryRepository newsCategoryRepository, NewsCategoryMapper newsCategoryMapper,
                                   HttpServletRequest request) {
        this.newsCategoryRepository = newsCategoryRepository;
        this.newsCategoryMapper = newsCategoryMapper;
        this.request = request;
    }

    @Override
    public NewsCategoryDTO save(NewsCategoryDTO newsCategoryDTO) {
        LOG.debug("Request to save NewsCategory : {}", newsCategoryDTO);
        Long tenantId = Long.valueOf(HttpRequestUtil.getHeader(request, RequestConstants.X_TENANT_ID));
        if (null != newsCategoryRepository.findByNameAndTenantId(newsCategoryDTO.getName(), tenantId)) {
            throw new BadRequestAlertException("新闻分类已存在", "newsCategory", "newsCategory exists");
        }
        NewsCategory newsCategory = newsCategoryMapper.toEntity(newsCategoryDTO);
        String username = HttpRequestUtil.getHeader(request, RequestConstants.X_USER_NAME);
        newsCategory.setTenantId(tenantId);
        newsCategory.setVersion(NumberConstants.ONE);
        newsCategory.setCreatedBy(username);
        newsCategory.setUpdatedBy(username);
        newsCategory.setCreatedAt(Instant.now());
        newsCategory.setUpdatedAt(Instant.now());
        newsCategory.setIsDeleted(Boolean.FALSE);
        newsCategory = newsCategoryRepository.save(newsCategory);
        return newsCategoryMapper.toDto(newsCategory);
    }

    @Override
    public NewsCategoryDTO update(NewsCategoryDTO newsCategoryDTO) {
        LOG.debug("Request to update NewsCategory : {}", newsCategoryDTO);
        NewsCategory newsCategory = newsCategoryMapper.toEntity(newsCategoryDTO);
        newsCategory = newsCategoryRepository.save(newsCategory);
        return newsCategoryMapper.toDto(newsCategory);
    }

    @Override
    public Optional<NewsCategoryDTO> partialUpdate(NewsCategoryDTO newsCategoryDTO) {
        LOG.debug("Request to partially update NewsCategory : {}", newsCategoryDTO);
        Long tenantId = Long.valueOf(HttpRequestUtil.getHeader(request, RequestConstants.X_TENANT_ID));
        NewsCategory newsCategory = newsCategoryRepository.findByNameAndTenantId(newsCategoryDTO.getName(), tenantId);
        if (null != newsCategory && !newsCategoryDTO.getId().equals(newsCategory.getId())) {
            throw new BadRequestAlertException("新闻分类已存在", "newsCategory", "newsCategory exists");
        }

        return newsCategoryRepository
            .findById(newsCategoryDTO.getId())
            .map(existingNewsCategory -> {
                newsCategoryMapper.partialUpdate(existingNewsCategory, newsCategoryDTO);
                String username = HttpRequestUtil.getHeader(request, RequestConstants.X_USER_NAME);
                existingNewsCategory.setUpdatedBy(username);
                existingNewsCategory.setUpdatedAt(Instant.now());
                return existingNewsCategory;
            })
            .map(newsCategoryRepository::save)
            .map(newsCategoryMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<NewsCategoryDTO> findAll(Pageable pageable) {
        Long tenantId = Long.valueOf(HttpRequestUtil.getHeader(request, RequestConstants.X_TENANT_ID));
        LOG.debug("Request to get all NewsCategories by tenant ID: {}", tenantId);
        return newsCategoryRepository.findByIsDeletedFalse(tenantId, pageable).map(newsCategoryMapper::toDto);
    }

    @Override
    public Page<NewsCategoryDTO> findAllWithEagerRelationships(Pageable pageable) {
        Long tenantId = Long.valueOf(HttpRequestUtil.getHeader(request, RequestConstants.X_TENANT_ID));
        LOG.debug("Request to get all NewsCategories in eager by tenant ID: {}", tenantId);
        return newsCategoryRepository.findAllWithEagerRelationshipsAndNotDeleted(tenantId, pageable).map(newsCategoryMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<NewsCategoryDTO> findOne(Long id) {
        LOG.debug("Request to get NewsCategory : {}", id);
        return newsCategoryRepository.findOneWithEagerRelationships(id).map(newsCategoryMapper::toDto);
    }

    @Override
    public void delete(Long id) {
        LOG.debug("Request to delete NewsCategory : {}", id);
        // 逻辑删除：将isDeleted设置为true，而不是物理删除
        newsCategoryRepository
            .findById(id)
            .ifPresent(newsCategory -> {
                newsCategory.setIsDeleted(true);
                newsCategoryRepository.save(newsCategory);
            });
    }
}

package com.whiskerguard.organization.service.impl;

import com.whiskerguard.common.constant.NumberConstants;
import com.whiskerguard.common.util.WgStringUtil;
import com.whiskerguard.organization.client.dto.EmailRequest;
import com.whiskerguard.organization.client.dto.NotificationResponse;
import com.whiskerguard.organization.client.general.GeneralServiceFeignClient;
import com.whiskerguard.organization.domain.Reservation;
import com.whiskerguard.organization.repository.ReservationRepository;
import com.whiskerguard.organization.service.ReservationService;
import com.whiskerguard.organization.service.dto.ReservationDTO;
import com.whiskerguard.organization.service.mapper.ReservationMapper;
import com.whiskerguard.organization.web.rest.errors.BadRequestAlertException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.List;

/**
 * 描述：预约体验表的服务实现类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/18
 */
@Service
@Transactional
public class ReservationServiceImpl implements ReservationService {

    private static final Logger LOG = LoggerFactory.getLogger(ReservationServiceImpl.class);

    private final ReservationRepository reservationRepository;

    private final ReservationMapper reservationMapper;

    private final GeneralServiceFeignClient generalServiceFeignClient;

    public ReservationServiceImpl(ReservationRepository reservationRepository, ReservationMapper reservationMapper,
                                  GeneralServiceFeignClient generalServiceFeignClient) {
        this.reservationRepository = reservationRepository;
        this.reservationMapper = reservationMapper;
        this.generalServiceFeignClient = generalServiceFeignClient;
    }

    /**
     * 方法名称：save
     * 描述：保存预约体验记录
     *
     * @param reservationDTO 要保存的预约体验记录DTO
     * @return 保存后的预约体验记录DTO
     * @since 1.0
     */
    @Override
    public ReservationDTO save(ReservationDTO reservationDTO) {
        LOG.debug("Request to save Reservation : {}", reservationDTO);
        //校验手机号
        if (!WgStringUtil.isValidMobile(reservationDTO.getMobile())) {
            throw new BadRequestAlertException("手机号格式不正确，请重新填写", "reservation", "phone format error");
        }
        //校验邮箱
        if (!WgStringUtil.isValidEmail(reservationDTO.getEmail())) {
            throw new BadRequestAlertException("邮箱格式不正确，请重新填写", "reservation", "email format error");
        }
        //查询预约体验记录是否已经存在
        Reservation reservation = reservationRepository.findByName(reservationDTO.getName());
        if (reservation != null) {
            //更新
            reservationMapper.partialUpdate(reservation, reservationDTO);
            reservation.setUpdatedAt(Instant.now());
            reservation.setUpdatedBy(reservationDTO.getName());
            reservationRepository.save(reservation);
            return reservationMapper.toDto(reservation);
        }
        reservation = reservationMapper.toEntity(reservationDTO);
        reservation.setVersion(NumberConstants.ONE);
        reservation.setCreatedBy(reservationDTO.getName());
        reservation.setUpdatedBy(reservationDTO.getName());
        reservation.setCreatedAt(Instant.now());
        reservation.setUpdatedAt(Instant.now());
        reservation.setIsDeleted(Boolean.FALSE);
        reservation = reservationRepository.save(reservation);

        //预约成功发送邮件通知
        try {
            ResponseEntity<NotificationResponse> responseEntity = generalServiceFeignClient.sendEmailAsync(new EmailRequest(List.of("<EMAIL>"), "预约体验",
                reservationDTO.getName() + "先生/女士，已成功预约体验，请客服小姐姐及时联系处理相关事宜，谢谢！"));
            if (!responseEntity.getStatusCode().is2xxSuccessful() || responseEntity.getBody() == null) {
                LOG.error("发送邮件失败: {}", responseEntity.getBody());
            }
            LOG.info("发送邮件成功");
        } catch (Exception e) {
            LOG.error("发送邮件失败: {}", e.getMessage(), e);
        }
        return reservationMapper.toDto(reservation);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<ReservationDTO> findAll(Pageable pageable) {
        LOG.debug("Request to get all Reservations");
        return reservationRepository.findAll(pageable).map(reservationMapper::toDto);
    }

}

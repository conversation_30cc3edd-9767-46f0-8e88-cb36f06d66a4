package com.whiskerguard.organization.service.impl;

import com.whiskerguard.common.config.RequestConstants;
import com.whiskerguard.common.constant.NumberConstants;
import com.whiskerguard.common.util.HttpRequestUtil;
import com.whiskerguard.common.util.WgStringUtil;
import com.whiskerguard.organization.domain.Tenant;
import com.whiskerguard.organization.domain.TenantProfile;
import com.whiskerguard.organization.repository.EmployeeRepository;
import com.whiskerguard.organization.repository.TenantProfileRepository;
import com.whiskerguard.organization.repository.TenantRepository;
import com.whiskerguard.organization.service.TenantProfileService;
import com.whiskerguard.organization.service.dto.TenantProfileDTO;
import com.whiskerguard.organization.service.mapper.TenantMapper;
import com.whiskerguard.organization.service.mapper.TenantProfileMapper;
import com.whiskerguard.organization.web.rest.errors.BadRequestAlertException;
import jakarta.servlet.http.HttpServletRequest;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.time.LocalDate;
import java.util.Optional;

/**
 * 描述：租户详情的服务实现类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/18
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class TenantProfileServiceImpl implements TenantProfileService {

    private static final Logger LOG = LoggerFactory.getLogger(TenantProfileServiceImpl.class);

    private static final String ENTITY_NAME = "tenantProfile";

    private final TenantProfileRepository tenantProfileRepository;

    private final TenantProfileMapper tenantProfileMapper;

    private final HttpServletRequest request;

    private final EmployeeRepository employeeRepository;

    private final TenantRepository tenantRepository;

    private final TenantMapper tenantMapper;

    public TenantProfileServiceImpl(TenantProfileRepository tenantProfileRepository, TenantProfileMapper tenantProfileMapper,
                                    HttpServletRequest request, EmployeeRepository employeeRepository, TenantMapper tenantMapper,
                                    TenantRepository tenantRepository) {
        this.tenantProfileRepository = tenantProfileRepository;
        this.tenantProfileMapper = tenantProfileMapper;
        this.request = request;
        this.employeeRepository = employeeRepository;
        this.tenantMapper = tenantMapper;
        this.tenantRepository = tenantRepository;
    }

    @Override
    public TenantProfileDTO save(TenantProfileDTO tenantProfileDTO) {
        LOG.debug("Request to save TenantProfile : {}", tenantProfileDTO);
        //校验租户配置信息
        validateTenantProfile(tenantProfileDTO);

        //判断租户是否存在
        if (tenantProfileDTO.getTenant() == null || StringUtils.isBlank(tenantProfileDTO.getTenant().getTenantCode())) {
            throw new BadRequestAlertException("租户编码不能为空", ENTITY_NAME, "invalid Tenant");
        }

        //判断租户是否存在
        if (tenantRepository.findByTenantCodeAndIsDeletedFalse(tenantProfileDTO.getTenant().getTenantCode()).isEmpty()) {
            throw new BadRequestAlertException("租户不存在", ENTITY_NAME, "invalid Tenant");
        }

        Optional<Tenant> optional = tenantRepository.findByTenantCodeAndIsDeletedFalse(tenantProfileDTO.getTenant().getTenantCode());
        Tenant tenant = optional.orElseThrow(() -> new BadRequestAlertException("无效的请求", ENTITY_NAME, "invalid Tenant"));
        TenantProfile tenantProfile = tenantProfileMapper.toEntity(tenantProfileDTO);
        String username = HttpRequestUtil.getHeader(request, RequestConstants.X_USER_NAME);
        tenantProfile.setTenant(tenant);
        tenantProfile.setVersion(NumberConstants.ONE);
        tenantProfile.setCreatedBy(username);
        tenantProfile.setUpdatedBy(username);
        tenantProfile.setCreatedAt(Instant.now());
        tenantProfile.setUpdatedAt(Instant.now());
        tenantProfile.setIsDeleted(Boolean.FALSE);
        tenantProfile = tenantProfileRepository.save(tenantProfile);
        return tenantProfileMapper.toDto(tenantProfile);
    }

    /**
     * 校验租户配置信息
     *
     * @param tenantProfileDTO 租户配置DTO
     */
    private void validateTenantProfile(TenantProfileDTO tenantProfileDTO) {
        //注册日期不得晚于今天
        if (tenantProfileDTO.getRegistrationDate() != null && tenantProfileDTO.getRegistrationDate().isAfter(LocalDate.now())) {
            throw new BadRequestAlertException("注册日期不得晚于今天", ENTITY_NAME, "registration date later than today");
        }
        //校验工商注册号
        if (!WgStringUtil.isValidUnifiedSocialCreditCode(tenantProfileDTO.getRegistrationNumber())) {
            throw new BadRequestAlertException("工商注册号格式不正确", ENTITY_NAME, "registration number format error");
        }
        //校验组织机构代码
        if (validateOrganizationCode(tenantProfileDTO.getOrganizationCode())) {
            throw new BadRequestAlertException("组织机构代码存在非法字符", ENTITY_NAME, "organization code exists");
        }
        //校验website
        if (StringUtils.isNotBlank(tenantProfileDTO.getWebsite()) && !WgStringUtil.isValidUrl(tenantProfileDTO.getWebsite())) {
            throw new BadRequestAlertException("官网格式不正确", ENTITY_NAME, "website format error");
        }
        //校验邮箱
        if (StringUtils.isNotBlank(tenantProfileDTO.getContactEmail()) && !WgStringUtil.isValidEmail(tenantProfileDTO.getContactEmail())) {
            throw new BadRequestAlertException("邮箱格式不正确", ENTITY_NAME, "email format error");
        }
        //校验手机号
        if (StringUtils.isNotBlank(tenantProfileDTO.getContactMobile()) && !WgStringUtil.isValidMobile(tenantProfileDTO.getContactMobile())) {
            throw new BadRequestAlertException("手机号格式不正确", ENTITY_NAME, "phone format error");
        }
        //校验银行卡号，是否为纯数字
        if (StringUtils.isNotBlank(tenantProfileDTO.getBankAccount()) && !WgStringUtil.isNumeric(tenantProfileDTO.getBankAccount())) {
            throw new BadRequestAlertException("银行卡号格式不正确", ENTITY_NAME, "bank account format error");
        }
        //校验身份证
        if (StringUtils.isNotBlank(tenantProfileDTO.getLegalPersonId()) && !WgStringUtil.isValidIdCard(tenantProfileDTO.getLegalPersonId())) {
            throw new BadRequestAlertException("法人证件号格式不正确", ENTITY_NAME, "legal person id format error");
        }
    }

    /**
     * 校验组织机构代码
     *
     * @param organizationCode 组织机构代码
     * @return true表示存在非法字符，false表示合法
     */
    private boolean validateOrganizationCode(String organizationCode) {
        return WgStringUtil.containsChineseChar(organizationCode) || WgStringUtil.containsSpecialChar(organizationCode) ||
            WgStringUtil.containsEmoji(organizationCode) || WgStringUtil.containsHtmlTag(organizationCode) ||
            WgStringUtil.containsSqlInjectionRisk(organizationCode) || WgStringUtil.containsXssRisk(organizationCode);
    }


    @Override
    public TenantProfileDTO update(TenantProfileDTO tenantProfileDTO) {
        LOG.debug("Request to update TenantProfile : {}", tenantProfileDTO);
        //校验租户配置信息
        validateTenantProfile(tenantProfileDTO);
        TenantProfile tenantProfile = tenantProfileMapper.toEntity(tenantProfileDTO);
        tenantProfile = tenantProfileRepository.save(tenantProfile);
        return tenantProfileMapper.toDto(tenantProfile);
    }

    @Override
    public Optional<TenantProfileDTO> partialUpdate(TenantProfileDTO tenantProfileDTO) {
        LOG.debug("Request to partially update TenantProfile : {}", tenantProfileDTO);
        //校验租户配置信息
        validateTenantProfile(tenantProfileDTO);

        return tenantProfileRepository
            .findById(tenantProfileDTO.getId())
            .map(existingTenantProfile -> {
                tenantProfileMapper.partialUpdate(existingTenantProfile, tenantProfileDTO);
                String username = HttpRequestUtil.getHeader(request, RequestConstants.X_USER_NAME);
                existingTenantProfile.setUpdatedBy(username);
                existingTenantProfile.setUpdatedAt(Instant.now());
                return existingTenantProfile;
            })
            .map(tenantProfileRepository::save)
            .map(tenantProfileMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<TenantProfileDTO> findAll(Pageable pageable) {
        Long tenantId = Long.valueOf(HttpRequestUtil.getHeader(request, RequestConstants.X_TENANT_ID));
        LOG.debug("Request to get all TenantProfiles by tenant ID: {}", tenantId);
        return tenantProfileRepository.findByIsDeletedFalse(tenantId, pageable).map(tenantProfileMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<TenantProfileDTO> findOne(Long id) {
        LOG.debug("Request to get TenantProfile : {}", id);
        return tenantProfileRepository.findById(id).map(tenantProfileMapper::toDto);
    }

    @Override
    public void delete(Long id) {
        LOG.debug("Request to delete TenantProfile : {}", id);
        // 逻辑删除：将isDeleted设置为true，而不是物理删除
        tenantProfileRepository
            .findById(id)
            .ifPresent(tenantProfile -> {
                tenantProfile.setIsDeleted(true);
                tenantProfileRepository.save(tenantProfile);
            });
    }

    @Override
    @Transactional(readOnly = true)
    public boolean existsByRegistrationNumber(String registrationNumber) {
        LOG.debug("Request to check if TenantProfile exists by registration number : {}", registrationNumber);
        return tenantProfileRepository.findByRegistrationNumber(registrationNumber).isPresent();
    }

    @Override
    @Transactional(readOnly = true)
    public void validateRegistrationNumber(String registrationNumber) {
        LOG.debug("Request to validate TenantProfile registration number : {}", registrationNumber);

        if (existsByRegistrationNumber(registrationNumber)) {
            throw new BadRequestAlertException("租户配置注册号已存在", ENTITY_NAME, "regnumberexists");
        }
    }

    @Override
    public Optional<TenantProfileDTO> findByTenantId(Long tenantId) {
        LOG.debug("Request to find TenantProfile by tenantId : {}", tenantId);
        Optional<TenantProfile> optional = tenantProfileRepository.findByTenantId(tenantId);
        TenantProfile tenantProfile = optional.orElseThrow(() -> new BadRequestAlertException("租户配置未找到", ENTITY_NAME, "tenant id find"));
        Tenant tenant = tenantProfile.getTenant();
        TenantProfileDTO dto = tenantProfileMapper.toDto(tenantProfile);
        dto.setTenant(tenantMapper.toDto(tenant));
        //获取该租户的所有员工数量
        int employeeCount = employeeRepository.countByTenantId(tenantId);
        dto.setEmployeeCount(employeeCount);
        return Optional.of(dto);
    }
}

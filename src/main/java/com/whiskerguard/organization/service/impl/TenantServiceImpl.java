package com.whiskerguard.organization.service.impl;

import cn.hutool.core.date.DatePattern;
import com.whiskerguard.common.config.RequestConstants;
import com.whiskerguard.common.constant.NumberConstants;
import com.whiskerguard.common.dto.ImportResultDTO;
import com.whiskerguard.common.util.ExcelImportUtil;
import com.whiskerguard.common.util.HttpRequestUtil;
import com.whiskerguard.common.util.WgStringUtil;
import com.whiskerguard.organization.client.licensse.LicenseServiceFeignClient;
import com.whiskerguard.organization.domain.*;
import com.whiskerguard.organization.domain.enumeration.EmployeeStatus;
import com.whiskerguard.organization.domain.enumeration.EnterpriseType;
import com.whiskerguard.organization.domain.enumeration.OrgUnitType;
import com.whiskerguard.organization.domain.enumeration.TenantStatus;
import com.whiskerguard.organization.repository.*;
import com.whiskerguard.organization.service.InitExternalService;
import com.whiskerguard.organization.service.TenantProfileService;
import com.whiskerguard.organization.service.TenantService;
import com.whiskerguard.organization.service.dto.*;
import com.whiskerguard.organization.service.exception.TenantStatusException;
import com.whiskerguard.organization.service.mapper.TenantAttachmentMapper;
import com.whiskerguard.organization.service.mapper.TenantMapper;
import com.whiskerguard.organization.service.mapper.TenantProfileMapper;
import com.whiskerguard.organization.util.ImportValidationUtil;
import com.whiskerguard.organization.util.PasswordUtils;
import com.whiskerguard.organization.util.TenantCodeGenerator;
import com.whiskerguard.organization.web.rest.errors.BadRequestAlertException;
import jakarta.servlet.http.HttpServletRequest;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 描述：租户管理的服务实现类
 * 提供租户的创建、更新、查询、删除等功能
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/18
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class TenantServiceImpl implements TenantService {

    private static final Logger LOG = LoggerFactory.getLogger(TenantServiceImpl.class);

    private static final String ENTITY_NAME = "tenant";

    private final TenantRepository tenantRepository;

    private final TenantMapper tenantMapper;

    private final TenantProfileRepository tenantProfileRepository;

    private final HttpServletRequest request;

    private final TenantInitializeRepository tenantInitializeRepository;

    private final OrgUnitRepository orgUnitRepository;

    private final PositionRepository positionRepository;

    private final RoleRepository roleRepository;

    private final LicenseServiceFeignClient licenseServiceFeignClient;

    private final EmployeeRepository employeeRepository;

    private final EmployeeRoleRepository employeeRoleRepository;

    private final PasswordEncoder passwordEncoder;

    private final TenantProfileMapper tenantProfileMapper;

    private final TenantAttachmentRepository tenantAttachmentRepository;

    private final TenantAttachmentMapper tenantAttachmentMapper;

    private final InitExternalService initExternalService;

    @Value("${mbb.base.password:mbb123456}")
    private String defaultPassword;

    private final TenantProfileService tenantProfileService;

    public TenantServiceImpl(TenantRepository tenantRepository, TenantMapper tenantMapper,
                             TenantProfileRepository tenantProfileRepository, HttpServletRequest request,
                             TenantInitializeRepository tenantInitializeRepository,
                             OrgUnitRepository orgUnitRepository, PositionRepository positionRepository,
                             RoleRepository roleRepository, LicenseServiceFeignClient licenseServiceFeignClient,
                             EmployeeRepository employeeRepository, EmployeeRoleRepository employeeRoleRepository,
                             PasswordEncoder passwordEncoder, TenantProfileMapper tenantProfileMapper,
                             TenantAttachmentRepository tenantAttachmentRepository,
                             TenantAttachmentMapper tenantAttachmentMapper,
                             InitExternalService initExternalService,
                             TenantProfileService tenantProfileService
    ) {
        this.tenantRepository = tenantRepository;
        this.tenantMapper = tenantMapper;
        this.tenantProfileRepository = tenantProfileRepository;
        this.request = request;
        this.tenantInitializeRepository = tenantInitializeRepository;
        this.orgUnitRepository = orgUnitRepository;
        this.positionRepository = positionRepository;
        this.roleRepository = roleRepository;
        this.licenseServiceFeignClient = licenseServiceFeignClient;
        this.employeeRepository = employeeRepository;
        this.employeeRoleRepository = employeeRoleRepository;
        this.passwordEncoder = passwordEncoder;
        this.tenantProfileMapper = tenantProfileMapper;
        this.tenantAttachmentRepository = tenantAttachmentRepository;
        this.tenantAttachmentMapper = tenantAttachmentMapper;
        this.initExternalService = initExternalService;
        this.tenantProfileService = tenantProfileService;
    }

    @Override
    public TenantDTO save(TenantDTO tenantDTO) {
        LOG.debug("Request to save Tenant : {}", tenantDTO);
        //校验租户信息
        validateTenant(tenantDTO);

        //查询租户是否已经存在
        Optional<Tenant> optional = tenantRepository.findByTenantCodeAndIsDeletedFalse(tenantDTO.getTenantCode());
        if (optional.isPresent()) {
            throw new BadRequestAlertException("租户编码已存在", ENTITY_NAME, "name exists");
        }

        Tenant tenant = tenantMapper.toEntity(tenantDTO);
        tenant.setVersion(NumberConstants.ONE);
        String username = HttpRequestUtil.getHeader(request, RequestConstants.X_USER_NAME);
        tenant.setCreatedBy(username);
        tenant.setUpdatedBy(username);
        tenant.setCreatedAt(Instant.now());
        tenant.setUpdatedAt(Instant.now());
        tenant.setIsDeleted(Boolean.FALSE);

        //查询parent是否存在
        if (tenantDTO.getParentId() != null) {
            Optional<Tenant> parent = tenantRepository.findByIdAndIsDeletedFalse(tenantDTO.getParentId());
            if (parent.isEmpty()) {
                throw new BadRequestAlertException("父级租户不存在", ENTITY_NAME, "parent not found");
            }
        }
        tenant.setParentId(tenantDTO.getParentId());
        tenant = tenantRepository.save(tenant);

        //添加租户详情
        TenantProfileDTO profileDTO = tenantDTO.getProfile();
        TenantProfile tenantProfile = tenantProfileMapper.toEntity(profileDTO);
        tenantProfile.setTenant(tenant);
        tenantProfile.setVersion(NumberConstants.ONE);
        tenantProfile.setCreatedBy(username);
        tenantProfile.setUpdatedBy(username);
        tenantProfile.setCreatedAt(Instant.now());
        tenantProfile.setUpdatedAt(Instant.now());
        tenantProfile.setIsDeleted(Boolean.FALSE);
        tenantProfile.setEnterpriseType(profileDTO.getEnterpriseType() == null ? EnterpriseType.LARGE :
            EnterpriseType.fromValue(profileDTO.getEnterpriseType()));
        tenantProfileRepository.save(tenantProfile);

        //添加租户附件
        if (CollectionUtils.isNotEmpty(tenantDTO.getAttachments())) {
            for (TenantAttachmentDTO attachment : tenantDTO.getAttachments()) {
                TenantAttachment tenantAttachment = tenantAttachmentMapper.toEntity(attachment);
                tenantAttachment.setTenant(tenant);
                tenantAttachment.setVersion(NumberConstants.ONE);
                tenantAttachment.setUploadedBy(username);
                tenantAttachment.setUploadedAt(Instant.now());
                tenantAttachment.setUpdatedBy(username);
                tenantAttachment.setUpdatedAt(Instant.now());
                tenantAttachment.setIsDeleted(Boolean.FALSE);
                tenantAttachmentRepository.save(tenantAttachment);
            }
        }

        // 初始化租户数据
        initTenantData(tenant, username);
        return tenantMapper.toDto(tenant);
    }

    /**
     * 校验租户信息
     *
     * @param tenantDTO 租户DTO
     */
    private void validateTenant(TenantDTO tenantDTO) {
        //校验tenantCode
        if (validateTenantCode(tenantDTO.getTenantCode())) {
            throw new BadRequestAlertException("租户编码存在非法字符", ENTITY_NAME, "tenant code exists");
        }
        //套餐开始日期必须大于套餐结束日期
        if (tenantDTO.getSubscriptionStart() != null && tenantDTO.getSubscriptionEnd() != null
            && tenantDTO.getSubscriptionStart().isAfter(tenantDTO.getSubscriptionEnd())) {
            throw new BadRequestAlertException("套餐开始日期必须大于套餐结束日期", ENTITY_NAME, "subscription start after end");
        }
        //校验手机号
        if (StringUtils.isNotBlank(tenantDTO.getContactPhone()) && !WgStringUtil.isValidMobile(tenantDTO.getContactPhone())) {
            throw new BadRequestAlertException("手机号格式不正确", ENTITY_NAME, "phone format error");
        }
        //校验邮箱
        if (StringUtils.isNotBlank(tenantDTO.getContactEmail()) && !WgStringUtil.isValidEmail(tenantDTO.getContactEmail())) {
            throw new BadRequestAlertException("邮箱格式不正确", ENTITY_NAME, "email format error");
        }
    }

    /**
     * 校验租户编码
     *
     * @param tenantCode 租户编码
     * @return true表示存在非法字符，false表示合法
     */
    private boolean validateTenantCode(String tenantCode) {
        return WgStringUtil.containsChineseChar(tenantCode) || WgStringUtil.containsSpecialChar(tenantCode) ||
            WgStringUtil.containsEmoji(tenantCode) || WgStringUtil.containsHtmlTag(tenantCode) ||
            WgStringUtil.containsSqlInjectionRisk(tenantCode) || WgStringUtil.containsXssRisk(tenantCode);
    }

    /**
     * 更新租户
     *
     * @param tenantDTO 租户DTO
     * @return 更新后的租户DTO
     */
    @Override
    public TenantDTO update(TenantDTO tenantDTO) {
        LOG.debug("Request to update Tenant : {}", tenantDTO);
        //校验租户信息
        validateTenant(tenantDTO);

        //查询租户是否已经存在
        Tenant tenant = tenantRepository.findByNameOrTenantCode(tenantDTO.getName(), tenantDTO.getTenantCode());
        if (tenant != null && !tenant.getId().equals(tenantDTO.getId())) {
            throw new BadRequestAlertException("租户名称或租户编码已存在", ENTITY_NAME, "tenant exists");
        }
        String username = HttpRequestUtil.getHeader(request, RequestConstants.X_USER_NAME);
        tenant = tenantMapper.toEntity(tenantDTO);
        tenant.setUpdatedBy(username);
        tenant.setUpdatedAt(Instant.now());

        //更新parent
        if (tenantDTO.getParentId() != null) {
            Optional<Tenant> parent = tenantRepository.findByIdAndIsDeletedFalse(tenantDTO.getParentId());
            if (parent.isEmpty()) {
                throw new BadRequestAlertException("父级租户不存在", ENTITY_NAME, "parent not found");
            }
        }
        tenant.setParentId(tenantDTO.getParentId());
        tenant = tenantRepository.save(tenant);
        //更新租户详情
        tenantProfileRepository.findByTenantId(tenant.getId()).ifPresent(tenantProfile -> {
            tenantProfileMapper.partialUpdate(tenantProfile, tenantDTO.getProfile());
            tenantProfile.setUpdatedBy(username);
            tenantProfile.setUpdatedAt(Instant.now());
            tenantProfileRepository.save(tenantProfile);
        });
        return tenantMapper.toDto(tenant);
    }

    /**
     * 部分更新租户
     * 只更新提供的字段，其他字段保持不变
     *
     * @param tenantDTO 包含要更新字段的租户DTO
     * @return 更新后的租户DTO
     */
    @Override
    public Optional<TenantDTO> partialUpdate(TenantDTO tenantDTO) {
        LOG.debug("Request to partially update Tenant : {}", tenantDTO);
        //校验租户信息
        validateTenant(tenantDTO);

        //查询租户是否已经存在
        Tenant tenant = tenantRepository.findByNameOrTenantCode(tenantDTO.getName(), tenantDTO.getTenantCode());
        if (tenant != null && !tenant.getId().equals(tenantDTO.getId())) {
            throw new BadRequestAlertException("租户名称或租户编码已存在", ENTITY_NAME, "tenant exists");
        }

        Optional<Tenant> optional = tenantRepository.findById(tenantDTO.getId())
            .map(existingTenant -> {
                tenantMapper.partialUpdate(existingTenant, tenantDTO);
                String username = HttpRequestUtil.getHeader(request, RequestConstants.X_USER_NAME);
                //更新parent
                if (tenantDTO.getParentId() != null) {
                    Optional<Tenant> parent = tenantRepository.findByIdAndIsDeletedFalse(tenantDTO.getParentId());
                    if (parent.isEmpty()) {
                        throw new BadRequestAlertException("父级租户不存在", ENTITY_NAME, "parent not found");
                    }
                }
                existingTenant.setParentId(tenantDTO.getParentId());
                existingTenant.setUpdatedBy(username);
                existingTenant.setUpdatedAt(Instant.now());
                return existingTenant;
            }).map(tenantRepository::save);

        if (optional.isPresent()) {
            tenant = optional.orElse(null);
            //更新租户详情
            tenantProfileRepository.findByTenantId(tenant.getId()).ifPresent(tenantProfile -> {
                tenantProfileMapper.partialUpdate(tenantProfile, tenantDTO.getProfile());
                String username = HttpRequestUtil.getHeader(request, RequestConstants.X_USER_NAME);
                tenantProfile.setUpdatedBy(username);
                tenantProfile.setUpdatedAt(Instant.now());
                tenantProfileRepository.save(tenantProfile);
            });
        }
        return Optional.of(tenantMapper.toDto(tenant));
    }

    /**
     * 查询所有租户
     *
     * @param pageable 分页参数
     * @return 租户DTO分页结果
     */
    @Override
    @Transactional(readOnly = true)
    public Page<TenantDTO> findAll(Pageable pageable) {
        LOG.debug("Request to get all Tenants");
        return tenantRepository.findByIsDeletedFalse(pageable).map(tenantMapper::toDto);
    }

    /**
     * 根据ID查询租户
     *
     * @param id 租户ID
     * @return 租户DTO
     */
    @Override
    @Transactional(readOnly = true)
    public Optional<TenantDTO> findOne(Long id) {
        LOG.debug("Request to get Tenant : {}", id);
        Optional<TenantDTO> optional = tenantRepository.findById(id).map(tenantMapper::toDto);
        TenantDTO tenantDTO = optional.orElseThrow(() -> new BadRequestAlertException("租户未找到", ENTITY_NAME, "tenant not found"));

        //查询父级租户
        if (tenantDTO.getParentId() != null) {
            Optional<TenantDTO> parent = tenantRepository.findByIdAndIsDeletedFalse(tenantDTO.getParentId()).map(tenantMapper::toDto);
            tenantDTO.setParent(parent.orElse(null));
        }

        //获取租户详情
        Optional<TenantProfileDTO> tenantProfileDTO = tenantProfileService.findByTenantId(id);
        tenantDTO.setProfile(tenantProfileDTO.orElse(null));

        //获取租户附件
        List<TenantAttachment> attachments = tenantAttachmentRepository.findByTenantId(id);
        tenantDTO.setAttachments(attachments.stream().map(tenantAttachmentMapper::toDto).toList());

        return Optional.of(tenantDTO);
    }

    /**
     * 删除租户
     *
     * @param id 租户ID
     */
    @Override
    public void delete(Long id) {
        LOG.debug("Request to delete Tenant : {}", id);
        tenantRepository
            .findById(id)
            .ifPresent(tenant -> {
                tenant.setIsDeleted(Boolean.TRUE);
                tenantRepository.save(tenant);
            });
        //删除租户详情
        tenantProfileRepository.findByTenantId(id).ifPresent(tenantProfile -> {
            tenantProfile.setIsDeleted(Boolean.TRUE);
            tenantProfileRepository.save(tenantProfile);
        });
        //删除租户附件
        tenantAttachmentRepository.findByTenantId(id).forEach(tenantAttachment -> {
            tenantAttachment.setIsDeleted(Boolean.TRUE);
            tenantAttachmentRepository.save(tenantAttachment);
        });
    }

    /**
     * 变更租户状态
     * 根据业务规则变更租户状态，并更新相关字段
     *
     * @param id           租户ID
     * @param targetStatus 目标状态
     * @return 更新后的租户DTO
     */
    @Override
    public TenantDTO changeStatus(Long id, TenantStatus targetStatus) {
        LOG.debug("Request to change Tenant status : {} to {}", id, targetStatus);

        Tenant tenant = tenantRepository.findById(id).orElseThrow(() -> new RuntimeException("Tenant not found"));

        TenantStatus currentStatus = TenantStatus.fromValue(tenant.getStatus());

        // 验证状态流转是否合法
        validateStatusTransition(currentStatus, targetStatus);

        // 更新状态
        tenant.setStatus(targetStatus.getValue());

        // 根据状态更新其他字段
        updateTenantFieldsByStatus(tenant, targetStatus);

        tenant = tenantRepository.save(tenant);
        return tenantMapper.toDto(tenant);
    }

    /**
     * 验证状态流转是否合法
     * 根据业务规则检查状态变更是否允许
     *
     * @param currentStatus 当前状态
     * @param targetStatus  目标状态
     * @throws TenantStatusException 如果状态变更不合法
     */
    private void validateStatusTransition(TenantStatus currentStatus, TenantStatus targetStatus) {
        // 已删除状态不能变更
        if (currentStatus == TenantStatus.DELETED) {
            throw new TenantStatusException(currentStatus, targetStatus);
        }

        // 状态流转规则
        switch (currentStatus) {
            case TRIAL, SUSPENDED:
                if (targetStatus != TenantStatus.ACTIVE && targetStatus != TenantStatus.EXPIRED) {
                    throw new TenantStatusException(currentStatus, targetStatus);
                }
                break;
            case ACTIVE:
                if (targetStatus != TenantStatus.SUSPENDED && targetStatus != TenantStatus.EXPIRED) {
                    throw new TenantStatusException(currentStatus, targetStatus);
                }
                break;
            case EXPIRED:
                if (targetStatus != TenantStatus.ACTIVE) {
                    throw new TenantStatusException(currentStatus, targetStatus);
                }
                break;
            default:
                throw new TenantStatusException(currentStatus, targetStatus);
        }
    }

    /**
     * 根据状态更新租户相关字段
     * 不同状态需要更新不同的租户字段
     *
     * @param tenant 租户实体
     * @param status 目标状态
     */
    private void updateTenantFieldsByStatus(Tenant tenant, TenantStatus status) {
        switch (status) {
            case ACTIVE:
                // 激活时设置订阅开始时间
                if (tenant.getSubscriptionStart() == null) {
                    tenant.setSubscriptionStart(Instant.now().atZone(ZoneId.systemDefault()).toLocalDate());
                }
                break;
            case EXPIRED:
                // 过期时设置订阅结束时间
                tenant.setSubscriptionEnd(Instant.now().atZone(ZoneId.systemDefault()).toLocalDate());
                break;
            case DELETED:
                // 删除时设置软删除标志
                tenant.setIsDeleted(true);
                break;
            default:
                break;
        }
    }

    /**
     * 根据状态查询所有租户
     *
     * @param status   租户状态
     * @param pageable 分页参数
     * @return 租户DTO分页结果
     */
    @Override
    @Transactional(readOnly = true)
    public Page<TenantDTO> findAllByStatus(TenantStatus status, Pageable pageable) {
        LOG.debug("Request to get all Tenants by status : {}", status);
        return tenantRepository.findAllByStatus(status.getValue(), pageable).map(tenantMapper::toDto);
    }

    /**
     * 检查租户名称是否存在
     *
     * @param name 租户名称
     * @return 如果存在返回true，否则返回false
     */
    @Override
    @Transactional(readOnly = true)
    public boolean existsByName(String name) {
        LOG.debug("Request to check if Tenant exists by name : {}", name);
        return tenantRepository.findByName(name).isPresent();
    }

    /**
     * 验证租户名称
     * 如果名称已存在则抛出异常
     *
     * @param name 租户名称
     * @throws BadRequestAlertException 如果名称已存在
     */
    @Override
    @Transactional(readOnly = true)
    public void validateName(String name) {
        LOG.debug("Request to validate Tenant name : {}", name);

        if (existsByName(name)) {
            throw new BadRequestAlertException("租户名称已存在", ENTITY_NAME, "name exists");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ImportResultDTO<TenantImportDTO> importFromExcel(MultipartFile file) {
        LOG.debug("Request to import Tenants from Excel file: {}", file.getOriginalFilename());

        // 1. 读取Excel文件
        List<TenantImportDTO> importList = ExcelImportUtil.readExcel(file, TenantImportDTO.class);
        LOG.info("从Excel文件中读取到{}条数据", importList.size());
        if (importList.isEmpty()) {
            throw new BadRequestAlertException("Excel文件中未找到数据", ENTITY_NAME, "no.data");
        }
        if (importList.size() > NumberConstants.TEN) {
            throw new BadRequestAlertException("Excel文件数据过多，最多100条", ENTITY_NAME, "too.many.data");
        }

        // 2. 验证和转换数据
        ImportResultDTO<TenantImportDTO> result = new ImportResultDTO<>();
        String username = HttpRequestUtil.getHeader(request, RequestConstants.X_USER_NAME);
        Instant now = Instant.now();

        for (int i = 0; i < importList.size(); i++) {
            TenantImportDTO importDTO = importList.get(i);
            int rowNumber = i + 2; // Excel行号，第1行是表头，数据从第2行开始

            // 1. 字段级别验证
            List<String> validationErrors = ImportValidationUtil.validateTenantImportData(importDTO, rowNumber);
            if (!validationErrors.isEmpty()) {
                for (String error : validationErrors) {
                    result.addFailed(error);
                }
                continue;
            }

            // 2. 业务逻辑验证 - 验证租户名称是否已存在
            if (existsByName(importDTO.getName())) {
                LOG.warn("租户名称已存在，跳过: {}", importDTO.getName());
                result.addFailed(ImportValidationUtil.formatErrorMessage(rowNumber, "租户名称", "已存在: " + importDTO.getName()));
                continue;
            }

            // 3. 创建租户
            try {
                Tenant tenant = saveTenant(importDTO, username, now);

                // 创建租户详情
                saveTenantProfile(importDTO, tenant, username, now);
                result.addSuccess();

                // 初始化租户数据
                initTenantData(tenant, username);

                //初始化租户增值服务
                try {
                    ResponseEntity<String> responseEntity = licenseServiceFeignClient.initLicense(tenant.getId());
                    if (responseEntity.getStatusCode().is2xxSuccessful()) {
                        LOG.info("初始化租户增值服务成功: {}", responseEntity.getBody());
                    } else {
                        LOG.error("初始化租户增值服务失败: {}", responseEntity.getBody());
                    }
                } catch (Exception e) {
                    LOG.error("初始化租户增值服务失败: {}", e.getMessage(), e);
                }

                LOG.debug("成功导入租户: {}", importDTO.getName());
            } catch (Exception e) {
                LOG.error("导入租户失败: {}, 错误: {}", importDTO.getName(), e.getMessage(), e);
                result.addFailed(ImportValidationUtil.formatErrorMessage(rowNumber, "租户", "保存失败: " + e.getMessage()));
            }
        }

        LOG.info("成功导入{}条租户", result.getSuccessCount());
        return result;
    }

    @Override
    public Boolean checkTenantAccess(Long tenantId) {
        LOG.debug("Request to check tenant access for tenant ID: {}", tenantId);
        Tenant tenant = tenantRepository.findById(tenantId).orElseThrow(() -> new BadRequestAlertException("租户不存在", ENTITY_NAME, "tenant not found"));
        if (tenant.getIsDeleted()) {
            LOG.warn("租户已删除，拒绝访问: {}", tenantId);
            return Boolean.FALSE;
        }
        return tenant.getStatus().equals(TenantStatus.ACTIVE.getValue()) || tenant.getStatus().equals(TenantStatus.TRIAL.getValue());
    }

    /**
     * 初始化租户数据
     *
     * @param tenant   租户
     * @param username 用户名
     */
    private void initTenantData(Tenant tenant, String username) {
        Long id = tenant.getId();
        LOG.debug("开始为租户 {} 初始化数据", id);

        try {
            //获取基础数据
            List<TenantInitialize> list = tenantInitializeRepository.findAllByIsDeleted(Boolean.FALSE);
            LOG.debug("获取到 {} 条基础数据", list.size());

            if (list.isEmpty()) {
                LOG.warn("没有找到基础数据，跳过租户数据初始化");
                return;
            }

            //根据type类型分组
            Map<Integer, List<TenantInitialize>> map = list.stream().collect(Collectors.groupingBy(TenantInitialize::getType));

            //创建部门
            List<OrgUnit> departments = createDepartments(tenant, map.get(NumberConstants.ONE), username);
            LOG.debug("为租户 {} 创建了 {} 个部门", id, departments.size());

            //创建岗位，根据岗位的部门编码，找到对应的部门ID，绑定岗位和部门关系
            List<Position> positions = createPositions(id, map.get(NumberConstants.TWO), username, departments);
            LOG.debug("为租户 {} 创建了 {} 个岗位", id, positions.size());

            //创建角色
            List<Role> roles = createRoles(id, map.get(NumberConstants.THREE), username);
            LOG.debug("为租户 {} 创建了 {} 个角色", id, roles.size());

            //初始化一个管理员用户
            createAdminUser(tenant, username);
            LOG.info("租户 {} 数据初始化完成", id);

            //异步调用合规服务初始化
            CompletableFuture<Void> completableFuture = initExternalService.initCompliance(tenant.getId(), username);
            completableFuture.thenAcceptAsync(result -> LOG.info("租户 {} 合规服务初始化完成", id));
        } catch (Exception e) {
            LOG.error("租户 {} 数据初始化失败", id, e);
        }
    }

    /**
     * 创建租户
     *
     * @param importDTO 导入数据
     * @param username  用户名
     * @param now       当前时间
     * @return 租户
     */
    private Tenant saveTenant(TenantImportDTO importDTO, String username, Instant now) {
        Tenant tenant = new Tenant();
        tenant.setTenantCode(TenantCodeGenerator.generate());
        tenant.setName(importDTO.getName());
        tenant.setStatus(TenantStatus.ACTIVE.getValue());
        tenant.setContactEmail(importDTO.getContactEmail());
        tenant.setContactPhone(importDTO.getContactPhone());
        tenant.setVersion(NumberConstants.ONE);
        tenant.setCreatedBy(username);
        tenant.setUpdatedBy(username);
        tenant.setCreatedAt(now);
        tenant.setUpdatedAt(now);
        tenant.setIsDeleted(Boolean.FALSE);
        tenant = tenantRepository.save(tenant);
        return tenant;
    }

    /**
     * 创建租户详情
     *
     * @param importDTO 导入数据
     * @param tenant    租户
     * @param username  用户名
     * @param now       当前时间
     */
    private void saveTenantProfile(TenantImportDTO importDTO, Tenant tenant, String username, Instant now) {
        TenantProfile tenantProfile = new TenantProfile();
        tenantProfile.setTenant(tenant);
        tenantProfile.setRegistrationNumber(importDTO.getRegistrationNumber());
        if (StringUtils.isNotBlank(importDTO.getRegistrationDate())) {
            tenantProfile.setRegistrationDate(LocalDate.parse(importDTO.getRegistrationDate(), DateTimeFormatter.ofPattern(DatePattern.NORM_DATE_PATTERN)));
        }
        tenantProfile.setRegisteredCapital(importDTO.getRegisteredCapital());
        tenantProfile.setCompanyType(importDTO.getCompanyType());
        tenantProfile.setBusinessScope(importDTO.getBusinessScope());
        tenantProfile.setIndustry(importDTO.getIndustry());
        tenantProfile.setTaxRegistrationNumber(importDTO.getTaxRegistrationNumber());
        tenantProfile.setOrganizationCode(importDTO.getOrganizationCode());
        tenantProfile.setRegisteredAddress(importDTO.getRegisteredAddress());
        tenantProfile.setPostalCode(importDTO.getPostalCode());
        tenantProfile.setWebsite(importDTO.getWebsite());
        tenantProfile.setFax(importDTO.getFax());
        tenantProfile.setContactPerson(importDTO.getContactPerson());
        tenantProfile.setContactMobile(importDTO.getContactMobile());
        tenantProfile.setContactEmail(importDTO.getProfileContactEmail());
        tenantProfile.setBankName(importDTO.getBankName());
        tenantProfile.setBankAccount(importDTO.getBankAccount());
        tenantProfile.setBusinessLicensePath(importDTO.getBusinessLicensePath());
        tenantProfile.setLegalPerson(importDTO.getLegalPerson());
        tenantProfile.setLegalPersonId(importDTO.getLegalPersonId());
        tenantProfile.setVersion(NumberConstants.ONE);
        tenantProfile.setCreatedBy(username);
        tenantProfile.setUpdatedBy(username);
        tenantProfile.setCreatedAt(now);
        tenantProfile.setUpdatedAt(now);
        tenantProfile.setIsDeleted(Boolean.FALSE);
        tenantProfileRepository.save(tenantProfile);
    }

    /**
     * 创建部门
     *
     * @param tenant         租户
     * @param initializeList 部门初始化数据列表
     * @param username       用户名
     * @return 创建的部门列表
     */
    private List<OrgUnit> createDepartments(Tenant tenant, List<TenantInitialize> initializeList, String username) {
        List<OrgUnit> departments = new ArrayList<>();

        if (initializeList == null || initializeList.isEmpty()) {
            LOG.debug("没有部门初始化数据");
            return departments;
        }
        Long tenantId = tenant.getId();
        Instant now = Instant.now();

        //首先创建一个顶级的组织单元-COMPANY
        OrgUnit company = createCompany(username, tenantId, now);

        company = orgUnitRepository.save(company);
        departments.add(company);
        LOG.debug("成功创建总公司: {} - {}", company.getCode(), company.getName());

        for (TenantInitialize initialize : initializeList) {
            try {
                // 检查部门编码是否已存在
                Optional<OrgUnit> existingDept = orgUnitRepository.findByTenantIdAndCodeAndIsDeletedFalse(tenantId, initialize.getCode());
                OrgUnit department = existingDept.orElse(null);
                if (department != null) {
                    LOG.warn("部门编码 {} 已存在，跳过创建", initialize.getCode());
                    departments.add(department);
                    continue;
                }

                department = new OrgUnit();
                department.setTenantId(tenantId);
                department.setCode(initialize.getCode());
                department.setName(initialize.getName());
                department.setType(OrgUnitType.DEPARTMENT);
                // 默认为第四级
                department.setLevel(NumberConstants.THREE);
                // 默认启用
                department.setStatus(NumberConstants.ONE);
                department.setSortOrder(NumberConstants.ZERO);
                department.setDescription(initialize.getDescription());

                department.setParent(company);

                // 设置审计字段
                department.setVersion(NumberConstants.ONE);
                department.setCreatedBy(username);
                department.setUpdatedBy(username);
                department.setCreatedAt(now);
                department.setUpdatedAt(now);
                department.setIsDeleted(Boolean.FALSE);

                department = orgUnitRepository.save(department);
                departments.add(department);

                LOG.debug("成功创建部门: {} - {}", department.getCode(), department.getName());
            } catch (Exception e) {
                LOG.error("创建部门失败: {} - {}", initialize.getCode(), e.getMessage(), e);
            }
        }
        return departments;
    }

    /**
     * 创建总公司
     *
     * @param username 用户名
     * @param tenantId 租户ID
     * @param now      当前时间
     * @return 总公司
     */
    private OrgUnit createCompany(String username, Long tenantId, Instant now) {
        OrgUnit company = new OrgUnit();
        company.setTenantId(tenantId);
        company.setCode("COMPANY");
        company.setName("总公司");
        company.setType(OrgUnitType.COMPANY);
        company.setLevel(NumberConstants.ZERO);
        company.setStatus(NumberConstants.ONE);
        company.setSortOrder(NumberConstants.ZERO);
        company.setDescription("总公司");

        // 设置审计字段
        company.setVersion(NumberConstants.ONE);
        company.setCreatedBy(username);
        company.setUpdatedBy(username);
        company.setCreatedAt(now);
        company.setUpdatedAt(now);
        company.setIsDeleted(Boolean.FALSE);
        return company;
    }

    /**
     * 创建岗位
     *
     * @param tenantId       租户ID
     * @param initializeList 岗位初始化数据列表
     * @param username       用户名
     * @param departments    已创建的部门列表
     * @return 创建的岗位列表
     */
    private List<Position> createPositions(Long tenantId, List<TenantInitialize> initializeList, String username, List<OrgUnit> departments) {
        List<Position> positions = new ArrayList<>();

        if (initializeList == null || initializeList.isEmpty()) {
            LOG.debug("没有岗位初始化数据");
            return positions;
        }

        // 创建部门编码到部门ID的映射
        Map<String, Long> deptCodeToIdMap = departments.stream()
            .collect(Collectors.toMap(OrgUnit::getCode, OrgUnit::getId));

        Instant now = Instant.now();

        for (TenantInitialize initialize : initializeList) {
            try {
                // 检查岗位编码是否已存在
                Optional<Position> existingPosition = positionRepository.findByTenantIdAndCodeAndIsDeletedFalse(tenantId, initialize.getCode());
                Position position = existingPosition.orElse(null);
                if (null != position) {
                    LOG.warn("岗位编码 {} 已存在，跳过创建", initialize.getCode());
                    positions.add(position);
                    continue;
                }

                // 根据部门编码找到对应的部门
                Long orgUnitId = deptCodeToIdMap.get(initialize.getDepartmentCode());
                if (orgUnitId == null) {
                    // 如果在新创建的部门中没找到，尝试从数据库中查找
                    Optional<OrgUnit> orgUnitOpt = orgUnitRepository.findByTenantIdAndCodeAndIsDeletedFalse(tenantId, initialize.getDepartmentCode());
                    OrgUnit orgUnit = orgUnitOpt.orElse(null);
                    if (null == orgUnit) {
                        LOG.warn("岗位 {} 对应的部门 {} 不存在，跳过创建", initialize.getCode(), initialize.getDepartmentCode());
                        continue;
                    }
                    orgUnitId = orgUnit.getId();
                }

                position = new Position();
                position.setTenantId(tenantId);
                position.setCode(initialize.getCode());
                position.setName(initialize.getName());
                // 默认岗位级别为1
                position.setLevel(NumberConstants.ONE);
                position.setDescription(initialize.getDescription());

                // 设置关联的组织单元
                OrgUnit orgUnit = new OrgUnit();
                orgUnit.setId(orgUnitId);
                position.setOrgUnit(orgUnit);

                // 设置审计字段
                position.setVersion(NumberConstants.ONE);
                position.setCreatedBy(username);
                position.setUpdatedBy(username);
                position.setCreatedAt(now);
                position.setUpdatedAt(now);
                position.setIsDeleted(Boolean.FALSE);

                position = positionRepository.save(position);
                positions.add(position);

                LOG.debug("成功创建岗位: {} - {} (部门: {})", position.getCode(), position.getName(), initialize.getDepartmentCode());
            } catch (Exception e) {
                LOG.error("创建岗位失败: {} - {}", initialize.getCode(), e.getMessage(), e);
            }
        }

        return positions;
    }

    /**
     * 创建角色
     *
     * @param tenantId       租户ID
     * @param initializeList 角色初始化数据列表
     * @param username       用户名
     * @return 创建的角色列表
     */
    private List<Role> createRoles(Long tenantId, List<TenantInitialize> initializeList, String username) {
        List<Role> roles = new ArrayList<>();

        if (initializeList == null || initializeList.isEmpty()) {
            LOG.debug("没有角色初始化数据");
            return roles;
        }

        Instant now = Instant.now();

        for (TenantInitialize initialize : initializeList) {
            try {
                // 检查角色编码是否已存在
                Optional<Role> existingRole = roleRepository.findByTenantIdAndNameAndIsDeletedFalse(tenantId, initialize.getName());
                Role role = existingRole.orElse(null);
                if (role != null) {
                    LOG.warn("角色编码 {} 已存在，跳过创建", initialize.getCode());
                    roles.add(role);
                    continue;
                }

                role = new Role();
                role.setTenantId(tenantId);
                role.setCode(initialize.getCode());
                role.setName(initialize.getName());
                role.setDescription(initialize.getDescription());

                // 设置审计字段
                role.setVersion(NumberConstants.ONE);
                role.setStatus(NumberConstants.ONE);
                role.setCreatedBy(username);
                role.setUpdatedBy(username);
                role.setCreatedAt(now);
                role.setUpdatedAt(now);
                role.setIsDeleted(Boolean.FALSE);

                role = roleRepository.save(role);
                roles.add(role);

                LOG.debug("成功创建角色: {} - {}", role.getCode(), role.getName());
            } catch (Exception e) {
                LOG.error("创建角色失败: {} - {}", initialize.getCode(), e.getMessage(), e);
            }
        }
        return roles;
    }

    /**
     * 创建租户管理员用户
     *
     * @param tenant   租户
     * @param username 用户名
     */
    private void createAdminUser(Tenant tenant, String username) {
        Employee employee = new Employee();
        employee.setTenantId(tenant.getId());
        employee.setUsername(tenant.getName());
        String salt = PasswordUtils.generateSalt();
        employee.setSalt(salt);
        employee.setPassword(passwordEncoder.encode(defaultPassword + salt));
        employee.setRealName(tenant.getName() + "管理员");
        employee.setEmployeeNo(tenant.getTenantCode() + "001");
        employee.setStatus(EmployeeStatus.ACTIVE);
        employee.setVersion(NumberConstants.ONE);
        employee.setCreatedBy(username);
        employee.setUpdatedBy(username);
        employee.setCreatedAt(Instant.now());
        employee.setUpdatedAt(Instant.now());
        employee.setIsDeleted(Boolean.FALSE);
        employee.setIsFirstLogin(Boolean.TRUE);
        employee.setForceChangePassword(Boolean.TRUE);
        employee.setLoginFailureCount(NumberConstants.ZERO);
        employeeRepository.save(employee);
        // 绑定到管理员角色
        EmployeeRole employeeRole = new EmployeeRole();
        employeeRole.setTenantId(tenant.getId());
        employeeRole.setEmployee(employee);
        employeeRole.setRole(roleRepository.findByTenantIdAndCode(tenant.getId(), "TENANT_ADMIN").orElse(null));
        employeeRole.setAssignedBy(username);
        employeeRole.setAssignedAt(Instant.now());
        employeeRole.setVersion(NumberConstants.ONE);
        employeeRole.setCreatedBy(username);
        employeeRole.setUpdatedBy(username);
        employeeRole.setCreatedAt(Instant.now());
        employeeRole.setUpdatedAt(Instant.now());
        employeeRole.setIsDeleted(Boolean.FALSE);
        employeeRoleRepository.save(employeeRole);
    }

    @Override
    @Transactional(readOnly = true)
    public List<TenantTreeDTO> getSubTenantTree() {
        // 获取当前租户ID
        Long currentTenantId = Long.valueOf(HttpRequestUtil.getHeader(request, RequestConstants.X_TENANT_ID));
        LOG.debug("Request to get sub tenant tree for tenant: {}", currentTenantId);

        try {
            // 直接构建当前租户的下级租户树
            return buildSubTenantTree(currentTenantId);

        } catch (Exception e) {
            LOG.error("Error getting sub tenant tree for tenant: {}", currentTenantId, e);
            throw new RuntimeException("获取下级租户树失败: " + e.getMessage(), e);
        }
    }

    /**
     * 构建指定租户的下级租户树结构
     *
     * @param parentTenantId 父级租户ID
     * @return 下级租户树结构列表
     */
    private List<TenantTreeDTO> buildSubTenantTree(Long parentTenantId) {
        List<TenantTreeDTO> result = new ArrayList<>();

        // 获取直接下级租户
        List<Tenant> directSubTenants = tenantRepository.findByParentIdAndIsDeletedFalse(parentTenantId);

        if (CollectionUtils.isEmpty(directSubTenants)) {
            LOG.debug("No direct sub tenants found for tenant: {}", parentTenantId);
            return result;
        }

        // 为每个直接下级租户构建树节点
        for (Tenant tenant : directSubTenants) {
            TenantTreeDTO treeDTO = convertToTenantTreeDTO(tenant);

            // 递归获取该租户的下级租户
            List<TenantTreeDTO> children = buildSubTenantTree(tenant.getId());
            if (!CollectionUtils.isEmpty(children)) {
                treeDTO.setChildren(children);
            }

            result.add(treeDTO);
        }

        return result;
    }

    /**
     * 将Tenant实体转换为TenantTreeDTO
     *
     * @param tenant 租户实体
     * @return 租户树DTO
     */
    private TenantTreeDTO convertToTenantTreeDTO(Tenant tenant) {
        TenantTreeDTO treeDTO = new TenantTreeDTO();
        treeDTO.setId(tenant.getId());
        treeDTO.setTenantCode(tenant.getTenantCode());
        treeDTO.setName(tenant.getName());
        treeDTO.setStatus(tenant.getStatus());
        treeDTO.setSubscriptionPlan(tenant.getSubscriptionPlan());
        treeDTO.setSubscriptionStart(tenant.getSubscriptionStart());
        treeDTO.setSubscriptionEnd(tenant.getSubscriptionEnd());
        treeDTO.setContactEmail(tenant.getContactEmail());
        treeDTO.setContactPhone(tenant.getContactPhone());
        treeDTO.setMetadata(tenant.getMetadata());
        treeDTO.setParentId(tenant.getParentId());
        treeDTO.setCreatedBy(tenant.getCreatedBy());
        treeDTO.setCreatedAt(tenant.getCreatedAt());
        treeDTO.setUpdatedBy(tenant.getUpdatedBy());
        treeDTO.setUpdatedAt(tenant.getUpdatedAt());
        return treeDTO;
    }

}

package com.whiskerguard.organization.service.impl;

import com.whiskerguard.organization.client.contract.ContractServiceClient;
import com.whiskerguard.organization.client.regulatory.RegulatoryServiceClient;
import com.whiskerguard.organization.service.InitExternalService;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.concurrent.CompletableFuture;

/**
 * 描述：外部服务初始化的服务实现类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/19
 */
@Service
public class InitExternalServiceImpl implements InitExternalService {

    private static final Logger LOG = LoggerFactory.getLogger(InitExternalServiceImpl.class);

    private final RegulatoryServiceClient regulatoryServiceClient;

    private final ContractServiceClient contractServiceClient;

    private final RedissonClient redissonClient;

    public InitExternalServiceImpl(RegulatoryServiceClient regulatoryServiceClient,
                                   ContractServiceClient contractServiceClient,
                                   RedissonClient redissonClient
    ) {
        this.regulatoryServiceClient = regulatoryServiceClient;
        this.contractServiceClient = contractServiceClient;
        this.redissonClient = redissonClient;
    }

    @Override
    @Async
    public CompletableFuture<Void> initCompliance(Long tenantId, String username) {
        LOG.info("开始异步初始化合规数据 tenantId: {}", tenantId);
        try {

            RBucket<Object> regulatory = redissonClient.getBucket("tenant:initialized:regulatory");
            Object regulatoryFlag = regulatory.get();

            // 如果Redis中的值是true，则执行初始化制度，案例
            if (Boolean.TRUE.equals(regulatoryFlag)) {
                ResponseEntity<Void> responseEntity = regulatoryServiceClient.init(tenantId, username);
                if (responseEntity.getStatusCode().is2xxSuccessful()) {
                    LOG.info("合规制度和案例初始化成功");
                } else {
                    LOG.error("合规制度和案例初始化失败: {}", responseEntity.getBody());
                }
            }

            RBucket<Object> compliance = redissonClient.getBucket("tenant:initialized:compliance");
            Object complianceFlag = compliance.get();

            // 如果Redis中的值是true，则执行初始化合规审查数据
            if (Boolean.TRUE.equals(complianceFlag)) {
                ResponseEntity<Void> responseEntity = contractServiceClient.init(tenantId, username);
                if (responseEntity.getStatusCode().is2xxSuccessful()) {
                    LOG.info("合规审查数据初始化成功");
                } else {
                    LOG.error("合规审查数据初始化失败: {}", responseEntity.getBody());
                }
            }
            LOG.info("合规服务初始化完成");
        } catch (Exception e) {
            LOG.error("合规服务初始化失败", e);
        }
        return CompletableFuture.completedFuture(null);
    }
}

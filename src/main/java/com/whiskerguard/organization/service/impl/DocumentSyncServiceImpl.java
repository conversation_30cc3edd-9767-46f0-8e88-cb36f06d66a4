package com.whiskerguard.organization.service.impl;

import com.whiskerguard.common.constant.DocumentIdConstants;
import com.whiskerguard.common.constant.NumberConstants;
import com.whiskerguard.common.util.DocumentIdUtil;
import com.whiskerguard.organization.client.dto.DocumentRecordDTO;
import com.whiskerguard.organization.client.retrieval.RetrievalServiceClient;
import com.whiskerguard.organization.domain.OrgUnit;
import com.whiskerguard.organization.domain.Tenant;
import com.whiskerguard.organization.repository.OrgUnitRepository;
import com.whiskerguard.organization.repository.TenantRepository;
import com.whiskerguard.organization.service.DocumentSyncService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 描述：文档同步的服务实现类
 * 用于将企业制度同步到检索服务
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/18
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class DocumentSyncServiceImpl implements DocumentSyncService {

    private static final Logger LOG = LoggerFactory.getLogger(DocumentSyncServiceImpl.class);

    private final OrgUnitRepository orgUnitRepository;

    private final TenantRepository tenantRepository;

    private final RetrievalServiceClient retrievalServiceClient;

    public DocumentSyncServiceImpl(OrgUnitRepository orgUnitRepository,
                                   TenantRepository tenantRepository,
                                   RetrievalServiceClient retrievalServiceClient) {
        this.orgUnitRepository = orgUnitRepository;
        this.tenantRepository = tenantRepository;
        this.retrievalServiceClient = retrievalServiceClient;
    }

    /**
     * 同步所有组织单元到检索服务
     * 按租户ID分组处理
     *
     * @return 同步结果列表
     */
    @Override
    @Transactional(readOnly = true)
    public List<DocumentRecordDTO> syncOrgUnits() {
        LOG.info("开始按租户同步组织单元到检索服务");
        List<DocumentRecordDTO> results = new ArrayList<>();

        try {
            // 获取所有活跃的租户
            List<Tenant> activeTenants = tenantRepository.findAll()
                .stream().filter(tenant -> !tenant.getIsDeleted()).toList();

            LOG.info("找到 {} 个活跃租户需要同步组织单元", activeTenants.size());

            // 按租户处理组织单元
            for (Tenant tenant : activeTenants) {
                try {
                    LOG.info("开始同步租户 {} (ID: {}) 的组织单元", tenant.getName(), tenant.getId());

                    // 获取该租户的所有未删除组织单元
                    List<OrgUnit> tenantOrgUnits = orgUnitRepository.findAllByTenantId(tenant.getId())
                        .stream().filter(orgUnit -> !orgUnit.getIsDeleted()).toList();

                    LOG.info("租户 {} 有 {} 个组织单元需要同步", tenant.getName(), tenantOrgUnits.size());

                    // 同步该租户的组织单元
                    List<DocumentRecordDTO> tenantResults = syncTenantOrgUnits(tenant, tenantOrgUnits);
                    results.addAll(tenantResults);
                    LOG.info("租户 {} 组织单元同步完成，成功同步 {} 个", tenant.getName(), tenantResults.size());
                } catch (Exception e) {
                    LOG.error("同步租户 {} (ID: {}) 的组织单元失败", tenant.getName(), tenant.getId(), e);
                }
            }

            LOG.info("所有租户组织单元同步完成，总共成功同步 {} 个", results.size());
        } catch (Exception e) {
            LOG.error("同步组织单元过程中发生异常", e);
        }
        return results;
    }

    /**
     * 同步单个租户的组织单元（一次性推送所有部门）
     *
     * @param tenant   租户信息
     * @param orgUnits 组织单元列表
     * @return 同步结果列表
     */
    private List<DocumentRecordDTO> syncTenantOrgUnits(Tenant tenant, List<OrgUnit> orgUnits) {
        List<DocumentRecordDTO> results = new ArrayList<>();

        if (orgUnits.isEmpty()) {
            LOG.info("租户 {} 没有组织单元需要同步", tenant.getName());
            return results;
        }

        try {
            // 构建租户所有组织单元的整合内容
            String content = buildTenantAllOrgUnitsContent(tenant, orgUnits);

            // 转换为文档记录（使用租户ID作为业务ID）
            DocumentRecordDTO documentRecord = convertToDocumentRecord(tenant.getId(), content,
                DocumentIdConstants.BUSINESS_TYPE_STRUCTURE, tenant.getId().toString());

            // 同步到检索服务
            DocumentRecordDTO result = syncDocumentRecord(documentRecord);
            if (result != null) {
                results.add(result);
                LOG.info("租户 {} 所有组织单元一次性同步成功，共 {} 个部门", tenant.getName(), orgUnits.size());
            } else {
                LOG.warn("租户 {} 所有组织单元一次性同步失败，共 {} 个部门", tenant.getName(), orgUnits.size());
            }
        } catch (Exception e) {
            LOG.error("租户 {} 所有组织单元一次性同步失败，共 {} 个部门", tenant.getName(), orgUnits.size(), e);
        }

        return results;
    }

    /**
     * 构建租户所有组织单元的整合内容用于向量化
     *
     * @param tenant   租户信息
     * @param orgUnits 组织单元列表
     * @return 整合的内容字符串
     */
    private String buildTenantAllOrgUnitsContent(Tenant tenant, List<OrgUnit> orgUnits) {
        StringBuilder content = new StringBuilder();

        // 租户基本信息
        content.append("=== 租户信息 ===\n");
        content.append("租户名称：").append(tenant.getName()).append("\n");
        content.append("租户编码：").append(tenant.getTenantCode()).append("\n");
        content.append("租户状态：").append(tenant.getStatus() == 1 ? "启用" : "禁用").append("\n");

        // 组织架构概览
        content.append("=== 组织架构概览 ===\n");
        buildOrgUnitsHierarchy(content, orgUnits);
        content.append("\n");

        // 详细组织单元信息
        content.append("=== 详细组织单元信息 ===\n");
        for (int i = 0; i < orgUnits.size(); i++) {
            OrgUnit orgUnit = orgUnits.get(i);
            content.append("--- 组织单元 ").append(i + 1).append(" ---\n");
            content.append("名称：").append(orgUnit.getName()).append("\n");
            content.append("编码：").append(orgUnit.getCode()).append("\n");
            content.append("类型：").append(getOrgUnitTypeDescription(orgUnit.getType().name())).append("\n");
            content.append("层级：").append(orgUnit.getLevel()).append("\n");
            content.append("状态：").append(orgUnit.getStatus() == 1 ? "启用" : "禁用").append("\n");

            if (orgUnit.getDescription() != null && !orgUnit.getDescription().trim().isEmpty()) {
                content.append("描述：").append(orgUnit.getDescription()).append("\n");
            }

            if (orgUnit.getParent() != null) {
                content.append("上级组织：").append(orgUnit.getParent().getName()).append("\n");
            }
            if (i < orgUnits.size() - 1) {
                content.append("\n");
            }
        }
        return content.toString();
    }

    /**
     * 构建组织单元层级结构概览
     *
     * @param content  内容构建器
     * @param orgUnits 组织单元列表
     */
    private void buildOrgUnitsHierarchy(StringBuilder content, List<OrgUnit> orgUnits) {
        // 按类型分组统计
        Map<String, Long> typeCount = orgUnits.stream()
            .collect(Collectors.groupingBy(
                orgUnit -> getOrgUnitTypeDescription(orgUnit.getType().name()),
                Collectors.counting()
            ));

        content.append("组织单元类型分布：\n");
        typeCount.forEach((type, count) ->
            content.append("  ").append(type).append("：").append(count).append("个\n"));

        // 按层级分组统计
        Map<Integer, Long> levelCount = orgUnits.stream()
            .collect(Collectors.groupingBy(OrgUnit::getLevel, Collectors.counting()));

        content.append("组织层级分布：\n");
        levelCount.entrySet().stream()
            .sorted(Map.Entry.comparingByKey())
            .forEach(entry ->
                content.append("  第").append(entry.getKey()).append("层：").append(entry.getValue()).append("个\n"));

        // 顶级组织单元
        List<OrgUnit> topLevelUnits = orgUnits.stream()
            .filter(orgUnit -> orgUnit.getParent() == null)
            .sorted(Comparator.comparing(OrgUnit::getSortOrder, Comparator.nullsLast(Comparator.naturalOrder())))
            .toList();

        if (!topLevelUnits.isEmpty()) {
            content.append("顶级组织单元：\n");
            topLevelUnits.forEach(orgUnit ->
                content.append("  ").append(orgUnit.getName())
                    .append("（").append(getOrgUnitTypeDescription(orgUnit.getType().name())).append("）\n"));
        }
    }

    /**
     * 获取组织单元类型描述
     *
     * @param type 组织单元类型
     * @return 类型描述
     */
    private String getOrgUnitTypeDescription(String type) {
        return switch (type) {
            case "COMPANY" -> "公司";
            case "SUBSIDIARY" -> "子公司";
            case "BUSINESS_GROUP" -> "事业群";
            case "DEPARTMENT" -> "部门";
            case "TEAM" -> "团队";
            default -> type;
        };
    }

    /**
     * 方法名称：convertToDocumentRecord
     * 描述：转换为DocumentRecordDTO。
     *
     * @param tenantId     租户ID
     * @param content      内容
     * @param businessType 业务类型
     * @param businessId   业务ID
     * @return DocumentRecordDTO
     */
    private DocumentRecordDTO convertToDocumentRecord(Long tenantId, String content, String businessType, String businessId) {
        DocumentRecordDTO documentRecord = new DocumentRecordDTO();

        // 基本信息
        documentRecord.setTenantId(tenantId);
        documentRecord.setDocumentId(DocumentIdUtil.generateDocumentId(DocumentIdConstants.SERVICE_ORG, businessType, businessId));
        documentRecord.setSource(content);
        documentRecord.setChunkCount(NumberConstants.ONE);
        documentRecord.setIndexedAt(Instant.now());

        // 审计信息
        documentRecord.setVersion(NumberConstants.ONE);
        documentRecord.setCreatedBy("system");
        documentRecord.setCreatedAt(Instant.now());
        documentRecord.setUpdatedBy("system");
        documentRecord.setUpdatedAt(Instant.now());
        documentRecord.setIsDeleted(Boolean.FALSE);
        return documentRecord;
    }

    /**
     * 同步文档记录到检索服务
     *
     * @param documentRecord 文档记录
     * @return 同步结果
     */
    private DocumentRecordDTO syncDocumentRecord(DocumentRecordDTO documentRecord) {
        try {
            LOG.debug("调用检索服务创建文档记录：{}", documentRecord);

            ResponseEntity<DocumentRecordDTO> response = retrievalServiceClient.createDocumentRecord(documentRecord);

            if (response.getStatusCode() == HttpStatus.OK || response.getStatusCode() == HttpStatus.CREATED) {
                DocumentRecordDTO result = response.getBody();
                LOG.debug("文档记录创建成功：{}", result != null ? result.getId() : "null");
                return result;
            } else {
                LOG.error("创建文档记录失败，状态码：{}", response.getStatusCode());
                return null;
            }
        } catch (Exception e) {
            LOG.error("调用检索服务失败：{}", documentRecord.getDocumentId(), e);
            return null;
        }
    }

}

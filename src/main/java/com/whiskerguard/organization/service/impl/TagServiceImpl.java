package com.whiskerguard.organization.service.impl;

import com.whiskerguard.common.config.RequestConstants;
import com.whiskerguard.common.constant.NumberConstants;
import com.whiskerguard.common.util.HttpRequestUtil;
import com.whiskerguard.organization.domain.Tag;
import com.whiskerguard.organization.domain.TagCategory;
import com.whiskerguard.organization.repository.TagCategoryRepository;
import com.whiskerguard.organization.repository.TagRepository;
import com.whiskerguard.organization.service.TagService;
import com.whiskerguard.organization.service.dto.TagDTO;
import com.whiskerguard.organization.service.mapper.TagMapper;
import com.whiskerguard.organization.web.rest.errors.BadRequestAlertException;
import jakarta.servlet.http.HttpServletRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.Optional;

/**
 * 描述：标签管理的服务实现类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/18
 */
@Service
@Transactional
public class TagServiceImpl implements TagService {

    private static final Logger LOG = LoggerFactory.getLogger(TagServiceImpl.class);

    private final TagRepository tagRepository;

    private final TagMapper tagMapper;

    private final HttpServletRequest request;

    private final TagCategoryRepository tagCategoryRepository;

    public TagServiceImpl(TagRepository tagRepository, TagMapper tagMapper, HttpServletRequest request,
                          TagCategoryRepository tagCategoryRepository) {
        this.tagRepository = tagRepository;
        this.tagMapper = tagMapper;
        this.request = request;
        this.tagCategoryRepository = tagCategoryRepository;
    }

    @Override
    public TagDTO save(TagDTO tagDTO) {
        LOG.debug("Request to save Tag : {}", tagDTO);
        Long tenantId = Long.valueOf(HttpRequestUtil.getHeader(request, RequestConstants.X_TENANT_ID));
        //判断当前标签是否已经存在，如果存在则抛出异常
        if (null != tagRepository.findByNameAndTenantId(tagDTO.getName(), tenantId)) {
            throw new BadRequestAlertException("标签已存在", "tag", "标签已存在");
        }

        //判断分类是否存在
        if (tagDTO.getCategory() != null) {
            Optional<TagCategory> optional = tagCategoryRepository.findById(tagDTO.getCategory().getId());
            if (optional.isEmpty()) {
                throw new BadRequestAlertException("标签分类不存在", "tagCategory", "标签分类不存在");
            }
        }

        Tag tag = tagMapper.toEntity(tagDTO);
        String username = HttpRequestUtil.getHeader(request, RequestConstants.X_USER_NAME);
        tag.setTenantId(tenantId);
        tag.setVersion(NumberConstants.ONE);
        tag.setCreatedBy(username);
        tag.setUpdatedBy(username);
        tag.setCreatedAt(Instant.now());
        tag.setUpdatedAt(Instant.now());
        tag.setIsDeleted(Boolean.FALSE);
        tag = tagRepository.save(tag);
        return tagMapper.toDto(tag);
    }

    @Override
    public TagDTO update(TagDTO tagDTO) {
        LOG.debug("Request to update Tag : {}", tagDTO);
        Tag tag = tagMapper.toEntity(tagDTO);
        tag = tagRepository.save(tag);
        return tagMapper.toDto(tag);
    }

    @Override
    public Optional<TagDTO> partialUpdate(TagDTO tagDTO) {
        LOG.debug("Request to partially update Tag : {}", tagDTO);
        Long tenantId = Long.valueOf(HttpRequestUtil.getHeader(request, RequestConstants.X_TENANT_ID));
        //判断当前标签是否已经存在，如果存在则抛出异常
        Tag tag = tagRepository.findByNameAndTenantId(tagDTO.getName(), tenantId);
        if (null != tag && !tag.getId().equals(tagDTO.getId())) {
            throw new BadRequestAlertException("标签已存在", "tag", "tag exists");
        }

        return tagRepository
            .findById(tagDTO.getId())
            .map(existingTag -> {
                tagMapper.partialUpdate(existingTag, tagDTO);
                String username = HttpRequestUtil.getHeader(request, RequestConstants.X_USER_NAME);
                existingTag.setUpdatedBy(username);
                existingTag.setUpdatedAt(Instant.now());
                return existingTag;
            })
            .map(tagRepository::save)
            .map(tagMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<TagDTO> findAll(Pageable pageable) {
        Long tenantId = Long.valueOf(HttpRequestUtil.getHeader(request, RequestConstants.X_TENANT_ID));
        LOG.debug("Request to get all Tags by tenant ID: {}", tenantId);
        return tagRepository.findByIsDeletedFalse(tenantId, pageable).map(tagMapper::toDto);
    }

    @Override
    public Page<TagDTO> findAllWithEagerRelationships(Pageable pageable) {
        Long tenantId = Long.valueOf(HttpRequestUtil.getHeader(request, RequestConstants.X_TENANT_ID));
        return tagRepository.findAllWithEagerRelationshipsAndNotDeleted(tenantId, pageable).map(tagMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<TagDTO> findOne(Long id) {
        LOG.debug("Request to get Tag : {}", id);
        return tagRepository.findOneWithEagerRelationships(id).map(tagMapper::toDto);
    }

    @Override
    public void delete(Long id) {
        LOG.debug("Request to delete Tag : {}", id);
        // 逻辑删除：将isDeleted设置为true，而不是物理删除
        tagRepository
            .findById(id)
            .ifPresent(tag -> {
                tag.setIsDeleted(true);
                tagRepository.save(tag);
            });
    }
}

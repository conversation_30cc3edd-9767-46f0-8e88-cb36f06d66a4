package com.whiskerguard.organization.service.impl;

import com.whiskerguard.organization.domain.NewsTags;
import com.whiskerguard.organization.repository.NewsTagsRepository;
import com.whiskerguard.organization.service.NewsTagsService;
import com.whiskerguard.organization.service.dto.NewsTagsDTO;
import com.whiskerguard.organization.service.mapper.NewsTagsMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

/**
 * 描述：新闻标签关联服务实现类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/18
 */
@Service
@Transactional
public class NewsTagsServiceImpl implements NewsTagsService {

    private static final Logger LOG = LoggerFactory.getLogger(NewsTagsServiceImpl.class);

    private final NewsTagsRepository newsTagsRepository;

    private final NewsTagsMapper newsTagsMapper;

    public NewsTagsServiceImpl(NewsTagsRepository newsTagsRepository, NewsTagsMapper newsTagsMapper) {
        this.newsTagsRepository = newsTagsRepository;
        this.newsTagsMapper = newsTagsMapper;
    }

    @Override
    public NewsTagsDTO save(NewsTagsDTO newsTagsDTO) {
        LOG.debug("Request to save NewsTags : {}", newsTagsDTO);
        NewsTags newsTags = newsTagsMapper.toEntity(newsTagsDTO);
        newsTags = newsTagsRepository.save(newsTags);
        return newsTagsMapper.toDto(newsTags);
    }

    @Override
    public NewsTagsDTO update(NewsTagsDTO newsTagsDTO) {
        LOG.debug("Request to update NewsTags : {}", newsTagsDTO);
        NewsTags newsTags = newsTagsMapper.toEntity(newsTagsDTO);
        newsTags = newsTagsRepository.save(newsTags);
        return newsTagsMapper.toDto(newsTags);
    }

    @Override
    public Optional<NewsTagsDTO> partialUpdate(NewsTagsDTO newsTagsDTO) {
        LOG.debug("Request to partially update NewsTags : {}", newsTagsDTO);

        return newsTagsRepository
            .findById(newsTagsDTO.getId())
            .map(existingNewsTags -> {
                newsTagsMapper.partialUpdate(existingNewsTags, newsTagsDTO);

                return existingNewsTags;
            })
            .map(newsTagsRepository::save)
            .map(newsTagsMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<NewsTagsDTO> findAll(Pageable pageable) {
        LOG.debug("Request to get all NewsTags");
        return newsTagsRepository.findAll(pageable).map(newsTagsMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<NewsTagsDTO> findOne(Long id) {
        LOG.debug("Request to get NewsTags : {}", id);
        return newsTagsRepository.findById(id).map(newsTagsMapper::toDto);
    }

    @Override
    public void delete(Long id) {
        LOG.debug("Request to delete NewsTags : {}", id);
        newsTagsRepository.deleteById(id);
    }
}

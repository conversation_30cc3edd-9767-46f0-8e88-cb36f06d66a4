package com.whiskerguard.organization.service.impl;

import com.whiskerguard.common.config.RequestConstants;
import com.whiskerguard.common.constant.NumberConstants;
import com.whiskerguard.common.util.HttpRequestUtil;
import com.whiskerguard.organization.domain.LetterCommitment;
import com.whiskerguard.organization.repository.LetterCommitmentRepository;
import com.whiskerguard.organization.service.LetterCommitmentService;
import com.whiskerguard.organization.service.dto.LetterCommitmentDTO;
import com.whiskerguard.organization.service.mapper.LetterCommitmentMapper;
import jakarta.servlet.http.HttpServletRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.Optional;

/**
 * 承诺书管理服务实现类
 * 提供承诺书的增删改查等业务操作的具体实现，包括合规承诺书等各类承诺书的管理
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/14
 */
@Service
@Transactional
public class LetterCommitmentServiceImpl implements LetterCommitmentService {

    private static final Logger LOG = LoggerFactory.getLogger(LetterCommitmentServiceImpl.class);

    private final LetterCommitmentRepository letterCommitmentRepository;

    private final LetterCommitmentMapper letterCommitmentMapper;

    private final HttpServletRequest request;

    public LetterCommitmentServiceImpl(
        LetterCommitmentRepository letterCommitmentRepository,
        LetterCommitmentMapper letterCommitmentMapper,
        HttpServletRequest request
    ) {
        this.letterCommitmentRepository = letterCommitmentRepository;
        this.letterCommitmentMapper = letterCommitmentMapper;
        this.request = request;
    }

    /**
     * 保存承诺书
     *
     * @param letterCommitmentDTO 要保存的承诺书DTO
     * @return 保存后的承诺书DTO
     */
    @Override
    public LetterCommitmentDTO save(LetterCommitmentDTO letterCommitmentDTO) {
        LOG.debug("Request to save LetterCommitment : {}", letterCommitmentDTO);
        LetterCommitment letterCommitment = letterCommitmentMapper.toEntity(letterCommitmentDTO);
        String username = HttpRequestUtil.getHeader(request, RequestConstants.X_USER_NAME);
        letterCommitment.setTenantId(Long.valueOf(HttpRequestUtil.getHeader(request, RequestConstants.X_TENANT_ID)));
        letterCommitment.setEmployeeId(Long.valueOf(HttpRequestUtil.getHeader(request, RequestConstants.X_USER_ID)));
        letterCommitment.setVersion(NumberConstants.ONE);
        letterCommitment.setCreatedBy(username);
        letterCommitment.setUpdatedBy(username);
        letterCommitment.setCreatedAt(Instant.now());
        letterCommitment.setUpdatedAt(Instant.now());
        letterCommitment.setIsDeleted(Boolean.FALSE);
        letterCommitment = letterCommitmentRepository.save(letterCommitment);
        return letterCommitmentMapper.toDto(letterCommitment);
    }

    @Override
    public Optional<LetterCommitmentDTO> findLetterCommitmentByEmployee() {
        Long employeeId = Long.valueOf(HttpRequestUtil.getHeader(request, RequestConstants.X_USER_ID));
        Optional<LetterCommitment> optional = letterCommitmentRepository.findByEmployeeId(employeeId);
        return optional.map(letterCommitmentMapper::toDto);
    }

}

package com.whiskerguard.organization.service.impl;

import com.whiskerguard.common.config.RequestConstants;
import com.whiskerguard.common.constant.NumberConstants;
import com.whiskerguard.common.util.ExcelImportUtil;
import com.whiskerguard.common.util.HttpRequestUtil;
import com.whiskerguard.common.util.WgStringUtil;
import com.whiskerguard.organization.domain.EmployeeOrg;
import com.whiskerguard.organization.domain.OrgUnit;
import com.whiskerguard.organization.domain.Position;
import com.whiskerguard.organization.domain.enumeration.OrgUnitType;
import com.whiskerguard.organization.repository.EmployeeOrgRepository;
import com.whiskerguard.organization.repository.OrgUnitRepository;
import com.whiskerguard.organization.repository.PositionRepository;
import com.whiskerguard.organization.service.OrgUnitService;
import com.whiskerguard.organization.service.dto.OrgStructureImportDTO;
import com.whiskerguard.organization.service.dto.OrgStructureImportResultDTO;
import com.whiskerguard.organization.service.dto.OrgUnitDTO;
import com.whiskerguard.organization.service.exception.OrgUnitException;
import com.whiskerguard.organization.service.mapper.OrgUnitMapper;
import com.whiskerguard.organization.service.mapper.PositionMapper;
import jakarta.servlet.http.HttpServletRequest;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.time.Instant;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 描述：组织单位的服务实现类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/18
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class OrgUnitServiceImpl implements OrgUnitService {

    private static final Logger LOG = LoggerFactory.getLogger(OrgUnitServiceImpl.class);

    private final OrgUnitRepository orgUnitRepository;
    private final OrgUnitMapper orgUnitMapper;
    private final PositionRepository positionRepository;
    private final PositionMapper positionMapper;
    private final EmployeeOrgRepository employeeOrgRepository;

    private final HttpServletRequest request;

    public OrgUnitServiceImpl(OrgUnitRepository orgUnitRepository, OrgUnitMapper orgUnitMapper,
                              PositionRepository positionRepository, PositionMapper positionMapper,
                              HttpServletRequest request, EmployeeOrgRepository employeeOrgRepository) {
        this.orgUnitRepository = orgUnitRepository;
        this.orgUnitMapper = orgUnitMapper;
        this.positionRepository = positionRepository;
        this.positionMapper = positionMapper;
        this.request = request;
        this.employeeOrgRepository = employeeOrgRepository;
    }

    @Override
    public OrgUnitDTO save(OrgUnitDTO orgUnitDTO) {
        LOG.debug("Request to save OrgUnit : {}", orgUnitDTO);

        // 验证编码
        if (validateCode(orgUnitDTO.getCode())) {
            throw new OrgUnitException("编码存在非法字符");
        }

        // 验证组织单元类型
        validateOrgUnitType(orgUnitDTO);

        String username = HttpRequestUtil.getHeader(request, RequestConstants.X_USER_NAME);
        Long tenantId = Long.valueOf(HttpRequestUtil.getHeader(request, RequestConstants.X_TENANT_ID));
        //判断当前组织单元是否已经存在，如果存在则抛出异常
        if (null != orgUnitRepository.findByTenantIdAndLevelAndCode(tenantId, orgUnitDTO.getName(), orgUnitDTO.getLevel())) {
            throw new OrgUnitException("名称为 " + orgUnitDTO.getName() + " 的组织单元在租户 " + tenantId + " 中已存在");
        }

        OrgUnit orgUnit = orgUnitMapper.toEntity(orgUnitDTO);

        // 设置层级
        if (orgUnitDTO.getParentId() != null) {
            OrgUnit parent = orgUnitRepository
                .findById(orgUnitDTO.getParentId())
                .orElseThrow(() -> new OrgUnitException("父级组织单元未找到"));
            orgUnit.setLevel(parent.getLevel() + 1);
            orgUnit.setParent(parent);
        } else {
            orgUnit.setLevel(1);
        }

        orgUnit.setTenantId(tenantId);
        orgUnit.setVersion(NumberConstants.ONE);
        orgUnit.setCreatedBy(username);
        orgUnit.setUpdatedBy(username);
        orgUnit.setCreatedAt(Instant.now());
        orgUnit.setUpdatedAt(Instant.now());
        orgUnit.setIsDeleted(Boolean.FALSE);
        orgUnit = orgUnitRepository.save(orgUnit);
        return orgUnitMapper.toDto(orgUnit);
    }

    /**
     * 校验编码
     *
     * @param code 编码
     * @return true表示存在非法字符，false表示合法
     */
    private boolean validateCode(String code) {
        return WgStringUtil.containsChineseChar(code) || WgStringUtil.containsSpecialChar(code) ||
            WgStringUtil.containsEmoji(code) || WgStringUtil.containsHtmlTag(code) ||
            WgStringUtil.containsSqlInjectionRisk(code) || WgStringUtil.containsXssRisk(code);
    }

    @Override
    public OrgUnitDTO update(OrgUnitDTO orgUnitDTO) {
        LOG.debug("Request to update OrgUnit : {}", orgUnitDTO);
        // 验证编码
        if (validateCode(orgUnitDTO.getCode())) {
            throw new OrgUnitException("编码存在非法字符");
        }
        // 验证组织单元类型
        validateOrgUnitType(orgUnitDTO);

        OrgUnit orgUnit = orgUnitMapper.toEntity(orgUnitDTO);
        orgUnit = orgUnitRepository.save(orgUnit);
        return orgUnitMapper.toDto(orgUnit);
    }

    @Override
    public Optional<OrgUnitDTO> partialUpdate(OrgUnitDTO orgUnitDTO) {
        LOG.debug("Request to partially update OrgUnit : {}", orgUnitDTO);
        // 验证编码
        if (validateCode(orgUnitDTO.getCode())) {
            throw new OrgUnitException("编码存在非法字符");
        }
        // 验证组织单元类型
        validateOrgUnitType(orgUnitDTO);
        Optional<OrgUnit> optional = orgUnitRepository.findById(orgUnitDTO.getId());
        OrgUnit orgUnit = optional.orElseThrow(() -> new OrgUnitException("组织单元未找到"));
        //判断当前组织单元是否已经存在，如果存在则抛出异常
        if (null != orgUnitRepository.findByTenantIdAndLevelAndCode(orgUnit.getTenantId(), orgUnitDTO.getName(), orgUnitDTO.getLevel())
            && !orgUnit.getId().equals(orgUnitDTO.getId())) {
            throw new OrgUnitException("名称为 " + orgUnitDTO.getName() + " 的组织单元在租户 " + orgUnit.getTenantId() + " 中已存在");
        }

        orgUnitMapper.partialUpdate(orgUnit, orgUnitDTO);
        // 设置层级
        if (orgUnitDTO.getParentId() != null) {
            OrgUnit parent = orgUnitRepository
                .findById(orgUnitDTO.getParentId())
                .orElseThrow(() -> new OrgUnitException("Parent org unit not found"));
            orgUnit.setLevel(parent.getLevel() + 1);
            orgUnit.setParent(parent);
        } else {
            orgUnit.setLevel(1);
        }
        String username = HttpRequestUtil.getHeader(request, RequestConstants.X_USER_NAME);
        orgUnit.setUpdatedBy(username);
        orgUnit.setUpdatedAt(Instant.now());
        orgUnitRepository.save(orgUnit);
        return Optional.of(orgUnitMapper.toDto(orgUnit));
    }

    @Override
    @Transactional(readOnly = true)
    public Page<OrgUnitDTO> findAll(Pageable pageable) {
        LOG.debug("Request to get all OrgUnits");

        // 从request header获取租户ID
        String tenantIdHeader = HttpRequestUtil.getHeader(request, RequestConstants.X_TENANT_ID);
        if (tenantIdHeader == null || tenantIdHeader.trim().isEmpty()) {
            LOG.warn("Tenant ID header is null or empty, this may cause data visibility issues");
            throw new IllegalArgumentException("请求头中缺少必需的租户ID");
        }

        Long tenantId;
        try {
            tenantId = Long.valueOf(tenantIdHeader);
        } catch (NumberFormatException e) {
            LOG.error("Invalid tenant ID format: {}", tenantIdHeader, e);
            throw new IllegalArgumentException("Invalid tenant ID format: " + tenantIdHeader, e);
        }

        LOG.debug("Filtering org units by tenant ID: {}", tenantId);
        return orgUnitRepository.findByTenantIdAndIsDeletedFalse(tenantId, pageable).map(orgUnitMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<OrgUnitDTO> findAllByParentId(Long parentId, Pageable pageable) {
        LOG.debug("Request to get all OrgUnits by parentId: {}", parentId);

        // 从request header获取租户ID
        String tenantIdHeader = HttpRequestUtil.getHeader(request, RequestConstants.X_TENANT_ID);
        if (tenantIdHeader == null || tenantIdHeader.trim().isEmpty()) {
            LOG.warn("Tenant ID header is null or empty, this may cause data visibility issues");
            throw new IllegalArgumentException("请求头中缺少必需的租户ID");
        }

        Long tenantId;
        try {
            tenantId = Long.valueOf(tenantIdHeader);
        } catch (NumberFormatException e) {
            LOG.error("Invalid tenant ID format: {}", tenantIdHeader, e);
            throw new IllegalArgumentException("Invalid tenant ID format: " + tenantIdHeader, e);
        }

        LOG.debug("Filtering org units by tenant ID: {} and parentId: {}", tenantId, parentId);
        //添加排序
        pageable = PageRequest.of(pageable.getPageNumber(), pageable.getPageSize(),
            Sort.by(Sort.Direction.ASC, "name"));
        return orgUnitRepository.findByTenantIdAndParentIdAndIsDeletedFalse(tenantId, parentId, pageable).map(orgUnitMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<OrgUnitDTO> findAllByParentIdAndKeyword(Long parentId, String keyword, Pageable pageable) {
        LOG.debug("Request to get all OrgUnits by parentId: {} and keyword: {}", parentId, keyword);

        // 从request header获取租户ID
        String tenantIdHeader = HttpRequestUtil.getHeader(request, RequestConstants.X_TENANT_ID);
        if (tenantIdHeader == null || tenantIdHeader.trim().isEmpty()) {
            LOG.warn("Tenant ID header is null or empty, this may cause data visibility issues");
            throw new IllegalArgumentException("请求头中缺少必需的租户ID");
        }

        Long tenantId;
        try {
            tenantId = Long.valueOf(tenantIdHeader);
        } catch (NumberFormatException e) {
            LOG.error("Invalid tenant ID format: {}", tenantIdHeader, e);
            throw new IllegalArgumentException("Invalid tenant ID format: " + tenantIdHeader, e);
        }

        LOG.debug("Filtering org units by tenant ID: {}, parentId: {} and keyword: {}", tenantId, parentId, keyword);
        //添加排序
        pageable = PageRequest.of(pageable.getPageNumber(), pageable.getPageSize(),
            Sort.by(Sort.Direction.ASC, "name"));
        return orgUnitRepository
            .findByTenantIdAndParentIdAndKeywordAndIsDeletedFalse(tenantId, parentId, keyword, pageable)
            .map(orgUnitMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<OrgUnitDTO> findOne(Long id) {
        LOG.debug("Request to get OrgUnit : {}", id);
        return orgUnitRepository.findById(id).map(orgUnitMapper::toDto);
    }

    @Override
    public void delete(Long id) {
        LOG.debug("Request to delete OrgUnit : {}", id);

        // 检查是否有子组织单元
        if (orgUnitRepository.existsByParentId(id)) {
            throw new OrgUnitException("不能删除有子组织单元的组织单元");
        }

        // 逻辑删除：将isDeleted设置为true，而不是物理删除
        orgUnitRepository
            .findById(id)
            .ifPresent(orgUnit -> {
                orgUnit.setIsDeleted(true);
                orgUnitRepository.save(orgUnit);
            });
    }

    /**
     * 获取组织单元树形结构
     */
    @Override
    @Transactional(readOnly = true)
    public List<OrgUnitDTO> getOrgUnitTree() {
        Long tenantId = Long.valueOf(HttpRequestUtil.getHeader(request, RequestConstants.X_TENANT_ID));
        LOG.debug("Request to get org unit tree for tenant : {}", tenantId);

        // 获取所有组织单元
        List<OrgUnit> allOrgUnits = orgUnitRepository.findAllByTenantId(tenantId);

        if (allOrgUnits.isEmpty()) {
            return new ArrayList<>();
        }

        // 使用内存构建树形结构，避免额外的数据库查询
        return buildOrgUnitTreeFromList(allOrgUnits);
    }

    /**
     * 从组织单元列表构建树形结构（内存操作，无数据库查询）
     *
     * @param allOrgUnits 所有组织单元列表
     * @return 树形结构的组织单元DTO列表
     */
    private List<OrgUnitDTO> buildOrgUnitTreeFromList(List<OrgUnit> allOrgUnits) {
        // 创建ID到组织单元的映射，提高查找效率
        Map<Long, OrgUnit> orgUnitMap = allOrgUnits.stream()
            .collect(Collectors.toMap(OrgUnit::getId, Function.identity()));

        // 创建ID到子节点列表的映射
        Map<Long, List<OrgUnit>> childrenMap = new HashMap<>();
        List<OrgUnit> rootNodes = new ArrayList<>();

        // 分类根节点和子节点
        for (OrgUnit orgUnit : allOrgUnits) {
            if (orgUnit.getParent() == null) {
                // 根节点
                rootNodes.add(orgUnit);
            } else {
                // 子节点，添加到父节点的子列表中
                Long parentId = orgUnit.getParent().getId();
                childrenMap.computeIfAbsent(parentId, k -> new ArrayList<>()).add(orgUnit);
            }
        }

        // 构建树形结构
        return rootNodes.stream()
            .map(rootNode -> buildOrgUnitTreeRecursive(rootNode, childrenMap))
            .collect(Collectors.toList());
    }

    /**
     * 递归构建组织单元树（使用内存中的数据）
     *
     * @param orgUnit     当前组织单元
     * @param childrenMap 子节点映射表
     * @return 组织单元DTO
     */
    private OrgUnitDTO buildOrgUnitTreeRecursive(OrgUnit orgUnit, Map<Long, List<OrgUnit>> childrenMap) {
        OrgUnitDTO dto = orgUnitMapper.toDto(orgUnit);

        // 获取子节点列表
        List<OrgUnit> children = childrenMap.getOrDefault(orgUnit.getId(), new ArrayList<>());

        if (!children.isEmpty()) {
            // 递归构建子节点
            List<OrgUnitDTO> childrenDTOs = children.stream()
                .map(child -> buildOrgUnitTreeRecursive(child, childrenMap))
                .collect(Collectors.toList());
            dto.setChildren(childrenDTOs);
        } else {
            // 没有子节点，设置为空列表
            dto.setChildren(new ArrayList<>());
        }

        return dto;
    }

    /**
     * 变更组织单元状态
     */
    @Override
    public OrgUnitDTO changeStatus(Long id, Integer status) {
        LOG.debug("Request to change org unit status : {} to {}", id, status);

        return orgUnitRepository
            .findById(id)
            .map(orgUnit -> {
                orgUnit.setStatus(status);
                return orgUnitRepository.save(orgUnit);
            })
            .map(orgUnitMapper::toDto)
            .orElseThrow(() -> new OrgUnitException("Org unit not found"));
    }

    /**
     * 更新组织单元排序
     */
    @Override
    public void updateSortOrder(List<Long> ids) {
        LOG.debug("Request to update org unit sort order : {}", ids);

        for (int i = 0; i < ids.size(); i++) {
            final int sortOrder = i;
            orgUnitRepository
                .findById(ids.get(i))
                .ifPresent(orgUnit -> {
                    orgUnit.setSortOrder(sortOrder);
                    orgUnitRepository.save(orgUnit);
                });
        }
    }

    /**
     * 验证组织单元类型
     */
    private void validateOrgUnitType(OrgUnitDTO orgUnitDTO) {
        if (orgUnitDTO.getParentId() != null) {
            OrgUnit parent = orgUnitRepository
                .findById(orgUnitDTO.getParentId())
                .orElseThrow(() -> new OrgUnitException("父级组织单元未找到"));

            if (!parent.getType().canContain(orgUnitDTO.getType())) {
                throw new OrgUnitException("组织单元类型与父级不匹配");
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public OrgStructureImportResultDTO importFromExcel(MultipartFile file) {
        LOG.debug("Request to import Organization Structure from Excel file: {}", file.getOriginalFilename());

        OrgStructureImportResultDTO result = new OrgStructureImportResultDTO();
        // 1. 读取Excel文件
        List<OrgStructureImportDTO> importList = ExcelImportUtil.readExcel(file, OrgStructureImportDTO.class);
        LOG.info("从Excel文件中读取到{}条数据", importList.size());

        // 2. 按数据类型分组
        List<OrgStructureImportDTO> orgUnitData = importList.stream()
            .filter(OrgStructureImportDTO::isOrgUnitData)
            .collect(Collectors.toList());

        List<OrgStructureImportDTO> positionData = importList.stream()
            .filter(OrgStructureImportDTO::isPositionData)
            .collect(Collectors.toList());

        LOG.info("组织单元数据: {}条, 岗位数据: {}条", orgUnitData.size(), positionData.size());

        Long tenantId = Long.valueOf(HttpRequestUtil.getHeader(request, RequestConstants.X_TENANT_ID));

        // 3. 先导入组织单元（按层级排序）
        importOrgUnits(orgUnitData, result, tenantId);

        // 4. 再导入岗位
        importPositions(positionData, result, tenantId);

        LOG.info("导入完成: 成功组织单元{}个, 成功岗位{}个, 失败{}个",
            result.getSuccessOrgUnitCount(), result.getSuccessPositionCount(), result.getFailureCount());

        return result;
    }

    /**
     * 导入组织单元
     */
    private void importOrgUnits(List<OrgStructureImportDTO> orgUnitData, OrgStructureImportResultDTO result, Long tenantId) {
        String username = HttpRequestUtil.getHeader(request, RequestConstants.X_USER_NAME);
        Instant now = Instant.now();

        // 按层级排序，确保父级先于子级导入
        orgUnitData.sort(Comparator.comparing(dto ->
            dto.getOrgUnitLevel() != null ? dto.getOrgUnitLevel() : NumberConstants.ONE));

        // 用于存储已导入的组织单元，便于查找父级
        Map<String, OrgUnit> importedOrgUnits = new HashMap<>();

        for (OrgStructureImportDTO importDTO : orgUnitData) {
            try {

                // 验证组织单元编码是否已存在
                Optional<OrgUnit> existingOrgUnit = orgUnitRepository.findByTenantIdAndCodeAndIsDeletedFalse(tenantId, importDTO.getOrgUnitCode());
                if (existingOrgUnit.isPresent()) {
                    result.addErrorMessage("组织单元 " + importDTO.getOrgUnitCode() + ": 编码已存在");
                    continue;
                }

                // 创建组织单元
                OrgUnit orgUnit = new OrgUnit();
                orgUnit.setTenantId(tenantId);
                orgUnit.setCode(importDTO.getOrgUnitCode());
                orgUnit.setName(importDTO.getOrgUnitName());
                orgUnit.setType(importDTO.parseOrgUnitType());
                orgUnit.setStatus(NumberConstants.ONE);
                orgUnit.setSortOrder(importDTO.getOrgUnitSortOrder() != null ? importDTO.getOrgUnitSortOrder() : NumberConstants.ONE);
                orgUnit.setDescription(importDTO.getOrgUnitDescription());

                // 处理父级关系
                if (importDTO.getParentOrgUnitCode() != null && !importDTO.getParentOrgUnitCode().trim().isEmpty()) {
                    // 先从已导入的组织单元中查找
                    OrgUnit parent = importedOrgUnits.get(importDTO.getParentOrgUnitCode());
                    if (parent == null) {
                        // 从数据库中查找
                        Optional<OrgUnit> parentOpt = orgUnitRepository.findByTenantIdAndCodeAndIsDeletedFalse(
                            tenantId, importDTO.getParentOrgUnitCode());
                        if (parentOpt.isPresent()) {
                            parent = parentOpt.orElse(null);
                        } else {
                            result.addErrorMessage("组织单元 " + importDTO.getOrgUnitCode() + ": 父级组织单元不存在 - " + importDTO.getParentOrgUnitCode());
                            continue;
                        }
                    }
                    orgUnit.setParent(parent);
                    orgUnit.setLevel(parent.getLevel() + 1);
                } else {
                    orgUnit.setLevel(importDTO.getOrgUnitLevel() != null ? importDTO.getOrgUnitLevel() : 1);
                }

                // 设置审计字段
                orgUnit.setVersion(NumberConstants.ONE);
                orgUnit.setCreatedBy(username);
                orgUnit.setUpdatedBy(username);
                orgUnit.setCreatedAt(now);
                orgUnit.setUpdatedAt(now);
                orgUnit.setIsDeleted(Boolean.FALSE);

                // 保存组织单元
                orgUnit = orgUnitRepository.save(orgUnit);

                // 添加到已导入的组织单元映射中
                importedOrgUnits.put(orgUnit.getCode(), orgUnit);

                result.addImportedOrgUnit(orgUnitMapper.toDto(orgUnit));
                LOG.debug("成功导入组织单元: {}", importDTO.getOrgUnitCode());

            } catch (Exception e) {
                LOG.error("导入组织单元失败: {}, 错误: {}", importDTO.getOrgUnitCode(), e.getMessage(), e);
                result.addErrorMessage("组织单元 " + importDTO.getOrgUnitCode() + ": " + e.getMessage());
            }
        }
    }

    /**
     * 导入岗位
     */
    private void importPositions(List<OrgStructureImportDTO> positionData, OrgStructureImportResultDTO result, Long tenantId) {
        String username = HttpRequestUtil.getHeader(request, RequestConstants.X_USER_NAME);
        Instant now = Instant.now();

        for (OrgStructureImportDTO importDTO : positionData) {
            try {
                // 验证岗位编码是否已存在
                Optional<Position> existingPosition = positionRepository.findByTenantIdAndCodeAndIsDeletedFalse(
                    tenantId, importDTO.getPositionCode());
                if (existingPosition.isPresent()) {
                    result.addErrorMessage("岗位 " + importDTO.getPositionCode() + ": 编码已存在");
                    continue;
                }

                // 验证所属组织单元是否存在
                Optional<OrgUnit> orgUnitOpt = orgUnitRepository.findByTenantIdAndCodeAndIsDeletedFalse(
                    tenantId, importDTO.getBelongsToOrgUnitCode());
                if (orgUnitOpt.isEmpty()) {
                    result.addErrorMessage("岗位 " + importDTO.getPositionCode() + ": 所属组织单元不存在 - " + importDTO.getBelongsToOrgUnitCode());
                    continue;
                }

                // 创建岗位
                Position position = new Position();
                position.setTenantId(tenantId);
                position.setCode(importDTO.getPositionCode());
                position.setName(importDTO.getPositionName());
                position.setLevel(importDTO.getPositionLevel());
                position.setCategory(importDTO.parsePositionCategory());
                position.setOrgUnit(orgUnitOpt.orElse(null));
                position.setDescription(importDTO.getPositionDescription());

                // 设置审计字段
                position.setVersion(NumberConstants.ONE);
                position.setCreatedBy(username);
                position.setUpdatedBy(username);
                position.setCreatedAt(now);
                position.setUpdatedAt(now);
                position.setIsDeleted(Boolean.FALSE);

                // 保存岗位
                position = positionRepository.save(position);

                result.addImportedPosition(positionMapper.toDto(position));
                LOG.debug("成功导入岗位: {}", importDTO.getPositionCode());

            } catch (Exception e) {
                LOG.error("导入岗位失败: {}, 错误: {}", importDTO.getPositionCode(), e.getMessage(), e);
                result.addErrorMessage("岗位 " + importDTO.getPositionCode() + ": " + e.getMessage());
            }
        }
    }

    @Override
    @Transactional(readOnly = true)
    public List<OrgUnitDTO> findByNameContainingIgnoreCase(String name) {
        LOG.debug("Request to search OrgUnits by name containing : {}", name);

        // 从request header获取租户ID
        String tenantIdHeader = HttpRequestUtil.getHeader(request, RequestConstants.X_TENANT_ID);
        if (tenantIdHeader == null || tenantIdHeader.trim().isEmpty()) {
            LOG.warn("Tenant ID header is null or empty, this may cause data visibility issues");
            throw new IllegalArgumentException("请求头中缺少必需的租户ID");
        }

        Long tenantId;
        try {
            tenantId = Long.valueOf(tenantIdHeader);
        } catch (NumberFormatException e) {
            LOG.error("Invalid tenant ID format: {}", tenantIdHeader, e);
            throw new IllegalArgumentException("租户ID格式无效: " + tenantIdHeader, e);
        }

        LOG.debug("Searching organization units by name containing '{}' for tenant ID: {}", name, tenantId);

        List<OrgUnit> orgUnits = orgUnitRepository.findByTenantIdAndNameContainingIgnoreCaseAndIsDeletedFalse(tenantId, name);
        return orgUnits.stream().map(orgUnitMapper::toDto).toList();
    }

    @Override
    public Boolean isTopOrgUnit(Long employeeId) {
        Long tenantId = Long.valueOf(HttpRequestUtil.getHeader(request, RequestConstants.X_TENANT_ID));
        //查询当前租户层级小于等于1的组织单元
        List<OrgUnit> orgUnits = orgUnitRepository.findByTenantIdAndLevelLessThanEqual(tenantId, NumberConstants.ONE);
        if (CollectionUtils.isEmpty(orgUnits)) {
            return false;
        }
        //根据层级进行分组
        Map<Integer, List<OrgUnit>> map = orgUnits.stream().collect(Collectors.groupingBy(OrgUnit::getLevel));

        //获取总公司
        List<OrgUnit> companyList = map.get(NumberConstants.ZERO);
        if (CollectionUtils.isEmpty(companyList)) {
            return false;
        }
        Set<Long> companyOrgIds = companyList.stream()
            .map(OrgUnit::getId).collect(Collectors.toSet());

        //判断当前用户是否在总公司中
        List<EmployeeOrg> employeeOrgList = employeeOrgRepository.findByEmployeeId(employeeId);
        if (CollectionUtils.isEmpty(employeeOrgList)) {
            return false;
        }
        Set<Long> employeeOrgIds = new HashSet<>();
        for (EmployeeOrg employeeOrg : employeeOrgList) {
            OrgUnit orgUnit = employeeOrg.getOrgUnit();
            if (orgUnit == null) {
                continue;
            }
            //如果当前组织单元是子公司，则直接添加
            if (orgUnit.getType() == OrgUnitType.SUBSIDIARY) {
                employeeOrgIds.add(orgUnit.getId());
                continue;
            }
            if (companyOrgIds.contains(orgUnit.getId())) {
                employeeOrgIds.add(orgUnit.getId());
            } else {
                //如果不在总公司中，则迭代判断其父级是否在总公司中
                while (orgUnit.getParent() != null) {
                    orgUnit = orgUnit.getParent();
                    //如果父级是子公司，则直接添加
                    if (orgUnit.getType() == OrgUnitType.SUBSIDIARY) {
                        employeeOrgIds.add(orgUnit.getId());
                        break;
                    }
                    if (companyOrgIds.contains(orgUnit.getId())) {
                        employeeOrgIds.add(orgUnit.getId());
                    }
                }
            }
        }
        //companyOrgIds 和 employeeOrgIds 有交集则返回false
        return CollectionUtils.isEmpty(CollectionUtils.intersection(companyOrgIds, employeeOrgIds));
    }

    @Override
    public List<OrgUnitDTO> findByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return List.of();
        }
        List<OrgUnit> list = orgUnitRepository.findByIdIn(ids);
        if (!CollectionUtils.isEmpty(list)) {
            return list.stream().map(orgUnitMapper::toDto).collect(Collectors.toList());
        }
        return List.of();
    }

}

package com.whiskerguard.organization.service.impl;

import com.whiskerguard.common.config.RequestConstants;
import com.whiskerguard.common.constant.NumberConstants;
import com.whiskerguard.common.util.HttpRequestUtil;
import com.whiskerguard.organization.domain.AuditLog;
import com.whiskerguard.organization.domain.enumeration.AuditOperation;
import com.whiskerguard.organization.repository.AuditLogRepository;
import com.whiskerguard.organization.service.AuditLogService;
import com.whiskerguard.organization.service.dto.AuditLogDTO;
import com.whiskerguard.organization.service.mapper.AuditLogMapper;
import jakarta.servlet.http.HttpServletRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.Optional;

/**
 * 描述：审计日志的服务实现类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/18
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class AuditLogServiceImpl implements AuditLogService {

    private static final Logger LOG = LoggerFactory.getLogger(AuditLogServiceImpl.class);

    private final AuditLogRepository auditLogRepository;
    private final AuditLogMapper auditLogMapper;

    private final HttpServletRequest request;

    public AuditLogServiceImpl(AuditLogRepository auditLogRepository, AuditLogMapper auditLogMapper, HttpServletRequest request) {
        this.auditLogRepository = auditLogRepository;
        this.auditLogMapper = auditLogMapper;
        this.request = request;
    }

    @Override
    public AuditLogDTO save(AuditLogDTO auditLogDTO) {
        LOG.debug("Request to save AuditLog : {}", auditLogDTO);
        AuditLog auditLog = auditLogMapper.toEntity(auditLogDTO);
        String username = HttpRequestUtil.getHeader(request, RequestConstants.X_USER_NAME);
        auditLog.setTimestamp(Instant.now());
        auditLog.setVersion(NumberConstants.ONE);
        auditLog.setCreatedBy(username);
        auditLog.setUpdatedBy(username);
        auditLog.setCreatedAt(Instant.now());
        auditLog.setUpdatedAt(Instant.now());
        auditLog.setIsDeleted(Boolean.FALSE);
        auditLog = auditLogRepository.save(auditLog);
        return auditLogMapper.toDto(auditLog);
    }

    @Override
    public AuditLogDTO update(AuditLogDTO auditLogDTO) {
        LOG.debug("Request to update AuditLog : {}", auditLogDTO);
        AuditLog auditLog = auditLogMapper.toEntity(auditLogDTO);
        auditLog = auditLogRepository.save(auditLog);
        return auditLogMapper.toDto(auditLog);
    }

    @Override
    public Optional<AuditLogDTO> partialUpdate(AuditLogDTO auditLogDTO) {
        LOG.debug("Request to partially update AuditLog : {}", auditLogDTO);
        return auditLogRepository
            .findById(auditLogDTO.getId())
            .map(existingAuditLog -> {
                auditLogMapper.partialUpdate(existingAuditLog, auditLogDTO);
                existingAuditLog.setTimestamp(Instant.now());
                existingAuditLog.setUpdatedAt(Instant.now());
                existingAuditLog.setUpdatedBy(HttpRequestUtil.getHeader(request, RequestConstants.X_USER_NAME));
                return existingAuditLog;
            })
            .map(auditLogRepository::save)
            .map(auditLogMapper::toDto);
    }

    @Override
    public void delete(Long id) {
        LOG.debug("Request to delete AuditLog : {}", id);
        // 逻辑删除：将isDeleted设置为true，而不是物理删除
        auditLogRepository
            .findById(id)
            .ifPresent(auditLog -> {
                auditLog.setIsDeleted(true);
                auditLogRepository.save(auditLog);
            });
    }

    @Override
    public AuditLogDTO log(String entityName, Long entityId, AuditOperation operation, String diff) {
        LOG.debug("Request to log audit : {} {} {} {}", entityName, entityId, operation, diff);

        AuditLog auditLog = null;
        try {
            auditLog = new AuditLog();
            // 从request获取租户ID
            String tenantId = HttpRequestUtil.getHeader(request, RequestConstants.X_TENANT_ID);
            auditLog.setTenantId(Long.valueOf(tenantId));
            auditLog.setEntityName(entityName);
            auditLog.setEntityId(entityId);
            auditLog.setOperation(operation.name());
            auditLog.setDiff(diff);
            auditLog.setTimestamp(Instant.now());
            auditLog.setVersion(1);
            auditLog.setIsDeleted(false);

            String username = HttpRequestUtil.getHeader(request, RequestConstants.X_USER_NAME);
            auditLog.setOperator(username);
            auditLog.setCreatedBy(username);
            auditLog.setUpdatedBy(username);
            auditLog.setCreatedAt(Instant.now());
            auditLog.setUpdatedAt(Instant.now());

            auditLog = auditLogRepository.save(auditLog);
        } catch (Exception e) {
            LOG.error("Failed to log audit", e);
        }
        return auditLogMapper.toDto(auditLog);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<AuditLogDTO> findAll(Pageable pageable) {
        LOG.debug("Request to get all AuditLogs");
        return auditLogRepository.findByIsDeletedFalse(pageable).map(auditLogMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<AuditLogDTO> findOne(Long id) {
        LOG.debug("Request to get AuditLog : {}", id);
        return auditLogRepository.findById(id).map(auditLogMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<AuditLogDTO> findByEntity(String entityName, Long entityId, Pageable pageable) {
        LOG.debug("Request to get AuditLogs by entity : {} {}", entityName, entityId);
        return auditLogRepository.findByEntityNameAndEntityId(entityName, entityId, pageable).map(auditLogMapper::toDto);
    }
}

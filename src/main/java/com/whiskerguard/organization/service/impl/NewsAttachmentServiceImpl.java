package com.whiskerguard.organization.service.impl;

import com.whiskerguard.common.config.RequestConstants;
import com.whiskerguard.common.constant.NumberConstants;
import com.whiskerguard.common.util.HttpRequestUtil;
import com.whiskerguard.organization.domain.NewsAttachment;
import com.whiskerguard.organization.repository.NewsAttachmentRepository;
import com.whiskerguard.organization.service.NewsAttachmentService;
import com.whiskerguard.organization.service.dto.NewsAttachmentDTO;
import com.whiskerguard.organization.service.mapper.NewsAttachmentMapper;
import jakarta.servlet.http.HttpServletRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.Optional;

/**
 * 描述：新闻附件的服务实现类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/18
 */
@Service
@Transactional
public class NewsAttachmentServiceImpl implements NewsAttachmentService {

    private static final Logger LOG = LoggerFactory.getLogger(NewsAttachmentServiceImpl.class);

    private final NewsAttachmentRepository newsAttachmentRepository;

    private final NewsAttachmentMapper newsAttachmentMapper;

    private final HttpServletRequest request;

    public NewsAttachmentServiceImpl(NewsAttachmentRepository newsAttachmentRepository, NewsAttachmentMapper newsAttachmentMapper,
                                     HttpServletRequest request) {
        this.newsAttachmentRepository = newsAttachmentRepository;
        this.newsAttachmentMapper = newsAttachmentMapper;
        this.request = request;
    }

    @Override
    public NewsAttachmentDTO save(NewsAttachmentDTO newsAttachmentDTO) {
        LOG.debug("Request to save NewsAttachment : {}", newsAttachmentDTO);
        NewsAttachment newsAttachment = newsAttachmentMapper.toEntity(newsAttachmentDTO);
        String username = HttpRequestUtil.getHeader(request, RequestConstants.X_USER_NAME);
        newsAttachment.setTenantId(Long.valueOf(HttpRequestUtil.getHeader(request, RequestConstants.X_TENANT_ID)));
        newsAttachment.setVersion(NumberConstants.ONE);
        newsAttachment.setCreatedBy(username);
        newsAttachment.setUpdatedBy(username);
        newsAttachment.setCreatedAt(Instant.now());
        newsAttachment.setUpdatedAt(Instant.now());
        newsAttachment.setIsDeleted(Boolean.FALSE);
        newsAttachment = newsAttachmentRepository.save(newsAttachment);
        return newsAttachmentMapper.toDto(newsAttachment);
    }

    @Override
    public NewsAttachmentDTO update(NewsAttachmentDTO newsAttachmentDTO) {
        LOG.debug("Request to update NewsAttachment : {}", newsAttachmentDTO);
        NewsAttachment newsAttachment = newsAttachmentMapper.toEntity(newsAttachmentDTO);
        newsAttachment = newsAttachmentRepository.save(newsAttachment);
        return newsAttachmentMapper.toDto(newsAttachment);
    }

    @Override
    public Optional<NewsAttachmentDTO> partialUpdate(NewsAttachmentDTO newsAttachmentDTO) {
        LOG.debug("Request to partially update NewsAttachment : {}", newsAttachmentDTO);

        return newsAttachmentRepository
            .findById(newsAttachmentDTO.getId())
            .map(existingNewsAttachment -> {
                newsAttachmentMapper.partialUpdate(existingNewsAttachment, newsAttachmentDTO);
                String username = HttpRequestUtil.getHeader(request, RequestConstants.X_USER_NAME);
                existingNewsAttachment.setUpdatedBy(username);
                existingNewsAttachment.setUpdatedAt(Instant.now());
                return existingNewsAttachment;
            })
            .map(newsAttachmentRepository::save)
            .map(newsAttachmentMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<NewsAttachmentDTO> findAll(Pageable pageable) {
        LOG.debug("Request to get all NewsAttachments");
        return newsAttachmentRepository.findByIsDeletedFalse(pageable).map(newsAttachmentMapper::toDto);
    }

    @Override
    public Page<NewsAttachmentDTO> findAllWithEagerRelationships(Pageable pageable) {
        return newsAttachmentRepository.findAllWithEagerRelationshipsAndNotDeleted(pageable).map(newsAttachmentMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<NewsAttachmentDTO> findOne(Long id) {
        LOG.debug("Request to get NewsAttachment : {}", id);
        return newsAttachmentRepository.findOneWithEagerRelationships(id).map(newsAttachmentMapper::toDto);
    }

    @Override
    public void delete(Long id) {
        LOG.debug("Request to delete NewsAttachment : {}", id);
        // 逻辑删除：将isDeleted设置为true，而不是物理删除
        newsAttachmentRepository
            .findById(id)
            .ifPresent(newsAttachment -> {
                newsAttachment.setIsDeleted(true);
                newsAttachmentRepository.save(newsAttachment);
            });
    }
}

package com.whiskerguard.organization.service;

import com.whiskerguard.common.dto.ImportResultDTO;
import com.whiskerguard.organization.domain.enumeration.EmployeeStatus;
import com.whiskerguard.organization.req.EmployeeReq;
import com.whiskerguard.organization.service.dto.*;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Optional;

/**
 * 描述：员工服务接口
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/18
 */
public interface EmployeeService {
    /**
     * Save a employee.
     * 保存员工信息
     *
     * @param employeeDTO the entity to save. 要保存的员工DTO对象
     * @return the persisted entity. 持久化后的员工DTO对象
     */
    EmployeeDTO save(EmployeeDTO employeeDTO);

    /**
     * Updates a employee.
     * 更新员工信息
     *
     * @param employeeDTO the entity to update. 要更新的员工DTO对象
     * @return the persisted entity. 持久化后的员工DTO对象
     */
    EmployeeDTO update(EmployeeDTO employeeDTO);

    /**
     * Partially updates a employee.
     * 部分更新员工信息
     *
     * @param employeeDTO the entity to update partially. 要部分更新的员工DTO对象
     * @return the persisted entity. 持久化后的员工DTO对象
     */
    Optional<EmployeeDTO> partialUpdate(EmployeeDTO employeeDTO);

    /**
     * Get the "id" employee.
     * 根据ID获取员工信息
     *
     * @param id the id of the entity. 员工ID
     * @return the entity. 员工DTO对象
     */
    Optional<EmployeeDTO> findOne(Long id);

    /**
     * Delete the "id" employee.
     * 根据ID删除员工
     *
     * @param id the id of the entity. 员工ID
     */
    void delete(Long id);

    /**
     * 修改员工状态
     *
     * @param id     员工ID
     * @param status 新状态
     * @return 更新后的员工DTO
     */
    EmployeeDTO changeStatus(Long id, EmployeeStatus status);

    /**
     * 获取指定组织单元下的员工列表
     *
     * @param orgUnitId 组织单元ID
     * @param pageable  分页信息
     * @return 员工列表
     */
    Page<EmployeeDTO> findByOrgUnit(Long orgUnitId, Pageable pageable);

    /**
     * 根据状态查询员工
     *
     * @param status   员工状态
     * @param pageable 分页参数
     * @return 员工分页列表
     */
    Page<EmployeeDTO> findByStatus(EmployeeStatus status, Pageable pageable);

    /**
     * 分配员工到组织单元
     *
     * @param employeeId 员工ID
     * @param orgUnitId  组织单元ID
     * @param positionId 职位ID
     * @param isPrimary  是否主部门
     * @return 员工组织关系信息
     */
    EmployeeDTO assignToOrgUnit(Long employeeId, Long orgUnitId, Long positionId, Boolean isPrimary);

    /**
     * 移除员工与组织单元的关系
     *
     * @param employeeId 员工ID
     * @param orgUnitId  组织单元ID
     */
    void removeFromOrgUnit(Long employeeId, Long orgUnitId);

    /**
     * 员工登录
     *
     * @param loginDTO 登录信息
     * @param clientIp 客户端IP
     * @return 登录响应
     */
    LoginResponseDTO login(LoginDTO loginDTO, String clientIp);

    /**
     * 微信手机号登录
     *
     * @param loginDTO 微信登录DTO
     * @param clientIp 客户端IP
     * @return 登录响应
     */
    EmployeeDTO wechatPhoneLogin(WechatPhoneLoginDTO loginDTO, String clientIp);

    /**
     * 根据手机号查找员工
     *
     * @param phone 手机号
     * @return 员工信息
     */
    Optional<EmployeeDTO> findByPhone(String phone);

    /**
     * 修改密码
     *
     * @param employeeId        员工ID
     * @param passwordChangeDTO 密码修改信息
     * @return 更新后的员工信息
     */
    EmployeeDTO changePassword(Long employeeId, PasswordChangeDTO passwordChangeDTO);

    /**
     * 更新个人信息
     *
     * @param employeeId  员工ID
     * @param employeeDTO 员工信息
     * @return 更新后的员工信息
     */
    EmployeeDTO updateProfile(Long employeeId, EmployeeDTO employeeDTO);

    /**
     * 根据用户名和密码查询员工
     * Find employee by username and password
     *
     * @param username 用户名 username
     * @param password 密码 password
     * @param status   员工状态 employee status
     * @return 员工信息 employee information
     */
    Optional<EmployeeDTO> findByUsernameAndPassword(String username, String password, EmployeeStatus status);

    /**
     * 获取员工的认证信息（角色和权限）
     * Get employee authentication information (roles and permissions)
     *
     * @param id 员工ID employee ID
     * @return 员工认证信息 employee authentication information
     */
    EmployeeAuthDTO getEmployeeAuthInfo(Long id);

    /**
     * 获取员工的认证信息（角色和权限）
     * Get employee authentication information by username
     *
     * @param username 员工用户名 employee username
     * @return 员工认证信息 employee authentication information
     */
    EmployeeDTO getEmployeeAuthInfoByUsername(String username);

    /**
     * 从Excel文件导入员工
     *
     * @param file the Excel file to import. 要导入的Excel文件
     * @return the import result. 导入结果
     */
    ImportResultDTO<EmployeeImportDTO> importFromExcel(MultipartFile file);

    /**
     * 重置密码
     *
     * @param passwordChangeDTO 密码修改信息
     * @return 更新后的员工信息
     */
    EmployeeDTO resetPassword(PasswordChangeDTO passwordChangeDTO);

    /**
     * 更换绑定手机号
     *
     * @param passwordChangeDTO 密码修改信息
     */
    void changePhone(PasswordChangeDTO passwordChangeDTO);

    /**
     * 根据员工ID集合返回员工信息
     *
     * @param ids 员工ID集合
     * @return 员工信息列表
     */
    List<EmployeeDTO> findByIds(List<Long> ids);

    /**
     * 更新头像
     *
     * @param id     员工ID
     * @param avatar 头像
     * @return 更新后的员工信息
     */
    EmployeeDTO updateAvatar(Long id, String avatar);

    /**
     * 根据租户ID获取所有员工ID
     *
     * @return 员工ID列表
     */
    List<Long> findEmployeeIdsByTenantId();

    /**
     * 绑定微信OpenID
     *
     * @param openId  微信OpenID
     * @param unionId 微信UnionID
     * @return 更新后的员工信息
     */
    EmployeeDTO bindOpenId(String openId, String unionId);

    /**
     * 解绑微信OpenID
     *
     * @param openId 微信OpenID
     * @return 更新后的员工信息
     */
    EmployeeDTO unbindOpenId(String openId);

    /**
     * 根据组织单元ID获取所有员工ID
     *
     * @param orgUnitId 组织单元ID
     * @return 员工ID列表
     */
    List<Long> findEmployeeIdsByOrgUnit(Long orgUnitId);

    /**
     * 根据条件查询员工
     *
     * @param employeeReq 员工查询请求对象
     * @return 员工分页列表
     */
    Page<EmployeeDTO> findByCondition(EmployeeReq employeeReq);

    /**
     * 获取用户统计信息
     *
     * @return 用户统计信息
     */
    UserStatisticsDTO getUserStatistics();

    /**
     * 根据姓名模糊搜索员工
     * 搜索用户名(username)和真实姓名(realName)字段
     *
     * @param name 搜索关键词
     * @return 员工列表
     */
    List<EmployeeDTO> searchByName(String name);
}

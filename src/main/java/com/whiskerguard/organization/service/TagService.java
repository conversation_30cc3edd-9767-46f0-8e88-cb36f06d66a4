package com.whiskerguard.organization.service;

import com.whiskerguard.organization.service.dto.TagDTO;
import java.util.Optional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

/**
 * 标签管理服务接口
 * Service Interface for managing {@link com.whiskerguard.organization.domain.Tag}.
 */
public interface TagService {
    /**
     * 保存标签信息
     * Save a tag.
     *
     * @param tagDTO 要保存的标签DTO对象 the entity to save.
     * @return 持久化后的标签DTO对象 the persisted entity.
     */
    TagDTO save(TagDTO tagDTO);

    /**
     * 更新标签信息
     * Updates a tag.
     *
     * @param tagDTO 要更新的标签DTO对象 the entity to update.
     * @return 持久化后的标签DTO对象 the persisted entity.
     */
    TagDTO update(TagDTO tagDTO);

    /**
     * 部分更新标签信息
     * Partially updates a tag.
     *
     * @param tagDTO 要部分更新的标签DTO对象 the entity to update partially.
     * @return 持久化后的标签DTO对象 the persisted entity.
     */
    Optional<TagDTO> partialUpdate(TagDTO tagDTO);

    /**
     * 获取所有标签信息
     * Get all the tags.
     *
     * @param pageable 分页信息 the pagination information.
     * @return 标签DTO对象分页列表 the list of entities.
     */
    Page<TagDTO> findAll(Pageable pageable);

    /**
     * 获取所有标签信息（包含关联关系的急加载）
     * Get all the tags with eager load of many-to-many relationships.
     *
     * @param pageable 分页信息 the pagination information.
     * @return 标签DTO对象分页列表 the list of entities.
     */
    Page<TagDTO> findAllWithEagerRelationships(Pageable pageable);

    /**
     * 根据ID获取标签信息
     * Get the "id" tag.
     *
     * @param id 标签ID the id of the entity.
     * @return 标签DTO对象 the entity.
     */
    Optional<TagDTO> findOne(Long id);

    /**
     * 根据ID删除标签
     * Delete the "id" tag.
     *
     * @param id 标签ID the id of the entity.
     */
    void delete(Long id);
}

package com.whiskerguard.organization.service;

import com.whiskerguard.organization.service.dto.RiskCategoryDTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.Optional;

/**
 * 描述：风险类别服务接口
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/20
 */
public interface RiskCategoryService {

    /**
     * 方法名称：save
     * 描述：保存风险类别记录
     *
     * @param riskCategoryDTO 要保存的风险类别DTO
     * @return 保存后的风险类别DTO
     * @since 1.0
     */
    RiskCategoryDTO save(RiskCategoryDTO riskCategoryDTO);

    /**
     * 方法名称：partialUpdate
     * 描述：部分更新风险类别记录
     *
     * @param riskCategoryDTO 要更新的风险类别DTO
     * @return 更新后的风险类别DTO
     * @since 1.0
     */
    Optional<RiskCategoryDTO> partialUpdate(RiskCategoryDTO riskCategoryDTO);

    /**
     * 方法名称：findAll
     * 描述：查询所有风险类别记录
     *
     * @param riskModelId 风险模型ID
     * @param pageable    分页参数
     * @return 风险类别DTO分页结果
     * @since 1.0
     */
    Page<RiskCategoryDTO> findAll(Long riskModelId, Pageable pageable);

    /**
     * 方法名称：findOne
     * 描述：根据ID查询风险类别记录
     *
     * @param id 风险类别ID
     * @return 风险类别DTO
     * @since 1.0
     */
    Optional<RiskCategoryDTO> findOne(Long id);

    /**
     * 方法名称：delete
     * 描述：删除风险类别记录
     *
     * @param id 风险类别ID
     * @since 1.0
     */
    void delete(Long id);
}

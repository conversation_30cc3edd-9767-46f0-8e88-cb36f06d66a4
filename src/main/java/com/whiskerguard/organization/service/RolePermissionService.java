package com.whiskerguard.organization.service;

import com.whiskerguard.organization.service.dto.RolePermissionDTO;
import java.util.List;
import java.util.Optional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

/**
 * Service Interface for managing {@link com.whiskerguard.organization.domain.RolePermission}.
 * 角色-权限关联管理服务接口
 */
public interface RolePermissionService {
    /**
     * Save a rolePermission.
     * 保存角色-权限关联信息
     *
     * @param rolePermissionDTO the entity to save. 要保存的角色-权限关联DTO对象
     * @return the persisted entity. 持久化后的角色-权限关联DTO对象
     */
    RolePermissionDTO save(RolePermissionDTO rolePermissionDTO);

    /**
     * Batch save rolePermissions.
     * 批量保存角色-权限关联信息
     *
     * @param list the entities to save. 要保存的角色-权限关联DTO对象列表
     * @return the persisted entities. 持久化后的角色-权限关联DTO对象列表
     */
    List<RolePermissionDTO> saveAll(List<RolePermissionDTO> list);

    /**
     * Updates a rolePermission.
     * 更新角色-权限关联信息
     *
     * @param rolePermissionDTO the entity to update. 要更新的角色-权限关联DTO对象
     * @return the persisted entity. 持久化后的角色-权限关联DTO对象
     */
    RolePermissionDTO update(RolePermissionDTO rolePermissionDTO);

    /**
     * Partially updates a rolePermission.
     * 部分更新角色-权限关联信息
     *
     * @param rolePermissionDTO the entity to update partially. 要部分更新的角色-权限关联DTO对象
     * @return the persisted entity. 持久化后的角色-权限关联DTO对象
     */
    Optional<RolePermissionDTO> partialUpdate(RolePermissionDTO rolePermissionDTO);

    /**
     * Get all the rolePermissions.
     * 获取所有角色-权限关联信息
     *
     * @param pageable the pagination information. 分页信息
     * @return the list of entities. 角色-权限关联DTO对象分页列表
     */
    Page<RolePermissionDTO> findAll(Pageable pageable);

    /**
     * Get the "id" rolePermission.
     * 根据ID获取角色-权限关联信息
     *
     * @param id the id of the entity. 角色-权限关联ID
     * @return the entity. 角色-权限关联DTO对象
     */
    Optional<RolePermissionDTO> findOne(Long id);

    /**
     * Delete the "id" rolePermission.
     * 根据ID删除角色-权限关联
     *
     * @param id the id of the entity. 角色-权限关联ID
     */
    void delete(Long id);

    /**
     * Batch delete rolePermissions by IDs.
     * 根据ID列表批量删除角色-权限关联
     *
     * @param ids the list of IDs of the entities. 角色-权限关联ID列表
     */
    void deleteAll(List<Long> ids);

    /**
     * 获取员工的所有权限
     * Get all permissions of an employee
     *
     * @param employeeId 员工ID employee ID
     * @return 权限列表 permission list
     */
    List<RolePermissionDTO> findByEmployeeId(Long employeeId);
}

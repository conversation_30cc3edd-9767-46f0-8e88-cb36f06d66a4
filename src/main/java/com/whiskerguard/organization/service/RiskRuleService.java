package com.whiskerguard.organization.service;

import com.whiskerguard.organization.service.dto.RiskRuleDTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.Optional;

/**
 * 描述：风险规则服务接口
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/20
 */
public interface RiskRuleService {

    /**
     * 方法名称：save
     * 描述：保存风险规则记录
     *
     * @param riskRuleDTO 要保存的风险规则DTO
     * @return 保存后的风险规则DTO
     * @since 1.0
     */
    RiskRuleDTO save(RiskRuleDTO riskRuleDTO);

    /**
     * 方法名称：partialUpdate
     * 描述：部分更新风险规则记录
     *
     * @param riskRuleDTO 要更新的风险规则DTO
     * @return 更新后的风险规则DTO
     * @since 1.0
     */
    Optional<RiskRuleDTO> partialUpdate(RiskRuleDTO riskRuleDTO);

    /**
     * 方法名称：findAll
     * 描述：查询所有风险规则记录
     *
     * @param riskCategoryId 风险类别ID
     * @param pageable       分页参数
     * @return 风险规则DTO分页结果
     * @since 1.0
     */
    Page<RiskRuleDTO> findAll(Long riskCategoryId, Pageable pageable);

    /**
     * 方法名称：findOne
     * 描述：根据ID查询风险规则记录
     *
     * @param id 风险规则ID
     * @return 风险规则DTO
     * @since 1.0
     */
    Optional<RiskRuleDTO> findOne(Long id);

    /**
     * 方法名称：delete
     * 描述：删除风险规则记录
     *
     * @param id 风险规则ID
     * @since 1.0
     */
    void delete(Long id);
}

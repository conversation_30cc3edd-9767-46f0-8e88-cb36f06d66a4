package com.whiskerguard.organization.service;

import com.whiskerguard.organization.service.dto.NewsDTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.Optional;

/**
 * 新闻管理服务接口
 * Service Interface for managing {@link com.whiskerguard.organization.domain.News}.
 */
public interface NewsService {

    /**
     * 保存新闻信息
     *
     * @param newsDTO 要保存的新闻DTO对象 the entity to save.
     * @return 持久化后的新闻DTO对象 the persisted entity.
     */
    NewsDTO save(NewsDTO newsDTO);
    
    /**
     * 部分更新新闻信息
     *
     * @param newsDTO 要部分更新的新闻DTO对象 the entity to update partially.
     * @return 持久化后的新闻DTO对象 the persisted entity.
     */
    Optional<NewsDTO> partialUpdate(NewsDTO newsDTO);

    /**
     * 获取所有新闻信息
     *
     * @param pageable   分页信息 the pagination information.
     * @param categoryId 新闻分类ID the category id.
     * @return 新闻DTO对象分页列表 the list of entities.
     */
    Page<NewsDTO> findAll(Pageable pageable, Long categoryId);

    /**
     * 获取所有新闻信息（包含关联关系的急加载）
     *
     * @param pageable   分页信息 the pagination information.
     * @param categoryId 新闻分类ID the category id.
     * @return 新闻DTO对象分页列表 the list of entities.
     */
    Page<NewsDTO> findAllWithEagerRelationships(Pageable pageable, Long categoryId);

    /**
     * 根据ID获取新闻信息
     *
     * @param id 新闻ID the id of the entity.
     * @return 新闻DTO对象 the entity.
     */
    Optional<NewsDTO> findOne(Long id);

    /**
     * 根据ID删除新闻
     *
     * @param id 新闻ID the id of the entity.
     */
    void delete(Long id);
}

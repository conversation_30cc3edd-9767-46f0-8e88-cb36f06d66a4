package com.whiskerguard.organization.service;

import com.whiskerguard.organization.service.dto.NewsReportDTO;
import java.util.Optional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

/**
 * Service Interface for managing {@link com.whiskerguard.organization.domain.NewsReport}.
 */
public interface NewsReportService {
    /**
     * Save a newsReport.
     *
     * @param newsReportDTO the entity to save.
     * @return the persisted entity.
     */
    NewsReportDTO save(NewsReportDTO newsReportDTO);

    /**
     * Updates a newsReport.
     *
     * @param newsReportDTO the entity to update.
     * @return the persisted entity.
     */
    NewsReportDTO update(NewsReportDTO newsReportDTO);

    /**
     * Partially updates a newsReport.
     *
     * @param newsReportDTO the entity to update partially.
     * @return the persisted entity.
     */
    Optional<NewsReportDTO> partialUpdate(NewsReportDTO newsReportDTO);

    /**
     * Get all the newsReports.
     *
     * @param pageable the pagination information.
     * @return the list of entities.
     */
    Page<NewsReportDTO> findAll(Pageable pageable);

    /**
     * Get all the newsReports with eager load of many-to-many relationships.
     *
     * @param pageable the pagination information.
     * @return the list of entities.
     */
    Page<NewsReportDTO> findAllWithEagerRelationships(Pageable pageable);

    /**
     * Get the "id" newsReport.
     *
     * @param id the id of the entity.
     * @return the entity.
     */
    Optional<NewsReportDTO> findOne(Long id);

    /**
     * Delete the "id" newsReport.
     *
     * @param id the id of the entity.
     */
    void delete(Long id);
}

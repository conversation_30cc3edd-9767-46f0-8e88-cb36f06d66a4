package com.whiskerguard.organization.service;

import com.whiskerguard.organization.service.dto.OrgStructureImportResultDTO;
import com.whiskerguard.organization.service.dto.OrgUnitDTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Optional;

/**
 * Service Interface for managing {@link com.whiskerguard.organization.domain.OrgUnit}.
 * 组织单元管理服务接口
 */
public interface OrgUnitService {
    /**
     * Save a orgUnit.
     * 保存组织单元信息
     *
     * @param orgUnitDTO the entity to save. 要保存的组织单元DTO对象
     * @return the persisted entity. 持久化后的组织单元DTO对象
     */
    OrgUnitDTO save(OrgUnitDTO orgUnitDTO);

    /**
     * Updates a orgUnit.
     * 更新组织单元信息
     *
     * @param orgUnitDTO the entity to update. 要更新的组织单元DTO对象
     * @return the persisted entity. 持久化后的组织单元DTO对象
     */
    OrgUnitDTO update(OrgUnitDTO orgUnitDTO);

    /**
     * Partially updates a orgUnit.
     * 部分更新组织单元信息
     *
     * @param orgUnitDTO the entity to update partially. 要部分更新的组织单元DTO对象
     * @return the persisted entity. 持久化后的组织单元DTO对象
     */
    Optional<OrgUnitDTO> partialUpdate(OrgUnitDTO orgUnitDTO);

    /**
     * Get all the orgUnits.
     * 获取所有组织单元信息
     *
     * @param pageable the pagination information. 分页信息
     * @return the list of entities. 组织单元DTO对象分页列表
     */
    Page<OrgUnitDTO> findAll(Pageable pageable);

    /**
     * Get all the orgUnits by parent ID.
     * 根据父级ID获取所有组织单元信息
     *
     * @param parentId the parent id to filter by. 父级ID筛选条件
     * @param pageable the pagination information. 分页信息
     * @return the list of entities. 组织单元DTO对象分页列表
     */
    Page<OrgUnitDTO> findAllByParentId(Long parentId, Pageable pageable);

    /**
     * Get all the orgUnits by parent ID and keyword.
     * 根据父级ID和关键词获取组织单元信息
     *
     * @param parentId the parent id to filter by. 父级ID筛选条件
     * @param keyword  the keyword to search in name or code. 关键词（搜索名称或编码）
     * @param pageable the pagination information. 分页信息
     * @return the list of entities. 组织单元DTO对象分页列表
     */
    Page<OrgUnitDTO> findAllByParentIdAndKeyword(Long parentId, String keyword, Pageable pageable);

    /**
     * Get the "id" orgUnit.
     * 根据ID获取组织单元信息
     *
     * @param id the id of the entity. 组织单元ID
     * @return the entity. 组织单元DTO对象
     */
    Optional<OrgUnitDTO> findOne(Long id);

    /**
     * Delete the "id" orgUnit.
     * 根据ID删除组织单元
     *
     * @param id the id of the entity. 组织单元ID
     */
    void delete(Long id);

    /**
     * 获取组织单元树形结构
     * Get organization unit tree structure
     *
     * @return 组织单元树 organization unit tree
     */
    List<OrgUnitDTO> getOrgUnitTree();

    /**
     * 变更组织单元状态
     * Change organization unit status
     *
     * @param id     组织单元ID organization unit ID
     * @param status 目标状态 target status
     * @return 更新后的组织单元 updated organization unit
     */
    OrgUnitDTO changeStatus(Long id, Integer status);

    /**
     * 更新组织单元排序
     * Update organization unit sort order
     *
     * @param ids 组织单元ID列表，按顺序排列 list of organization unit IDs, in order
     */
    void updateSortOrder(List<Long> ids);

    /**
     * Import organization structure from Excel file.
     * 从Excel文件导入组织架构
     *
     * @param file the Excel file to import. 要导入的Excel文件
     * @return the import result. 导入结果
     */
    OrgStructureImportResultDTO importFromExcel(MultipartFile file);

    /**
     * 根据组织单元名称模糊查询本租户下的组织单元列表
     * Search organization units by name (fuzzy search) within current tenant
     *
     * @param name 组织单元名称关键词 organization unit name keyword
     * @return 组织单元列表 organization unit list
     */
    List<OrgUnitDTO> findByNameContainingIgnoreCase(String name);

    /**
     * 判断用户所在的部门是否已经是顶级组织单元
     *
     * @param employeeId 员工ID
     * @return 如果是顶级组织单元则返回true，否则返回false
     */
    Boolean isTopOrgUnit(Long employeeId);

    /**
     * 根据组织单元ID列表获取组织单元数据
     *
     * @param ids 组织单元ID列表
     * @return 组织单元列表
     */
    List<OrgUnitDTO> findByIds(List<Long> ids);
}

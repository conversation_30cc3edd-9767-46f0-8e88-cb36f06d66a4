package com.whiskerguard.organization.service;

import com.whiskerguard.organization.service.dto.TenantProfileDTO;
import java.util.Optional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

/**
 * Service Interface for managing {@link com.whiskerguard.organization.domain.TenantProfile}.
 * 租户配置管理服务接口
 */
public interface TenantProfileService {
    /**
     * Save a tenant profile.
     * 保存租户配置信息
     *
     * @param tenantProfileDTO the entity to save. 要保存的租户配置DTO对象
     * @return the persisted entity. 持久化后的租户配置DTO对象
     */
    TenantProfileDTO save(TenantProfileDTO tenantProfileDTO);

    /**
     * Updates a tenant profile.
     * 更新租户配置信息
     *
     * @param tenantProfileDTO the entity to update. 要更新的租户配置DTO对象
     * @return the persisted entity. 持久化后的租户配置DTO对象
     */
    TenantProfileDTO update(TenantProfileDTO tenantProfileDTO);

    /**
     * Partially updates a tenant profile.
     * 部分更新租户配置信息
     *
     * @param tenantProfileDTO the entity to update partially. 要部分更新的租户配置DTO对象
     * @return the persisted entity. 持久化后的租户配置DTO对象
     */
    Optional<TenantProfileDTO> partialUpdate(TenantProfileDTO tenantProfileDTO);

    /**
     * Get all the tenant profiles.
     * 获取所有租户配置信息
     *
     * @param pageable the pagination information. 分页信息
     * @return the list of entities. 租户配置DTO对象分页列表
     */
    Page<TenantProfileDTO> findAll(Pageable pageable);

    /**
     * Get the "id" tenant profile.
     * 根据ID获取租户配置信息
     *
     * @param id the id of the entity. 租户配置ID
     * @return the entity. 租户配置DTO对象
     */
    Optional<TenantProfileDTO> findOne(Long id);

    /**
     * Delete the "id" tenant profile.
     * 根据ID删除租户配置
     *
     * @param id the id of the entity. 租户配置ID
     */
    void delete(Long id);

    /**
     * Check if a tenant profile exists by registration number.
     * 检查指定注册号的租户配置是否存在
     *
     * @param registrationNumber the registration number to check 要检查的注册号
     * @return true if tenant profile exists, false otherwise 如果租户配置存在则返回true，否则返回false
     */
    boolean existsByRegistrationNumber(String registrationNumber);

    /**
     * Validate tenant profile registration number.
     * Throws BadRequestAlertException if registration number already exists.
     * 验证租户配置注册号，如果注册号已存在则抛出BadRequestAlertException异常
     *
     * @param registrationNumber the registration number to validate 要验证的注册号
     * @throws com.whiskerguard.organization.web.rest.errors.BadRequestAlertException if validation fails 如果验证失败则抛出异常
     */
    void validateRegistrationNumber(String registrationNumber);

    /**
     * Find a tenant profile by tenant ID.
     *
     * @param tenantId the tenant ID to search for
     * @return an optional tenant profile
     */
    Optional<TenantProfileDTO> findByTenantId(Long tenantId);
}

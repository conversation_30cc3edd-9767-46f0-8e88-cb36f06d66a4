package com.whiskerguard.organization.service;

import com.whiskerguard.organization.service.dto.TenantInitializeDTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.Optional;

/**
 * 描述：租户基础信息初始化表服务接口
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/6/20
 */
public interface TenantInitializeService {

    /**
     * 方法名称：save
     * 描述：保存租户基础信息初始化记录
     *
     * @param tenantInitializeDTO 要保存的租户基础信息初始化记录DTO
     * @return 保存后的租户基础信息初始化记录DTO
     * @since 1.0
     */
    TenantInitializeDTO save(TenantInitializeDTO tenantInitializeDTO);

    /**
     * 方法名称：partialUpdate
     * 描述：部分更新租户基础信息初始化记录
     *
     * @param tenantInitializeDTO 要保存的租户基础信息初始化记录DTO
     * @return 保存后的租户基础信息初始化记录DTO
     * @since 1.0
     */
    Optional<TenantInitializeDTO> partialUpdate(TenantInitializeDTO tenantInitializeDTO);

    /**
     * 方法名称：findAll
     * 描述：查询所有租户基础信息初始化记录
     *
     * @param pageable 分页参数
     * @return 租户基础信息初始化记录DTO分页结果
     * @since 1.0
     */
    Page<TenantInitializeDTO> findAll(Pageable pageable);

    /**
     * 方法名称：delete
     * 描述：删除租户基础信息初始化记录
     *
     * @param id 租户基础信息初始化记录ID
     * @since 1.0
     */
    void delete(Long id);
}

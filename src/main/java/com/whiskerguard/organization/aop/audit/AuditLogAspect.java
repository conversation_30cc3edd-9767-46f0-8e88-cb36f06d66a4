package com.whiskerguard.organization.aop.audit;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.whiskerguard.organization.domain.enumeration.AuditOperation;
import com.whiskerguard.organization.service.AuditLogService;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 审计日志切面
 * 用于自动记录实体变更
 */
//@Aspect
//@Component
public class AuditLogAspect {

    private static final Logger LOG = LoggerFactory.getLogger(AuditLogAspect.class);

    private final AuditLogService auditLogService;
    private final ObjectMapper objectMapper;

    public AuditLogAspect(AuditLogService auditLogService, ObjectMapper objectMapper) {
        this.auditLogService = auditLogService;
        this.objectMapper = objectMapper;
    }

    /**
     * 环绕通知，记录实体变更
     */
    @Around("execution(* com.whiskerguard.organization.service.impl.*ServiceImpl.save(..))")
    public Object logCreate(ProceedingJoinPoint joinPoint) {
        return logOperation(joinPoint, AuditOperation.CREATE);
    }

    @Around("execution(* com.whiskerguard.organization.service.impl.*ServiceImpl.update(..))")
    public Object logUpdate(ProceedingJoinPoint joinPoint) {
        return logOperation(joinPoint, AuditOperation.UPDATE);
    }

    @Around("execution(* com.whiskerguard.organization.service.impl.*ServiceImpl.delete(..))")
    public Object logDelete(ProceedingJoinPoint joinPoint) {
        return logOperation(joinPoint, AuditOperation.DELETE);
    }

    private Object logOperation(ProceedingJoinPoint joinPoint, AuditOperation operation) {
        Object result;
        try {
            result = joinPoint.proceed();
        } catch (Throwable e) {
            LOG.error("Failed to proceed", e);
            return null;
        }

        try {

            Object arg = joinPoint.getArgs()[0];
            String entityName = arg.getClass().getSimpleName();
            Long entityId = getEntityId(arg);

            if (entityId != null) {
                String diff = objectMapper.writeValueAsString(arg);
                auditLogService.log(entityName, entityId, operation, diff);
            }
            return result;
        } catch (Exception e) {
            LOG.error("Failed to log audit", e);
        }
        return null;
    }

    private Long getEntityId(Object entity) {
        try {
            if (entity instanceof Number) {
                return ((Number) entity).longValue();
            } else {
                return (Long) entity.getClass().getMethod("getId").invoke(entity);

            }
        } catch (Exception e) {
            LOG.error("Failed to get entity id", e);
            return null;
        }
    }
}

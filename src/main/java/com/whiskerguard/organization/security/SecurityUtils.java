package com.whiskerguard.organization.security;

import java.util.Arrays;
import java.util.Optional;
import java.util.stream.Stream;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.oauth2.jose.jws.MacAlgorithm;
import org.springframework.security.oauth2.jwt.Jwt;

/**
 * Spring Security 工具类。
 * 提供获取当前用户信息、判断权限、获取JWT等常用安全相关方法，便于在业务代码中统一调用。
 */
public final class SecurityUtils {

    // JWT签名算法
    public static final MacAlgorithm JWT_ALGORITHM = MacAlgorithm.HS512;

    // 权限字段key
    public static final String AUTHORITIES_KEY = "auth";

    private SecurityUtils() {}

    /**
     * 获取当前用户的登录名。
     *
     * @return 当前用户的登录名（Optional）
     */
    public static Optional<String> getCurrentUserLogin() {
        SecurityContext securityContext = SecurityContextHolder.getContext();
        return Optional.ofNullable(extractPrincipal(securityContext.getAuthentication()));
    }

    /**
     * 从Authentication中提取用户名。
     * @param authentication 认证对象
     * @return 用户名
     */
    private static String extractPrincipal(Authentication authentication) {
        if (authentication == null) {
            return null;
        } else if (authentication.getPrincipal() instanceof UserDetails springSecurityUser) {
            return springSecurityUser.getUsername();
        } else if (authentication.getPrincipal() instanceof Jwt jwt) {
            return jwt.getSubject();
        } else if (authentication.getPrincipal() instanceof String s) {
            return s;
        }
        return null;
    }

    /**
     * 获取当前用户的JWT令牌。
     *
     * @return 当前用户的JWT（Optional）
     */
    public static Optional<String> getCurrentUserJWT() {
        SecurityContext securityContext = SecurityContextHolder.getContext();
        return Optional.ofNullable(securityContext.getAuthentication())
            .filter(authentication -> authentication.getCredentials() instanceof String)
            .map(authentication -> (String) authentication.getCredentials());
    }

    /**
     * 判断当前用户是否已认证。
     *
     * @return 已认证返回true，否则返回false
     */
    public static boolean isAuthenticated() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        return authentication != null && getAuthorities(authentication).noneMatch(AuthoritiesConstants.ANONYMOUS::equals);
    }

    /**
     * 判断当前用户是否拥有任意指定权限。
     *
     * @param authorities 权限列表
     * @return 拥有任意权限返回true，否则返回false
     */
    public static boolean hasCurrentUserAnyOfAuthorities(String... authorities) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        return (
            authentication != null && getAuthorities(authentication).anyMatch(authority -> Arrays.asList(authorities).contains(authority))
        );
    }

    /**
     * 判断当前用户是否没有指定权限。
     *
     * @param authorities 权限列表
     * @return 没有任何指定权限返回true，否则返回false
     */
    public static boolean hasCurrentUserNoneOfAuthorities(String... authorities) {
        return !hasCurrentUserAnyOfAuthorities(authorities);
    }

    /**
     * 判断当前用户是否拥有某个具体权限。
     *
     * @param authority 权限
     * @return 拥有该权限返回true，否则返回false
     */
    public static boolean hasCurrentUserThisAuthority(String authority) {
        return hasCurrentUserAnyOfAuthorities(authority);
    }

    /**
     * 获取认证对象的所有权限。
     * @param authentication 认证对象
     * @return 权限字符串流
     */
    private static Stream<String> getAuthorities(Authentication authentication) {
        return authentication.getAuthorities().stream().map(GrantedAuthority::getAuthority);
    }
}

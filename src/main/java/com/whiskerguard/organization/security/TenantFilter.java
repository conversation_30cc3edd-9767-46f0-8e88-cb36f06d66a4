package com.whiskerguard.organization.security;

import jakarta.servlet.Filter;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.ServletRequest;
import jakarta.servlet.ServletResponse;
import jakarta.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.Optional;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * Filter that sets the tenant context based on the JWT token or request header.
 */
@Component
@Order(Ordered.HIGHEST_PRECEDENCE)
public class TenantFilter implements Filter {

    /**
     * 多租户过滤器的核心方法。
     * 该方法会优先尝试从JWT token中获取租户ID，如果没有则从请求头X-Tenant-ID获取，
     * 并将租户ID设置到当前线程上下文，保证后续业务代码可以正确获取租户信息。
     * 请求处理完毕后会清理租户上下文，防止线程复用导致租户信息泄漏。
     *
     * @param request  Servlet请求对象
     * @param response Servlet响应对象
     * @param chain    过滤器链
     * @throws IOException      IO异常
     * @throws ServletException Servlet异常
     */
    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
        try {
            // 首先尝试从 JWT token 获取租户ID
            Optional<Long> tenantId = TenantContext.getCurrentTenantId();

            // 如果 JWT 中没有，则从请求头获取
            if (tenantId.isEmpty()) {
                String tenantIdHeader = ((HttpServletRequest) request).getHeader("X-TENANT-ID");
                if (tenantIdHeader != null) {
                    TenantContext.setCurrentTenantId(Long.parseLong(tenantIdHeader));
                }
            }

            chain.doFilter(request, response);
        } finally {
            // 清理租户上下文，防止线程复用导致租户信息泄漏
            TenantContext.clear();
        }
    }
}

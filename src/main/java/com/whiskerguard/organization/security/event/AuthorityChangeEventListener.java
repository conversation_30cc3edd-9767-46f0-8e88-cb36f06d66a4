package com.whiskerguard.organization.security.event;

import com.whiskerguard.organization.domain.EmployeeRole;
import com.whiskerguard.organization.domain.RolePermission;
import com.whiskerguard.organization.repository.EmployeeRoleRepository;
import com.whiskerguard.organization.repository.RolePermissionRepository;
import com.whiskerguard.organization.service.AuthorityCacheService;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;

/**
 * 权限变更事件监听器
 */
@Component
public class AuthorityChangeEventListener {

    private static final Logger LOG = LoggerFactory.getLogger(AuthorityChangeEventListener.class);

    private final EmployeeRoleRepository employeeRoleRepository;
    private final RolePermissionRepository rolePermissionRepository;
    private final AuthorityCacheService authorityCacheService;

    public AuthorityChangeEventListener(
        EmployeeRoleRepository employeeRoleRepository,
        RolePermissionRepository rolePermissionRepository,
        AuthorityCacheService authorityCacheService
    ) {
        this.employeeRoleRepository = employeeRoleRepository;
        this.rolePermissionRepository = rolePermissionRepository;
        this.authorityCacheService = authorityCacheService;
    }

    /**
     * 处理角色权限变更事件
     */
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    public void handleRolePermissionChange(RolePermissionChangeEvent event) {
        LOG.info("Handling role permission change event for role: {}", event.getRoleId());

        // 获取所有拥有该角色的员工
        List<EmployeeRole> employeeRoles = employeeRoleRepository.findByRoleId(event.getRoleId());

        // 清除这些员工的权限缓存
        for (EmployeeRole employeeRole : employeeRoles) {
            if (employeeRole.getEmployee() != null) {
                authorityCacheService.evictAuthorities(employeeRole.getEmployee().getUsername());
                LOG.debug("Evicted authorities cache for user: {}", employeeRole.getEmployee().getUsername());
            }
        }
    }

    /**
     * 处理员工角色变更事件
     */
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    public void handleEmployeeRoleChange(EmployeeRoleChangeEvent event) {
        LOG.info("Handling employee role change event for user: {}", event.getUsername());

        // 清除该员工的权限缓存
        authorityCacheService.evictAuthorities(event.getUsername());
        LOG.debug("Evicted authorities cache for user: {}", event.getUsername());
    }

    /**
     * 处理权限变更事件
     */
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    public void handlePermissionChange(PermissionChangeEvent event) {
        LOG.info("Handling permission change event for permission: {}", event.getPermissionId());

        // 获取所有拥有该权限的角色
        List<RolePermission> rolePermissions = rolePermissionRepository.findByRoleId(event.getRoleId());

        // 清除所有相关员工的权限缓存
        for (RolePermission rolePermission : rolePermissions) {
            if (rolePermission.getRole() != null) {
                List<EmployeeRole> employeeRoles = employeeRoleRepository.findByRoleId(rolePermission.getRole().getId());
                for (EmployeeRole employeeRole : employeeRoles) {
                    if (employeeRole.getEmployee() != null) {
                        authorityCacheService.evictAuthorities(employeeRole.getEmployee().getUsername());
                        LOG.debug("Evicted authorities cache for user: {}", employeeRole.getEmployee().getUsername());
                    }
                }
            }
        }
    }
}

package com.whiskerguard.organization.security;

import com.whiskerguard.organization.service.TokenStoreService;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.ServletRequest;
import jakarta.servlet.ServletResponse;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.filter.GenericFilterBean;

/**
 * Filter for validating JWT tokens.
 * This filter checks if the token is valid and not invalidated before allowing the request to proceed.
 */
public class TokenValidationFilter extends GenericFilterBean {

    private static final Logger log = LoggerFactory.getLogger(TokenValidationFilter.class);
    private final TokenStoreService tokenStoreService;

    public TokenValidationFilter(TokenStoreService tokenStoreService) {
        this.tokenStoreService = tokenStoreService;
    }

    /**
     * 过滤器核心方法。
     * 校验JWT令牌是否有效且未被作废。
     * 如果令牌无效，则返回401错误并清空安全上下文；否则继续处理请求。
     *
     * @param request  Servlet请求对象
     * @param response Servlet响应对象
     * @param chain    过滤器链
     * @throws IOException      IO异常
     * @throws ServletException Servlet异常
     */
    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
        HttpServletRequest httpRequest = (HttpServletRequest) request;
        String token = extractToken(httpRequest);

        if (token != null) {
            log.debug("Validating token for request: {}", httpRequest.getRequestURI());
            if (!tokenStoreService.isValidToken(token)) {
                log.warn("Invalid token detected for request: {}", httpRequest.getRequestURI());
                SecurityContextHolder.clearContext();
                ((HttpServletResponse) response).setStatus(HttpServletResponse.SC_UNAUTHORIZED);
                return;
            }
            log.debug("Token validation successful for request: {}", httpRequest.getRequestURI());
        }

        chain.doFilter(request, response);
    }

    /**
     * 从请求头中提取JWT令牌。
     * 只支持以"Bearer "开头的Authorization头。
     *
     * @param request HTTP请求对象
     * @return Bearer类型的JWT令牌字符串，如果不存在则返回null
     */
    private String extractToken(HttpServletRequest request) {
        String bearerToken = request.getHeader("Authorization");
        if (bearerToken != null && bearerToken.startsWith("Bearer ")) {
            return bearerToken.substring(7);
        }
        return null;
    }
}

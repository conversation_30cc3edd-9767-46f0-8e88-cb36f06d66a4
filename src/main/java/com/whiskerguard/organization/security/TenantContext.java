package com.whiskerguard.organization.security;

import java.util.Optional;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.jwt.Jwt;

/**
 * Utility class for tenant context management.
 */
public final class TenantContext {

    private static final String TENANT_ID_CLAIM = "tenant_id";
    private static final ThreadLocal<Long> tenantContext = new ThreadLocal<>();

    private TenantContext() {}

    /**
     * Get the current tenant ID from JWT token or ThreadLocal.
     *
     * @return the current tenant ID.
     */
    public static Optional<Long> getCurrentTenantId() {
        // 首先检查 ThreadLocal
        Long tenantId = tenantContext.get();
        if (tenantId != null) {
            return Optional.of(tenantId);
        }

        // 然后从 JWT token 中获取
        return Optional.ofNullable(SecurityContextHolder.getContext().getAuthentication())
            .filter(authentication -> authentication.getPrincipal() instanceof Jwt)
            .map(authentication -> (Jwt) authentication.getPrincipal())
            .map(jwt -> {
                Object claim = jwt.getClaim(TENANT_ID_CLAIM);
                return claim instanceof Number ? ((Number) claim).longValue() : null;
            });
    }

    /**
     * 设置当前线程的租户ID。
     *
     * @param tenantId 要设置的租户ID
     */
    public static void setCurrentTenantId(Long tenantId) {
        tenantContext.set(tenantId);
    }

    /**
     * 清除当前线程的租户上下文，防止线程复用导致租户信息泄漏。
     */
    public static void clear() {
        tenantContext.remove();
    }
}

package com.whiskerguard.organization.security;

/**
 * 系统权限常量定义
 *
 * 用于定义系统中所有角色的权限标识，采用 Spring Security 的 ROLE_ 前缀规范。
 * 这些常量主要用于：
 * 1. 权限控制注解（如 @PreAuthorize）
 * 2. 安全配置
 * 3. 权限检查
 * 4. 前端权限控制
 */
public final class AuthoritiesConstants {

    /**
     * 系统管理员权限
     * 拥有系统的最高权限，可以管理所有租户、用户、角色和权限
     */
    public static final String ADMIN = "ROLE_ADMIN";

    /**
     * 普通用户权限
     * 基础用户权限，可以访问基本的系统功能
     */
    public static final String USER = "ROLE_USER";

    /**
     * 匿名用户权限
     * 未登录用户的默认权限，仅能访问公开资源
     */
    public static final String ANONYMOUS = "ROLE_ANONYMOUS";

    /**
     * 租户管理员权限
     * 租户级别的管理员，可以管理本租户内的所有资源
     * 包括：用户管理、角色管理、权限分配等
     */
    public static final String TENANT_ADMIN = "ROLE_TENANT_ADMIN";

    /**
     * 租户用户权限
     * 租户内的普通用户，可以访问租户内的基本功能
     */
    public static final String TENANT_USER = "ROLE_TENANT_USER";

    private AuthoritiesConstants() {}
}

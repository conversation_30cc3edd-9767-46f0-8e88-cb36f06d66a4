package com.whiskerguard.organization.security;

import com.whiskerguard.organization.config.Constants;
import java.util.Optional;
import org.springframework.data.domain.AuditorAware;
import org.springframework.stereotype.Component;

/**
 * 基于Spring Security的审计员获取实现类。
 * 用于自动为数据实体的创建人、修改人等审计字段赋值。
 */
@Component
public class SpringSecurityAuditorAware implements AuditorAware<String> {

    /**
     * 获取当前操作用户的登录名，若未登录则返回系统常量。
     * @return 当前用户登录名或系统默认值
     */
    @Override
    public Optional<String> getCurrentAuditor() {
        return Optional.of(SecurityUtils.getCurrentUserLogin().orElse(Constants.SYSTEM));
    }
}

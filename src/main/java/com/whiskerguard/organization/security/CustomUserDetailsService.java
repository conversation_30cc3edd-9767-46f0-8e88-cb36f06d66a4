package com.whiskerguard.organization.security;

import com.whiskerguard.common.constant.FileTypeConstants;
import com.whiskerguard.common.constant.NumberConstants;
import com.whiskerguard.organization.domain.Employee;
import com.whiskerguard.organization.domain.EmployeeRole;
import com.whiskerguard.organization.domain.Role;
import com.whiskerguard.organization.domain.RolePermission;
import com.whiskerguard.organization.repository.EmployeeRepository;
import com.whiskerguard.organization.repository.EmployeeRoleRepository;
import com.whiskerguard.organization.repository.RolePermissionRepository;
import com.whiskerguard.organization.service.AuthorityCacheService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.User;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 自定义用户详情服务
 */
@Service
public class CustomUserDetailsService implements UserDetailsService {

    private static final Logger LOG = LoggerFactory.getLogger(CustomUserDetailsService.class);

    private final EmployeeRepository employeeRepository;
    private final EmployeeRoleRepository employeeRoleRepository;
    private final RolePermissionRepository rolePermissionRepository;
    private final AuthorityCacheService authorityCacheService;

    public CustomUserDetailsService(
        EmployeeRepository employeeRepository,
        EmployeeRoleRepository employeeRoleRepository,
        RolePermissionRepository rolePermissionRepository,
        AuthorityCacheService authorityCacheService
    ) {
        this.employeeRepository = employeeRepository;
        this.employeeRoleRepository = employeeRoleRepository;
        this.rolePermissionRepository = rolePermissionRepository;
        this.authorityCacheService = authorityCacheService;
    }

    @Override
    @Transactional(readOnly = true)
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        LOG.debug("Loading user by username: {}", username);

        // 从缓存中获取权限
        Set<GrantedAuthority> authorities = authorityCacheService.getAuthorities(username);

        // 如果缓存中没有权限，则从数据库中加载
        if (authorities == null) {
            authorities = loadAuthoritiesFromDatabase(username);
            // 将权限缓存起来
            authorityCacheService.cacheAuthorities(username, authorities);
        }

        // 获取用户信息
        Employee employee = employeeRepository
            .findOneByUsernameAndIsDeletedFalse(username)
            .orElseThrow(() -> new UsernameNotFoundException("User not found with username: " + username));

        return new User(employee.getUsername(), employee.getPassword(), authorities);
    }

    private Set<GrantedAuthority> loadAuthoritiesFromDatabase(String username) {
        Set<GrantedAuthority> authorities = new HashSet<>();

        // 获取员工信息
        Employee employee = employeeRepository
            .findOneByUsernameAndIsDeletedFalse(username)
            .orElseThrow(() -> new UsernameNotFoundException("User not found with username: " + username));

        // 获取员工的所有角色
        List<EmployeeRole> employeeRoles = employeeRoleRepository.findByEmployee(employee);

        // 收集所有权限
        for (EmployeeRole employeeRole : employeeRoles) {
            Role role = employeeRole.getRole();
            if (role != null && !role.getIsDeleted() && role.getStatus() == NumberConstants.ONE) {
                // 添加角色权限
                authorities.add(new SimpleGrantedAuthority(role.getCode()));

                // 获取角色的所有权限
                List<RolePermission> rolePermissions = rolePermissionRepository.findByRoleId(role.getId());
                for (RolePermission rolePermission : rolePermissions) {
                    if (rolePermission.getPermission() != null) {
                        // 添加功能权限
                        String method = rolePermission.getPermission().getMethod();
                        String backendUrl = rolePermission.getPermission().getBackendUrl();
                        if (StringUtils.isNotBlank(backendUrl)) {
                            String path = StringUtils.isBlank(method) ? "POST" + FileTypeConstants.COLON + backendUrl :
                                method + FileTypeConstants.COLON + backendUrl;
                            authorities.add(new SimpleGrantedAuthority(path));
                        }
                    }
                }
            }
        }

        return authorities;
    }
}

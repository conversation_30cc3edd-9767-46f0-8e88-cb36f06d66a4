{"java.compile.nullAnalysis.mode": "automatic", "java.configuration.updateBuildConfiguration": "automatic", "sqltools.connections": [{"mysqlOptions": {"authProtocol": "default", "enableSsl": "Disabled"}, "previewLimit": 50, "server": "**************", "port": 3306, "driver": "MySQL", "name": "MBB-Tecent-MySQL", "database": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "username": "root"}, {"mysqlOptions": {"authProtocol": "default", "enableSsl": "Disabled"}, "previewLimit": 50, "server": "localhost", "port": 3306, "driver": "MySQL", "name": "Mac-Local", "database": "mysql", "username": "root"}], "java.debug.settings.onBuildFailureProceed": true}
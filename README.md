# Whiskerguard Organization Service

## 项目简介

Whiskerguard Organization Service 是一个基于 JHipster 8.10.0 构建的微服务应用，主要负责组织架构管理功能。该服务提供了组织单元、员工、职位、角色、权限等核心功能的管理。

## 技术栈

- **后端框架**: Spring Boot 3.4.4
- **构建工具**: Maven 3.2.5
- **数据库**: MySQL
- **缓存**: Redis
- **服务发现**: Consul
- **认证方式**: JWT
- **API 文档**: OpenAPI/Swagger
- **代码质量工具**:
  - Checkstyle
  - SonarQube
  - JaCoCo (代码覆盖率)
  - ArchUnit (架构测试)

## 系统要求

- Java 17
- Maven 3.2.5+
- MySQL 8.0+
- Redis
- Consul

## 主要功能

- 组织单元管理
- 员工管理
- 职位管理
- 角色权限管理
- 租户管理
- 审计日志

## 开发环境设置

1. 克隆项目

```bash
git clone [repository-url]
```

2. 安装依赖

```bash
./mvnw
```

3. 配置数据库

- 创建 MySQL 数据库
- 在 `src/main/resources/config/application-dev.yml` 中配置数据库连接信息

4. 启动服务

```bash
./mvnw spring-boot:run
```

## 项目结构

```
src/
├── main/
│   ├── java/
│   │   └── com/whiskerguard/organization/
│   │       ├── config/          # 配置类
│   │       ├── domain/          # 实体类
│   │       ├── repository/      # 数据访问层
│   │       ├── service/         # 业务逻辑层
│   │       ├── web/             # 控制器层
│   │       └── WhiskerguardOrgServiceApp.java
│   └── resources/
│       ├── config/              # 配置文件
│       ├── i18n/                # 国际化资源
│       └── liquibase/           # 数据库变更脚本
└── test/                        # 测试代码
```

## 主要实体

- OrgUnit (组织单元)
- Employee (员工)
- Position (职位)
- EmployeeOrg (员工组织关系)
- Role (角色)
- Permission (权限)
- RolePermission (角色权限关系)
- EmployeeRole (员工角色关系)
- AuditLog (审计日志)
- Tenant (租户)
- TenantProfile (租户配置)
- TenantAttachment (租户附件)

## 开发规范

- 遵循 JHipster 开发规范
- 使用 Checkstyle 进行代码风格检查
- 使用 SonarQube 进行代码质量分析
- 编写单元测试和集成测试
- 使用 Liquibase 管理数据库变更

## 部署

项目支持 Docker 部署，使用 Jib Maven 插件构建 Docker 镜像：

```bash
./mvnw package -Pprod jib:build
```

## 监控和运维

- 使用 Spring Boot Actuator 进行应用监控
- 集成 Prometheus 和 Grafana 进行性能监控
- 使用 ELK 进行日志收集和分析

## 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 许可证

[MBB License]

## 联系方式

[<EMAIL> HaishuiYan]

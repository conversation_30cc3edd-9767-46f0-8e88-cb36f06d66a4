{"annotations": {"changelogDate": "20250427130054"}, "applications": "*", "clientRootFolder": "whiskerguardOrgService", "databaseType": "sql", "documentation": "员工—组织关联（EmployeeOrg）实体", "dto": "mapstruct", "fields": [{"documentation": "主键 ID", "fieldName": "id", "fieldType": "<PERSON>"}, {"documentation": "租户 ID", "fieldName": "tenantId", "fieldType": "<PERSON>", "fieldValidateRules": ["required"]}, {"documentation": "任职开始日期", "fieldName": "startDate", "fieldType": "LocalDate"}, {"documentation": "任职结束日期", "fieldName": "endDate", "fieldType": "LocalDate"}, {"documentation": "是否主部门", "fieldName": "isPrimary", "fieldType": "Boolean", "fieldValidateRules": ["required"]}, {"documentation": "乐观锁版本", "fieldName": "version", "fieldType": "Integer", "fieldValidateRules": ["required"]}, {"documentation": "创建者", "fieldName": "created<PERSON>y", "fieldType": "String"}, {"documentation": "创建时间", "fieldName": "createdAt", "fieldType": "Instant", "fieldValidateRules": ["required"]}, {"documentation": "更新者", "fieldName": "updatedBy", "fieldType": "String"}, {"documentation": "更新时间", "fieldName": "updatedAt", "fieldType": "Instant", "fieldValidateRules": ["required"]}, {"documentation": "软删除标志", "fieldName": "isDeleted", "fieldType": "Boolean", "fieldValidateRules": ["required"]}], "microserviceName": "whiskerguardOrgService", "name": "EmployeeOrg", "pagination": "pagination", "relationships": [{"otherEntityName": "employee", "relationshipName": "employee", "relationshipSide": "left", "relationshipType": "many-to-one"}, {"otherEntityName": "orgUnit", "relationshipName": "orgUnit", "relationshipSide": "left", "relationshipType": "many-to-one"}, {"otherEntityName": "position", "relationshipName": "position", "relationshipSide": "left", "relationshipType": "many-to-one"}], "searchEngine": "no", "service": "serviceImpl"}
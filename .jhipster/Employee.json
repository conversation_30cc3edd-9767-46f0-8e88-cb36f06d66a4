{"annotations": {"changelogDate": "20250427130052"}, "applications": "*", "clientRootFolder": "whiskerguardOrgService", "databaseType": "sql", "documentation": "员工（Employee）实体", "dto": "mapstruct", "fields": [{"documentation": "主键 ID", "fieldName": "id", "fieldType": "<PERSON>"}, {"documentation": "租户 ID", "fieldName": "tenantId", "fieldType": "<PERSON>", "fieldValidateRules": ["required"]}, {"documentation": "登录用户名", "fieldName": "username", "fieldType": "String", "fieldValidateRules": ["required", "maxlength"], "fieldValidateRulesMaxlength": "64"}, {"documentation": "登录密码（加密存储）", "fieldName": "password", "fieldType": "String", "fieldValidateRules": ["required", "maxlength"], "fieldValidateRulesMaxlength": "128"}, {"documentation": "密码盐值", "fieldName": "salt", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "32"}, {"documentation": "真实姓名", "fieldName": "realName", "fieldType": "String", "fieldValidateRules": ["required", "maxlength"], "fieldValidateRulesMaxlength": "128"}, {"documentation": "头像", "fieldName": "avatar", "fieldType": "String", "fieldValidateRules": ["required", "maxlength"], "fieldValidateRulesMaxlength": "255"}, {"documentation": "邮箱地址", "fieldName": "email", "fieldType": "String", "fieldValidateRules": ["required", "maxlength"], "fieldValidateRulesMaxlength": "128"}, {"documentation": "手机号", "fieldName": "phone", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "32"}, {"documentation": "性别", "fieldName": "gender", "fieldType": "Employ<PERSON><PERSON><PERSON>", "fieldTypeDocumentation": "员工性别枚举", "fieldValues": "UNKNOWN,MALE,FEMALE"}, {"documentation": "生日", "fieldName": "birthDate", "fieldType": "LocalDate"}, {"documentation": "身份证号", "fieldName": "idCard", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "18"}, {"documentation": "员工编号（工号）", "fieldName": "employeeNo", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "64"}, {"documentation": "员工状态", "fieldName": "status", "fieldType": "EmployeeStatus", "fieldTypeDocumentation": "员工状态枚举", "fieldValidateRules": ["required"], "fieldValues": "ACTIVE,INACTIVE,FROZEN"}, {"documentation": "入职日期", "fieldName": "hireDate", "fieldType": "LocalDate"}, {"documentation": "离职日期", "fieldName": "leaveDate", "fieldType": "LocalDate"}, {"documentation": "扩展元数据", "fieldName": "metadata", "fieldType": "String"}, {"documentation": "乐观锁版本", "fieldName": "version", "fieldType": "Integer", "fieldValidateRules": ["required"]}, {"documentation": "创建者", "fieldName": "created<PERSON>y", "fieldType": "String"}, {"documentation": "创建时间", "fieldName": "createdAt", "fieldType": "Instant", "fieldValidateRules": ["required"]}, {"documentation": "更新者", "fieldName": "updatedBy", "fieldType": "String"}, {"documentation": "更新时间", "fieldName": "updatedAt", "fieldType": "Instant", "fieldValidateRules": ["required"]}, {"documentation": "软删除标志", "fieldName": "isDeleted", "fieldType": "Boolean", "fieldValidateRules": ["required"]}, {"documentation": "最后登录时间", "fieldName": "lastLoginTime", "fieldType": "Instant"}, {"documentation": "最后登录IP", "fieldName": "lastLoginIp", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "64"}, {"documentation": "登录失败次数", "fieldName": "loginFailureCount", "fieldType": "Integer"}, {"documentation": "账号锁定时间", "fieldName": "accountLockedTime", "fieldType": "Instant"}, {"documentation": "密码修改时间", "fieldName": "passwordChangedTime", "fieldType": "Instant"}, {"documentation": "密码过期时间", "fieldName": "passwordExpiredTime", "fieldType": "Instant"}, {"documentation": "是否首次登录", "fieldName": "is<PERSON>irstL<PERSON>in", "fieldType": "Boolean", "fieldValidateRules": ["required"]}, {"documentation": "是否强制修改密码", "fieldName": "forceChangePassword", "fieldType": "Boolean", "fieldValidateRules": ["required"]}, {"documentation": "微信OpenID", "fieldName": "wechatOpenId", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "64"}, {"documentation": "微信UnionID", "fieldName": "wechatUnionId", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "64"}], "microserviceName": "whiskerguardOrgService", "name": "Employee", "pagination": "pagination", "relationships": [], "searchEngine": "no", "service": "serviceImpl"}
{"annotations": {"changelogDate": "20250427130056"}, "applications": "*", "clientRootFolder": "whiskerguardOrgService", "databaseType": "sql", "documentation": "权限（Permission）实体", "dto": "mapstruct", "fields": [{"documentation": "主键 ID", "fieldName": "id", "fieldType": "<PERSON>"}, {"documentation": "租户 ID", "fieldName": "tenantId", "fieldType": "<PERSON>", "fieldValidateRules": ["required"]}, {"documentation": "服务名称", "fieldName": "serviceName", "fieldType": "String", "fieldValidateRules": ["required", "maxlength"], "fieldValidateRulesMaxlength": "64"}, {"documentation": "权限编码", "fieldName": "code", "fieldType": "String", "fieldValidateRules": ["required", "maxlength"], "fieldValidateRulesMaxlength": "64"}, {"documentation": "权限名称", "fieldName": "name", "fieldType": "String", "fieldValidateRules": ["required", "maxlength"], "fieldValidateRulesMaxlength": "128"}, {"documentation": "资源类型", "fieldName": "resourceType", "fieldType": "ResourceType", "fieldTypeDocumentation": "资源类型枚举\\n区分后端接口权限与前端菜单/按钮/字段权限", "fieldValidateRules": ["required"], "fieldValues": "API,MENU,BUTTON,FIELD"}, {"documentation": "URL 模式", "fieldName": "urlPattern", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "256"}, {"documentation": "HTTP 方法", "fieldName": "method", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "16"}, {"documentation": "前端路由", "fieldName": "frontendRoute", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "128"}, {"documentation": "后端接口", "fieldName": "backendUrl", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "128"}, {"documentation": "图标", "fieldName": "icon", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "64"}, {"documentation": "排序", "fieldName": "sortOrder", "fieldType": "Integer"}, {"documentation": "组件路径", "fieldName": "component", "fieldType": "String"}, {"documentation": "重定向", "fieldName": "redirect", "fieldType": "String"}, {"documentation": "描述", "fieldName": "description", "fieldType": "String"}, {"documentation": "扩展元数据", "fieldName": "metadata", "fieldType": "String"}, {"documentation": "乐观锁版本", "fieldName": "version", "fieldType": "Integer", "fieldValidateRules": ["required"]}, {"documentation": "创建者", "fieldName": "created<PERSON>y", "fieldType": "String"}, {"documentation": "创建时间", "fieldName": "createdAt", "fieldType": "Instant", "fieldValidateRules": ["required"]}, {"documentation": "更新者", "fieldName": "updatedBy", "fieldType": "String"}, {"documentation": "更新时间", "fieldName": "updatedAt", "fieldType": "Instant", "fieldValidateRules": ["required"]}, {"documentation": "软删除标志", "fieldName": "isDeleted", "fieldType": "Boolean", "fieldValidateRules": ["required"]}], "microserviceName": "whiskerguardOrgService", "name": "Permission", "pagination": "pagination", "relationships": [], "searchEngine": "no", "service": "serviceImpl"}
{"annotations": {"changelogDate": "20250522065325"}, "applications": "*", "clientRootFolder": "whiskerguardOrgService", "databaseType": "sql", "dto": "mapstruct", "fields": [{"fieldName": "id", "fieldType": "<PERSON>"}, {"documentation": "标签名称", "fieldName": "name", "fieldType": "String", "fieldValidateRules": ["required"]}, {"documentation": "描述", "fieldName": "description", "fieldType": "String"}, {"documentation": "乐观锁版本", "fieldName": "version", "fieldType": "Integer", "fieldValidateRules": ["required"]}, {"documentation": "创建者", "fieldName": "created<PERSON>y", "fieldType": "String"}, {"documentation": "创建时间", "fieldName": "createdAt", "fieldType": "Instant", "fieldValidateRules": ["required"]}, {"documentation": "更新者", "fieldName": "updatedBy", "fieldType": "String"}, {"documentation": "更新时间", "fieldName": "updatedAt", "fieldType": "Instant", "fieldValidateRules": ["required"]}, {"documentation": "软删除标志", "fieldName": "isDeleted", "fieldType": "Boolean", "fieldValidateRules": ["required"]}], "microserviceName": "whiskerguardOrgService", "name": "Tag", "pagination": "pagination", "relationships": [{"otherEntityField": "name", "otherEntityName": "tagCategory", "relationshipName": "category", "relationshipSide": "left", "relationshipType": "many-to-one"}], "searchEngine": "no", "service": "serviceImpl"}
{"annotations": {"changelogDate": "20250522065330"}, "applications": "*", "clientRootFolder": "whiskerguardOrgService", "databaseType": "sql", "dto": "mapstruct", "fields": [{"fieldName": "id", "fieldType": "<PERSON>"}, {"documentation": "举报原因", "fieldName": "reason", "fieldType": "String", "fieldValidateRules": ["required"]}, {"documentation": "举报详情", "fieldName": "details", "fieldType": "String"}, {"documentation": "处理状态", "fieldName": "status", "fieldType": "ReportStatus", "fieldValidateRules": ["required"], "fieldValues": "PENDING,PROCESSING,RESOLVED,REJECTED,IGNORED"}, {"documentation": "处理结果", "fieldName": "result", "fieldType": "String"}, {"documentation": "扩展元数据（JSONB）", "fieldName": "metadata", "fieldType": "String"}, {"documentation": "乐观锁版本", "fieldName": "version", "fieldType": "Integer", "fieldValidateRules": ["required"]}, {"documentation": "创建者", "fieldName": "created<PERSON>y", "fieldType": "String"}, {"documentation": "创建时间", "fieldName": "createdAt", "fieldType": "Instant", "fieldValidateRules": ["required"]}, {"documentation": "更新者", "fieldName": "updatedBy", "fieldType": "String"}, {"documentation": "更新时间", "fieldName": "updatedAt", "fieldType": "Instant", "fieldValidateRules": ["required"]}, {"documentation": "软删除标志", "fieldName": "isDeleted", "fieldType": "Boolean", "fieldValidateRules": ["required"]}], "microserviceName": "whiskerguardOrgService", "name": "NewsReport", "pagination": "pagination", "relationships": [{"otherEntityField": "title", "otherEntityName": "news", "relationshipName": "news", "relationshipSide": "left", "relationshipType": "many-to-one"}, {"otherEntityField": "id", "otherEntityName": "newsComment", "relationshipName": "comment", "relationshipSide": "left", "relationshipType": "many-to-one"}, {"otherEntityField": "username", "otherEntityName": "employee", "relationshipName": "reporter", "relationshipSide": "left", "relationshipType": "many-to-one", "relationshipWithBuiltInEntity": true}], "searchEngine": "no", "service": "serviceImpl"}
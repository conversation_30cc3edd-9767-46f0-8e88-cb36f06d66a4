{"annotations": {"changelogDate": "20250427130051"}, "applications": "*", "clientRootFolder": "whiskerguardOrgService", "databaseType": "sql", "documentation": "组织单元（OrgUnit）实体\\n管理公司、部门、事业群、团队等组织层级，并维护树形结构", "dto": "mapstruct", "fields": [{"documentation": "主键 ID", "fieldName": "id", "fieldType": "<PERSON>"}, {"documentation": "租户 ID，标识不同租户的数据隔离", "fieldName": "tenantId", "fieldType": "<PERSON>", "fieldValidateRules": ["required"]}, {"documentation": "组织单位名称", "fieldName": "name", "fieldType": "String", "fieldValidateRules": ["required", "maxlength"], "fieldValidateRulesMaxlength": "128"}, {"documentation": "唯一编码，用于外部系统对接或导入映射", "fieldName": "code", "fieldType": "String", "fieldValidateRules": ["required", "maxlength"], "fieldValidateRulesMaxlength": "64"}, {"documentation": "组织单元类型", "fieldName": "type", "fieldType": "OrgUnitType", "fieldTypeDocumentation": "组织单元类型枚举\\n定义组织单元的分类：公司、部门、事业群、团队", "fieldValidateRules": ["required"], "fieldValues": "COMPANY,DEPARTME<PERSON>,BUSINESS_GROUP,TEAM"}, {"documentation": "层级深度，根节点为 1", "fieldName": "level", "fieldType": "Integer", "fieldValidateRules": ["required"]}, {"documentation": "状态：1=启用，0=禁用", "fieldName": "status", "fieldType": "Integer", "fieldValidateRules": ["required"]}, {"documentation": "排序序号", "fieldName": "sortOrder", "fieldType": "Integer"}, {"documentation": "描述信息", "fieldName": "description", "fieldType": "String"}, {"documentation": "扩展元数据（JSON）", "fieldName": "metadata", "fieldType": "String"}, {"documentation": "乐观锁版本", "fieldName": "version", "fieldType": "Integer", "fieldValidateRules": ["required"]}, {"documentation": "创建者", "fieldName": "created<PERSON>y", "fieldType": "String"}, {"documentation": "创建时间", "fieldName": "createdAt", "fieldType": "Instant", "fieldValidateRules": ["required"]}, {"documentation": "更新者", "fieldName": "updatedBy", "fieldType": "String"}, {"documentation": "更新时间", "fieldName": "updatedAt", "fieldType": "Instant", "fieldValidateRules": ["required"]}, {"documentation": "软删除标志", "fieldName": "isDeleted", "fieldType": "Boolean", "fieldValidateRules": ["required"]}], "microserviceName": "whiskerguardOrgService", "name": "OrgUnit", "pagination": "pagination", "relationships": [{"otherEntityName": "orgUnit", "relationshipName": "parent", "relationshipSide": "left", "relationshipType": "many-to-one"}], "searchEngine": "no", "service": "serviceImpl"}
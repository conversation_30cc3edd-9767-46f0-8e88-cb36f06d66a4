{"annotations": {"changelogDate": "20250717101428"}, "applications": "*", "clientRootFolder": "whiskerguardOrgService", "databaseType": "sql", "documentation": "预约体验（Reservation）实体", "dto": "mapstruct", "fields": [{"documentation": "主键ID", "fieldName": "id", "fieldType": "<PERSON>"}, {"documentation": "您的姓名", "fieldName": "name", "fieldType": "String", "fieldValidateRules": ["required", "maxlength"], "fieldValidateRulesMaxlength": "64"}, {"documentation": "职位", "fieldName": "position", "fieldType": "String", "fieldValidateRules": ["required", "maxlength"], "fieldValidateRulesMaxlength": "64"}, {"documentation": "手机号码", "fieldName": "mobile", "fieldType": "String", "fieldValidateRules": ["required", "maxlength"], "fieldValidateRulesMaxlength": "32"}, {"documentation": "电子邮箱", "fieldName": "email", "fieldType": "String", "fieldValidateRules": ["required", "maxlength"], "fieldValidateRulesMaxlength": "128"}, {"documentation": "公司名称", "fieldName": "company", "fieldType": "String", "fieldValidateRules": ["required", "maxlength"], "fieldValidateRulesMaxlength": "128"}, {"documentation": "所属行业", "fieldName": "industry", "fieldType": "String", "fieldValidateRules": ["required", "maxlength"], "fieldValidateRulesMaxlength": "64"}, {"documentation": "企业规模", "fieldName": "companySize", "fieldType": "String", "fieldValidateRules": ["required"]}, {"documentation": "最关注合规管理需求", "fieldName": "focusNeed", "fieldType": "String", "fieldValidateRules": ["required"]}, {"documentation": "其他需求说明", "fieldName": "otherDesc", "fieldType": "String"}, {"documentation": "补充字段", "fieldName": "metadata", "fieldType": "String"}, {"documentation": "当前版本号", "fieldName": "version", "fieldType": "Integer", "fieldValidateRules": ["required"]}, {"documentation": "创建者账号或姓名", "fieldName": "created<PERSON>y", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "64"}, {"documentation": "创建时间", "fieldName": "createdAt", "fieldType": "Instant", "fieldValidateRules": ["required"]}, {"documentation": "最后修改者", "fieldName": "updatedBy", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "64"}, {"documentation": "最后更新时间", "fieldName": "updatedAt", "fieldType": "Instant", "fieldValidateRules": ["required"]}, {"documentation": "是否删除：0 表示正常 1 表示已删除", "fieldName": "isDeleted", "fieldType": "Boolean", "fieldValidateRules": ["required"]}], "microserviceName": "whiskerguardOrgService", "name": "Reservation", "pagination": "pagination", "relationships": [], "searchEngine": "no", "service": "serviceImpl"}
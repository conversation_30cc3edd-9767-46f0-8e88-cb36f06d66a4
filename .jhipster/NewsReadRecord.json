{"annotations": {"changelogDate": "20250522065331"}, "applications": "*", "clientRootFolder": "whiskerguardOrgService", "databaseType": "sql", "dto": "mapstruct", "fields": [{"fieldName": "id", "fieldType": "<PERSON>"}, {"documentation": "阅读时间", "fieldName": "readAt", "fieldType": "Instant", "fieldValidateRules": ["required"]}, {"documentation": "阅读来源", "fieldName": "source", "fieldType": "String"}, {"documentation": "阅读设备", "fieldName": "device", "fieldType": "String"}, {"documentation": "阅读时长(秒)", "fieldName": "duration", "fieldType": "Integer"}, {"documentation": "扩展元数据（JSONB）", "fieldName": "metadata", "fieldType": "String"}, {"documentation": "乐观锁版本", "fieldName": "version", "fieldType": "Integer", "fieldValidateRules": ["required"]}, {"documentation": "创建者", "fieldName": "created<PERSON>y", "fieldType": "String"}, {"documentation": "创建时间", "fieldName": "createdAt", "fieldType": "Instant", "fieldValidateRules": ["required"]}, {"documentation": "更新者", "fieldName": "updatedBy", "fieldType": "String"}, {"documentation": "更新时间", "fieldName": "updatedAt", "fieldType": "Instant", "fieldValidateRules": ["required"]}, {"documentation": "软删除标志", "fieldName": "isDeleted", "fieldType": "Boolean", "fieldValidateRules": ["required"]}], "microserviceName": "whiskerguardOrgService", "name": "NewsReadRecord", "pagination": "pagination", "relationships": [{"otherEntityField": "title", "otherEntityName": "news", "relationshipName": "news", "relationshipSide": "left", "relationshipType": "many-to-one"}, {"otherEntityField": "username", "otherEntityName": "employee", "relationshipName": "reader", "relationshipSide": "left", "relationshipType": "many-to-one", "relationshipWithBuiltInEntity": true}], "searchEngine": "no", "service": "serviceImpl"}
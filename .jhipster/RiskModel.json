{"annotations": {"changelogDate": "20250704013855"}, "applications": "*", "clientRootFolder": "whiskerguardOrgService", "databaseType": "sql", "documentation": "风险模型主表", "dto": "mapstruct", "fields": [{"documentation": "租户 ID", "fieldName": "tenantId", "fieldType": "<PERSON>", "fieldValidateRules": ["required"]}, {"documentation": "模型名称", "fieldName": "name", "fieldType": "String", "fieldValidateRules": ["required", "maxlength"], "fieldValidateRulesMaxlength": "128"}, {"documentation": "模型描述", "fieldName": "description", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "512"}, {"documentation": "生效开始时间", "fieldName": "effectiveFrom", "fieldType": "Instant", "fieldValidateRules": ["required"]}, {"documentation": "生效结束时间", "fieldName": "effectiveTo", "fieldType": "Instant"}, {"documentation": "是否默认模型", "fieldName": "isDefault", "fieldType": "Boolean", "fieldValidateRules": ["required"]}, {"documentation": "乐观锁版本", "fieldName": "version", "fieldType": "Integer", "fieldValidateRules": ["required"]}, {"documentation": "扩展元数据", "fieldName": "metadata", "fieldType": "String"}, {"documentation": "创建者", "fieldName": "created<PERSON>y", "fieldType": "String"}, {"documentation": "创建时间", "fieldName": "createdAt", "fieldType": "Instant", "fieldValidateRules": ["required"]}, {"documentation": "更新者", "fieldName": "updatedBy", "fieldType": "String"}, {"documentation": "更新时间", "fieldName": "updatedAt", "fieldType": "Instant", "fieldValidateRules": ["required"]}, {"documentation": "软删除标志", "fieldName": "isDeleted", "fieldType": "Boolean", "fieldValidateRules": ["required"]}], "microserviceName": "whiskerguardOrgService", "name": "RiskModel", "pagination": "pagination", "relationships": [], "searchEngine": "no", "service": "serviceImpl"}
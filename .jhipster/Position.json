{"annotations": {"changelogDate": "20250427130053"}, "applications": "*", "clientRootFolder": "whiskerguardOrgService", "databaseType": "sql", "documentation": "岗位/职位（Position）实体", "dto": "mapstruct", "fields": [{"documentation": "主键 ID", "fieldName": "id", "fieldType": "<PERSON>"}, {"documentation": "租户 ID", "fieldName": "tenantId", "fieldType": "<PERSON>", "fieldValidateRules": ["required"]}, {"documentation": "岗位编码", "fieldName": "code", "fieldType": "String", "fieldValidateRules": ["required", "maxlength", "unique"], "fieldValidateRulesMaxlength": "64"}, {"documentation": "岗位名称", "fieldName": "name", "fieldType": "String", "fieldValidateRules": ["required", "maxlength"], "fieldValidateRulesMaxlength": "128"}, {"documentation": "岗位级别", "fieldName": "level", "fieldType": "Integer", "fieldValidateRules": ["min", "max"], "fieldValidateRulesMax": "10", "fieldValidateRulesMin": "1"}, {"documentation": "岗位分类", "fieldName": "category", "fieldType": "PositionCategory", "fieldTypeDocumentation": "岗位分类枚举", "fieldValues": "MANAGEMENT (管理类),TECHNICAL (技术类),BUSINESS (业务类),SUPPORT (支持类)"}, {"documentation": "描述信息", "fieldName": "description", "fieldType": "String"}, {"documentation": "扩展元数据", "fieldName": "metadata", "fieldType": "String"}, {"documentation": "乐观锁版本", "fieldName": "version", "fieldType": "Integer", "fieldValidateRules": ["required"]}, {"documentation": "创建者", "fieldName": "created<PERSON>y", "fieldType": "String"}, {"documentation": "创建时间", "fieldName": "createdAt", "fieldType": "Instant", "fieldValidateRules": ["required"]}, {"documentation": "更新者", "fieldName": "updatedBy", "fieldType": "String"}, {"documentation": "更新时间", "fieldName": "updatedAt", "fieldType": "Instant", "fieldValidateRules": ["required"]}, {"documentation": "软删除标志", "fieldName": "isDeleted", "fieldType": "Boolean", "fieldValidateRules": ["required"]}], "microserviceName": "whiskerguardOrgService", "name": "Position", "pagination": "pagination", "relationships": [{"otherEntityName": "orgUnit", "relationshipName": "orgUnit", "relationshipSide": "left", "relationshipType": "many-to-one", "relationshipWithBuiltInEntity": true}], "searchEngine": "no", "service": "serviceImpl"}
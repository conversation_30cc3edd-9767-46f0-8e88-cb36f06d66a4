{"annotations": {"changelogDate": "20250427130101"}, "applications": "*", "clientRootFolder": "whiskerguardOrgService", "databaseType": "sql", "documentation": "租户详情（TenantProfile）实体", "dto": "mapstruct", "fields": [{"documentation": "主键 ID", "fieldName": "id", "fieldType": "<PERSON>"}, {"documentation": "工商注册号", "fieldName": "registrationNumber", "fieldType": "String"}, {"documentation": "注册日期", "fieldName": "registrationDate", "fieldType": "LocalDate"}, {"documentation": "注册资本", "fieldName": "registeredCapital", "fieldType": "BigDecimal"}, {"documentation": "公司类型", "fieldName": "companyType", "fieldType": "String"}, {"documentation": "经营范围", "fieldName": "businessScope", "fieldType": "String"}, {"documentation": "所属行业", "fieldName": "industry", "fieldType": "String"}, {"documentation": "税务登记号", "fieldName": "taxRegistrationNumber", "fieldType": "String"}, {"documentation": "组织机构代码", "fieldName": "organizationCode", "fieldType": "String"}, {"documentation": "注册地址", "fieldName": "registeredAddress", "fieldType": "String"}, {"documentation": "邮政编码", "fieldName": "postalCode", "fieldType": "String"}, {"documentation": "官网", "fieldName": "website", "fieldType": "String"}, {"documentation": "传真", "fieldName": "fax", "fieldType": "String"}, {"documentation": "联系人", "fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "String"}, {"documentation": "联系人手机", "fieldName": "contactMobile", "fieldType": "String"}, {"documentation": "联系人邮箱", "fieldName": "contactEmail", "fieldType": "String"}, {"documentation": "开户行", "fieldName": "bankName", "fieldType": "String"}, {"documentation": "银行账号", "fieldName": "bankAccount", "fieldType": "String"}, {"documentation": "营业执照路径", "fieldName": "businessLicensePath", "fieldType": "String"}, {"documentation": "法人代表", "fieldName": "legal<PERSON>erson", "fieldType": "String"}, {"documentation": "法人证件号", "fieldName": "legalPersonId", "fieldType": "String"}, {"documentation": "扩展元数据", "fieldName": "metadata", "fieldType": "String"}, {"documentation": "乐观锁版本", "fieldName": "version", "fieldType": "Integer", "fieldValidateRules": ["required"]}, {"documentation": "创建者", "fieldName": "created<PERSON>y", "fieldType": "String"}, {"documentation": "创建时间", "fieldName": "createdAt", "fieldType": "Instant", "fieldValidateRules": ["required"]}, {"documentation": "更新者", "fieldName": "updatedBy", "fieldType": "String"}, {"documentation": "更新时间", "fieldName": "updatedAt", "fieldType": "Instant", "fieldValidateRules": ["required"]}, {"documentation": "软删除标志", "fieldName": "isDeleted", "fieldType": "Boolean", "fieldValidateRules": ["required"]}], "microserviceName": "whiskerguardOrgService", "name": "TenantProfile", "pagination": "pagination", "relationships": [{"otherEntityName": "tenant", "relationshipName": "tenant", "relationshipSide": "left", "relationshipType": "many-to-one", "relationshipWithBuiltInEntity": true}], "searchEngine": "no", "service": "serviceImpl"}
{"annotations": {"changelogDate": "20250522065323"}, "applications": "*", "clientRootFolder": "whiskerguardOrgService", "databaseType": "sql", "dto": "mapstruct", "fields": [{"fieldName": "id", "fieldType": "<PERSON>"}, {"documentation": "状态", "fieldName": "status", "fieldType": "NewsStatus", "fieldTypeDocumentation": "完善后的新闻管理实体 — 添加到 whiskerguard-org-service 的 JDL\\n包含状态枚举、Tag 实体、多对多标签、JSONB 元数据及审计字段\\n增加了附件管理、评论状态、阅读记录、举报功能、标签分类等功能", "fieldValidateRules": ["required"], "fieldValues": "DRAFT,PENDING_REVIEW,PUBLISHED,ARCHIVED"}, {"documentation": "排序序号", "fieldName": "sortOrder", "fieldType": "Integer"}, {"documentation": "分类名称", "fieldName": "name", "fieldType": "String", "fieldValidateRules": ["required"]}, {"documentation": "描述信息", "fieldName": "description", "fieldType": "String"}, {"documentation": "封面图 URL", "fieldName": "coverImageUrl", "fieldType": "String"}, {"documentation": "扩展元数据（JSONB）", "fieldName": "metadata", "fieldType": "String"}, {"documentation": "乐观锁版本", "fieldName": "version", "fieldType": "Integer", "fieldValidateRules": ["required"]}, {"documentation": "创建者", "fieldName": "created<PERSON>y", "fieldType": "String"}, {"documentation": "创建时间", "fieldName": "createdAt", "fieldType": "Instant", "fieldValidateRules": ["required"]}, {"documentation": "更新者", "fieldName": "updatedBy", "fieldType": "String"}, {"documentation": "更新时间", "fieldName": "updatedAt", "fieldType": "Instant", "fieldValidateRules": ["required"]}, {"documentation": "软删除标志", "fieldName": "isDeleted", "fieldType": "Boolean", "fieldValidateRules": ["required"]}], "microserviceName": "whiskerguardOrgService", "name": "NewsCategory", "pagination": "pagination", "relationships": [{"otherEntityField": "name", "otherEntityName": "orgUnit", "relationshipName": "orgUnit", "relationshipSide": "left", "relationshipType": "many-to-one", "relationshipWithBuiltInEntity": true}, {"otherEntityField": "name", "otherEntityName": "newsCategory", "relationshipName": "parent", "relationshipSide": "left", "relationshipType": "many-to-one"}], "searchEngine": "no", "service": "serviceImpl"}
{"annotations": {"changelogDate": "20250427130055"}, "applications": "*", "clientRootFolder": "whiskerguardOrgService", "databaseType": "sql", "documentation": "角色（Role）实体", "dto": "mapstruct", "fields": [{"documentation": "主键 ID", "fieldName": "id", "fieldType": "<PERSON>"}, {"documentation": "租户 ID", "fieldName": "tenantId", "fieldType": "<PERSON>", "fieldValidateRules": ["required"]}, {"documentation": "角色名称", "fieldName": "name", "fieldType": "String", "fieldValidateRules": ["required", "maxlength"], "fieldValidateRulesMaxlength": "64"}, {"documentation": "角色编码", "fieldName": "code", "fieldType": "String", "fieldValidateRules": ["required", "maxlength"], "fieldValidateRulesMaxlength": "64"}, {"documentation": "描述", "fieldName": "description", "fieldType": "String"}, {"documentation": "状态", "fieldName": "status", "fieldType": "Integer", "fieldValidateRules": ["required"]}, {"documentation": "扩展元数据", "fieldName": "metadata", "fieldType": "String"}, {"documentation": "乐观锁版本", "fieldName": "version", "fieldType": "Integer", "fieldValidateRules": ["required"]}, {"documentation": "创建者", "fieldName": "created<PERSON>y", "fieldType": "String"}, {"documentation": "创建时间", "fieldName": "createdAt", "fieldType": "Instant", "fieldValidateRules": ["required"]}, {"documentation": "更新者", "fieldName": "updatedBy", "fieldType": "String"}, {"documentation": "更新时间", "fieldName": "updatedAt", "fieldType": "Instant", "fieldValidateRules": ["required"]}, {"documentation": "软删除标志", "fieldName": "isDeleted", "fieldType": "Boolean", "fieldValidateRules": ["required"]}], "microserviceName": "whiskerguardOrgService", "name": "Role", "pagination": "pagination", "relationships": [{"otherEntityName": "role", "relationshipName": "parent", "relationshipSide": "left", "relationshipType": "many-to-one"}], "searchEngine": "no", "service": "serviceImpl"}
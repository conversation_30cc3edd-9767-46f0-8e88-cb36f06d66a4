{"annotations": {"changelogDate": "20250715060316"}, "applications": "*", "clientRootFolder": "whiskerguardOrgService", "databaseType": "sql", "documentation": "新闻标签关联实体\\n\\n用于管理新闻和标签之间的多对多关联关系\\n包含租户隔离、版本控制和审计字段", "dto": "mapstruct", "fields": [{"documentation": "主键 ID", "fieldName": "id", "fieldType": "<PERSON>", "fieldValidateRules": ["required"]}, {"documentation": "租户 ID", "fieldName": "tenantId", "fieldType": "<PERSON>", "fieldValidateRules": ["required"]}, {"documentation": "扩展元数据", "fieldName": "metadata", "fieldType": "TextBlob"}, {"documentation": "乐观锁版本", "fieldName": "version", "fieldType": "Integer", "fieldValidateRules": ["required"]}, {"documentation": "创建者", "fieldName": "created<PERSON>y", "fieldType": "String"}, {"documentation": "创建时间", "fieldName": "createdAt", "fieldType": "Instant", "fieldValidateRules": ["required"]}, {"documentation": "更新者", "fieldName": "updatedBy", "fieldType": "String"}, {"documentation": "更新时间", "fieldName": "updatedAt", "fieldType": "Instant", "fieldValidateRules": ["required"]}, {"documentation": "软删除标志", "fieldName": "isDeleted", "fieldType": "Boolean", "fieldValidateRules": ["required"]}], "microserviceName": "whiskerguardOrgService", "name": "NewsTags", "pagination": "pagination", "relationships": [{"otherEntityName": "tag", "relationshipName": "tags", "relationshipSide": "left", "relationshipType": "many-to-one", "relationshipValidateRules": "required", "relationshipWithBuiltInEntity": true}, {"otherEntityName": "news", "relationshipName": "news", "relationshipSide": "left", "relationshipType": "many-to-one", "relationshipValidateRules": "required", "relationshipWithBuiltInEntity": true}], "searchEngine": "no", "service": "serviceImpl"}
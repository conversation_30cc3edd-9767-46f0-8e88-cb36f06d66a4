{"annotations": {"changelogDate": "20250704013857"}, "applications": "*", "clientRootFolder": "whiskerguardOrgService", "databaseType": "sql", "documentation": "具体规则（关键词、阈值、脚本等）", "dto": "mapstruct", "fields": [{"documentation": "租户 ID", "fieldName": "tenantId", "fieldType": "<PERSON>", "fieldValidateRules": ["required"]}, {"documentation": "规则编码", "fieldName": "code", "fieldType": "String", "fieldValidateRules": ["required", "minlength", "maxlength"], "fieldValidateRulesMaxlength": "64", "fieldValidateRulesMinlength": "1"}, {"documentation": "规则名称", "fieldName": "name", "fieldType": "String", "fieldValidateRules": ["required", "maxlength"], "fieldValidateRulesMaxlength": "128"}, {"documentation": "规则类型", "fieldName": "ruleType", "fieldType": "RiskRuleType", "fieldValidateRules": ["required"], "fieldValues": "THRESHOLD,PATTERN,SCRIPT"}, {"documentation": "规则参数", "fieldName": "params", "fieldType": "String"}, {"documentation": "描述信息", "fieldName": "description", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "512"}, {"documentation": "评分/权重", "fieldName": "score", "fieldType": "Integer"}, {"documentation": "乐观锁版本", "fieldName": "version", "fieldType": "Integer", "fieldValidateRules": ["required"]}, {"documentation": "扩展元数据", "fieldName": "metadata", "fieldType": "String"}, {"documentation": "创建者", "fieldName": "created<PERSON>y", "fieldType": "String"}, {"documentation": "创建时间", "fieldName": "createdAt", "fieldType": "Instant", "fieldValidateRules": ["required"]}, {"documentation": "更新者", "fieldName": "updatedBy", "fieldType": "String"}, {"documentation": "更新时间", "fieldName": "updatedAt", "fieldType": "Instant", "fieldValidateRules": ["required"]}, {"documentation": "软删除标志", "fieldName": "isDeleted", "fieldType": "Boolean", "fieldValidateRules": ["required"]}], "microserviceName": "whiskerguardOrgService", "name": "RiskRule", "pagination": "pagination", "relationships": [{"documentation": "RiskRule ➜ RiskCategory", "otherEntityField": "name", "otherEntityName": "riskCategory", "relationshipName": "riskCategory", "relationshipSide": "left", "relationshipType": "many-to-one"}], "searchEngine": "no", "service": "serviceImpl"}
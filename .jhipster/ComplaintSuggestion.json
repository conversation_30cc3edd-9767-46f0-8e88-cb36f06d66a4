{"annotations": {"changelogDate": "20250704065920"}, "applications": "*", "clientRootFolder": "whiskerguardOrgService", "databaseType": "sql", "documentation": "投诉与建议表", "dto": "mapstruct", "fields": [{"documentation": "租户ID", "fieldName": "tenantId", "fieldType": "<PERSON>"}, {"documentation": "员工ID", "fieldName": "employeeId", "fieldType": "<PERSON>"}, {"documentation": "详情", "fieldName": "detail", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "500"}, {"documentation": "是否匿名：0、否 1、是", "fieldName": "isAnonymous", "fieldType": "Integer"}, {"documentation": "联系方式", "fieldName": "contactWay", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "32"}, {"documentation": "扩展元数据（JSONB）", "fieldName": "metadata", "fieldType": "TextBlob"}, {"documentation": "乐观锁版本", "fieldName": "version", "fieldType": "Integer", "fieldValidateRules": ["required"]}, {"documentation": "创建者", "fieldName": "created<PERSON>y", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "64"}, {"documentation": "创建时间", "fieldName": "createdAt", "fieldType": "Instant", "fieldValidateRules": ["required"]}, {"documentation": "更新者", "fieldName": "updatedBy", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "64"}, {"documentation": "更新时间", "fieldName": "updatedAt", "fieldType": "Instant", "fieldValidateRules": ["required"]}, {"documentation": "软删除标志", "fieldName": "isDeleted", "fieldType": "Boolean", "fieldValidateRules": ["required"]}], "microserviceName": "whiskerguardOrgService", "name": "ComplaintSuggestion", "pagination": "pagination", "relationships": [], "searchEngine": "no", "service": "serviceImpl"}
{"annotations": {"changelogDate": "20250704013856"}, "applications": "*", "clientRootFolder": "whiskerguardOrgService", "databaseType": "sql", "documentation": "风险类别（高/中/低等，可自定义）", "dto": "mapstruct", "fields": [{"documentation": "租户 ID", "fieldName": "tenantId", "fieldType": "<PERSON>", "fieldValidateRules": ["required"]}, {"documentation": "类别名称", "fieldName": "name", "fieldType": "String", "fieldValidateRules": ["required", "maxlength"], "fieldValidateRulesMaxlength": "128"}, {"documentation": "风险等级", "fieldName": "level", "fieldType": "RiskLevel", "fieldTypeDocumentation": "1. 枚举类型", "fieldValidateRules": ["required"], "fieldValues": "HIGH,MEDIUM,LOW"}, {"documentation": "入组表达式", "fieldName": "expression", "fieldType": "String", "fieldValidateRules": ["required", "maxlength"], "fieldValidateRulesMaxlength": "1024"}, {"documentation": "描述信息", "fieldName": "description", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "512"}, {"documentation": "乐观锁版本", "fieldName": "version", "fieldType": "Integer", "fieldValidateRules": ["required"]}, {"documentation": "扩展元数据", "fieldName": "metadata", "fieldType": "String"}, {"documentation": "创建者", "fieldName": "created<PERSON>y", "fieldType": "String"}, {"documentation": "创建时间", "fieldName": "createdAt", "fieldType": "Instant", "fieldValidateRules": ["required"]}, {"documentation": "更新者", "fieldName": "updatedBy", "fieldType": "String"}, {"documentation": "更新时间", "fieldName": "updatedAt", "fieldType": "Instant", "fieldValidateRules": ["required"]}, {"documentation": "软删除标志", "fieldName": "isDeleted", "fieldType": "Boolean", "fieldValidateRules": ["required"]}], "microserviceName": "whiskerguardOrgService", "name": "RiskCategory", "pagination": "pagination", "relationships": [{"documentation": "RiskCategory ➜ RiskModel", "otherEntityField": "name", "otherEntityName": "riskModel", "relationshipName": "riskModel", "relationshipSide": "left", "relationshipType": "many-to-one"}], "searchEngine": "no", "service": "serviceImpl"}
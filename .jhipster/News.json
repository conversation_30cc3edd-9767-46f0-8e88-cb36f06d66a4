{"annotations": {"changelogDate": "20250522065326"}, "applications": "*", "clientRootFolder": "whiskerguardOrgService", "databaseType": "sql", "dto": "mapstruct", "fields": [{"fieldName": "id", "fieldType": "<PERSON>"}, {"documentation": "状态", "fieldName": "status", "fieldType": "NewsStatus", "fieldTypeDocumentation": "完善后的新闻管理实体 — 添加到 whiskerguard-org-service 的 JDL\\n包含状态枚举、Tag 实体、多对多标签、JSONB 元数据及审计字段\\n增加了附件管理、评论状态、阅读记录、举报功能、标签分类等功能", "fieldValidateRules": ["required"], "fieldValues": "DRAFT,PENDING_REVIEW,PUBLISHED,ARCHIVED"}, {"documentation": "排序序号", "fieldName": "sortOrder", "fieldType": "Integer"}, {"documentation": "副标题", "fieldName": "subtitle", "fieldType": "String"}, {"documentation": "标题", "fieldName": "title", "fieldType": "String", "fieldValidateRules": ["required"]}, {"documentation": "摘要", "fieldName": "summary", "fieldType": "String"}, {"documentation": "关键词（用于SEO，全局搜索）", "fieldName": "keywords", "fieldType": "String"}, {"documentation": "正文内容", "fieldName": "content", "fieldType": "String"}, {"documentation": "发布时间", "fieldName": "publishDate", "fieldType": "Instant"}, {"documentation": "正式发布时戳", "fieldName": "publishedAt", "fieldType": "Instant"}, {"documentation": "浏览量", "fieldName": "viewCount", "fieldType": "Integer"}, {"documentation": "点赞数", "fieldName": "likeCount", "fieldType": "Integer"}, {"documentation": "评论数", "fieldName": "commentCount", "fieldType": "Integer"}, {"documentation": "分享数", "fieldName": "shareCount", "fieldType": "Integer"}, {"documentation": "封面图 URL", "fieldName": "coverImageUrl", "fieldType": "String"}, {"documentation": "是否置顶", "fieldName": "isSticky", "fieldType": "Boolean"}, {"documentation": "置顶开始时间", "fieldName": "stickyStartTime", "fieldType": "Instant"}, {"documentation": "置顶结束时间", "fieldName": "stickyEndTime", "fieldType": "Instant"}, {"documentation": "扩展元数据（JSONB）", "fieldName": "metadata", "fieldType": "String"}, {"documentation": "乐观锁版本", "fieldName": "version", "fieldType": "Integer", "fieldValidateRules": ["required"]}, {"documentation": "创建者", "fieldName": "created<PERSON>y", "fieldType": "String"}, {"documentation": "创建时间", "fieldName": "createdAt", "fieldType": "Instant", "fieldValidateRules": ["required"]}, {"documentation": "更新者", "fieldName": "updatedBy", "fieldType": "String"}, {"documentation": "更新时间", "fieldName": "updatedAt", "fieldType": "Instant", "fieldValidateRules": ["required"]}, {"documentation": "软删除标志", "fieldName": "isDeleted", "fieldType": "Boolean", "fieldValidateRules": ["required"]}], "microserviceName": "whiskerguardOrgService", "name": "News", "pagination": "pagination", "relationships": [{"otherEntityField": "name", "otherEntityName": "newsCategory", "relationshipName": "category", "relationshipSide": "left", "relationshipType": "many-to-one"}, {"otherEntityField": "name", "otherEntityName": "orgUnit", "relationshipName": "orgUnit", "relationshipSide": "left", "relationshipType": "many-to-one", "relationshipWithBuiltInEntity": true}, {"otherEntityField": "username", "otherEntityName": "employee", "relationshipName": "author", "relationshipSide": "left", "relationshipType": "many-to-one", "relationshipWithBuiltInEntity": true}, {"otherEntityField": "name", "otherEntityName": "tag", "relationshipName": "tags", "relationshipSide": "left", "relationshipType": "many-to-many"}], "searchEngine": "no", "service": "serviceImpl"}
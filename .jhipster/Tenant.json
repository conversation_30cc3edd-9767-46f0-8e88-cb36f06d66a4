{"annotations": {"changelogDate": "20250427130100"}, "applications": "*", "clientRootFolder": "whiskerguardOrgService", "databaseType": "sql", "documentation": "租户（Tenant）实体", "dto": "mapstruct", "fields": [{"documentation": "主键 ID", "fieldName": "id", "fieldType": "<PERSON>"}, {"documentation": "租户编码", "fieldName": "tenantCode", "fieldType": "String", "fieldValidateRules": ["required", "maxlength"], "fieldValidateRulesMaxlength": "64"}, {"documentation": "租户名称", "fieldName": "name", "fieldType": "String", "fieldValidateRules": ["required", "maxlength"], "fieldValidateRulesMaxlength": "128"}, {"documentation": "租户状态", "fieldName": "status", "fieldType": "Integer", "fieldValidateRules": ["required"]}, {"documentation": "套餐类型", "fieldName": "subscriptionPlan", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "64"}, {"documentation": "套餐开始日期", "fieldName": "subscriptionStart", "fieldType": "LocalDate"}, {"documentation": "套餐结束日期", "fieldName": "subscriptionEnd", "fieldType": "LocalDate"}, {"documentation": "联系人邮箱", "fieldName": "contactEmail", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "128"}, {"documentation": "联系人电话", "fieldName": "contactPhone", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "32"}, {"documentation": "是否是系统租户", "fieldName": "isSystem", "fieldType": "Boolean"}, {"documentation": "扩展元数据", "fieldName": "metadata", "fieldType": "String"}, {"documentation": "乐观锁版本", "fieldName": "version", "fieldType": "Integer", "fieldValidateRules": ["required"]}, {"documentation": "创建者", "fieldName": "created<PERSON>y", "fieldType": "String"}, {"documentation": "创建时间", "fieldName": "createdAt", "fieldType": "Instant", "fieldValidateRules": ["required"]}, {"documentation": "更新者", "fieldName": "updatedBy", "fieldType": "String"}, {"documentation": "更新时间", "fieldName": "updatedAt", "fieldType": "Instant", "fieldValidateRules": ["required"]}, {"documentation": "软删除标志", "fieldName": "isDeleted", "fieldType": "Boolean", "fieldValidateRules": ["required"]}], "microserviceName": "whiskerguardOrgService", "name": "Tenant", "pagination": "pagination", "relationships": [], "searchEngine": "no", "service": "serviceImpl"}
{"annotations": {"changelogDate": "20250427130102"}, "applications": "*", "clientRootFolder": "whiskerguardOrgService", "databaseType": "sql", "documentation": "租户附件（TenantAttachment）实体", "dto": "mapstruct", "fields": [{"documentation": "主键 ID", "fieldName": "id", "fieldType": "<PERSON>"}, {"documentation": "附件类型", "fieldName": "type", "fieldType": "String"}, {"documentation": "文件 URL", "fieldName": "fileUrl", "fieldType": "String"}, {"documentation": "文件名", "fieldName": "fileName", "fieldType": "String"}, {"documentation": "文件大小", "fieldName": "fileSize", "fieldType": "<PERSON>"}, {"documentation": "描述", "fieldName": "description", "fieldType": "String"}, {"documentation": "扩展元数据", "fieldName": "metadata", "fieldType": "String"}, {"documentation": "乐观锁版本", "fieldName": "version", "fieldType": "Integer", "fieldValidateRules": ["required"]}, {"documentation": "上传者", "fieldName": "uploadedBy", "fieldType": "String"}, {"documentation": "上传时间", "fieldName": "uploadedAt", "fieldType": "Instant", "fieldValidateRules": ["required"]}, {"documentation": "更新者", "fieldName": "updatedBy", "fieldType": "String"}, {"documentation": "更新时间", "fieldName": "updatedAt", "fieldType": "Instant", "fieldValidateRules": ["required"]}, {"documentation": "软删除标志", "fieldName": "isDeleted", "fieldType": "Boolean", "fieldValidateRules": ["required"]}], "microserviceName": "whiskerguardOrgService", "name": "TenantAttachment", "pagination": "pagination", "relationships": [{"otherEntityName": "tenant", "relationshipName": "tenant", "relationshipSide": "left", "relationshipType": "many-to-one"}], "searchEngine": "no", "service": "serviceImpl"}
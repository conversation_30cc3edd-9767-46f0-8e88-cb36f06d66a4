{"generator-jhipster": {"applicationType": "microservice", "authenticationType": "jwt", "baseName": "whiskerguardOrgService", "buildTool": "maven", "cacheProvider": "redis", "clientFramework": "no", "clientTestFrameworks": null, "clientTheme": null, "creationTimestamp": 1745757459076, "databaseType": "sql", "devDatabaseType": "mysql", "enableHibernateCache": true, "enableTranslation": true, "entities": ["OrgUnit", "Employee", "Position", "EmployeeOrg", "Role", "Permission", "RolePermission", "EmployeeRole", "AuditLog", "Tenant", "TenantProfile", "TenantAttachment", "NewsCategory", "TagCategory", "Tag", "News", "NewsComment", "NewsLike", "NewsAttachment", "NewsReport", "NewsReadRecord", "TenantInitialize", "RiskModel", "RiskCategory", "RiskRule", "ComplaintSuggestion", "ComplaintSuggestionAttachment", "LetterCommitment", "NewsTags", "Reservation"], "feignClient": true, "jhipsterVersion": "8.10.0", "jwtSecretKey": "ZjUwMTQ3MjYzODBmYjVmOTFmMDRhOGI5ZDQxMzcyNzYwZTc0NzRiZDA0NzgyYzYxMDc4NjA3YzQwZmI2OWUzNTM4MDQyMGNmN2Y2Yzk0Yjg1ZmYzZmU1NTY2MTZlNDFjYzcwMTI2YTA5OWVjZjM3NWRjYTFiN2FhY2E3MzU0MDc=", "languages": ["zh-cn"], "lastLiquibaseTimestamp": 1752747268000, "microfrontend": null, "microfrontends": [], "nativeLanguage": "zh-cn", "packageName": "com.whiskerguard.organization", "prodDatabaseType": "mysql", "reactive": false, "serverPort": "8083", "serviceDiscoveryType": "consul", "skipClient": true, "skipUserManagement": true, "syncUserWithIdp": null, "testFrameworks": [], "withAdminUi": null}}
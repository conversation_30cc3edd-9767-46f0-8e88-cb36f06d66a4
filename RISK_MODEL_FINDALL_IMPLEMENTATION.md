# RiskModel findAll 方法实现

## 概述

实现了`RiskModelService.findAll(RiskModelReq req)`方法，支持多种查询条件和分页功能。

## 实现的功能

### 1. 主要的 findAll 方法

```java
@Override
@Transactional(readOnly = true)
public Page<RiskModelDTO> findAll(RiskModelReq req) {
    Long tenantId = Long.valueOf(HttpRequestUtil.getHeader(request, RequestConstants.X_TENANT_ID));
    LOG.debug("Request to get all RiskModels by condition: {}", req);
    
    // 创建分页参数
    Pageable pageable = createPageable(req);
    
    // 解析日期范围
    Instant startDate = parseDate(req.getCreatedAtStart());
    Instant endDate = parseDate(req.getCreatedAtEnd());
    
    // 如果结束日期存在，设置为当天的23:59:59
    if (endDate != null) {
        endDate = endDate.plusSeconds(86399);
    }
    
    // 使用自定义查询方法处理所有条件
    Page<RiskModel> riskModelPage = riskModelRepository.findByCondition(
        tenantId, 
        req.getName(), 
        startDate, 
        endDate, 
        pageable
    );
    
    // 转换为DTO并返回
    return riskModelPage.map(riskModelMapper::toDto);
}
```

### 2. Example API 示例方法

```java
public Page<RiskModelDTO> findAllUsingExample(RiskModelReq req) {
    Long tenantId = Long.valueOf(HttpRequestUtil.getHeader(request, RequestConstants.X_TENANT_ID));
    
    // 创建RiskModel实体作为Example的probe（查询模板）
    RiskModel probe = new RiskModel();
    probe.setTenantId(tenantId);
    probe.setIsDeleted(false);
    
    // 创建ExampleMatcher，配置匹配规则
    ExampleMatcher matcher = ExampleMatcher.matching()
        .withIgnoreNullValues() // 忽略null值字段
        .withIgnoreCase() // 忽略大小写
        .withStringMatcher(ExampleMatcher.StringMatcher.CONTAINING); // 字符串包含匹配
    
    // 如果有名称搜索，设置到name字段进行模糊匹配
    if (StringUtils.hasText(req.getName())) {
        probe.setName(req.getName());
        // 为name字段配置特定的匹配规则
        matcher = matcher.withMatcher("name", 
            ExampleMatcher.GenericPropertyMatchers.contains().ignoreCase());
    }
    
    // 创建Example对象
    Example<RiskModel> example = Example.of(probe, matcher);
    
    // 创建分页参数
    Pageable pageable = createPageable(req);
    
    // 使用Example查询
    Page<RiskModel> riskModelPage = riskModelRepository.findAll(example, pageable);
    
    // 转换为DTO并返回
    return riskModelPage.map(riskModelMapper::toDto);
}
```

## Repository 自定义查询方法

```java
@Query("SELECT rm FROM RiskModel rm WHERE rm.tenantId = :tenantId AND rm.isDeleted = false " +
       "AND (:name IS NULL OR :name = '' OR LOWER(rm.name) LIKE LOWER(CONCAT('%', :name, '%'))) " +
       "AND (:startDate IS NULL OR rm.createdAt >= :startDate) " +
       "AND (:endDate IS NULL OR rm.createdAt <= :endDate)")
Page<RiskModel> findByCondition(
    @Param("tenantId") Long tenantId,
    @Param("name") String name,
    @Param("startDate") Instant startDate,
    @Param("endDate") Instant endDate,
    Pageable pageable
);
```

## 支持的查询条件

### RiskModelReq 参数说明

| 字段 | 类型 | 描述 | 查询方式 |
|------|------|------|----------|
| categoryId | Long | 风险模型ID | 暂未使用 |
| name | String | 风险模型名称 | 模糊匹配（LIKE '%name%'） |
| createdAtStart | String | 创建开始时间 | 日期范围查询（>=） |
| createdAtEnd | String | 创建结束时间 | 日期范围查询（<=） |
| page | Integer | 页码 | 分页参数 |
| size | Integer | 每页大小 | 分页参数 |

### 固定过滤条件

- **租户隔离**：自动过滤当前租户的数据
- **软删除过滤**：只查询未删除的记录（isDeleted = false）

## 辅助方法

### 1. 创建分页参数

```java
private Pageable createPageable(RiskModelReq req) {
    int page = req.getPage() != null ? req.getPage() : 0;
    int size = req.getSize() != null ? req.getSize() : NumberConstants.TEN;
    
    // 默认按创建时间倒序排列
    Sort sort = Sort.by(Sort.Direction.DESC, "createdAt");
    
    return PageRequest.of(page, size, sort);
}
```

### 2. 日期解析

```java
private Instant parseDate(String dateStr) {
    if (!StringUtils.hasText(dateStr)) {
        return null;
    }
    
    try {
        // 假设日期格式为 yyyy-MM-dd
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate localDate = LocalDate.parse(dateStr, formatter);
        return localDate.atStartOfDay(ZoneId.systemDefault()).toInstant();
    } catch (Exception e) {
        LOG.warn("日期解析失败: {}", dateStr, e);
        return null;
    }
}
```

## 查询示例

### 1. 基本分页查询

```json
{
  "page": 0,
  "size": 10
}
```

### 2. 按名称模糊搜索

```json
{
  "name": "风险",
  "page": 0,
  "size": 10
}
```

### 3. 日期范围查询

```json
{
  "createdAtStart": "2024-01-01",
  "createdAtEnd": "2024-12-31",
  "page": 0,
  "size": 10
}
```

### 4. 组合条件查询

```json
{
  "name": "信用风险",
  "createdAtStart": "2024-06-01",
  "createdAtEnd": "2024-08-31",
  "page": 0,
  "size": 20
}
```

## 返回结果格式

```json
{
  "content": [
    {
      "id": 1,
      "name": "信用风险模型",
      "description": "用于评估信用风险的模型",
      "effectiveFrom": "2024-01-01 00:00:00",
      "effectiveTo": "2024-12-31 23:59:59",
      "isDefault": true,
      "version": 1,
      "createdBy": "admin",
      "createdAt": "2024-08-04 10:00:00",
      "updatedBy": "admin",
      "updatedAt": "2024-08-04 10:00:00",
      "isDeleted": false
    }
  ],
  "pageable": {
    "sort": {
      "sorted": true,
      "unsorted": false
    },
    "pageNumber": 0,
    "pageSize": 10
  },
  "totalElements": 1,
  "totalPages": 1,
  "last": true,
  "first": true,
  "numberOfElements": 1
}
```

## 技术特点

### 优势

1. **灵活的条件查询**：支持名称模糊搜索和日期范围查询
2. **分页支持**：完整的分页功能，包括排序
3. **租户隔离**：自动处理多租户数据隔离
4. **类型安全**：使用强类型的查询参数
5. **性能优化**：使用索引友好的查询条件

### Example API vs 自定义查询

| 特性 | Example API | 自定义@Query |
|------|-------------|--------------|
| 简单条件 | ✅ 适合 | ✅ 适合 |
| 复杂条件 | ❌ 不支持 | ✅ 支持 |
| 日期范围 | ❌ 不支持 | ✅ 支持 |
| OR条件 | ❌ 不支持 | ✅ 支持 |
| 代码简洁性 | ✅ 简洁 | ⚠️ 较复杂 |
| 类型安全 | ✅ 类型安全 | ⚠️ 字符串查询 |

## 最佳实践

1. **简单查询**：使用Example API（如示例方法所示）
2. **复杂查询**：使用自定义@Query方法
3. **性能考虑**：为常用查询字段添加数据库索引
4. **日期处理**：统一使用Instant类型，支持时区转换
5. **分页优化**：合理设置默认页面大小，避免大结果集

通过这种实现方式，RiskModel的查询功能既保持了灵活性，又确保了性能和可维护性。
